<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <groupId>com.ruijing.order</groupId>
    <artifactId>order-base-dependency</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <description>订单通用依赖</description>

    <properties>
        <testable.version>0.6.6</testable.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <msharp.version>1.5.7-SNAPSHOT</msharp.version>
        <pagehelper.version>5.3.3</pagehelper.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>msharp-spring-boot-autoconfigure</artifactId>
                <version>${msharp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>msharp-cat-spring-boot-starter</artifactId>
                <version>${msharp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>msharp-pearl-spring-boot-starter</artifactId>
                <version>${msharp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>msharp-cache-spring-boot-starter</artifactId>
                <version>${msharp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ruijing.fundamental</groupId>
                <artifactId>inf-bom-spring-boot-starter</artifactId>
                <version>${msharp.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.ruijing.base</groupId>
                <version>1.0.0-SNAPSHOT</version>
                <artifactId>base-biz-logger-logback</artifactId>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>
        </repository>
        <repository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repositories/maven-public/</url>
        </repository>
    </repositories>

    <distributionManagement>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://nexus.rj-info.com/repository/maven-snapshots/</url>

        </snapshotRepository>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://nexus.rj-info.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>maven-public</id>
            <name>maven-public</name>
            <url>http://nexus.rj-info.com/repository/maven-public/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>