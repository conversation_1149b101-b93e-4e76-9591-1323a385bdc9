package com.ruijing.order.configure;

import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.remoting.msharp.constant.RpcConstant;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @createTime: 2023-05-18 15:11
 * @description: 使用该SDK所有项目通用配置
 **/
@Configuration
public class CommonPropertiesConfiguration implements InitializingBean {

    @Override
    public void afterPropertiesSet() {
        // 不需要钉钉异常告警的类
        Environment.setProperty(RpcConstant.RPC_ALARM_IGNORE_EXCEPTIONS, "com.ruijing.order.exception.BusinessInterceptException");
    }
}
