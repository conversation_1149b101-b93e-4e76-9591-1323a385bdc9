<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>store-order-service</artifactId>
        <groupId>com.ruijing.store</groupId>
        <version>1.0.6-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ruijing</groupId>
    <artifactId>store-order-server</artifactId>
    <packaging>jar</packaging>

    <properties>
        <reagent.supp.version>3.2.0-SNAPSHOT</reagent.supp.version>
        <spring-boot.version>2.1.5.RELEASE</spring-boot.version>
    </properties>

    <dependencies>
        <!-- 用户标签       -->
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>store-user-tag-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--采购限额管控依赖-->
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-risk-control-service-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-api</artifactId>
            <version>${msharp.version}</version>
        </dependency>
        <!--采购规则依赖        -->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-rule-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-bid-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <!--logback if标签依赖包-->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.6</version>
        </dependency>
        <!--        上传文件依赖-->
        <dependency>
            <artifactId>msharp-upload-client</artifactId>
            <groupId>com.ruijing.fundamental</groupId>
            <version>${msharp.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
        </dependency>
    <!--供应商-->
        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-crm-api</artifactId>
            <version>1.3.9-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing</groupId>
            <artifactId>store-apply-api</artifactId>
            <version>1.5.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-carry-fee-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
<!--        引入商品服务-->
        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-goods-api</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-cms-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <!--中爆对接-->
        <dependency>
            <groupId>com.ruijing.cooperation</groupId>
            <artifactId>cooperation-cbsd-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <!--        &lt;!&ndash; 经费卡rpc&ndash;&gt;-->
        <dependency>
            <groupId>com.reagent.research</groupId>
            <artifactId>research-fundcard-api</artifactId>
            <version>2.2.8-SNAPSHOT</version>
        </dependency>

<!--        采购审批日志-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-approval-api</artifactId>
            <version>2.4.0-SNAPSHOT</version>
        </dependency>

<!--        竞价审批日志-->
        <dependency>
            <groupId>com.reagent.bid</groupId>
            <artifactId>bid-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.reagent.auth</groupId>
            <artifactId>auth-api</artifactId>
            <version>1.3.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-gaea-api</artifactId>
            <version>1.2.7-SNAPSHOT</version>
        </dependency>
        <!--用户中心-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-user-api</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>base-biz-gateway-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-oms-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <!--中大判断-->
        <dependency>
            <groupId>com.reagent.research</groupId>
            <artifactId>custom-business-api</artifactId>
            <version>1.2.7-SNAPSHOT</version>
        </dependency>
        <!--评论Rpc-->
        <dependency>
            <groupId>com.reagent.credit</groupId>
            <artifactId>credit-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <!--用户中心Rpc服务-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-organization-api</artifactId>
            <version>1.0.25-SNAPSHOT</version>
        </dependency>
        <!--商品分类-->
        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-category-api</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>
        <!--基础配置rpc服务-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-baseconfig-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <!--        危化品rpc服务-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-category-api</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <!--        结算单rpc-->
        <dependency>
            <groupId>com.reagent.research</groupId>
            <artifactId>research-statement-api</artifactId>
            <version>3.0.3-SNAPSHOT</version>
        </dependency>
        <!--供应商入驻-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-supp-include-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--        mybatis分页插件-->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>

        <!--供应商-->
        <dependency>
            <groupId>com.reagent.supp</groupId>
            <artifactId>supp-api</artifactId>
            <version>3.3.16-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-order-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.reagent.order</groupId>
            <artifactId>order-galaxy-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--        钱包扣费rpc接口-->
        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-wallet-manager-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <!--锐竞springboot 基础框架-->
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>inf-bom-spring-boot-starter</artifactId>
            <version>${msharp.version}</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>store-wms-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-wms-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.reagent.local.deploy</groupId>
            <artifactId>local-deploy-platform-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.reagent.tpi.tpiclient</groupId>
            <artifactId>tpiclient-api</artifactId>
            <version>9.0.3-SNAPSHOT</version>
        </dependency>

<!--        订单同步服务-->
        <dependency>
            <groupId>com.ruijing.sync</groupId>
            <artifactId>store-search-sync-order-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
<!--        价格合同服务-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-price-contract-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>1.10.19</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.alibaba.testable</groupId>
            <artifactId>testable-all</artifactId>
            <version>${testable.version}</version>
            <scope>test</scope>
        </dependency>

        <!--站内信-->
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>base-letter-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--电子签名-->
        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>electronic-sign-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.reagent.order</groupId>
            <artifactId>order-thunder-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.order</groupId>
            <artifactId>order-whitehole-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.order</groupId>
            <artifactId>order-saturn-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.order</groupId>
            <artifactId>order-base-client</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.job</groupId>
            <artifactId>msharp-job-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--        自研消息队列-->
        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>base-biz-mq-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <!--政采目录-->
        <dependency>
            <groupId>com.ruijing</groupId>
            <artifactId>store-control-api</artifactId>
            <version>1.2.7-SNAPSHOT</version>
        </dependency>

        <!--消息系统-->
        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-message-api</artifactId>
            <version>1.5.6-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>research-reimbursement-api</artifactId>
            <groupId>com.reagent.research</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-srm-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--供应商-->
        <dependency>
            <groupId>com.ruijing.shop</groupId>
            <artifactId>shop-trade-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-filing-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.reagent.research</groupId>
            <artifactId>research-sysu-order-service-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>base-biz-organization-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <artifactId>base-biz-address-api</artifactId>
            <groupId>com.ruijing.base</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.fundamental</groupId>
            <artifactId>msharp-commons-collections</artifactId>
            <version>${msharp.version}</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.store</groupId>
            <artifactId>store-batchcode-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.ruijing.base</groupId>
            <artifactId>store-auth-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13</version>
            <scope>test</scope>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <!--<plugin>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    &lt;!&ndash;如果想在没有web.xml文件的情况下构建WAR，请设置为false&ndash;&gt;
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    &lt;!&ndash;设置war包的名字&ndash;&gt;
                    <warName>store-order-service</warName>
                </configuration>
            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.0.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.txt</include>
                    <include>*.yml</include>
                    <include>*.properties</include>
                    <include>*.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
