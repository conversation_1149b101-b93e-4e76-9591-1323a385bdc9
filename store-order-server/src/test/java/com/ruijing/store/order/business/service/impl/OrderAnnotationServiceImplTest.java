package com.ruijing.store.order.business.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.gateway.buyercenter.request.OrderTipsRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderTipsVO;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.internal.bytebuddy.implementation.bytecode.Throw;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

public class OrderAnnotationServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderAnnotationServiceImpl orderAnnotationService;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @Mock
    private CacheClient cacheClient;

    @Test
    public void overtimeSettleHint() {
        OrderTipsRequest tipsReq = new OrderTipsRequest();
        tipsReq.setCode("code");
        tipsReq.setUserId(17191);

        Mockito.when(cacheClient.exists(Mockito.any())).thenReturn(false, true);

        SearchPageResultDTO<OrderMasterSearchDTO> searchRes0 = new SearchPageResultDTO<>();
        SearchPageResultDTO<OrderMasterSearchDTO> searchRes1 = new SearchPageResultDTO<>();
        searchRes0.setTotalHits(0L);
        searchRes1.setTotalHits(1L);
        Mockito.when(orderSearchBoostService.commonSearch(Mockito.any())).thenReturn(searchRes1, searchRes1);

        // 正常测试，需要检索和显示
        RemoteResponse<List<OrderTipsVO>> response = orderAnnotationService.overtimeSettleHint(null, tipsReq);
        Assert.assertTrue(CollectionUtils.isNotEmpty(response.getData()));

        // 不需要检索或显示
        response = orderAnnotationService.overtimeSettleHint(null, tipsReq);
        Assert.assertTrue(CollectionUtils.isEmpty(response.getData()));
    }

    @Test
    public void clickHints() {
        OrderTipsRequest req = new OrderTipsRequest();
        req.setUserId(123);
        req.setCode("code");
        // 正常
        Mockito.doNothing().when(cacheClient).putToCache(Mockito.any(), Mockito.any(), Mockito.any());
        RemoteResponse<Boolean> response = orderAnnotationService.clickHints(null, req);
        Assert.assertTrue(response.isSuccess());
        // Error不阻塞
        Error error = new Error();
        Mockito.doThrow(error).when(cacheClient).putToCache(Mockito.any(), Mockito.any(), Mockito.any());
        response = orderAnnotationService.clickHints(null, req);
        Assert.assertFalse(response.isSuccess());
    }

    @Test
    public void removeClickHistory() {
        OrderTipsRequest req = new OrderTipsRequest();
        req.setUserId(123);
        req.setCode("code");
        // 正常
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.any());
        RemoteResponse<Boolean> response = orderAnnotationService.removeClickHistory(null, req);
        Assert.assertTrue(response.isSuccess());
        // Error不阻塞
        Error error = new Error();
        Mockito.doThrow(error).when(cacheClient).removeCache(Mockito.any());
        response = orderAnnotationService.removeClickHistory(null, req);
        Assert.assertFalse(response.isSuccess());
    }
}