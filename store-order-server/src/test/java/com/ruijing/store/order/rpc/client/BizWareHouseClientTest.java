package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.InboundReqDTO;
import com.ruijing.store.wms.api.service.BizEntryService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;


public class BizWareHouseClientTest extends MockBaseTestCase {

    @InjectMocks
    private BizWareHouseClient bizWareHouseClient;

    @Mock
    private BizEntryService bizEntryService;

    @Test
    public void autoInbound() {
        RemoteResponse<Boolean> mockRes = RemoteResponse.success();

        Mockito.when(bizEntryService.autoInbound(Mockito.any(InboundReqDTO.class))).thenReturn(mockRes);

        String masterJson = "{\"id\":177219,\"fmasterguid\":null,\"ftbuyappid\":250620,\"forderno\":\"DC202103221306401\",\"forderdate\":1616380253000,\"fbuyerid\":16142,\"fbuyercode\":null,\"fbuyername\":\"临时采购\",\"fbuyeremail\":\"<EMAIL>\",\"fbuyercontactman\":\"临时人\",\"fbuyertelephone\":\"18666666666\",\"fbuydepartmentid\":28918,\"fbuydepartment\":\"临时部门\",\"fsuppid\":655,\"fsuppcode\":\"S0655\",\"fsuppname\":\"宏兴生物信息科技有限公司（测试）5\",\"fbiderdeliveryplace\":\"广东省广州市番禺区新造镇广州医科大学实验教学楼A1栋5楼配送中心 转送 广东省广州市海珠区火星1号楼666，临时人，18666666666\",\"forderamounttotal\":110.00,\"fundStatus\":1,\"failedReason\":null,\"status\":8,\"fconfirmdate\":null,\"fconfirmmanid\":null,\"fconfirmman\":null,\"fcanceldate\":null,\"fcancelmanid\":null,\"fcancelman\":null,\"fdeliverydate\":null,\"fdeliverymanid\":null,\"fdeliveryman\":null,\"flastreceivedate\":null,\"flastreceivemanid\":null,\"flastreceiveman\":null,\"fassessdate\":null,\"fassessmanid\":null,\"fassessman\":null,\"projectid\":\"\",\"projectnumber\":\"1000\",\"projecttitle\":\"\",\"fuserid\":111,\"fusercode\":\"GUANG_ZHOU_YI_KE_DA_XUE\",\"fusername\":\"广州医科大学\",\"statementId\":null,\"fcancelreason\":null,\"frefuseCancelReason\":null,\"shutDownDate\":null,\"deliveryInfo\":null,\"deliveryNo\":null,\"returnAmount\":0.0,\"frefuseCancelDate\":null,\"fdeliveryid\":88808,\"bidOrderId\":null,\"orderType\":0,\"receivePicUrls\":null,\"tpiProjectId\":null,\"originalAmount\":110.00,\"inventoryStatus\":0,\"species\":0,\"updateTime\":1616380253000,\"inStateTime\":24372000000,\"purchaseRootinType\":0,\"carryFee\":10.00,\"invoiceTitleId\":null,\"invoiceTitle\":null,\"invoiceTitleNumber\":null,\"fundType\":0,\"fundTypeName\":null,\"paymentAmount\":0.00,\"statementStatus\":-1}";
        OrderMasterDO orderMasterDO = JsonUtils.fromJson(masterJson, OrderMasterDO.class);

        String detailJson = "[{\"id\":181506,\"fmasterid\":177219,\"fbiddate\":1616342400000,\"fdetailno\":\"DC202103221306401\",\"categoryid\":216,\"fclassification\":\"化学试剂\",\"fgoodcode\":\"S1397-100g\",\"fgoodname\":\"氯化钠;氯化钠\",\"fbrand\":null,\"fspec\":\"100g\",\"funit\":\"瓶\",\"fquantity\":1.00,\"fbidprice\":110.00,\"fbidamount\":110.00,\"fpicpath\":\"https://www.gray.rjmart.cn/supplier/null\",\"fremainquantity\":0.00,\"fbrandid\":null,\"tsuppmerpassid\":null,\"fcancelquantity\":0.00,\"fcancelamount\":0.00,\"productSn\":100000757443,\"returnStatus\":null,\"returnAmount\":0.0,\"originalAmount\":110.00,\"originalPrice\":110.00,\"modifyPrice\":false,\"deliveryTime\":7,\"remainderPrice\":0.00,\"negotiatedPrice\":null,\"sysuCategoryId\":null,\"categoryDirectoryId\":0,\"updateTime\":1616380253000,\"carryFee\":10.00,\"firstCategoryId\":51,\"firstCategoryName\":\"化学试剂\",\"secondCategoryId\":216,\"secondCategoryName\":\"化学试剂\",\"feeTypeTag\":\"实验耗材费\",\"categoryTag\":\"试剂\",\"dangerousTypeId\":9,\"dangerousTypeName\":\"常规化学品\",\"regulatoryTypeId\":2,\"regulatoryTypeName\":\"非管制\",\"casno\":\"7647-14-5\",\"suppId\":655,\"suppName\":\"宏兴生物信息科技有限公司（测试）5\",\"suppCode\":null,\"brandEname\":\"往圣(oneshine)\"}]";
        List orderDetailDTO = JsonUtils.fromJson(detailJson, List.class);
        List<OrderDetailDO> detailDTOS = new ArrayList<>();
        for (Object o : orderDetailDTO) {
            OrderDetailDO orderDetailDTO1 = JsonUtils.fromJson(JsonUtils.toJson(o), OrderDetailDO.class);
            detailDTOS.add(orderDetailDTO1);
        }
        UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
        userBaseInfoDTO.setGuid("test");
        userBaseInfoDTO.setName("hehe");
        Boolean aBoolean = bizWareHouseClient.autoInbound(orderMasterDO, detailDTOS, userBaseInfoDTO, Mockito.any());
        Assert.assertTrue("error", aBoolean);
    }
}