package com.ruijing.store.order.base.core.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.fundcard.dto.FundCardResultDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.fundcard.enums.HandleResultEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderOperatorDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.FundCardProjectRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.FundCardSubjectRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderFundCardRequestDTO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.utils.MyMockUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;


public class RefFundcardOrderServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private RefFundcardOrderServiceImpl refFundcardOrderService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Mock
    private StatementPlatformClient statementPlatformClient;

    @Mock
    private SysConfigClient sysConfigClient;

    @Mock
    private UserClient userClient;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private DepartmentRpcClient departmentRpcClient;


    @Test
    public void packageFundCardOrderDTOListTest() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<RefFundcardOrderServiceImpl> ref = (Class<RefFundcardOrderServiceImpl>) refFundcardOrderService.getClass();
        Method method = ref.getDeclaredMethod("packageFundCardOrderDTOList", List.class, ChangeFundCardRequestDTO.class, String.class);
        method.setAccessible(true);

        OrderMasterDO o = new OrderMasterDO();
        o.setId(1);
        o.setForderamounttotal(BigDecimal.valueOf(10.0));
        o.setReturnAmount(0D);
        Mockito.when(orderMasterMapper.findByIdIn(Mockito.anyList())).thenReturn(Arrays.asList(o));
        Mockito.when(sysConfigClient.getConfigByOrgCodeAndConfigCode(Mockito.anyString(), Mockito.anyString())).thenReturn("3");

        ChangeFundCardRequestDTO changeFundCard = new ChangeFundCardRequestDTO();
        changeFundCard.setOrderIds(Arrays.asList(1));
        FundCardProjectRequestDTO project = new FundCardProjectRequestDTO();
        project.setProjectCode("test");
        OrderFundCardRequestDTO fundCard = new OrderFundCardRequestDTO();
        fundCard.setCardId("test");
        FundCardSubjectRequestDTO subject = new FundCardSubjectRequestDTO();
        subject.setUseAmount(BigDecimal.TEN);
        fundCard.setSaveFundCardSubjectList(Arrays.asList(subject));
        project.setSaveFundCardList(Arrays.asList(fundCard));

        changeFundCard.setSaveProjectList(Arrays.asList(project));
        changeFundCard.setAnalysisFee(BigDecimal.ONE);
        changeFundCard.setConsumablesFee(BigDecimal.ONE);
        List<RefFundcardOrderDTO> invoke = (List<RefFundcardOrderDTO>) method.invoke(refFundcardOrderService, Arrays.asList(1), changeFundCard, OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getCode());
        Assert.assertTrue(invoke.size() > 0);

        invoke = (List<RefFundcardOrderDTO>) method.invoke(refFundcardOrderService, Arrays.asList(1), changeFundCard, OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode());
        Assert.assertTrue(invoke.size() > 0);

    }

    @Test
    public void orderFundCardFreeze() {
        UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
        userBaseInfoDTO.setJobnumber("111");
        userBaseInfoDTO.setName("lige");

        Map<String, Object> session = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid("test");
        sessionInfo.setSuppId(165);
        session.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setProviderContext(ProviderContext.getProviderContext());
        RpcContext.getProviderContext().setCallAttachments(session);

        Mockito.when(userClient.getUserInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(userBaseInfoDTO);

        FundCardResultDTO resultDTO = new FundCardResultDTO();
        resultDTO.setHandleResult(HandleResultEnum.UN_HANDLE.getCode());
        Mockito.when(researchFundCardServiceClient.fundCardFreezeBatch(Mockito.any())).thenReturn(resultDTO);

        FundCardDTO fundCardDTO = new FundCardDTO();
        FundCardManagerDTO fundCardManagerDTO = new FundCardManagerDTO();
        fundCardManagerDTO.setManagerCode("test");
        fundCardManagerDTO.setManagerName("test");
        fundCardDTO.setFundCardManagerDTOs(Arrays.asList(fundCardManagerDTO));
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.any(), Mockito.anyList())).thenReturn(Arrays.asList(fundCardDTO));
        Mockito.when(researchFundCardServiceClient.orderDefray(Mockito.any(), Mockito.anyList())).thenReturn(resultDTO);
        RefFundcardOrderDO refFundcardOrderDO = new RefFundcardOrderDO();
        refFundcardOrderDO.setCardId("test");
        Mockito.when(refFundcardOrderMapper.findByOrderId(Mockito.any())).thenReturn(Arrays.asList(refFundcardOrderDO));
        LoginUserInfoBO loginUserInfoBO = new LoginUserInfoBO();
        Mockito.when(userClient.getLoginUserInfo(Mockito.any(), Mockito.anyInt())).thenReturn(loginUserInfoBO);
        DepartmentDTO departmentDTO = new DepartmentDTO();
        departmentDTO.setManagerId(1);
        Mockito.when(departmentRpcClient.getDepartmentsByIds(Mockito.any())).thenReturn(Arrays.asList(departmentDTO));
        Mockito.when(userClient.getUserByIdsAndOrgId(Mockito.anyList(), Mockito.anyInt())).thenReturn(Arrays.asList(userBaseInfoDTO));
        Mockito.when(userClient.getLoginUserInfo(Mockito.any(), Mockito.anyInt())).thenReturn(loginUserInfoBO);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        orderMasterDO.setForderno("test");
        orderMasterDO.setFuserid(1);
        orderMasterDO.setForderamounttotal(BigDecimal.TEN);
        orderMasterDO.setFbuydepartmentid(1);
        orderMasterDO.setReturnAmount(1.00D);

        List<OrderDetailDO> orderDetailDOList = new ArrayList<>();
        OrderDetailDO detailDO = new OrderDetailDO();
        detailDO.setFgoodname("goodsName");
        detailDO.setFbidamount(BigDecimal.TEN);
        orderDetailDOList.add(detailDO);
        Integer operatorId = 1;
        Integer integer = refFundcardOrderService.orderFundCardFreeze(orderMasterDO, orderDetailDOList, loginUserInfoBO);
        Assert.assertTrue(integer > 0);


        resultDTO = new FundCardResultDTO();
        resultDTO.setRelyAsyCallback("0");
        resultDTO.setHandleResult(null);
//        Mockito.when(researchFundCardServiceClient.fundCardFreezeBatch(Mockito.any())).thenReturn(resultDTO);
        Mockito.when(researchFundCardServiceClient.orderDefray(Mockito.any(), Mockito.anyList())).thenReturn(resultDTO);
        integer = refFundcardOrderService.orderFundCardFreeze(null, orderDetailDOList, loginUserInfoBO);
        Assert.assertTrue(integer == 0);

        Mockito.when(researchFundCardServiceClient.orderDefray(Mockito.any(), Mockito.anyList())).thenThrow(new IllegalStateException("error"));
        integer = refFundcardOrderService.orderFundCardFreeze(orderMasterDO, orderDetailDOList, loginUserInfoBO);
        Assert.assertTrue(integer == 0);
    }

    @Test
    public void orderFundCardFreeze2() {
        OrderBasicParamDTO request = new OrderBasicParamDTO();
        request.setOrderId(1);
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(orderMasterDO);

        OrderDetailDO detailDO = new OrderDetailDO();
        detailDO.setId(1);
        detailDO.setFmasterid(1);
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(Arrays.asList(detailDO));
        MyMockUtils.setRPCContextRJSession("test", OrgEnum.SHEN_ZHEN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue(), 0);
        LoginUserInfoBO loginUserInfoBO = new LoginUserInfoBO();
        loginUserInfoBO.setUserId(1);
        Mockito.when(userClient.getLoginUserInfo(Mockito.anyString(), Mockito.anyInt())).thenReturn(loginUserInfoBO);
        Mockito.when(refFundcardOrderMapper.findByOrderId(Mockito.any())).thenReturn(Arrays.asList());

        Integer affect = refFundcardOrderService.orderFundCardFreeze(request);
        Assert.assertTrue(affect == 0);
    }

    @Test
    public void validateFundCard() {
        List<RefFundcardOrderDTO> refFundCardOrderDTO = new ArrayList<>();
        RefFundcardOrderDTO ref1 = new RefFundcardOrderDTO();
        ref1.setOrderId("1");
        ref1.setCardId("1");
        refFundCardOrderDTO.add(ref1);
        OrderOperatorDTO user = new OrderOperatorDTO();
        user.setOrgCode(OrgEnum.NAN_FANG_YI_KE.getCode());
        BigDecimal consumablesFee = BigDecimal.ONE;
        BigDecimal analysisFee = BigDecimal.ONE;

        Mockito.when(researchFundCardServiceClient.judgeUsableFundCardByRule(Mockito.any())).thenReturn(Arrays.asList());
        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setFirstCategoryId(1);
        orderDetailDO.setFbidamount(BigDecimal.TEN);
        Mockito.when(orderDetailMapper.findAllByFmasteridIn(Mockito.any())).thenReturn(Arrays.asList(orderDetailDO));

        refFundcardOrderService.validateFundCard(refFundCardOrderDTO, user, consumablesFee, analysisFee);
    }
}
