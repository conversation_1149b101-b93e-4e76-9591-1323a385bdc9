package com.ruijing.store.order.rpc.client;

import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.research.statement.api.statement.service.StatementUpdateApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;


public class ResearchStatementClientTest extends MockBaseTestCase {

    @Mock
    private StatementUpdateApi statementUpdateApi;

    @InjectMocks
    private ResearchStatementClient researchStatementClient;


    @Test
    public void createStatementSingle() {

        RemoteResponse<StatementResultDTO> response = RemoteResponse.<StatementResultDTO>custom().setSuccess();
        response.setData(new StatementResultDTO());
        Mockito.when(statementUpdateApi.createStatementSingle(Mockito.any())).thenReturn(response);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFsuppid(10);
        orderMasterDO.setId(10);
        orderMasterDO.setFbuydepartmentid(10);
        orderMasterDO.setFbuyerid(10);
        orderMasterDO.setForderamounttotal(BigDecimal.valueOf(100.0));
        orderMasterDO.setReturnAmount(0.00);
        orderMasterDO.setSpecies((byte) 0);
        orderMasterDO.setFuserid(2);
        StatementResultDTO result = researchStatementClient.createStatementSingle(100, "啊强", orderMasterDO);
        Assert.assertTrue(result != null);
    }
}
