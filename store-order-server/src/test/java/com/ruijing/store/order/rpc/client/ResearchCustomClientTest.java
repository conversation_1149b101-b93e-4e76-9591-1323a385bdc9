package com.ruijing.store.order.rpc.client;

import com.reagent.research.custom.api.order.OrderService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class ResearchCustomClientTest extends MockBaseTestCase {

    @InjectMocks
    private ResearchCustomClient researchCustomClient;

    @Mock
    private OrderService orderService;

    @Test
    public void agree2ReturnOrderForSYSU() {
        RemoteResponse<Boolean> remoteResponse = RemoteResponse.<Boolean>custom().setSuccess();
        remoteResponse.setData(true);
        Mockito.when(orderService.agree2ReturnOrder(Mockito.any())).thenReturn(remoteResponse);
        boolean success = researchCustomClient.agree2ReturnOrderForSYSU(1, "ZHONG_SHAN_DA_XUE");
        Assert.assertTrue("失败", success);
    }
}
