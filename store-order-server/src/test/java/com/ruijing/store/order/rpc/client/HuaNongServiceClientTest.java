package com.ruijing.store.order.rpc.client;

import com.reagent.tpi.tpiclient.api.orgapi.HuaNongService;
import com.reagent.tpi.tpiclient.message.req.order.OrderReq;
import com.reagent.tpi.tpiclient.message.resp.BaseResp;
import com.reagent.tpi.tpiclient.message.resp.order.OrderResp;
import com.ruijing.store.MockBaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import com.reagent.tpi.tpiclient.message.req.order.HNOrderBySelfBuyingReq;

public class HuaNongServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    private HuaNongServiceClient huaNongServiceClient;
    @Mock
    private HuaNongService huaNongService;

    @Test
    public void cancelOrder() {
        // 返回null
        Mockito.when(huaNongService.cancelOrder(Mockito.any())).thenReturn(null);
//        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog("test","test","test",
//                "test","test","test");
        try {
            huaNongServiceClient.cancelOrder(new OrderReq(), "test", "test");
        } catch (IllegalStateException ex) {
            Assert.assertTrue(ex.getMessage().equals("基理返回对象为null"));
        }

        // 失败
        BaseResp baseResp = new BaseResp();
        baseResp.setCode("0002");
        baseResp.setMessage("失败了");
        Mockito.when(huaNongService.cancelOrder(Mockito.any())).thenReturn(baseResp);
        try {
            huaNongServiceClient.cancelOrder(new OrderReq(), "test", "test");
        } catch (IllegalStateException ex) {
            System.out.println(ex.getMessage());
            Assert.assertTrue(ex.getMessage().equals("取消失败,失败原因失败了"));
        }

        // 成功
        baseResp = new BaseResp();
        baseResp.setCode("8");
        OrderResp orderResp = new OrderResp();
        orderResp.setStatus("8");
        baseResp.setData(orderResp);
        Mockito.when(huaNongService.cancelOrder(Mockito.any())).thenReturn(baseResp);
        huaNongServiceClient.cancelOrder(new OrderReq(), "test", "test");
    }

    @Test
    public void saveOrder() {
        Mockito.when(huaNongService.saveOrder(Mockito.any(HNOrderBySelfBuyingReq.class))).thenReturn(null);
        try {
            huaNongServiceClient.saveOrder(new HNOrderBySelfBuyingReq(), "sfdsgfsgkdjkj1kj123");
        }catch (Exception e){
            System.out.println(e instanceof IllegalStateException);
        }

        BaseResp baseResp = new BaseResp();
        baseResp.setData(null);
        Mockito.when(huaNongService.saveOrder(Mockito.any(HNOrderBySelfBuyingReq.class))).thenReturn(baseResp);
        try {
            huaNongServiceClient.saveOrder(new HNOrderBySelfBuyingReq(), "sfdsgfsgkdjkj1kj123");
        }catch (Exception e){
            System.out.println(e instanceof IllegalStateException);
        }
    }
}