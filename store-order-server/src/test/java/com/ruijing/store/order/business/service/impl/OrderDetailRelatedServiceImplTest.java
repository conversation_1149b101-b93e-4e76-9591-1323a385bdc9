package com.ruijing.store.order.business.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.reagent.supp.api.supplier.dto.SupplierDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.shop.crm.api.pojo.dto.OfflineSupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.crm.api.service.OfflineSupplierService;
import com.ruijing.shop.crm.api.support.PageResultBean;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyRefSuppBusinessDTO;
import com.ruijing.store.apply.dto.offline.OfflineExtraDTO;
import com.ruijing.store.apply.enums.application.ProcurementChannelEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper;
import com.ruijing.store.order.base.minor.model.ProductDescriptionSnapshotDO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.OrderDetailGoodsReturnBO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.PriceContractBaseBO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.SuppShopInfoBO;
import com.ruijing.store.order.business.service.orgondemand.SYSUOrderService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.contract.ContractInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderLogisticsInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderSnapshotVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderMasterVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderPicVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;

public class OrderDetailRelatedServiceImplTest extends MockBaseTestCase {

    @org.mockito.Mock
    private SYSUOrderService sysuOrderService;

    @org.mockito.Mock
    private PriceContractClient priceContractClient;

    @InjectMocks
    private OrderDetailRelatedServiceImpl orderDetailRelatedService;

    @org.mockito.Mock
    private UserClient userClient;

    @org.mockito.Mock
    private BuyerOrderServiceImpl buyerOrderService;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private SuppClient suppClient;

    @org.mockito.Mock
    private ApplicationBaseClient applicationBaseClient;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private InvoiceClient invoiceClient;

    @org.mockito.Mock
    private OfflineSupplierService offlineSupplierService;

    @InjectMocks
    private SuppClient suppClientInject;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private ProductDescriptionSnapshotMapper descSnapshotMapper;

    @org.mockito.Mock
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @org.mockito.Mock
    private DockingExtraMapper dockingExtraMapper;

    @org.mockito.Mock
    private RefFundcardOrderService refFundcardOrderService;

    @org.mockito.Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private OrderAddressRPCClient orderAddressRPCClient;

    @org.mockito.Mock
    private BidClient bidClient;

    public static class Mock {
        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private String getDeliveryMobile(OrderMasterDO orderMasterInfo) {
            return "deliveryMobile";
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        public ApplicationMasterDTO setOrderSource(OrderMasterDO orderMasterInfo, OrderInfoVO curOrderInfo) {
            return new ApplicationMasterDTO();
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private String getReceiveDesc(String orderId) {
            return "ReceiveDesc";
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        public Boolean specialOrgApprovePermission(LoginUserInfoBO loginInfo, OrderInfoVO curOrderInfo) {
            return false;
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private String getOrderRemark(Integer buyAppId, Integer suppId) {
            return "ReceiveDesc";
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private Map<Integer, List<OrderDetailGoodsReturnBO>> detailIdGoodsReturnMap(List<Integer> orderIdList) {
            return new HashMap<>();
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private void setGoodsReturnInfo(OrderInfoVO curOrderInfo, Map<Integer, List<OrderDetailGoodsReturnBO>> detailIdGoodsReturnMap, Boolean statementCheck) {
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private List<OrderPicVO> getReceiveImgs(OrderMasterDO orderMasterInfo) {
            return New.list();
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        public OrderOfflineInfoVO getOfflineInfo(OrderMasterDO orderMasterInfo) {
            return new OrderOfflineInfoVO();
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private String sysuSpecialOrderDate(LoginUserInfoBO loginInfo, Integer appId) {
            return "2021-01-01 10:10:00";
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        public List<ContractInfoVO> getOrderContractInfo(List<Integer> orderIdList) {
            return New.list();
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private OrderLogisticsInfoVO getLogisticsInfo(OrderMasterDO orderMasterInfo) {
            return new OrderLogisticsInfoVO();
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private Boolean getPublicity(OrderMasterDO orderMasterInfo, ApplicationMasterDTO appMasterInfo) {
            return true;
        }

        @MockMethod(targetClass = OrderDetailRelatedServiceImpl.class)
        private Integer canAddReceptPic(Boolean canAcceptByPermission, OrderMasterDO orderMasterInfo, LoginUserInfoBO loginInfo) {
            return 1;
        }
    }

    @Test
    public void testSetOrderPriceContractInfo() {
        Integer ftbuyappid = 12389237;
        OrderMasterVO orderMasterVO = new OrderMasterVO();
        RemoteResponse<List<ApplyMasterExtensionDTO>> response = new RemoteResponse<>();
        // mock intermediate objects
        List<PriceContractBaseBO> priceContractInfo = New.list();
        Long priceContractId = 39L;

        PriceContractBaseBO priceContractBaseBO = new PriceContractBaseBO() {{
            setContractId(39L);
            setContractNo("123");
        }};
        priceContractInfo.add(priceContractBaseBO);

        Mockito.when(priceContractClient.getPriceContractId(Mockito.anyInt())).thenReturn(priceContractId);
        Mockito.when(priceContractClient.getPriceContractBaseList(Mockito.anyList())).thenReturn(priceContractInfo);
//        orderDetailRelatedService.setOrderPriceContractInfo(ftbuyappid, orderMasterVO);

        Assert.assertEquals(orderMasterVO.getContractNo(), "123");
    }

    @Test
    public void testSpecialOrgApprovePermission() {
        // mock input-sysu
        LoginUserInfoBO loginInfo = new LoginUserInfoBO();
        OrderInfoVO curOrderInfo = new OrderInfoVO();
        OrderMasterVO orderMasterVO = new OrderMasterVO();
        orderMasterVO.setId(10086);
        curOrderInfo.setOrder(orderMasterVO);

        loginInfo.setOrgCode("ZHONG_SHAN_DA_XUE");
        loginInfo.setUserId(89757);
        Mockito.when(sysuOrderService.checkReceivePermission(Mockito.any(OrderInfoVO.class), Mockito.any(LoginUserInfoBO.class))).thenReturn(true);
        // run -sysu
        Boolean approveCheck = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertTrue(approveCheck);

        // mock input, with null user input -southern medical university
        loginInfo.setOrgCode("NAN_FANG_YI_KE");
        List<UserBaseInfoDTO> userInfoList = New.list();
        for (int i = 0; i < 5; i++) {
            UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
            userBaseInfoDTO.setId(i);
            userInfoList.add(userBaseInfoDTO);
        }
        userInfoList.add(0, null);
        userInfoList.add(1, new UserBaseInfoDTO());
        UserBaseInfoDTO userBaseInfoTrue = new UserBaseInfoDTO();
        userBaseInfoTrue.setId(89757);
        userInfoList.add(userBaseInfoTrue);
        Mockito.when(userClient.findUserByRoleIdAndOrgId(Mockito.anyList(), Mockito.anyInt())).thenReturn(userInfoList);
        // run -smu
        Mockito.when(userClient.checkUserInDepartmentAccess(Mockito.any(), Mockito.any(List.class), Mockito.any(), Mockito.any())).thenReturn(true);
        approveCheck = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertTrue(approveCheck);

        // mock other orgs
        loginInfo.setOrgCode("JI_NAN_DA_XUE");
        Mockito.when(userClient.checkUserInDepartmentAccess(Mockito.anyInt(), Mockito.anyList(), Mockito.anyList(), Mockito.anyInt())).thenReturn(true);
        // run other
        approveCheck = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertTrue(approveCheck);
    }

    @Test
    public void testGetOrderDetail() throws Exception {
        // mock input
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setGuid("f4hhgk2npcjjckwjwiad5rd4");
        rjSessionInfo.setOrgId(15);
        Integer orderId = 1768;

        // mock intermediate variables
        List<DepartmentDTO> deptList = New.list();
        DepartmentDTO departmentDTO = new DepartmentDTO();
        departmentDTO.setId(891);
        departmentDTO.setOrganizationId(15);
        departmentDTO.setManagerId(1719);
        deptList.add(departmentDTO);

        LoginUserInfoBO loginUserInfo = new LoginUserInfoBO();
        loginUserInfo.setDeptIdList(New.list(891));
        loginUserInfo.setOrgId(15);
        loginUserInfo.setRootDepartmentId(1228);
        loginUserInfo.setUserId(1724);
        loginUserInfo.setMobile("15600790320");
        loginUserInfo.setOrgCode("GUANG_DONG_GONG_YE_DA_XUE");
        loginUserInfo.setDeptList(deptList);
        loginUserInfo.setJobNumber("a6");
        loginUserInfo.setUserName("林广圣");
        loginUserInfo.setOrgName("广东工业大学");
        loginUserInfo.setUserGuid("f4hhgk2npcjjckwjwiad5rd4");
        Mockito.when(userClient.getLoginUserInfo(Mockito.anyString(), Mockito.anyInt(), Mockito.anyBoolean(), Mockito.anyString())).thenReturn(loginUserInfo);

        BaseConfigDTO baseConfigDTO = new BaseConfigDTO();
        baseConfigDTO.setConfigValue("1");
        baseConfigDTO.setConfigCode(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM);
        BaseConfigDTO baseConfigDTO1 = new BaseConfigDTO();
        baseConfigDTO1.setConfigValue("1");
        baseConfigDTO1.setConfigCode(ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
        List<BaseConfigDTO> baseConfigList = New.list(baseConfigDTO, baseConfigDTO1);
        Mockito.when(sysConfigClient.getValueByOrgCodeAndConfigCode(Mockito.anyString(), Mockito.anyList())).thenReturn(baseConfigList);

        // mock orderlist in www
        String masterRespJson = "{\"orderList\":[{\"order\":{\"id\":1768,\"orderNo\":\"DC201712150067201\",\"buyAppId\":1970,\"buyAppNo\":null,\"bidOrderId\":null,\"bidOrderSn\":null,\"orderDate\":\"2017-12-15 18:51:36\",\"fundCardIdList\":[],\"confirmDate\":\"2017-12-15 18:52:50\",\"confirmMan\":null,\"deliveryDate\":\"2017-12-15 18:53:06\",\"lastReceiveDate\":\"2017-12-15 18:53:59\",\"settleCreateDate\":\"2017-12-15 18:55:53\",\"settleFinishDate\":null,\"buyUserId\":1724,\"buyerName\":\"林广圣\",\"departmentName\":\"测试\",\"buyerTelephone\":\"15600790320\",\"deliveryAddress\":\"浙江省湖州市吴兴区中山大道北888 \",\"buyerContactMan\":\"张林\",\"status\":10,\"fundStatus\":0,\"failedReason\":null,\"totalPrice\":120.0,\"supplierName\":\"宏兴生物科技有限公司\",\"suppUser\":null,\"suppTelephone\":\"020-88990066\",\"suppQQ\":\"1231241414\",\"projectNumbers\":null,\"cancelReason\":null,\"wholePrice\":null,\"lastPrice\":null,\"acceptance\":0,\"contactPhone\":null,\"shutdownDate\":null,\"supplierId\":655,\"canCancel\":0,\"urls\":null,\"checkMan\":\"林广圣\",\"beforeModifyPriceAll\":\"120.0\",\"orderType\":\"0\",\"deliveryName\":null,\"deliveryMobile\":null,\"inventoryStatus\":0,\"canInbound\":true,\"statementStatus\":1,\"orderReceiveDesc\":\"\",\"buyDepartmentId\":891,\"deptGuid\":null,\"offlineInfo\":null,\"orgName\":\"广东工业大学\",\"orgId\":15,\"orderRemark\":null,\"relateInfo\":null,\"confirmPics\":null,\"confirmAddPics\":null,\"commentStatus\":2,\"overTimeFlag\":false,\"totalCarryFee\":0.0,\"dockingStatus\":null,\"collegeName\":\"\",\"canSubmitWarehouse\":0,\"canPrintInAndOutWarehouse\":0,\"publicity\":false,\"invoiceTitleId\":null,\"species\":0,\"waitForSettleTime\":null,\"contractId\":null,\"contractNo\":null,\"fbiderdeliveryplace\":\"浙江省湖州市吴兴区中山大道北888 \"},\"acceptanceDay\":null,\"balanceDay\":null,\"approvalPermit\":false,\"orderDetails\":[{\"id\":2524,\"goodsName\":\"免疫分析试剂\",\"quantity\":2.0,\"goodsCode\":\"hx-012\",\"unit\":\"个\",\"specification\":\"101ml\",\"brand\":\"绿森林(Green Forest)\",\"price\":60.0,\"totalPrice\":120.0,\"picturePath\":\"https://www.rjmart.cn/download/reagent/image/d15682ec/39c31e8c-184f-40ff-9241-dc76fecd8ecd.\",\"level\":null,\"comment\":null,\"returnStatus\":null,\"productSn\":100000206992,\"showOriginalPrice\":false,\"originalPrice\":60.0,\"originAmount\":120.0,\"activityOff\":0.0,\"carryFee\":null,\"categoryName\":null,\"dangerousTag\":\"其他\",\"categoryDirectoryName\":null,\"casNo\":null,\"firstCategoryId\":518,\"firstCategoryTag\":\"生物试剂\",\"quantityCanReturn\":0.0,\"modifyPriceCheck\":false}],\"fundCardIdList\":null,\"acceptance\":null,\"images\":null,\"receiveImages\":null,\"photoAcceptance\":null,\"dockingMemo\":null,\"confirmForTheRecord\":0,\"orderContractStatus\":2,\"contractInfoVOList\":null,\"relateInfo\":null,\"totalCarryFee\":0.0,\"canAppendReceivePic\":null,\"orderLogisticsInfo\":null,\"goodsReturnInfoList\":null,\"orderApprovalInfoList\":null}],\"acceptancePrint\":false,\"photoAcceptance\":false,\"totalAmount\":0,\"totalPages\":1,\"depts\":null}";
        OrderListRespVO orderListRespVO = new OrderListRespVO();
        orderListRespVO = JsonUtils.fromJson(masterRespJson, OrderListRespVO.class);

        PageableResponse<OrderListRespVO> curOrderListResp = PageableResponse.<OrderListRespVO>custom().setData(orderListRespVO).setSuccess();
        Mockito.when(buyerOrderService.getOrderListForWWW(Mockito.any(OrderListRequest.class), Mockito.any(LoginUserInfoBO.class), Mockito.anyBoolean())).thenReturn(curOrderListResp);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        String masterJson = "{\"id\":1768, \"fmasterguid\":\"null\", \"ftbuyappid\":1970, \"forderno\":\"DC201712150067201\", \"forderdate\":\"2017-12-15\", \"fbuyerid\":1724, \"fbuyercode\":\"null\", \"fbuyername\":\"林广圣\", \"fbuyeremail\":\"<EMAIL>\", \"fbuyercontactman\":\"张林\", \"fbuyertelephone\":\"15600790320\", \"fbuydepartmentid\":891, \"fbuydepartment\":\"测试\", \"fsuppid\":655, \"fsuppcode\":\"S0655\", \"fsuppname\":\"宏兴生物科技有限公司\", \"fbiderdeliveryplace\":\"浙江省湖州市吴兴区中山大道北888 \", \"forderamounttotal\":120.00, \"fundStatus\":0, \"failedReason\":\"\", \"status\":10, \"fconfirmdate\":\"2017-12-15\", \"fconfirmmanid\":\"655\", \"fconfirmman\":\"苑玲\", \"fcanceldate\":null, \"fcancelmanid\":\"null\", \"fcancelman\":\"null\", \"fdeliverydate\":\"2017-12-15\", \"fdeliverymanid\":\"655\", \"fdeliveryman\":\"苑玲\", \"flastreceivedate\":\"2017-12-15\", \"flastreceivemanid\":\"1724\", \"flastreceiveman\":\"林广圣\", \"fassessdate\":null, \"fassessmanid\":\"null\", \"fassessman\":\"null\", \"piemail\":\"null\", \"projectid\":\"229\", \"projectnumber\":\"11\", \"projecttitle\":\"11\", \"fuserid\":15, \"fusercode\":\"GUANG_DONG_GONG_YE_DA_XUE\", \"fusername\":\"广东工业大学\", \"statementId\":895, \"fcancelreason\":\"null\", \"frefuseCancelReason\":\"null\", \"shutDownDate\":null, \"deliveryInfo\":\"发货说明哈哈哈哈\", \"deliveryNo\":\"H0002\", \"returnAmount\":0.0, \"frefuseCancelDate\":null, \"fdeliveryid\":404, \"bidOrderId\":\"null\", \"orderType\":0, \"receivePicUrls\":\"null\", \"tpiProjectId\":\"null\", \"originalAmount\":120.00, \"inventoryStatus\":0, \"species\":0, \"updateTime\":\"2021-01-11\", \"inStateTime\":\"1970-10-10\", \"purchaseRootinType\":0, \"carryFee\":0.00, \"invoiceTitleId\":0, \"invoiceTitle\":\"\", \"invoiceTitleNumber\":\"\", \"statementStatus\":\"1\"}";
        orderMasterDO = JsonUtils.fromJson(masterJson, OrderMasterDO.class);

        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(orderMasterDO);

        UserBaseInfoDTO buyerInfo = new UserBaseInfoDTO();
        buyerInfo.setMobile("contactPhone");
        Mockito.when(userClient.getUserInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(buyerInfo);

        // Mockito.when(buyerOrderService.getOrderApprovalInfo(Mockito.any(OrderMasterDO.class))).thenReturn(New.list());

        // suppid和supp的联系信息对应
        SuppShopInfoBO suppShopInfoBO = new SuppShopInfoBO();
        suppShopInfoBO.setQq("654987321");
        suppShopInfoBO.setMobile("987654321");
        suppShopInfoBO.setId(655L);
        Map<Integer, SuppShopInfoBO> suppIdContactInfoMap = new HashMap<>();
        Mockito.when(suppClient.getSupplierContactInfoMap(Mockito.any(), Mockito.any())).thenReturn(suppIdContactInfoMap);

        DockingExtra dockingExtra = new DockingExtra();
        dockingExtra.setStatusextra(123);
        dockingExtra.setMemo("memo");
        Mockito.when(dockingExtraMapper.findByInfoInAndType(Mockito.anyList(), Mockito.anyInt())).thenReturn(New.list(dockingExtra));
        OrderAddressDTO addressDTO = new OrderAddressDTO();
        addressDTO.setDeliveryType(1);
        Mockito.when(orderAddressRPCClient.findByOrderId(Mockito.anyInt())).thenReturn(addressDTO);

        // hms额外逻辑（南方医）
        orderMasterDO.setFuserid(OrgEnum.NAN_FANG_YI_KE.getValue());
        orderMasterDO.setFusercode(OrgEnum.NAN_FANG_YI_KE.getCode());
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);

        OrderFundCardDTO fundCardCache = new OrderFundCardDTO();
        fundCardCache.setFundCardId("fundcardid");
        Mockito.when(orderFundCardCacheClient.findOrderFundCardCache(Mockito.any())).thenReturn(New.list(fundCardCache));

        OrderInfoVO orderDetail = orderDetailRelatedService.getOrderDetail(loginUserInfo, new OrderBasicParamDTO(){{setOrderId(orderId);}}, true);

        // hms额外逻辑（拥有二级经费卡的单位）
        orderMasterDO.setFuserid(OrgEnum.WU_YI_DA_XUE.getValue());
        orderMasterDO.setFusercode(OrgEnum.WU_YI_DA_XUE.getCode());
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMasterDO);
        Mockito.when(orderFundCardCacheClient.findOrderFundCardCache(Mockito.any())).thenReturn(New.list());
        Mockito.when(refFundcardOrderService.findByOrderIdList(Mockito.any())).thenReturn(New.list());

        // 拥有二级经费卡单位的，二级经费卡号卡名连接
        FundCardDTO refFundCard = new FundCardDTO();
        refFundCard.setLevel(FundCardLevelEnum.FUND_CARD_PROJECT.getValue());

        FundCardDTO refSubFundCard1 = new FundCardDTO();
        FundCardDTO refSubFundCard2 = new FundCardDTO();
        // 有效的二级经费卡
        refSubFundCard1.setLevel(FundCardLevelEnum.FUND_CARD.getValue());
        refSubFundCard1.setCode("sub fund card code1");
        refSubFundCard1.setName("sub fund card name1");
        refSubFundCard1.setStartTime(new Date());
        // 无效的子经费卡
        refSubFundCard2.setLevel(FundCardLevelEnum.FUND_CARD_SUBJECT.getValue());
        refFundCard.setFundCardDTOs(New.list(refSubFundCard1, refSubFundCard2));
        Mockito.when(researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(Mockito.any(), Mockito.any())).thenReturn(New.list(refFundCard));

        orderDetail = orderDetailRelatedService.getOrderDetail(loginUserInfo, new OrderBasicParamDTO(){{setOrderId(orderId);}}, true);
        // 能正常运行跑完说明已经通过
        Assert.assertTrue(orderDetail != null);

    }

    @Test
    public void testSetOrderSource() {
        // mock input
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        String masterJson = "{\"id\":1768, \"fmasterguid\":\"null\", \"ftbuyappid\":1970, \"forderno\":\"DC201712150067201\", \"forderdate\":\"2017-12-15\", \"fbuyerid\":1724, \"fbuyercode\":\"null\", \"fbuyername\":\"林广圣\", \"fbuyeremail\":\"<EMAIL>\", \"fbuyercontactman\":\"张林\", \"fbuyertelephone\":\"15600790320\", \"fbuydepartmentid\":891, \"fbuydepartment\":\"测试\", \"fsuppid\":655, \"fsuppcode\":\"S0655\", \"fsuppname\":\"宏兴生物科技有限公司\", \"fbiderdeliveryplace\":\"浙江省湖州市吴兴区中山大道北888 \", \"forderamounttotal\":120.00, \"fundStatus\":0, \"failedReason\":\"\", \"status\":10, \"fconfirmdate\":\"2017-12-15\", \"fconfirmmanid\":\"655\", \"fconfirmman\":\"苑玲\", \"fcanceldate\":null, \"fcancelmanid\":\"null\", \"fcancelman\":\"null\", \"fdeliverydate\":\"2017-12-15\", \"fdeliverymanid\":\"655\", \"fdeliveryman\":\"苑玲\", \"flastreceivedate\":\"2017-12-15\", \"flastreceivemanid\":\"1724\", \"flastreceiveman\":\"林广圣\", \"fassessdate\":null, \"fassessmanid\":\"null\", \"fassessman\":\"null\", \"piemail\":\"null\", \"projectid\":\"229\", \"projectnumber\":\"11\", \"projecttitle\":\"11\", \"fuserid\":15, \"fusercode\":\"GUANG_DONG_GONG_YE_DA_XUE\", \"fusername\":\"广东工业大学\", \"statementId\":895, \"fcancelreason\":\"null\", \"frefuseCancelReason\":\"null\", \"shutDownDate\":null, \"deliveryInfo\":\"发货说明哈哈哈哈\", \"deliveryNo\":\"H0002\", \"returnAmount\":0.0, \"frefuseCancelDate\":null, \"fdeliveryid\":404, \"bidOrderId\":\"null\", \"orderType\":0, \"receivePicUrls\":\"null\", \"tpiProjectId\":\"null\", \"originalAmount\":120.00, \"inventoryStatus\":0, \"species\":0, \"updateTime\":\"2021-01-11\", \"inStateTime\":\"1970-10-10\", \"purchaseRootinType\":0, \"carryFee\":0.00, \"invoiceTitleId\":0, \"invoiceTitle\":\"\", \"invoiceTitleNumber\":\"\", \"statementStatus\":\"1\"}";
        orderMasterDO = JsonUtils.fromJson(masterJson, OrderMasterDO.class);

        String masterRespJson = "{\"orderList\":[{\"order\":{\"id\":1768,\"orderNo\":\"DC201712150067201\",\"buyAppId\":1970,\"buyAppNo\":null,\"bidOrderId\":null,\"bidOrderSn\":null,\"orderDate\":\"2017-12-15 18:51:36\",\"fundCardIdList\":[],\"confirmDate\":\"2017-12-15 18:52:50\",\"confirmMan\":null,\"deliveryDate\":\"2017-12-15 18:53:06\",\"lastReceiveDate\":\"2017-12-15 18:53:59\",\"settleCreateDate\":\"2017-12-15 18:55:53\",\"settleFinishDate\":null,\"buyUserId\":1724,\"buyerName\":\"林广圣\",\"departmentName\":\"测试\",\"buyerTelephone\":\"15600790320\",\"deliveryAddress\":\"浙江省湖州市吴兴区中山大道北888 \",\"buyerContactMan\":\"张林\",\"status\":10,\"fundStatus\":0,\"failedReason\":null,\"totalPrice\":120.0,\"supplierName\":\"宏兴生物科技有限公司\",\"suppUser\":null,\"suppTelephone\":\"020-88990066\",\"suppQQ\":\"1231241414\",\"projectNumbers\":null,\"cancelReason\":null,\"wholePrice\":null,\"lastPrice\":null,\"acceptance\":0,\"contactPhone\":null,\"shutdownDate\":null,\"supplierId\":655,\"canCancel\":0,\"urls\":null,\"checkMan\":\"林广圣\",\"beforeModifyPriceAll\":\"120.0\",\"orderType\":\"0\",\"deliveryName\":null,\"deliveryMobile\":null,\"inventoryStatus\":0,\"canInbound\":true,\"statementStatus\":1,\"orderReceiveDesc\":\"\",\"buyDepartmentId\":891,\"deptGuid\":null,\"offlineInfo\":null,\"orgName\":\"广东工业大学\",\"orgId\":15,\"orderRemark\":null,\"relateInfo\":null,\"confirmPics\":null,\"confirmAddPics\":null,\"commentStatus\":2,\"overTimeFlag\":false,\"totalCarryFee\":0.0,\"dockingStatus\":null,\"collegeName\":\"\",\"canSubmitWarehouse\":0,\"canPrintInAndOutWarehouse\":0,\"publicity\":false,\"invoiceTitleId\":null,\"species\":0,\"waitForSettleTime\":null,\"contractId\":null,\"contractNo\":null,\"fbiderdeliveryplace\":\"浙江省湖州市吴兴区中山大道北888 \"},\"acceptanceDay\":null,\"balanceDay\":null,\"approvalPermit\":false,\"orderDetails\":[{\"id\":2524,\"goodsName\":\"免疫分析试剂\",\"quantity\":2.0,\"goodsCode\":\"hx-012\",\"unit\":\"个\",\"specification\":\"101ml\",\"brand\":\"绿森林(Green Forest)\",\"price\":60.0,\"totalPrice\":120.0,\"picturePath\":\"https://www.rjmart.cn/download/reagent/image/d15682ec/39c31e8c-184f-40ff-9241-dc76fecd8ecd.\",\"level\":null,\"comment\":null,\"returnStatus\":null,\"productSn\":100000206992,\"showOriginalPrice\":false,\"originalPrice\":60.0,\"originAmount\":120.0,\"activityOff\":0.0,\"carryFee\":null,\"categoryName\":null,\"dangerousTag\":\"其他\",\"categoryDirectoryName\":null,\"casNo\":null,\"firstCategoryId\":518,\"firstCategoryTag\":\"生物试剂\",\"quantityCanReturn\":0.0,\"modifyPriceCheck\":false}],\"fundCardIdList\":null,\"acceptance\":null,\"images\":null,\"receiveImages\":null,\"photoAcceptance\":null,\"dockingMemo\":null,\"confirmForTheRecord\":0,\"orderContractStatus\":2,\"contractInfoVOList\":null,\"relateInfo\":null,\"totalCarryFee\":0.0,\"canAppendReceivePic\":null,\"orderLogisticsInfo\":null,\"goodsReturnInfoList\":null,\"orderApprovalInfoList\":null}],\"acceptancePrint\":false,\"photoAcceptance\":false,\"totalAmount\":0,\"totalPages\":1,\"depts\":null}";
        OrderListRespVO orderListRespVO = new OrderListRespVO();
        orderListRespVO = JsonUtils.fromJson(masterRespJson, OrderListRespVO.class);
        OrderInfoVO orderInfoVO = orderListRespVO.getOrderList().get(0);

        // mock search for apply orders
        ApplicationMasterDTO appMaster = new ApplicationMasterDTO();
        appMaster.setApplyNumber("C2017121500672");

        Mockito.when(applicationBaseClient.findByMasterId(Mockito.any(ApplicationQueryDTO.class))).thenReturn(New.list(appMaster));
        Mockito.when(priceContractClient.getPriceContractId(Mockito.anyInt())).thenReturn(45L);

        PriceContractBaseBO priceContractBaseBO = new PriceContractBaseBO() {{
            setContractNo("contractcontractcontract");
        }};
        Mockito.when(priceContractClient.getPriceContractBaseList(Mockito.anyList())).thenReturn(New.list(priceContractBaseBO));

        BidApprovalLogDTO oldBidLog = new BidApprovalLogDTO();
        oldBidLog.setBidNo("old bid number");
        Mockito.when(bidClient.findOldApprovalLogInfo(Mockito.anyString())).thenReturn(New.list(oldBidLog));
        // for purchase
        orderMasterDO.setOrderType(0);
        ApplicationMasterDTO applicationMasterDTO = orderDetailRelatedService.setOrderSource(orderMasterDO, orderInfoVO);
        Assert.assertTrue(orderInfoVO.getOrder().getBuyAppNo() != null);

        // for clinical
        orderMasterDO.setOrderType(2);
        applicationMasterDTO = orderDetailRelatedService.setOrderSource(orderMasterDO, orderInfoVO);
        Assert.assertTrue(orderInfoVO.getOrder().getBuyAppNo() != null && orderInfoVO.getOrder().getContractNo() != null);

        // for bid order
        orderMasterDO.setOrderType(1);
        applicationMasterDTO = orderDetailRelatedService.setOrderSource(orderMasterDO, orderInfoVO);
        Assert.assertTrue(orderInfoVO.getOrder().getBidOrderSn() != null);
    }

    @Test
    public void testListInvoiceByOrder() {
        // Integer orderId = 12345;
        // Mockito.when(invoiceClient.findInvoiceVOList(Mockito.any(InvoiceQueryDTO.class))).thenReturn(New.list());
        // List<OrderInvoiceInfoVO> invoiceInfoList = orderDetailRelatedService.listInvoiceByOrder(orderId.longValue(), "", 94);
        // Assert.assertTrue(CollectionUtils.isEmpty(invoiceInfoList));
        //
        // OrderMasterDO orderMasterDO = new OrderMasterDO();
        // orderMasterDO.setId(1);
        // Mockito.when(orderMasterMapper.findByForderno(Mockito.anyString())).thenReturn(orderMasterDO);
        // invoiceInfoList = orderDetailRelatedService.listInvoiceByOrder(null, "111", 94);
        // Assert.assertTrue(CollectionUtils.isEmpty(invoiceInfoList));
    }

    @Test
    public void testTestSpecialOrgApprovePermission() {
        // mock input
        OrderInfoVO curOrderInfo = new OrderInfoVO();
        curOrderInfo.setOrder(new OrderMasterVO() {{
            setId(123546);
        }});

        LoginUserInfoBO loginInfo = new LoginUserInfoBO();
        loginInfo.setOrgCode(OrgEnum.ZHONG_SHAN_DA_XUE.getCode());
        loginInfo.setUserId(2454);
        loginInfo.setDeptIdList(New.list(1402, 263, 27836));
        loginInfo.setOrgId(3);

        // if sysu
        Mockito.when(sysuOrderService.checkReceivePermission(Mockito.any(OrderInfoVO.class), Mockito.any(LoginUserInfoBO.class))).thenReturn(true);
        Boolean resultSYSU = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertTrue(resultSYSU == true);

        // if smu
        loginInfo.setUserId(1924);
        loginInfo.setDeptIdList(New.list(17120, 17121, 17123));
        loginInfo.setOrgId(18);
        loginInfo.setOrgCode(OrgEnum.NAN_FANG_YI_KE.getCode());

        List<UserBaseInfoDTO> userWithRoleList = New.list();
        Mockito.when(userClient.findUserByRoleIdAndOrgId(Mockito.anyList(), Mockito.anyInt())).thenReturn(userWithRoleList);
        Boolean resultSMU1 = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertFalse(resultSMU1);

        UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
        userBaseInfoDTO.setId(1924);
        userWithRoleList = New.list(userBaseInfoDTO);
        Mockito.when(userClient.findUserByRoleIdAndOrgId(Mockito.anyList(), Mockito.anyInt())).thenReturn(userWithRoleList);
        Mockito.when(userClient.checkUserInDepartmentAccess(Mockito.anyInt(), Mockito.anyList(), Mockito.anyList(), Mockito.anyInt())).thenReturn(true);
        Boolean resultSMU2 = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertTrue(resultSMU2);

        // others
        Mockito.when(userClient.checkUserInDepartmentAccess(Mockito.anyInt(), Mockito.anyList(), Mockito.anyList(), Mockito.anyInt())).thenReturn(true);
        loginInfo.setUserId(1234);
        loginInfo.setDeptIdList(New.list(17120, 17121, 17123));
        loginInfo.setOrgId(60);
        loginInfo.setOrgCode(OrgEnum.WU_YI_DA_XUE.getCode());
        Boolean resultOther = orderDetailRelatedService.specialOrgApprovePermission(loginInfo, curOrderInfo);
        Assert.assertTrue(resultOther);
    }

    @Test
    public void getOfflineSuppById() {
        PageResultBean<OfflineSupplierDTO> objectPageResultBean = new PageResultBean<>();
        OfflineSupplierDTO offlineSupplierDTO = new OfflineSupplierDTO();
        objectPageResultBean.setRecords(Arrays.asList(offlineSupplierDTO));
        Mockito.when(offlineSupplierService.getOfflineSupplierPage(Mockito.any())).thenReturn(objectPageResultBean);
        OfflineSupplierDTO supp = suppClientInject.getOfflineSuppById(1540);
        Assert.assertTrue(supp != null);
    }

    @Test
    public void getOrderSnapshot() {
        Integer suppId = 123;
        Integer dangerousTypeId = 321;
        Integer orderType = OrderTypeEnum.PURCHASE_ORDER.getCode();
        BigDecimal bidPrice = BigDecimal.valueOf(123L);
        BigDecimal originalPrice = BigDecimal.valueOf(321L);

        // 订单详情
        OrderDetailDO orderDetail = new OrderDetailDO();
        orderDetail.setSuppId(suppId);
        orderDetail.setDangerousTypeId(dangerousTypeId);
        orderDetail.setFbidprice(bidPrice);
        orderDetail.setOriginalPrice(originalPrice);
        orderDetail.setFquantity(BigDecimal.ONE);
        Mockito.when(orderDetailMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderDetail);
        // 订单主表
        OrderMasterDO orderMaster = new OrderMasterDO();
        orderMaster.setSpecies((byte)0);
        orderMaster.setOrderType(orderType);
        orderMaster.setFtbuyappid(556);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(orderMaster);

        SupplierDTO supp = new SupplierDTO();
        supp.setId(suppId.longValue());
        // Mockito.when(suppClient.getSuppById(Mockito.any())).thenReturn(supp);

        // 供应商资质
        Map<Integer, QualificationDTO> suppIdContactInfoMap = new HashMap<>();
        QualificationDTO suppQual = new QualificationDTO();
        suppQual.setLicensePic("license pic");
        suppIdContactInfoMap.put(suppId, suppQual);
        Mockito.when(suppClient.getSuppContactInfo(Mockito.any())).thenReturn(suppIdContactInfoMap);

        // Mockito.when(suppClient.getSuppBrand(Mockito.anyInt(), Mockito.any())).thenReturn(new SuppBrandDTO());

        ProductDescriptionSnapshotDO descDO = new ProductDescriptionSnapshotDO();
        descDO.setDescription("description");
        List<ProductDescriptionSnapshotDO> descSnapshotList = New.list(descDO);
        Mockito.when(descSnapshotMapper.findProductDescByIds(Mockito.any(), Mockito.any(), Mockito.anyLong())).thenReturn(descSnapshotList);

        OrderSnapshotVO orderSnapshot = orderDetailRelatedService.getOrderSnapshot(443);
        Assert.assertTrue(orderSnapshot != null && orderSnapshot.getProductPrice().equals(bidPrice.doubleValue()));
    }

    @Test
    public void getOfflineInfoList() {
        Integer orderId = 1;
        Integer buyAppId = 1;
        Integer suppId = 1;

        OrderMasterSearchDTO master = new OrderMasterSearchDTO();
        master.setSpecies(ProcessSpeciesEnum.OFFLINE.getValue());
        master.setId(orderId);
        master.setFtbuyappid(buyAppId);
        master.setFsuppid(suppId);

        // 线下单附件
        OfflineExtraDTO offlineInfo = new OfflineExtraDTO();
        offlineInfo.setApplicationId(buyAppId);
        Mockito.when(applicationBaseClient.findOfflineByAppIdList(Mockito.any())).thenReturn(New.list(offlineInfo));
        // 采购渠道
        ApplyRefSuppBusinessDTO applyRef = new ApplyRefSuppBusinessDTO();
        applyRef.setApplicationId(buyAppId);
        applyRef.setProcurementChannelId(ProcurementChannelEnum.NORMAL.getValue());

        // 运行
        List<OrderOfflineInfoVO> offlineInfoList = orderDetailRelatedService.getOfflineInfoList(New.list(master));
        Assert.assertTrue(CollectionUtils.isNotEmpty(offlineInfoList));
    }
}