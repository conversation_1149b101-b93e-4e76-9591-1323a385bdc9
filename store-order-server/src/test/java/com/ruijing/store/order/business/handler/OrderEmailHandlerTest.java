package com.ruijing.store.order.business.handler;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.model.MockScope;
import com.alibaba.testable.core.tool.PrivateAccessor;
import com.alibaba.testable.processor.annotation.EnablePrivateAccess;
import com.reagent.supp.api.user.dto.SuppUserDTO;
import com.reagent.supp.api.user.service.SupplierUserService;
import com.ruijing.base.letter.msg.SysSendDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.message.api.service.MessageService;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.cms.api.dto.SendingPersonalAndDefaultDTO;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.cms.api.enums.SendingSettingStatusEnum;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import com.ruijing.store.cms.api.request.SendingPersonalAndDefaultParam;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.CmsServerClient;
import com.ruijing.store.order.rpc.client.SysLetterRpcServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.utils.MyMockUtils;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@EnablePrivateAccess(srcClass = OrderEmailHandler.class)
public class OrderEmailHandlerTest extends MockBaseTestCase {

    @org.mockito.Mock
    private CmsServerClient cmsServerClient;

    @org.mockito.Mock
    private MessageService messageService;

    @org.mockito.Mock
    private SupplierUserService supplierUserService;

    @InjectMocks
    private OrderEmailHandler orderEmailHandler;

    @org.mockito.Mock
    private OrderSearchBoostService orderSearchBoostService;

    @org.mockito.Mock
    private UserClient userClient;

    @Test
    public void sendOrderGenerateEmailToSupp() {
        // 用反射给从配置中心读的值强行改了，预防NPE
        MyMockUtils.setThreadLocalField(orderEmailHandler, "orderDetailLink", "test");

        ApiResult<List<SuppUserDTO>> suppList = ApiResult.newSuccess();
        suppList.setData(New.list());
        // Mockito.when(supplierUserService.queryForList(Mockito.any())).thenReturn(suppList);
        Mockito.when(cmsServerClient.getSendSetting(Mockito.anyList())).thenReturn(New.list());
        Mockito.doThrow(new IllegalStateException("error")).when(messageService).asyncSend(Mockito.any());

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setFsuppname("test");
        orderMasterDO.setForderamounttotal(BigDecimal.valueOf(100.01));
        orderEmailHandler.sendOrderGenerateEmailToSupp(orderMasterDO);
    }

    public static class Mock {
        @MockMethod(targetClass = OrderEmailHandler.class)
        public void commonMessageTOSystemSupplier(OrderMasterDO masterDO,String letterTitle, String letterContent) {}

        @MockMethod(targetClass = SysLetterRpcServiceClient.class)
        public void sendLetterToUser(SysSendDTO request) {}

        public List<SuppUserDTO> getSuppliersContactByType(Integer suppId,Integer orgId, Integer supplieType){
            SuppUserDTO suppUserDTO = new SuppUserDTO();
            suppUserDTO.setEmail("test");
            return New.list(suppUserDTO);
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatOrderCancelEmailContextToSuppliers(OrderMasterDO orderMasterDO){
            return "test";
        }

        @MockMethod(targetClass = MessageService.class)
        public void asyncSend( MessageDTO var1){}

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatPurchaserAgreeCancelOrderEmailContextToSuppliers(OrderMasterDO orderMasterDO){
            return "test";
        }

        @MockMethod(targetClass = CmsServerClient.class, scope = MockScope.ASSOCIATED)
        public List<SendingPersonalAndDefaultDTO> getSettingListByPersonalAndDefault(SendingPersonalAndDefaultParam sendingSettingParamList) {
            SendingPersonalAndDefaultDTO result = new SendingPersonalAndDefaultDTO();
            result.setWay(SendingWayEnum.EMAIL.getValue().byteValue());
            result.setEnable(SendingSettingStatusEnum.OPEN.getValue().byteValue());
            return Collections.singletonList(result);
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        public Set<String> getSuppValidEmails(OrderMasterDO orderMasterDO, SendingBusinessEnum sendingBusiness){
            return New.set("1", "2");
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatPurchaseApplyCancelemailContext(OrderMasterDO orderMaster, long overDay){
            return "test";
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatSupplierApplyCancelEmailContextToSupplier(OrderMasterDO orderMaster, long overDay){
            return "test";
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatUnConfirmOrderWarnEmailContextForSupplier(OrderMasterDO orderMaster, long overDay, long sendDay){
            return "test";
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatCancelemailContextForSupplier(OrderMasterDO orderMaster, long overDay){
            return "test";
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatPurchaseApplyCancelWarnEmailContextToSupplier(OrderMasterDO orderMaster, long overDay, long sendDay){
          return "test";
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private void commonSendEmailToSupplier(OrderMasterDO orderMaster, SendingBusinessEnum sendingBusinessEnum, String subject, String emailContext, MessageTypeEnum messageType){
        }

        @MockMethod(targetClass = OrderEmailHandler.class)
        private String creatSupplierApplyCancelWarnEmailForPurchase(OrderMasterDO orderMaster, long overDay, long sendDay){
            return "test";
        }
    }

    @Test
    public void commonSendEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        PrivateAccessor.invoke(orderEmailHandler, "commonSendEmailToSupplier",  orderMasterDO, SendingBusinessEnum.CANCEL_CONFIRM_ORDER_TO_SUPP, "主题", "内容", MessageTypeEnum.EMAIL_HTML);
    }

    @Test
    public void sendPurchaseApplyCancelEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendPurchaseApplyCancelEmailToSupplier(New.list(orderMasterDO), 10);
    }

    @Test
    public void sendSupplierApplyCancelEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendSupplierApplyCancelEmailToSupplier(New.list(orderMasterDO), 10);
    }

    @Test
    public void sendUnConfirmOrderWarnEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendUnConfirmOrderWarnEmailToSupplier(New.list(orderMasterDO), 10, 10);
    }

    @Test
    public void sendUnConfirmOrderCancelEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendUnConfirmOrderCancelEmailToSupplier(New.list(orderMasterDO), 10);
    }

    @Test
    public void sendPurchaseApplyCancelWarnEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendPurchaseApplyCancelWarnEmailToSupplier(New.list(orderMasterDO), 10, 10);
    }

    @Test
    public void sendSupplierApplyCancelWarnEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendSupplierApplyCancelWarnEmailToSupplier(New.list(orderMasterDO), 10, 10);
    }

    @Test
    public void sendDeliveryRemindMessageTOSystemSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderno("DC202106112748201");
        orderMasterDO.setFusername("南方医科大学医院");
        orderMasterDO.setFbuydepartment("测试课题组");
        orderMasterDO.setForderamounttotal(new BigDecimal(10000));
        orderMasterDO.setReturnAmount(100.0);
        orderEmailHandler.orderDetailLink = "baidu.com";
        orderEmailHandler.sendDeliveryRemindMessageTOSystemSupplier(orderMasterDO);
    }

    @Test
    public void sendOrderGenerateMessageTOSystemSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderno("DC202106112748201");
        orderMasterDO.setFusername("南方医科大学医院");
        orderMasterDO.setFbuydepartment("测试课题组");
        orderMasterDO.setForderamounttotal(new BigDecimal(10000));
        orderMasterDO.setReturnAmount(100.0);
        orderEmailHandler.orderDetailLink = "baidu.com";
        orderEmailHandler.sendOrderGenerateMessageTOSystemSupplier(orderMasterDO);
    }

    @Test
    public void commonMessageTOSystemSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.commonMessageTOSystemSupplier(orderMasterDO,"发货提醒通知","发货提醒!");
    }

    @Test
    public void sendOrderCancelEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendOrderCancelEmailToSupplier(orderMasterDO,"111");
    }

    @Test
    public void sendPurchaserAgreeCancelOrderEmailToSupplier(){
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderEmailHandler.sendPurchaserAgreeCancelOrderEmailToSupplier(orderMasterDO,"111");
    }

    @Test
    public void sendSplitOrderDeliveryEmail() {
        OrderMasterSearchDTO searchDTO = new OrderMasterSearchDTO();
        searchDTO.setId(1);
        searchDTO.setForderno("DC001A");
        searchDTO.setFuserid(1);
        searchDTO.setFbuyerid(2);
        OrderDetailSearchDTO detailSearchDTO = new OrderDetailSearchDTO();
        detailSearchDTO.setFgoodname("tt");
        searchDTO.setOrderDetail(Collections.singletonList(detailSearchDTO));
        Mockito.when(orderSearchBoostService.searchOrderById(Mockito.anyInt())).thenReturn(Collections.singletonList(searchDTO));
        UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
        userBaseInfoDTO.setGuid("guid");
        userBaseInfoDTO.setEmail("email");
        Mockito.when(userClient.getUserByUserIds(Mockito.anyList())).thenReturn(Collections.singletonList(userBaseInfoDTO));
        Mockito.doNothing().when(messageService).asyncSend(Mockito.any(MessageDTO.class));
        MyMockUtils.setThreadLocalField(orderEmailHandler, "SEND_EMAIL_TARGET_CONFIG", "test");
        orderEmailHandler.sendSplitOrderDeliveryEmail(1);
    }


}
