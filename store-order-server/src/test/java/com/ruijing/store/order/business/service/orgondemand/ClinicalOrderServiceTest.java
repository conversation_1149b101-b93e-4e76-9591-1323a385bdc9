package com.ruijing.store.order.business.service.orgondemand;

import com.alibaba.testable.core.annotation.MockMethod;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.contract.dto.PriceContractProductDTO;
import com.ruijing.store.contract.dto.PriceContractTransactionDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.PriceContractBaseBO;
import com.ruijing.store.order.rpc.client.ApplicationBaseClient;
import com.ruijing.store.order.rpc.client.PriceContractClient;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public class ClinicalOrderServiceTest extends MockBaseTestCase {

    @InjectMocks
    private ClinicalOrderService clinicalOrderService;

    @org.mockito.Mock
    private PriceContractClient priceContractClient;

    @org.mockito.Mock
    private ApplicationBaseClient applicationBaseClient;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private OrderDetailMapper oderDetailMapper;

    public static class Mock {
        @MockMethod(targetClass = OrderMasterMapper.class)
        List<OrderMasterDO> findByIdIn(Collection<Integer> idCollection){
            if(idCollection == null) return null;

            OrderMasterDO orderMasterDO = new OrderMasterDO();
            if(idCollection.contains(1)){
                orderMasterDO.setId(1);
            }else{
                orderMasterDO.setId(123);
            }
            orderMasterDO.setFtbuyappid(1);
            return New.list(orderMasterDO);
        }

    }

    @Test
    public void testReleaseContractAmount() {
        // mock
        ApplicationMasterDTO applyMasterInfo = new ApplicationMasterDTO();
        applyMasterInfo.setCreateTime(new Date());
        List<ApplicationMasterDTO> applyInfoList = New.list(applyMasterInfo);

        // mock input -cancel order(non sense order-master values)
        OrderMasterDO orderMaster = new OrderMasterDO();
        orderMaster.setOrderType(2);
        orderMaster.setFuserid(123);
        orderMaster.setFtbuyappid(186);
        orderMaster.setFbuydepartmentid(62);
        orderMaster.setId(52468);

        OrderDetailDO orderDetail = new OrderDetailDO();
        orderDetail.setFbidprice(BigDecimal.valueOf(100.00));
        orderDetail.setFquantity(BigDecimal.valueOf(2.00));
        orderDetail.setProductSn(987654321L);
        List<OrderDetailDO> orderDetailDOList = New.list(orderDetail);

        Long contractId = 39L;
        PriceContractTransactionDTO priceContractTransaction = new PriceContractTransactionDTO();
        Mockito.when(priceContractClient.getPriceContractId(Mockito.anyInt())).thenReturn(contractId);
        Mockito.when(applicationBaseClient.findByMasterId(Mockito.any(ApplicationQueryDTO.class))).thenReturn(applyInfoList);
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(orderDetailDOList);
//        Mockito.when(priceContractClient.releaseContractUseAmount(Mockito.any(PriceContractTransactionDTO.class))).thenReturn(true);
//        clinicalOrderService.releaseContractAmount(OrderApprovalEnum.CANCEL.getValue(), orderMaster, null);

        // mock input -return goods
        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setId(789);
        goodsReturn.setGoodsReturnDetailJSON("[{\"detailId\":2,\"goodsName\":\"试管耗材\",\"goodsCode\":\"SGHC-VB-001\",\"specification\":\"10ml\",\"brand\":\"LOREM\",\"goodsPicturePath\":\"https://www.rjmart.cn/download/reagent/image/d15682ec/ddee3db0-bf7b-41d4-b055-77ca9a9b20b8.jpg\",\"price\":210.00,\"quantity\":3.00,\"amount\":3.0,\"returnReason\":\"供应商发错货\",\"remark\":\"1个发错\",\"dangerousTag\":\"\",\"productId\":\"100000168239\",\"unit\":\"盒/箱\"}]");
//        clinicalOrderService.releaseContractAmount(OrderApprovalEnum.GOODS_RETURN.getValue(), orderMaster, goodsReturn);
    }

    @Test
    public void listOrderProductContractInfo() {
        // 获取master 为空
        clinicalOrderService.listOrderProductByOrderIds(null);
        ApplyMasterExtensionDTO applyMasterExtensionDTO = new ApplyMasterExtensionDTO();
        applyMasterExtensionDTO.setContractId(1L);
        applyMasterExtensionDTO.setApplyId(1L);
        Mockito.when(priceContractClient.getPriceContractList(Mockito.anyList())).thenReturn(New.list(applyMasterExtensionDTO));
        PriceContractBaseBO priceContractBaseBO = new PriceContractBaseBO();
        priceContractBaseBO.setContractId(1L);
        priceContractBaseBO.setContractNo("合同编号");
        Mockito.when(priceContractClient.getPriceContractBaseList(Mockito.anyList())).thenReturn(New.list(priceContractBaseBO));

        // 测试订单明细,根据商品id获取商品对应的合同信息(方法学、单位、品牌产地)
        PriceContractProductDTO contractProductDTO = new PriceContractProductDTO();
        contractProductDTO.setId(1l);
        contractProductDTO.setProductSn(1383L);
        contractProductDTO.setMethodology("方法学1");

        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setFmasterid(1);
        orderDetailDO.setProductSn(1l);
        orderDetailDO.setFquantity(new BigDecimal(100));
        orderDetailDO.setOriginalPrice(new BigDecimal(100));

        // 返回为空
        Mockito.when(oderDetailMapper.findAllByFmasteridIn(New.list(1))).thenReturn(New.list());
        clinicalOrderService.listOrderProductByOrderIds(New.list(1383, 1384, 1385, 1386));
        Mockito.when(oderDetailMapper.findAllByFmasteridIn(New.list(1))).thenReturn(New.list(orderDetailDO));

        // 如果返回值为空
//        Mockito.when(priceContractClient.listPriceContractProductByIds(Mockito.anyList())).thenReturn(null);
        clinicalOrderService.listOrderProductByOrderIds(New.list(1383, 1384, 1385, 1386));
        // 如果返回值不为空
//        Mockito.when(priceContractClient.listPriceContractProductByIds(Mockito.anyList())).thenReturn(New.list(contractProductDTO));
        clinicalOrderService.listOrderProductByOrderIds(New.list(123));
        clinicalOrderService.listOrderProductByOrderIds(New.list(1));

        // 如果返回值为空
        Mockito.when(priceContractClient.getPriceContractList(Mockito.anyList())).thenReturn(New.list());
        clinicalOrderService.listOrderProductByOrderIds(New.list(1));
        Mockito.when(priceContractClient.getPriceContractList(Mockito.anyList())).thenReturn(New.list(applyMasterExtensionDTO));
        Mockito.when(priceContractClient.getPriceContractBaseList(Mockito.anyList())).thenReturn(null);
        clinicalOrderService.listOrderProductByOrderIds(New.list(1));
    }
}