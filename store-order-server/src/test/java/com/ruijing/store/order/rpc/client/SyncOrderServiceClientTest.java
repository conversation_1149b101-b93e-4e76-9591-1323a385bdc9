package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.gateway.dto.SyncOrderParamDTO;
import com.ruijing.store.order.gateway.OrderPrivateGWService;
import com.ruijing.sync.common.api.SyncDataCommonRPCService;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;


public class SyncOrderServiceClientTest extends MockBaseTestCase {

    @InjectMocks
    OrderPrivateGWService orderPrivateGWService;

    @Mock
    SyncOrderServiceClient syncOrderServiceClient;

    @Mock
    SyncDataCommonRPCService syncDataCommonRPCService;

    @InjectMocks
    SyncOrderServiceClient syncOrderServiceClientTest;

    @Test
    public void syncOrderData() {
        Mockito.doNothing().when(syncOrderServiceClient).sync(Mockito.any(), Mockito.any());

        SyncOrderParamDTO request = new SyncOrderParamDTO();
        request.setKey(0);
        request.setToken("<EMAIL>");
        request.setDays(1);

        RemoteResponse<Boolean> response = orderPrivateGWService.syncOrderData(request);
        Assert.assertTrue("调用失败", response.isSuccess());

        response = orderPrivateGWService.syncOrderData(request);
        Assert.assertTrue("调用失败", response.isSuccess());
    }

    @Test
    public void checkDBAndESWriteToExcel() {
        Mockito.when(syncDataCommonRPCService.checkDBAndESWriteToExcel(Mockito.any())).thenReturn(RemoteResponse.success());
        SyncOrderParamDTO request = new SyncOrderParamDTO();
        request.setKey(0);
        request.setToken("<EMAIL>");
        request.setDays(1);

        Boolean checkSuccess = syncOrderServiceClientTest.checkDBAndESWriteToExcel(new SyncDataRequestDTO());
        Assert.assertTrue(checkSuccess);
    }
}
