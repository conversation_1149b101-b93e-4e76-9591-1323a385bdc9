package com.ruijing.store.order.rpc.callback;

import com.alibaba.testable.core.annotation.MockMethod;
import com.reagent.research.financial.docking.dto.order.OrderDTO;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.CancelOrderManageService;
import com.ruijing.store.order.constant.DockingConstant;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.lang.reflect.Method;
import java.util.Date;

public class OrderStatusUpdateCallbackServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderStatusUpdateCallbackServiceImpl orderStatusUpdateCallbackService;

    public static class Mock {
        @MockMethod(targetClass = CancelOrderManageService.class)
        public void cancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        }
    }

    /**
     * private void updateOrderStatusProcess(OrderMasterDO orderInfo, OrderDTO dockingOrderInfo,
     *                                      String failedReason, UpdateOrderParamDTO updateOrderParam,
     *                                      String orderNo, Date nowDate) {
     */
    @Test
    public void updateOrderStatusProcess() throws Exception {
        Class<OrderStatusUpdateCallbackServiceImpl> orderStatusUpdateCallbackServiceClass = OrderStatusUpdateCallbackServiceImpl.class;
        Method updateOrderStatusProcess = orderStatusUpdateCallbackServiceClass.getDeclaredMethod("updateOrderStatusProcess"
                , OrderMasterDO.class, OrderDTO.class, String.class
                , UpdateOrderParamDTO.class, String.class, Date.class);

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(123);
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setStatus(DockingConstant.PURCHASE_APPLY_TO_CANCEL);

        updateOrderStatusProcess.setAccessible(true);
        Object invoke = updateOrderStatusProcess.invoke(orderStatusUpdateCallbackService
                , orderMasterDO, orderDTO, null
                , null, null, null);
    }
}