package com.ruijing.store.order.base.docking.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class DockingExtraRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private DockingExtraRpcServiceImpl dockingExtraRpcService;

    @Mock
    private DockingExtraService dockingExtraService;

    @Test
    public void saveOrUpdateByInfo() {
        Mockito.when(dockingExtraService.saveOrUpdateDockingExtra(Mockito.any())).thenReturn(1);
        DockingExtraDTO request = new DockingExtraDTO();
        request.setInfo("aaa");
        RemoteResponse<Integer> response = dockingExtraRpcService.saveOrUpdateByInfo(request);
        Assert.assertTrue(response.isSuccess());
    }
}