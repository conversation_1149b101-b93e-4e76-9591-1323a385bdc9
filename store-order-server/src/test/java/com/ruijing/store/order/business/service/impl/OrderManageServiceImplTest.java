package com.ruijing.store.order.business.service.impl;

import com.alibaba.testable.core.annotation.MockMethod;
import com.alibaba.testable.core.model.MockScope;
import com.alibaba.testable.core.tool.TestableTool;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.fundcard.dto.ChangeFundCardCallbackResult;
import com.reagent.research.fundcard.dto.FundCardResultDTO;
import com.reagent.research.fundcard.dto.UnfreezeCallbackResult;
import com.reagent.research.fundcard.enums.ChangeFundCardStatusEnum;
import com.reagent.research.fundcard.enums.FundCardAsyCallBackEnum;
import com.reagent.research.fundcard.enums.HandleResultEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.component.BeanContainer;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.MoneyControlOperatingEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderUnFreezeTypeEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderApprovalParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterTimeOutDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.api.base.ordermaster.dto.TimeOutOrderParamsDTO;
import com.ruijing.store.order.api.base.other.dto.OrderUnFreezeRequestDTO;
import com.ruijing.store.order.api.search.dto.OrderStatisticsParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.*;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper;
import com.ruijing.store.order.base.minor.mapper.ProductDescriptionSnapshotMapper;
import com.ruijing.store.order.base.minor.mapper.RefCouponPurchaserDOMapper;
import com.ruijing.store.order.base.minor.model.DangerousTagDO;
import com.ruijing.store.order.base.minor.model.RefCouponPurchaserDO;
import com.ruijing.store.order.base.minor.service.impl.DangerousTagDOServiceImpl;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.OrderDetailRelatedService;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.rpc.impl.OrderManageRpcServiceImpl;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.TimeUtil;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import org.apache.ibatis.annotations.Param;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

public class OrderManageServiceImplTest extends MockBaseTestCase {

    public static Map<String, Object> MOCK_CONTEXT = TestableTool.MOCK_CONTEXT;

    @InjectMocks
    private OrderManageServiceImpl orderManageService;

    @org.mockito.Mock
    private OrderMasterMapper orderMasterMapper;

    @org.mockito.Mock
    private OrderDetailMapper orderDetailMapper;

    @org.mockito.Mock
    private UserClient userClient;

    @org.mockito.Mock
    private ProductDescriptionSnapshotMapper productDescriptionSnapshotMapper;

    @org.mockito.Mock
    private RefOrderDetailTagDOMapper refOrderDetailTagDOMapper;

    @org.mockito.Mock
    private DangerousTagDOMapper dangerousTagDOMapper;

    @org.mockito.Mock
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @org.mockito.Mock
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @org.mockito.Mock
    private OrderEmailHandler orderEmailHandler;

    @org.mockito.Mock
    private TPIOrderClient tpiOrderClient;

    @InjectMocks
    OrderManageServiceImpl orderManageServiceImpl;

    @org.mockito.Mock
    private SysConfigClient sysConfigClient;

    @org.mockito.Mock
    private InvoiceClient invoiceClient;

    @org.mockito.Mock
    private ProductClient productClient;

    @org.mockito.Mock
    private DepartmentRpcClient departmentRpcClient;

    @org.mockito.Mock
    private DockingExtraService dockingExtraService;

    @org.mockito.Mock
    private OrderOtherLogClient orderOtherLogClient;

    @org.mockito.Mock
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @org.mockito.Mock
    private OrderManageServiceImpl mockOrderManageServiceImpl;

    @org.mockito.Mock
    private WmsRuleRpcServiceClient wmsRuleRpcServiceClient;

    @org.mockito.Mock
    private BizWareHouseClient bizWareHouseClient;

    @org.mockito.Mock
    private WaitingStatementService waitingStatementService;

    @org.mockito.Mock
    private ResearchStatementClient researchStatementClient;

    @org.mockito.Mock
    private OrderDetailRelatedService orderDetailRelatedService;

    @org.mockito.Mock
    private SuppClient suppClient;

    @org.mockito.Mock
    private DangerousTagDOServiceImpl dangerousTagDOServiceImpl;

    @org.mockito.Mock
    private GoodsReturnMapper goodsReturnMapper;

    @org.mockito.Mock
    private WareHouseClient wareHouseClient;

    @org.mockito.Mock
    private ApplicationBaseClient applicationBaseClient;

    @org.mockito.Mock
    private BidClient bidClient;

    @org.mockito.Mock
    private OrderApprovalLogService orderApprovalLogService;

    @org.mockito.Mock
    private OrderAddressRPCClient orderAddressRPCClient;

    @org.mockito.Mock
    private OrderManageRpcServiceImpl orderManageRpcService;

    @org.mockito.Mock
    private OrderAcceptService orderAcceptService;

    @org.mockito.Mock
    private OrderStatementService orderStatementService;

    @org.mockito.Mock
    private OrderFundCardService orderFundCardService;

    @org.mockito.Mock
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Test
    public void test(){
       Long i = 1l;
       int j = 1;
        System.out.println(i==j);
        System.out.println(i.equals(j));
    }

    public static class Mock {
        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void submitWareHouse(Integer orgId, String orgCode, List<String> picUrlList, OrderMasterDO orderMasterDO, Integer departmentId, List<OrderDetailDO> orderDetailList) throws CallRpcException {
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void orderFundCardUnFreeze(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO) {
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        public boolean addSku(Integer suppId, Integer orderMasterId, Integer orderType) {
            return true;
        }

        @MockMethod(targetClass = RefCouponPurchaserDOMapper.class)
        int updateByPrimaryKeySelective(RefCouponPurchaserDO record) {
            return 1;
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        public void deleteInvoiceByOrderId(String orgCode, List<Integer> orderIdList) {

        }

        @MockMethod(targetClass = CacheClient.class)
        public void controlRepeatOperation(String uniqKey, Integer timeLimit) {

        }

        @MockMethod(targetClass = CacheClient.class)
        public void removeCache(String uniqKey) {
        }

        @MockMethod(targetClass = UserClient.class)
        public String getOrgCodeById(Integer orgId) {
            return OrgConst.HUA_NAN_NONG_YE_DA_XUE;
        }

        @MockMethod(targetClass = UserClient.class)
        public UserBaseInfoDTO getUserDetailByID(Integer userId) {
            UserBaseInfoDTO userBaseInfoDTO = new UserBaseInfoDTO();
            userBaseInfoDTO.setName("TEST");
            return userBaseInfoDTO;
        }

        @MockMethod(targetClass = UserClient.class)
        public LoginUserInfoBO getLoginUserInfo(String currentUserGuid, Integer orgId
                , Boolean requireDeptList, String accessCode) {
            LoginUserInfoBO loginUserInfoBO = new LoginUserInfoBO();
            loginUserInfoBO.setDeptIdList(New.list(1));
            return loginUserInfoBO;
        }

        @MockMethod(targetClass = ApplicationBaseClient.class)
        public ApplicationMasterDTO getApplicationMasterByApplyId(Integer applyId, Boolean withDetail) {
            ApplicationMasterDTO applicationMasterDTO = new ApplicationMasterDTO();
            applicationMasterDTO.setApplyNumber("1");
            applicationMasterDTO.setCreateTime(new Date());
            ApplicationDetailDTO applicationDetailDTO = new ApplicationDetailDTO();
            applicationDetailDTO.setQuantity(new BigDecimal(1));
            applicationDetailDTO.setBidPrice(new BigDecimal(2));
            applicationMasterDTO.setDetails(New.list(applicationDetailDTO));
            return applicationMasterDTO;
        }

        @MockMethod(targetClass = OrderSearchBoostService.class)
        public Map<Integer, Integer> countOrderByStatus(OrderStatisticsParamDTO orderStatisticsParamDTO) {
            return new HashMap<>();
        }

        @MockMethod(targetClass = SysConfigClient.class)
        public String getConfigByOrgCodeAndConfigCode(String orgCode, String configCode) throws CallRpcException {
            return "2";
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private ReceiptOrderResponseDO receiptOrderCore(Integer orgId, String orgCode, Integer operatorId, String operatorName, OrderMasterDO orderMasterDO,
                                                        Map<String, String> receiptConfigMap, List<OrderDetailDO> orderDetailList, String urls, String reason) throws CallRpcException {
            switch ((String)MOCK_CONTEXT.get("case")) {
                case "mock-receive-core":
                default:
                    return new ReceiptOrderResponseDO();
            }
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void saveOrderPic(List<String> picUrlList, String orderNo) {
        }


        @MockMethod(targetClass = DepartmentRpcClient.class, scope = MockScope.ASSOCIATED)
        public void createOrderDockingLog(String orderNo,String orgCode,String paramInfo
                ,String resultInfo,String operation,String result){
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void huaNongCancelOrder(OrderMasterDO orderMasterDO, String cancelReason){ }

        @MockMethod(targetClass = OrderManageServiceImpl.class)
        private void orderFundCardUnFreeze(OrderMasterDO orderMasterDO) {
        }

        @MockMethod(targetClass = OrderMasterMapper.class)
        int updateByPrimaryKeySelective(OrderMasterDO record) {
            return 1;
        }

        @MockMethod(targetClass = ApplicationBaseService.class)
        void changeCardRollBackMoneyControl(OrderMasterDO orderMasterDO, MoneyControlOperatingEnum moneyControlOperatingEnum) {
        }

        @MockMethod(targetClass = BeanContainer.class)
        public static <T> T getBean(Class<T> clazz, String beanName) {
            return null;
        }

        @MockMethod(targetClass = BizWareHouseClient.class, scope = MockScope.ASSOCIATED)
        public boolean batchSaveWarehouseApplication(List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList) {
            return true;
        }

        @MockMethod(targetClass = UserClient.class, scope = MockScope.ASSOCIATED)
        public boolean checkUserInDepartmentAccess(Integer userId, List<Integer> departmentIds, List<String> accessList, Integer orgId){
            return true;
        }

        @MockMethod(targetClass = CacheClient.class, scope = MockScope.ASSOCIATED)
        public void controlRepeatOperation(String uniqKey, Integer timeLimit, String tipsMessage){
        }

        @MockMethod(targetClass = OrderMasterMapper.class, scope = MockScope.ASSOCIATED)
        List<OrderMasterDO> findByDeptIdAndIdIn(@Param("deptIdCollection") Collection<Integer> deptIdCollection, @Param("idCollection") Collection<Integer> idCollection){
            OrderMasterDO orderMasterDO = new OrderMasterDO();
            switch ((String)MOCK_CONTEXT.get("case")) {
                case "mock-findByOrgIdAndIdIn-batchUnfreezeFund-allReturn":
                    orderMasterDO.setStatus(OrderStatusEnum.Close.getValue());
                    orderMasterDO.setFundStatus(OrderFundStatusEnum.ThrawFailed.getValue());
                    orderMasterDO.setForderamounttotal(new BigDecimal(1));
                    // 整单退货
                    orderMasterDO.setReturnAmount(1.0);
                    return New.list(orderMasterDO);
                case "mock-findByOrgIdAndIdIn-batchUnfreezeFund-cancel":
                    orderMasterDO.setStatus(OrderStatusEnum.Close.getValue());
                    orderMasterDO.setFundStatus(OrderFundStatusEnum.ThrawFailed.getValue());
                    orderMasterDO.setForderamounttotal(new BigDecimal(2));
                    orderMasterDO.setReturnAmount(1.0);
                    orderMasterDO.setFcancelmanid("1");
                    return New.list(orderMasterDO);
                default:
                    return New.list(orderMasterDO);
            }
        }

        @MockMethod(targetClass = OrderMasterMapper.class, scope = MockScope.ASSOCIATED)
        List<OrderMasterTimeOutDTO> findAllByFbuyDepartmentIdAndStatus(@Param("userReq") TimeOutOrderParamsDTO userReq,
                                                                       @Param("examineStatus") Collection<Integer> examineStatus,
                                                                       @Param("balanceStatus") Collection<Integer> balanceStatus,
                                                                       @Param("returnStatusCollection") Collection<Integer> returnStatusCollection,
                                                                       @Param("fundStatusNotInCollection") Collection<Integer> fundStatusNotInCollection,
                                                                       @Param("config") Map<String, Integer> config) {

            return New.list(new OrderMasterTimeOutDTO());
        }

        @MockMethod(targetClass = OrderManageServiceImpl.class, scope = MockScope.ASSOCIATED)
        private void executeTimeOutType(int balanceDaysInt, int examineDaysInt, OrderMasterTimeOutDTO order) {
        }
    }

    @Test
    public void batchUnfreezeFund() {
        LoginUserInfoBO loginUserInfoBO = new LoginUserInfoBO();
        // 整单退货 --> 订单关闭
        MOCK_CONTEXT.put("case", "mock-findByOrgIdAndIdIn-batchUnfreezeFund-allReturn");
        orderManageService.batchUnfreezeFund(loginUserInfoBO,New.list(1));
        // 取消订单 --> 订单关闭
        MOCK_CONTEXT.put("case", "mock-findByOrgIdAndIdIn-batchUnfreezeFund-cancel");
        orderManageService.batchUnfreezeFund(loginUserInfoBO,New.list(1));
    }

    @Test
    public void getOrderCountByStatus() {
        orderManageService.getOrderCountByStatus(111,222);
    }

    @Test
    public void orderFundCardUnFreeze() {
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setId(1);
        orderMasterDO.setFusercode("ZHONG_SHAN_DA_XUE");
        orderMasterDO.setForderdate(TimeUtil.getSettingDate(-1));
        orderMasterDO.setFundStatus(OrderFundStatusEnum.Freezed.getValue());
        orderMasterDO.setFbuydepartmentid(1);
        orderMasterDO.setForderamounttotal(BigDecimal.ONE);
        orderMasterDO.setReturnAmount(0.00d);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(orderMasterDO);
        RefFundcardOrderDO refFundcardOrderDO = new RefFundcardOrderDO();
        refFundcardOrderDO.setCardId("test");
        refFundcardOrderDO.setOrderId("1");
        refFundcardOrderDO.setFreezeAmount(BigDecimal.ONE);
        Mockito.when(refFundcardOrderMapper.findByOrderId(Mockito.anyString())).thenReturn(Arrays.asList(refFundcardOrderDO));
        DockingExtraDTO dockingExtraDTO = new DockingExtraDTO();
        Mockito.when(dockingExtraService.findDockingExtra(Mockito.any())).thenReturn(Arrays.asList(dockingExtraDTO));

        Mockito.when(researchFundCardServiceClient.isNewBudgetByOrgCode(Mockito.anyString())).thenReturn(true);
        Mockito.when(userClient.getUserInfo(Mockito.anyInt(), Mockito.anyInt())).thenReturn(new UserBaseInfoDTO());
        Mockito.when(userClient.getOrgById(Mockito.anyInt())).thenReturn(new OrganizationDTO());

        DepartmentDTO departmentDTO = new DepartmentDTO();
        Mockito.when(departmentRpcClient.getDepartmentsByIds(Mockito.anyList())).thenReturn(Arrays.asList(departmentDTO));
        Mockito.when(orderMasterMapper.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);

        FundCardResultDTO fundCardResultDTO = new FundCardResultDTO();
        fundCardResultDTO.setHandleResult(HandleResultEnum.UN_HANDLE.getCode());
        Mockito.when(researchFundCardServiceClient.fundCardUnFreezeBatch(Mockito.any())).thenReturn(fundCardResultDTO);

        orderManageService.orderFundCardUnFreezeById(1);

        fundCardResultDTO = new FundCardResultDTO();
        fundCardResultDTO.setRelyAsyCallback(FundCardAsyCallBackEnum.NO_NEED.getValue());
        Mockito.when(researchFundCardServiceClient.fundCardUnFreezeBatch(Mockito.any())).thenReturn(fundCardResultDTO);
        orderManageService.orderFundCardUnFreezeById(1);

        Mockito.when(researchFundCardServiceClient.fundCardUnFreezeBatch(Mockito.any(OrgRequest.class))).thenThrow(new IllegalStateException("time out"));
        orderManageService.orderFundCardUnFreezeById(1);

        OrderUnFreezeRequestDTO orderUnFreezeRequestDTO = new OrderUnFreezeRequestDTO();
        orderUnFreezeRequestDTO.setFreezeAmount(BigDecimal.ONE);
        orderUnFreezeRequestDTO.setOrderId(1);
        orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.RETURN);
        orderUnFreezeRequestDTO.setReturnId("1");
        orderManageService.orderFundCardUnFreeze(orderUnFreezeRequestDTO);
    }


    @Test
    public void unFrozenCallback() {
        CallbackRequest<UnfreezeCallbackResult> callbackRequest = new CallbackRequest<>();
        UnfreezeCallbackResult unfreezeCallbackResult = new UnfreezeCallbackResult();
        unfreezeCallbackResult.setSerialNumber("test");
        callbackRequest.setData(unfreezeCallbackResult);
        callbackRequest.setData(unfreezeCallbackResult);
        callbackRequest.setCode(CallbackRequest.SUCCESS);

        OrderMasterDO order = new OrderMasterDO();
        order.setStatus(3);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(order);
        Mockito.when(orderMasterMapper.updateOrderByOrderNo(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());

        orderFundCardService.unFrozenCallback(callbackRequest);
        callbackRequest.setCode(CallbackRequest.FAILURE);
        orderFundCardService.unFrozenCallback(callbackRequest);
    }

    @Test
    public void changeFundCardCallBack() throws Exception {
        CallbackRequest<ChangeFundCardCallbackResult> callbackRequest = new CallbackRequest<>();
        ChangeFundCardCallbackResult cardCallbackResult = new ChangeFundCardCallbackResult();
        cardCallbackResult.setStatus(ChangeFundCardStatusEnum.SUCCESS.getCode());
        cardCallbackResult.setSerialNumber("test");
        ExtraDTO extraDTO = new ExtraDTO();
        extraDTO.setValue("{}");
        cardCallbackResult.setExtraDTOs(Arrays.asList(extraDTO));
        callbackRequest.setData(cardCallbackResult);
        callbackRequest.setCode(CallbackRequest.SUCCESS);

        OrderMasterDO order = new OrderMasterDO();
        order.setId(1);
        order.setStatus(20);
        order.setSpecies(new Byte("1"));
        order.setInventoryStatus(new Byte("1"));

        Mockito.when(orderMasterMapper.findByForderno(Mockito.any())).thenReturn(order);
        Mockito.when(orderMasterMapper.updateOrderByOrderNo(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(orderOtherLogClient).createOrderDockingLog(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.any());
        Mockito.doNothing().when(orderApprovalLogService).createOrderOperateLog(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.any(), Mockito.any());

        OrderFundCardDTO orderFundCardDTO = new OrderFundCardDTO();
        orderFundCardDTO.setOrderId(1);
        Mockito.when(orderFundCardCacheClient.findOrderFundCardCache(Mockito.anyList())).thenReturn(Arrays.asList(orderFundCardDTO));
        Mockito.doNothing().when(mockOrderManageServiceImpl).acceptanceApprovalSuccess(Mockito.any(), Mockito.any());
        Mockito.when(orderMasterMapper.updateOrderById(Mockito.any())).thenReturn(1);

        callbackRequest.setCode(CallbackRequest.SUCCESS);
        orderManageService.changeFundCardCallBack(callbackRequest);

        callbackRequest.setCode(CallbackRequest.FAILURE);
        orderManageService.changeFundCardCallBack(callbackRequest);

        cardCallbackResult.setStatus(2);
        callbackRequest.setData(cardCallbackResult);
        orderManageService.changeFundCardCallBack(callbackRequest);
    }

    @Test
    public void changeFundCard() {
        RefFundcardOrderDO fundcardOrderDO = new RefFundcardOrderDO();
        Mockito.when(refFundcardOrderMapper.findByOrderIdIn(Mockito.anyList())).thenReturn(Arrays.asList(fundcardOrderDO));
        DockingExtraDTO dockingExtraDTO = new DockingExtraDTO();
        Mockito.when(dockingExtraService.findDockingExtra(Mockito.any())).thenReturn(Arrays.asList(dockingExtraDTO));

        FundCardResultDTO fundCardResultDTO = new FundCardResultDTO();
        fundCardResultDTO.setHandleResult(204);
        Mockito.when(researchFundCardServiceClient.changeFundCard(Mockito.any(), Mockito.any(),null)).thenReturn(fundCardResultDTO);
        OrderDetailDO detailDO = new OrderDetailDO();
        detailDO.setFbidamount(BigDecimal.ONE);
        detailDO.setReturnAmount(BigDecimal.ZERO.doubleValue());
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.any())).thenReturn(Arrays.asList(detailDO));

        OrderApprovalParamDTO orderApprovalParamDTO = new OrderApprovalParamDTO();
        orderApprovalParamDTO.setUserName("test1");
        orderApprovalParamDTO.setJobNumber("test2");
        orderApprovalParamDTO.setOrgCode("test3");

        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderno("test1");
        orderMasterDO.setId(1);
        orderMasterDO.setForderamounttotal(BigDecimal.valueOf(654));
        OrderFundCardDTO fundCardDTO = new OrderFundCardDTO();
        fundCardDTO.setFundCardId("test1");
        fundCardDTO.setFreezeAmount(BigDecimal.ONE);
        List<OrderFundCardDTO> orderFundCardDTOList = Arrays.asList(fundCardDTO);
        orderManageService.changeFundCard(orderApprovalParamDTO, orderMasterDO, orderFundCardDTOList);
    }


    @Test
    public void executeDetailDangerousTag() throws Exception {
        Class<OrderManageServiceImpl> orderManageServiceImpl = (Class<OrderManageServiceImpl>) orderManageService.getClass();
        Method executeDetailDangerousTag = orderManageServiceImpl.getDeclaredMethod("executeDetailDangerousTag", Map.class);
        executeDetailDangerousTag.setAccessible(true);

        Map<String, DangerousTagDO> dangerousTagDOMap = new HashMap<>();
        Mockito.when(dangerousTagDOMapper.selectByBusinessIdInAndBusinessType(Mockito.anyCollection()
                ,Mockito.anyInt())).thenReturn(New.list());

        Map<Integer, List<OrderDetailTimeOutDTO>> timeOutDetailDTOMap = new HashMap();
        OrderDetailTimeOutDTO orderDetailTimeOutDTO = new OrderDetailTimeOutDTO();
        orderDetailTimeOutDTO.setId(1);
        timeOutDetailDTOMap.put(1,New.list(orderDetailTimeOutDTO));

        executeDetailDangerousTag.invoke(orderManageService,timeOutDetailDTOMap);

    }

    @Test
    public void submitOrderApproval() {
        OrderMasterDO o = new OrderMasterDO();
        o.setStatus(20);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(o);
        Mockito.when(orderMasterMapper.updateOrderById(Mockito.any())).thenReturn(1);


        Mockito.when(userClient.findUserHasAccess(Mockito.anyInt(),  Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString())).thenReturn(true);

        OrderApprovalParamDTO request = new OrderApprovalParamDTO();
        request.setApprovalMode(0);
        request.setOperateType(0);
        request.setReason("test");
        orderManageService.submitOrderApproval(request);
    }


}