package com.ruijing.store.order.search.rpc;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.search.dto.OrderPullParamDTO;
import com.ruijing.store.order.api.search.dto.SuppOrderPullDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.business.service.GoodsReturnService;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;


public class OrderSearchRpcServiceImplTest extends MockBaseTestCase {

    @InjectMocks
    private OrderSearchRpcServiceImpl orderSearchRpcService;

    @Mock
    private GoodsReturnService goodsReturnService;

    @Mock
    private OrderSearchBoostService orderSearchBoostService;

    @Test
    public void getReturnOrderForSupp() {
        BasePageResponseDTO<GoodsReturn> r = new BasePageResponseDTO();
        GoodsReturn g = new GoodsReturn();
        g.setGoodsReturnDetailJSON("[{\"detailId\": \"1\"}, {\"detailId\": \"2\"}]");
        r.setData(Arrays.asList(g));
        Mockito.when(goodsReturnService.getRangeDateReturnOrder(Mockito.any())).thenReturn(r);
        Mockito.when(orderSearchBoostService.searchOrderByDetailIds(Mockito.any())).thenReturn(Arrays.asList());

        OrderPullParamDTO params = new OrderPullParamDTO();
        RemoteResponse<BasePageResponseDTO<SuppOrderPullDTO>> response = orderSearchRpcService.getReturnOrderForSupp(params);
        Assert.assertTrue(response.isSuccess());
    }
}