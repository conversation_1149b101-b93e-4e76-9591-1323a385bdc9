package com.ruijing.store.goodsreturn.service.impl;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyDetailRequestDTO;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnApplyResponseVO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.UserClient;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class BuyerGoodsReturnServiceImplTest extends MockBaseTestCase {

    @Mock
    private GoodsReturnMapper goodsReturnMapper;

    @Mock
    private UserClient userClient;

    @Mock
    private CommonGoodsReturnService commonGoodsReturnService;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private OrderManageRpcService orderManageRpcService;

    @Mock
    private OrderEmailHandler orderEmailHandler;

    @Mock
    private CacheClient cacheClient;

    @InjectMocks
    private BuyerGoodsReturnServiceImpl buyerGoodsReturnService;

    @Mock
    private DockingExtraService dockingExtraService;

    @Test
    public void applyGoodsReturn() {
        GoodsReturn response1 = new GoodsReturn();
        response1.setOrgId(4);
        response1.setId(1);
        response1.setGoodsReturnStatus(2);
        Mockito.when(goodsReturnMapper.findByOrderNo(Mockito.anyString())).thenReturn(Arrays.asList(response1));
        Mockito.when(goodsReturnMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(new GoodsReturn());
        Mockito.when(goodsReturnMapper.updateGoodsDetailRemarkById(Mockito.any())).thenReturn(1);
        Mockito.when(userClient.getLoginUserInfo(Mockito.anyString(), Mockito.anyInt())).thenReturn(new LoginUserInfoBO());
        Mockito.doNothing().when(commonGoodsReturnService).saveReturnOperationLog(Mockito.any(GoodsReturnLogDO.class));
        Mockito.when(commonGoodsReturnService.createReturnNo(Mockito.anyString())).thenReturn("test");
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setStatus(5);
        Mockito.when(orderMasterMapper.findByForderno(Mockito.anyString())).thenReturn(orderMasterDO);
        Mockito.when(userClient.getLoginUserInfoBySessionInfo(Mockito.any())).thenReturn(new LoginUserInfoBO());
        Mockito.when(goodsReturnMapper.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.when(orderManageRpcService.applyGoodsReturnWithTimeOut(Mockito.any())).thenReturn(RemoteResponse.custom().setSuccess());
        Mockito.doNothing().when(orderEmailHandler).sendApplyReturnEmailToSupplier(Mockito.any(), Mockito.anyString());

        // detail 更新的商品
        OrderDetailDO detail = new OrderDetailDO();
        detail.setId(1);
        detail.setFquantity(BigDecimal.valueOf(12));
        detail.setFcancelquantity(BigDecimal.valueOf(10));
        detail.setFbidamount(BigDecimal.valueOf(123));
        detail.setReturnAmount(Double.valueOf(10));
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(new ArrayList<OrderDetailDO>(){{add(detail);}});
        Mockito.when(orderDetailMapper.findByIdIn(Mockito.anyList())).thenReturn(New.list(detail));

        // cache的模拟，正常会到finally
        Mockito.doNothing().when(cacheClient).controlRepeatOperation(Mockito.anyString(), Mockito.anyInt());
        Mockito.doNothing().when(cacheClient).removeCache(Mockito.anyString());

        GoodsReturnApplyRequestDTO requestDTO = new GoodsReturnApplyRequestDTO();
        requestDTO.setOrderNo("test");
        // 模拟真实退货入参
        GoodsReturnApplyDetailRequestDTO requestDetailList = new GoodsReturnApplyDetailRequestDTO();
        requestDetailList.setDetailId(1);
        requestDetailList.setQuantity(BigDecimal.ONE);
        requestDetailList.setAmount(BigDecimal.valueOf(123));
        requestDTO.setReturnApplyDetailList(New.list(requestDetailList));
        RjSessionInfo rjSessionInfo = new RjSessionInfo();
        rjSessionInfo.setOrgId(10);
        GoodsReturnApplyResponseVO responseVO = buyerGoodsReturnService.applyGoodsReturn(requestDTO, rjSessionInfo);
        Assert.assertTrue("error", responseVO != null);
        Mockito.verify(cacheClient, Mockito.atLeast(1)).removeCache(Mockito.anyString());

        // cache运行过程中，报错，finallyremove cache也一定会执行；
        Mockito.when(orderDetailMapper.loopUpdateByIdIn(Mockito.anyList())).thenThrow(new IllegalStateException("mock test exception"));
        Assert.assertThrows(IllegalStateException.class, () -> buyerGoodsReturnService.applyGoodsReturn(requestDTO, rjSessionInfo));
        Mockito.verify(cacheClient, Mockito.atLeast(3)).removeCache(Mockito.anyString());

        Mockito.when(commonGoodsReturnService.createReturnNo(Mockito.anyString())).thenThrow(new IllegalStateException("mock error"));
        Assert.assertThrows(IllegalStateException.class, () -> buyerGoodsReturnService.applyGoodsReturn(requestDTO, rjSessionInfo));

        rjSessionInfo.setOrgId(42);
        OrderMasterDO o1 = new OrderMasterDO();
        o1.setFusercode(DockingConstant.GUANG_XI_ZHONG_LIU);
        o1.setForderdate(new Date());
        Mockito.when(orderMasterMapper.findByForderno(Mockito.anyString())).thenReturn(o1);
        Mockito.doNothing().when(dockingExtraService).customValidationDockingStatus(Mockito.anyString());
        requestDTO.setId(null);
        responseVO = buyerGoodsReturnService.applyGoodsReturn(requestDTO, rjSessionInfo);
        Assert.assertTrue("error", responseVO != null);
    }
}