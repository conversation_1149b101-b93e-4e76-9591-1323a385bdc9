package com.ruijing.store.goodsreturn.service.impl;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.research.statement.api.service.StatementConfigApi;
import com.reagent.supp.api.wallet.dto.OrderForWalletDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.concurrent.ListenableFutures;
import com.ruijing.fundamental.remoting.msharp.context.ProviderContext;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.shop.crm.api.pojo.dto.SuppUserOrganizationDTO;
import com.ruijing.shop.crm.api.pojo.dto.UserDTO;
import com.ruijing.shop.crm.api.pojo.param.UserParam;
import com.ruijing.shop.crm.api.service.CrmUserService;
import com.ruijing.shop.crm.api.support.ResultBean;
import com.ruijing.shop.crm.api.support.enums.UserTagEnum;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.shop.wallet.api.dto.WaitingChargingOrderDTO;
import com.ruijing.shop.wallet.api.dto.WalletOrderReturnDTO;
import com.ruijing.shop.wallet.api.service.WalletOrderService;
import com.ruijing.store.MockBaseTestCase;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.service.BarCodeGoodsReturnService;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnStatisticsVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnCountDO;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.orgondemand.ClinicalOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.rpc.client.OrderAddressRPCClient;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;
import com.ruijing.store.order.rpc.client.WalletOrderRpcClient;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.organization.SuppOrgDTO;
import com.ruijing.store.user.api.service.DepartmentRpcService;
import com.ruijing.store.user.api.service.OrganizationRpcService;
import com.ruijing.store.user.api.service.UserInfoService;
import com.ruijing.store.utils.MyMockUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;

public class SupplierGoodsReturnServiceImplTest extends MockBaseTestCase {

    @Mock
    private GoodsReturnMapper goodsReturnMapper;

    @Mock
    private OrderMasterMapper orderMasterMapper;

    @Mock
    private OrderDetailMapper orderDetailMapper;

    @Mock
    private CrmUserService crmUserService;

    @Mock
    private OrganizationRpcService organizationRpcService;

    @Mock
    private UserInfoService userInfoService;

    @Mock
    private WalletOrderService walletOrderService;

    @Mock
    private WalletOrderRpcClient walletOrderRpcClient;

    @Mock
    private OrderManageService orderManageService;

    @Mock
    private ClinicalOrderService clinicalOrderService;

    @InjectMocks
    private SupplierGoodsReturnServiceImpl supplierGoodsReturnService;

    @Mock
    private CommonGoodsReturnService commonGoodsReturnService;

    @Mock
    private StatementConfigApi statementConfigApi;

    @Mock
    private CacheClient cacheClient;

    @Mock
    private ApplicationBaseService applicationBaseService;

    @Mock
    private OrderMasterForTPIService orderMasterForTPIService;

    @Mock
    private StatementPlatformClient statementPlatformClient;

    @Mock
    private DepartmentRpcService departmentRpcService;

    @Mock
    private OrderAddressRPCClient orderAddressRPCClient;

    @Mock
    private BarCodeGoodsReturnService barCodeGoodsReturnService;

    @Test
    public void updateGoodsReturnStatus() {

        Map<String, Object> session = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid("test");
        sessionInfo.setSuppId(165);
        sessionInfo.setUserId(4L);
        sessionInfo.setUserType(RjUserTypeEnum.SUPPLIER_USER);
        session.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setProviderContext(ProviderContext.getProviderContext());
        RpcContext.getProviderContext().setCallAttachments(session);

        ResultBean<UserDTO> resultBean = new ResultBean<>();
        resultBean.setCode(ResultBean.SUCCESS);
        UserDTO userDTO = new UserDTO();
        SuppUserOrganizationDTO suppUserOrganizationDTO = new SuppUserOrganizationDTO();
        suppUserOrganizationDTO.setOrgId(4);
        userDTO.setSuppUserOrgs(Arrays.asList(suppUserOrganizationDTO));
        userDTO.setTag(UserTagEnum.ORG_ADMIN.getValue());
        resultBean.setData(userDTO);
        Mockito.when(crmUserService.getUser(Mockito.any(UserParam.class))).thenReturn(resultBean);

        SuppOrgDTO suppOrgDTO = new SuppOrgDTO();
        suppOrgDTO.setOrgId(4);
        suppOrgDTO.setStatus(2);
        RemoteResponse<List<SuppOrgDTO>> remoteResponse = RemoteResponse.<List<SuppOrgDTO>>custom().setSuccess().setData(Arrays.asList(suppOrgDTO));
        Mockito.when(userIncludeSuppRpcService.findIncludeBySuppId(Mockito.anyInt(), Mockito.any())).thenReturn(remoteResponse);

        Mockito.when(goodsReturnMapper.countGroupByGoodsReturnStatus(Mockito.any())).thenReturn(new ArrayList<>());
        GoodsReturn goodsReturn = new GoodsReturn();
        goodsReturn.setId(1);
        goodsReturn.setOrgId(4);
        goodsReturn.setSupplierId(165);
        goodsReturn.setGoodsReturnDetailJSON("[{\"amount\": 101, \"detailId\": 1}]");
        goodsReturn.setGoodsReturnStatus(1);
        Mockito.when(goodsReturnMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(goodsReturn);
        Mockito.when(barCodeGoodsReturnService.updateOrderReturnForBarCode(Mockito.any(), Mockito.anyInt())).thenReturn(true);


        RemoteResponse<List<OrganizationDTO>> response2 = RemoteResponse.<List<OrganizationDTO>>custom().setSuccess().setData(Arrays.asList(new OrganizationDTO()));
        Mockito.when(organizationRpcService.getByIds(Mockito.anyList())).thenReturn(response2);

        RemoteResponse<List<UserBaseInfoDTO>> response3 = RemoteResponse.<List<UserBaseInfoDTO>>custom().setSuccess().setData(Arrays.asList(new UserBaseInfoDTO()));
        Mockito.when(userInfoService.getUserByUserIds(Mockito.anyList())).thenReturn(response3);

        OrderDetailDO orderDetailDO = new OrderDetailDO();
        orderDetailDO.setReturnStatus(1);
        orderDetailDO.setId(1);
        Mockito.when(orderDetailMapper.findByFmasterid(Mockito.anyInt())).thenReturn(Arrays.asList(orderDetailDO));
        OrderMasterDO orderMasterDO = new OrderMasterDO();
        orderMasterDO.setForderamounttotal(BigDecimal.valueOf(101.00));
        orderMasterDO.setFbuyerid(1);
        orderMasterDO.setFuserid(2);
        orderDetailDO.setReturnAmount(101.00d);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.anyInt())).thenReturn(orderMasterDO);

//        Mockito.when(orderManageService.addSku(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(true);
//        Mockito.doNothing().when(clinicalOrderService).releaseContractAmount(Mockito.anyInt(), Mockito.any(), Mockito.any());

        PageApiResult<List<WaitingChargingOrderDTO>> pageApiResult = new PageApiResult<>();
        pageApiResult.setData(Arrays.asList());
        Mockito.when(walletOrderService.queryWaitingChargingOrderPage(Mockito.any())).thenReturn(pageApiResult);

        Mockito.when(walletOrderRpcClient.findByOrderNo(Mockito.anyString())).thenReturn(Arrays.asList(new OrderForWalletDTO()));
        WalletOrderReturnDTO walletOrderReturnDTO = new WalletOrderReturnDTO();
        walletOrderReturnDTO.setAmount(BigDecimal.valueOf(1.00));
        Mockito.when(walletOrderRpcClient.findRefundByReturnNo(Mockito.anyString())).thenReturn(Arrays.asList());

        ApiResult apiResult = ApiResult.newSuccess();
        Mockito.when(walletOrderService.saveOrderReturn(Mockito.any())).thenReturn(apiResult);

        Mockito.doNothing().when(orderManageService).orderFundCardUnFreeze(Mockito.any(OrderMasterDO.class));
        Mockito.when(commonGoodsReturnService.getFirstReturnStatusMapByReturn(Mockito.any())).thenReturn(new HashMap<>());

        Mockito.when(applicationBaseService.updateApplyManageProductUsage(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Mockito.when(orderMasterForTPIService.asyncOrderReturn(Mockito.any(), Mockito.any())).thenReturn(ListenableFutures.forValue(true));
        Mockito.doNothing().when(statementPlatformClient).deleteWaitingStatementByOrderId(Mockito.any());

        Mockito.doNothing().when(statementPlatformClient).deleteWaitingStatementByOrderId(Mockito.any());

        GoodsReturnBaseRequestDTO request = new GoodsReturnBaseRequestDTO();
        request.setReturnId(1);
        request.setReturnStatus(5);
        RemoteResponse<Boolean> response = supplierGoodsReturnService.updateGoodsReturnStatus(request);
        Assert.assertTrue("模拟失败", response.isSuccess());
    }

    @Test
    public void getGoodsReturnStatistics() {
        Map<String, Object> session = new HashMap<>();
        RjSessionInfo sessionInfo = new RjSessionInfo();
        sessionInfo.setGuid("test");
        sessionInfo.setSuppId(165);
        session.put("RJ_SESSION_INFO", sessionInfo);

        RpcContext.getProviderContext().setProviderContext(ProviderContext.getProviderContext());
        RpcContext.getProviderContext().setCallAttachments(session);

        ResultBean<UserDTO> resultBean = new ResultBean<>();
        UserDTO userDTO = new UserDTO();
        userDTO.setSuppId(1);
        SuppUserOrganizationDTO suppUserOrganizationDTO = new SuppUserOrganizationDTO();
        suppUserOrganizationDTO.setOrgId(1);
        userDTO.setSuppUserOrgs(New.list(suppUserOrganizationDTO));

        resultBean.setCode(ResultBean.SUCCESS);
        resultBean.setData(userDTO);
        Mockito.when(crmUserService.getUser(Mockito.any(UserParam.class))).thenReturn(resultBean);

        SuppOrgDTO suppOrgDTO = new SuppOrgDTO();
        suppOrgDTO.setOrgId(1);
        suppOrgDTO.setStatus(2);
        RemoteResponse<List<SuppOrgDTO>> remoteResponse = RemoteResponse.<List<SuppOrgDTO>>custom().setSuccess().setData(New.list(suppOrgDTO));


        Mockito.when(cacheClient.getFromCache(Mockito.any())).thenReturn(null);

        Mockito.when(userIncludeSuppRpcService.findIncludeBySuppId(Mockito.anyInt(), Mockito.any())).thenReturn(remoteResponse);
        GoodsReturnCountDO watingForConfirm = new GoodsReturnCountDO();
        watingForConfirm.setGoodsReturnStatus(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode());
        watingForConfirm.setCount(1);
        GoodsReturnCountDO agreeToReturn = new GoodsReturnCountDO();
        agreeToReturn.setGoodsReturnStatus(GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode());
        agreeToReturn.setCount(2);
        GoodsReturnCountDO returnGoods = new GoodsReturnCountDO();
        returnGoods.setGoodsReturnStatus(GoodsReturnStatusEnum.RETURNED_GOODS.getCode());
        returnGoods.setCount(3);
        GoodsReturnCountDO success = new GoodsReturnCountDO();
        success.setGoodsReturnStatus(GoodsReturnStatusEnum.SUCCESS.getCode());
        success.setCount(4);
        GoodsReturnCountDO goodsReturnCountDO = new GoodsReturnCountDO();
        goodsReturnCountDO.setCount(5);
        Mockito.when(goodsReturnMapper.countGroupByGoodsReturnStatus(Mockito.any())).thenReturn(New.list(watingForConfirm,agreeToReturn,returnGoods,success,goodsReturnCountDO));
        RemoteResponse<GoodsReturnStatisticsVO> response = supplierGoodsReturnService.getGoodsReturnStatistics();
        Assert.assertTrue("error", response.isSuccess());

        GoodsReturnStatisticsVO goodsReturnStatistics =  new GoodsReturnStatisticsVO();
        Mockito.when(cacheClient.getFromCache(Mockito.any())).thenReturn(goodsReturnStatistics);
        response = supplierGoodsReturnService.getGoodsReturnStatistics();
        Assert.assertTrue("error", response.isSuccess());
    }

    @Test
    public void getGoodsReturnInfo() {
        GoodsReturn g = new GoodsReturn();
        g.setOrgId(1);
        g.setOrderId(1);
        Mockito.when(goodsReturnMapper.selectByPrimaryKey(Mockito.any())).thenReturn(g);
        OrderMasterDO o = new OrderMasterDO();
        o.setFbuydepartmentid(1);
        o.setFbuyerid(1);
        Mockito.when(orderMasterMapper.selectByPrimaryKey(Mockito.any())).thenReturn(o);

        MyMockUtils.setRPCContextRJSession("test", 1, 1);
        ResultBean<UserDTO> resultBean = new ResultBean<>();
        UserDTO userDTO = new UserDTO();
        userDTO.setSuppId(1);
        SuppUserOrganizationDTO suppUserOrganizationDTO = new SuppUserOrganizationDTO();
        suppUserOrganizationDTO.setOrgId(1);
        userDTO.setSuppUserOrgs(New.list(suppUserOrganizationDTO));

        resultBean.setCode(ResultBean.SUCCESS);
        resultBean.setData(userDTO);
        Mockito.when(crmUserService.getUser(Mockito.any(UserParam.class))).thenReturn(resultBean);

        SuppOrgDTO suppOrgDTO = new SuppOrgDTO();
        suppOrgDTO.setOrgId(1);
        suppOrgDTO.setStatus(2);
        RemoteResponse<List<SuppOrgDTO>> remoteResponse = RemoteResponse.<List<SuppOrgDTO>>custom().setSuccess().setData(New.list(suppOrgDTO));

        Mockito.when(cacheClient.getFromCache(Mockito.any())).thenReturn(null);

        Mockito.when(userIncludeSuppRpcService.findIncludeBySuppId(Mockito.anyInt(), Mockito.any())).thenReturn(remoteResponse);


        DepartmentDTO d = new DepartmentDTO();
        d.setManagerId(1);
        RemoteResponse<List<DepartmentDTO>> dResponse = RemoteResponse.<List<DepartmentDTO>>custom().setSuccess().setData(Arrays.asList(d));
        Mockito.when(departmentRpcService.getDepartmentsByIds(Mockito.any())).thenReturn(dResponse);

        UserBaseInfoDTO u = new UserBaseInfoDTO();
        u.setId(1);
        RemoteResponse<List<UserBaseInfoDTO>> uResponse = RemoteResponse.<List<UserBaseInfoDTO>>custom().setSuccess().setData(Arrays.asList(u));
        Mockito.when(userInfoService.getUserByUserIds(Mockito.any())).thenReturn(uResponse);

        OrderAddressDTO a = new OrderAddressDTO();
        a.setDeliveryType(1);
        Mockito.when(orderAddressRPCClient.findByOrderId(Mockito.any(Integer.class))).thenReturn(a);

        GoodsReturnBaseRequestDTO request = new GoodsReturnBaseRequestDTO();
        request.setReturnId(1);
        supplierGoodsReturnService.getGoodsReturnInfo(request);
    }
}
