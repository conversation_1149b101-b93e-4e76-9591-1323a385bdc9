package com.ruijing.store.order.base.core.mapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface OrderApprovalLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrderApprovalLog record);

    int insertSelective(OrderApprovalLog record);

    OrderApprovalLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OrderApprovalLog record);

    int updateByPrimaryKeyListSelective(List<OrderApprovalLog> recordList);

    /**
     * 操作日志批量操作
     * @param list
     * @return
     */
    int insertList(@Param("list")List<OrderApprovalLog> list);

    /**
     * 通过订单id查询审批日志
     * @param orderId
     * @return
     */
    List<OrderApprovalLog> findByOrderIdDesc(@Param("orderId")Integer orderId);

    /**
     * @description: 通过订单id查询审批日志，时间升序排列
     * @date: 2021/1/13 10:22
     * @author: zengyanru
     * @param orderId
     * @return java.util.List<com.ruijing.store.order.base.core.model.OrderApprovalLog>
     */
    List<OrderApprovalLog> findByOrderIdOrderByCreationTime(@Param("orderId")Integer orderId);

    /**
     * 根据订单id
     * @param orderIdCollection
     * @param approveStatusCollection
     * @return
     */
    List<OrderApprovalLog> findByOrderIdInAndApproveStatusIn(@Param("orderIdCollection")Collection<Integer> orderIdCollection,@Param("approveStatusCollection")Collection<Integer> approveStatusCollection);

    /**
     * 批量更新操作日志
     * @param logs
     * @return
     */
    int loopUpdateByIds(@Param("list") List<OrderApprovalLog> logs);
}