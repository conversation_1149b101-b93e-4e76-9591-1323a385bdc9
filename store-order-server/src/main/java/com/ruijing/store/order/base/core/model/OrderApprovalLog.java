package com.ruijing.store.order.base.core.model;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.order.whitehole.database.dto.eventlog.OpUserTypeEnum;

import java.io.Serializable;
import java.util.Date;

@RpcModel(value = "com.ruijing.store.order.base.core.model.OrderApprovalLog")
public class OrderApprovalLog implements Serializable {
    @RpcModelProperty(value = "null")
    private Integer id;

    /**
     * 订单ID
     */
    @RpcModelProperty(value = "订单ID")
    private Integer orderId;

    /**
     * 收货照片,多张用分号分隔
     */
    @RpcModelProperty(value = "收货照片,多张用分号分隔")
    private String photo;

    /**
     * 审批原因
     */
    @RpcModelProperty(value = "审批原因")
    private String reason;

    /**
     * 审批结果状态0驳回1通过
     */
    @RpcModelProperty(value = "审批结果状态0驳回1通过")
    private Integer approveStatus;

    /**
     * 审批等级
     */
    @RpcModelProperty(value = "审批等级")
    private Integer approveLevel;

    /**
     * 操作人ID
     */
    @RpcModelProperty(value = "操作人ID")
    private Integer operatorId;

    /**
     * 操作人姓名，为空时操作人是通过operatorId查出来的值
     */
    private String operatorName;

    /**
     * 操作人类型
     * {@link OpUserTypeEnum}
     */
    @RpcModelProperty("操作人类型")
    private Integer opUserType;

    /**
     * 操作时间
     */
    @RpcModelProperty(value = "操作时间")
    private Date creationTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public OrderApprovalLog setOperatorName(String operatorName) {
        this.operatorName = operatorName;
        return this;
    }

    public Integer getOpUserType() {
        return opUserType;
    }

    public void setOpUserType(Integer opUserType) {
        this.opUserType = opUserType;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Override
    public String toString() {
        return "OrderApprovalLog{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", photo='" + photo + '\'' +
                ", reason='" + reason + '\'' +
                ", approveStatus=" + approveStatus +
                ", approveLevel=" + approveLevel +
                ", operatorId=" + operatorId +
                ", operatorName='" + operatorName + '\'' +
                ", opUserType=" + opUserType +
                ", creationTime=" + creationTime +
                '}';
    }
}