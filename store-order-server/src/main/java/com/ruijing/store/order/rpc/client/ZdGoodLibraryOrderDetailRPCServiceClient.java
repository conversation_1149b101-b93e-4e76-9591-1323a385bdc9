package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.ZdGoodLibraryOrderDetailDTO;
import com.reagent.order.base.order.service.ZdGoodLibraryOrderDetailRPCService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2023/11/24 44
 */
@ServiceClient
public class ZdGoodLibraryOrderDetailRPCServiceClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private ZdGoodLibraryOrderDetailRPCService zdGoodLibraryOrderDetailRPCService;

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "批量插入中大订单库订单详情")
    public void batchInsertSelective(List<ZdGoodLibraryOrderDetailDTO> dtoList){
        RemoteResponse<Boolean> remoteResponse = zdGoodLibraryOrderDetailRPCService.batchInsertSelective(dtoList);
        Preconditions.isTrue(remoteResponse.isSuccess(), "批量插入中大订单库订单详情失败：" + remoteResponse.getMsg());
    }
}
