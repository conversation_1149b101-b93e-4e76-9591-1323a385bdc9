package com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.OrderDetailGoodsReturnBO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @Date 2020/11/9 9:33
 * @Description
 **/
@RpcModel("我的退货-退货单详情信息")
public class GoodsReturnOrderDetailVO implements Serializable {

    private static final long serialVersionUID = 6969862898455904514L;

    /**
     * 退货单id
     */
    @RpcModelProperty("退货单id")
    private Integer id;

    /**
     * 创建时间
     */
    @RpcModelProperty("创建时间")
    private Date creationTime;

    /**
     * 更新时间
     */
    @RpcModelProperty("更新时间")
    private Date updateTime;

    /**
     * 退货原因
     */
    @RpcModelProperty("退货原因")
    private String returnReason;

    /**
     * 说明
     */
    @RpcModelProperty("说明")
    private String remark;

    /**
     * 退货数量
     */
    @RpcModelProperty("退货数量")
    private Double quantity;

    /**
     * 退货价格
     */
    @RpcModelProperty("退货价格")
    private Double price;

    /**
     * 关联的订单详情Id
     */
    @RpcModelProperty("关联的订单详情Id")
    private Integer detailId;

    /**
     * 用户Id
     */
    @RpcModelProperty("用户Id")
    private Integer userId;

    /**
     * 供应商名称
     */
    @RpcModelProperty("供应商名称")
    private String supplierName;

    /**
     * 退货单号
     */
    @RpcModelProperty("退货单号")
    private String returnNo;

    /**
     * 回复时间（包括同意时间和拒绝时间）
     */
    @RpcModelProperty("回复时间（包括同意时间和拒绝时间）")
    private Date replyTime;

    /**
     * 采购人退货时间
     */
    @RpcModelProperty("采购人退货时间")
    private Date returnTime;

    /**
     * 供应商收货时间
     */
    @RpcModelProperty("供应商收货时间")
    private Date receiveTime;

    /**
     * 取消退货申请时间
     */
    @RpcModelProperty("取消退货申请时间")
    private Date cancelTime;

    /**
     * 拒绝原因
     */
    @RpcModelProperty("拒绝原因")
    private String refuseReason;

    /**
     * 申请人
     */
    @RpcModelProperty("申请人")
    private String applyName;

    /**
     * 同意理由
     */
    @RpcModelProperty("同意理由")
    private String agreeReason;

    /**
     * 规格
     */
    @RpcModelProperty("规格")
    private String specification;

    /**
     * 品牌
     */
    @RpcModelProperty("品牌")
    private String brand;

    /**
     * 商品名
     */
    @RpcModelProperty("商品名")
    private String goodsName;

    /**
     * 商品号，货号
     */
    @RpcModelProperty("商品号，货号")
    private String code;

    /**
     * 退货图片路径
     */
    @RpcModelProperty("退货图片路径")
    private String picturePath;

    /**
     * 订单详情退货状态
     */
    @RpcModelProperty("订单详情退货状态")
    private Integer status;

    /**
     * sn退货商品id或者编号
     */
    @RpcModelProperty("sn退货商品id或者编号")
    private Long sn;

    /**
     * 供应商id
     */
    @RpcModelProperty("供应商id")
    private Integer supplierId;

    /**
     * 退货总额
     */
    @RpcModelProperty("退货总额")
    private String amount;

    /**
     * 危化品标签
     */
    @RpcModelProperty("危化品标签")
    private String dangerousTag;

    @RpcModelProperty("退货的气瓶--商品纬度")
    private List<GasBottleVO> gasBottles;

    public Integer getId() {
        return id;
    }

    public GoodsReturnOrderDetailVO setId(Integer id) {
        this.id = id;
        return this;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public GoodsReturnOrderDetailVO setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public GoodsReturnOrderDetailVO setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public GoodsReturnOrderDetailVO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public GoodsReturnOrderDetailVO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Double getQuantity() {
        return quantity;
    }

    public GoodsReturnOrderDetailVO setQuantity(Double quantity) {
        this.quantity = quantity;
        return this;
    }

    public Double getPrice() {
        return price;
    }

    public GoodsReturnOrderDetailVO setPrice(Double price) {
        this.price = price;
        return this;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public GoodsReturnOrderDetailVO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public Integer getUserId() {
        return userId;
    }

    public GoodsReturnOrderDetailVO setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public GoodsReturnOrderDetailVO setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public GoodsReturnOrderDetailVO setReturnNo(String returnNo) {
        this.returnNo = returnNo;
        return this;
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public GoodsReturnOrderDetailVO setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
        return this;
    }

    public Date getReturnTime() {
        return returnTime;
    }

    public GoodsReturnOrderDetailVO setReturnTime(Date returnTime) {
        this.returnTime = returnTime;
        return this;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public GoodsReturnOrderDetailVO setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
        return this;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public GoodsReturnOrderDetailVO setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
        return this;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public GoodsReturnOrderDetailVO setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
        return this;
    }

    public String getApplyName() {
        return applyName;
    }

    public GoodsReturnOrderDetailVO setApplyName(String applyName) {
        this.applyName = applyName;
        return this;
    }

    public String getAgreeReason() {
        return agreeReason;
    }

    public GoodsReturnOrderDetailVO setAgreeReason(String agreeReason) {
        this.agreeReason = agreeReason;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public GoodsReturnOrderDetailVO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public GoodsReturnOrderDetailVO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public GoodsReturnOrderDetailVO setGoodsName(String goodsName) {
        this.goodsName = goodsName;
        return this;
    }

    public String getCode() {
        return code;
    }

    public GoodsReturnOrderDetailVO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public GoodsReturnOrderDetailVO setPicturePath(String picturePath) {
        this.picturePath = picturePath;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public GoodsReturnOrderDetailVO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Long getSn() {
        return sn;
    }

    public GoodsReturnOrderDetailVO setSn(Long sn) {
        this.sn = sn;
        return this;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public GoodsReturnOrderDetailVO setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
        return this;
    }

    public String getAmount() {
        return amount;
    }

    public GoodsReturnOrderDetailVO setAmount(String amount) {
        this.amount = amount;
        return this;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public GoodsReturnOrderDetailVO setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
        return this;
    }

    public List<GasBottleVO> getGasBottles() {
        return gasBottles;
    }

    public GoodsReturnOrderDetailVO setGasBottles(List<GasBottleVO> gasBottles) {
        this.gasBottles = gasBottles;
        return this;
    }

    public GoodsReturnOrderDetailVO() {
    }

    public GoodsReturnOrderDetailVO(OrderDetailGoodsReturnBO goodsReturnInfo, OrderDetailVO orderDetailVO) {
        // TODO: 精度问题可后续整理
        DecimalFormat df = new DecimalFormat("0.00");
        this.setId(goodsReturnInfo.getGoodsReturnId());
        this.setQuantity(goodsReturnInfo.getQuantity().doubleValue());
        this.setSupplierName(goodsReturnInfo.getSupplierName());
        this.setBrand(orderDetailVO.getBrand());
        this.setCode(orderDetailVO.getGoodsCode());
        this.setPrice(orderDetailVO.getPrice());
        this.setGoodsName(orderDetailVO.getGoodsName());
        this.setSn(orderDetailVO.getProductSn());
        this.setStatus(goodsReturnInfo.getGoodsReturnStatus());
        this.setSpecification(orderDetailVO.getSpecification());
        this.setPicturePath(orderDetailVO.getPicturePath());
        this.setDetailId(orderDetailVO.getId());
        this.setReturnNo(goodsReturnInfo.getReturnNo());

        if (goodsReturnInfo.getAmount() == null) {
            this.setAmount(df.format(0.00));
        } else {
            this.setAmount(df.format(goodsReturnInfo.getAmount()));
        }
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GoodsReturnOrderDetailVO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("creationTime=" + creationTime)
                .add("updateTime=" + updateTime)
                .add("returnReason='" + returnReason + "'")
                .add("remark='" + remark + "'")
                .add("quantity=" + quantity)
                .add("price=" + price)
                .add("detailId=" + detailId)
                .add("userId=" + userId)
                .add("supplierName='" + supplierName + "'")
                .add("returnNo='" + returnNo + "'")
                .add("replyTime=" + replyTime)
                .add("returnTime=" + returnTime)
                .add("receiveTime=" + receiveTime)
                .add("cancelTime=" + cancelTime)
                .add("refuseReason='" + refuseReason + "'")
                .add("applyName='" + applyName + "'")
                .add("agreeReason='" + agreeReason + "'")
                .add("specification='" + specification + "'")
                .add("brand='" + brand + "'")
                .add("goodsName='" + goodsName + "'")
                .add("code='" + code + "'")
                .add("picturePath='" + picturePath + "'")
                .add("status=" + status)
                .add("sn=" + sn)
                .add("supplierId=" + supplierId)
                .add("amount='" + amount + "'")
                .add("dangerousTag='" + dangerousTag + "'")
                .add("gasBottles=" + gasBottles)
                .toString();
    }
}
