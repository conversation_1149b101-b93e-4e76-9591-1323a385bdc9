package com.ruijing.store.order.business.service.constant;

import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.fundamental.common.collections.MapBuilder;

import java.util.Map;

/**
 * 订单验收常量
 */
public class OrderAcceptConstant {

    /**
     * 验收时图片上传上限
     */
    public final static int UPLOAD_RECEIVE_PIC_THRESHOLD_WHEN_RECEIVING = 15;

    /**
     * 验收图片上传数量上限
     */
    public final static int UPLOAD_RECEIVE_PIC_THRESHOLD = 20;

    /**
     * 验收附件上传数量上限
     */
    public final static int UPLOAD_RECEIVE_ATTACHMENT = 15;

    /**
     * 验收视频附件上传数量上限
     */
    public final static int UPLOAD_RECEIVE_ATTACHMENT_VIDEO = 1;

    /**
     * 验收 付款记录上传 数量上限
     */
    public final static int UPLOAD_RECEIVE_PAYMENT_RECORD = 5;

    /**
     * 订单验收编码
     */
    public static final String ACCEPT_SUFFIX = "_ACCEPT";

    public static final String JIANG_XI_ZHONG_YI_YAO_DA_XUE = "JIANG_XI_ZHONG_YI_YAO_DA_XUE";

    public static final String JIANG_XI_ZHONG_YI_YAO_DA_XUE_ACCEPT = JIANG_XI_ZHONG_YI_YAO_DA_XUE + ACCEPT_SUFFIX;

    public static final String GUANG_DONG_YAO_KE_DA_XUE = "GUANG_DONG_YAO_KE_DA_XUE";

    public static final String GUANG_DONG_YAO_KE_DA_XUE_ACCEPT = GUANG_DONG_YAO_KE_DA_XUE + ACCEPT_SUFFIX;

    public static final String ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN = "ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN";

    public static final String ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN_ACCEPT = ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN + ACCEPT_SUFFIX;

    public static final String FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN = "FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN";

    public static final String FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN_ACCEPT = FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN + ACCEPT_SUFFIX;

    public static final String ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN = "ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN";

    public static final String ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN_ACCEPT = ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN + ACCEPT_SUFFIX;

    private static final Map<String, String> orderAcceptStrategy = MapBuilder.<String, String>custom()
            .put(JIANG_XI_ZHONG_YI_YAO_DA_XUE, JIANG_XI_ZHONG_YI_YAO_DA_XUE_ACCEPT)
            .put(GUANG_DONG_YAO_KE_DA_XUE, GUANG_DONG_YAO_KE_DA_XUE_ACCEPT)
            .put(ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN, ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN_ACCEPT)
            .put(FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN, FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN_ACCEPT)
            .put(ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN, ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN_ACCEPT)
            .put(OrgConst.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN, OrgConst.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN + OrderAcceptConstant.ACCEPT_SUFFIX)
            .put(OrgConst.HUA_NAN_NONG_YE_DA_XUE, OrgConst.HUA_NAN_NONG_YE_DA_XUE+ACCEPT_SUFFIX)
            .put(OrgConst.ZHONG_KE_YUAN_ZHONG_JI_SUO, OrgConst.ZHONG_KE_YUAN_ZHONG_JI_SUO + ACCEPT_SUFFIX)
            .put(OrgConst.JI_NAN_DA_XUE, OrgConst.JI_NAN_DA_XUE + ACCEPT_SUFFIX)
            .build();

    public static Map<String, String> getAcceptStrategy() {
        return orderAcceptStrategy;
    }
}
