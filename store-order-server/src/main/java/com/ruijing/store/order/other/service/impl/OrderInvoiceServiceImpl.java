package com.ruijing.store.order.other.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.file.UploadFileSimpleDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.AttachmentDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.gateway.buyercenter.request.CreateInvoiceDTO;
import com.ruijing.store.order.other.service.OrderInvoiceService;
import com.ruijing.store.order.rpc.client.InvoiceClient;
import com.ruijing.store.order.rpc.client.OrderPushEventStatusClient;
import com.ruijing.store.order.rpc.client.RePushRPCServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-05-09 10:20
 * @description:
 **/
@Service
public class OrderInvoiceServiceImpl implements OrderInvoiceService {

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private UserClient userClient;

    @Resource
    private RePushRPCServiceClient rePushRpcServiceClient;

    @Resource
    private OrderPushEventStatusClient orderPushEventStatusClient;

    //更新发票后要重推的单位
    private static final Set<Integer> UPDATE_INVOICE_NEED_REPUSH_ORG_ID_SET = New.set(OrgEnum.ZHONG_KAI_NONG_YE_GONG_CHENG_XUE_YUAN.getValue());

    @Override
    @ServiceLog(description = "修改发票",serviceType = ServiceType.COMMON_SERVICE)
    public void saveInvoice(List<CreateInvoiceDTO> createInvoiceDTOList, Long userId, Integer orgId, String accessCode) {
        if(CollectionUtils.isEmpty(createInvoiceDTOList)){
            return;
        }
        //前置校验
        OrderMasterDO orderMasterDO = this.performGeneralPreValidation(createInvoiceDTOList);
        this.performOrgPreValidation(orderMasterDO.getForderno(), orgId);
        //订单条件校验
        Integer status = orderMasterDO.getStatus();
        Byte species = orderMasterDO.getSpecies();

        //【线下单】&&【待收货】不进行权限校验
        if(!(OrderSpeciesEnum.OFFLINE.getValue().equals(species.intValue()) && OrderStatusEnum.WaitingForReceive.getValue().equals(status))){
            Boolean canModifyInvoice = userClient.findUserHasAccess(orgId, userId.intValue(), null, accessCode);
            BusinessErrUtil.isTrue(canModifyInvoice, ExecptionMessageEnum.NO_PERMISSION_MODIFY_INVOICE);
        }
        //金额校验
        BigDecimal total = createInvoiceDTOList.stream()
                .map(CreateInvoiceDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //订单金额 = 订单总价格 - 退货金额
        BigDecimal returnAmount = orderMasterDO.getReturnAmount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderMasterDO.getReturnAmount());
        BigDecimal orderAmount = orderMasterDO.getForderamounttotal().subtract(returnAmount);
        BusinessErrUtil.isTrue(total.compareTo(orderAmount) == 0, ExecptionMessageEnum.INVOICE_AMOUNT_MISMATCH);

        if(OrderSpeciesEnum.OFFLINE.getValue().equals(species.intValue())){
            BusinessErrUtil.isTrue(OrderStatusEnum.Finish.getValue().equals(status) || OrderStatusEnum.WaitingForReceive.getValue().equals(status), ExecptionMessageEnum.OFFLINE_ORDER_INCOMPLETE_OR_PENDING);
        }else {
            BusinessErrUtil.isTrue(OrderStatusEnum.Finish.getValue().equals(status), ExecptionMessageEnum.ONLINE_ORDER_INCOMPLETE);
        }
        //保存发票
        this.saveCommonInvoice(createInvoiceDTOList, userId, orderMasterDO);

        //发票号
        String invoiceNoStr = createInvoiceDTOList.stream()
                .filter(Objects::nonNull)
                .map(createInvoiceDTO -> createInvoiceDTO.getInvoiceCode() + "-" + createInvoiceDTO.getInvoiceNo()
                ).collect(Collectors.joining(","));
        //修改发票成功，记录日志
        orderApprovalLogService.saveApprovalLog(orderMasterDO.getId(), OrderApprovalEnum.MODIFY_INVOICE.getValue(), orderMasterDO.getFbuyerid(), invoiceNoStr);
        if(UPDATE_INVOICE_NEED_REPUSH_ORG_ID_SET.contains(orgId)){
            //不管推送结果成功/失败，都推送
            rePushRpcServiceClient.rePush(orderMasterDO.getForderno(), null, OrderPushEventEnum.INVOICE_PUSH, true);
        }
    }


    /**
     * 通用保存发票
     *
     * @param createInvoiceDTOList
     * @param userId
     */
    @ServiceLog(description = "通用保存发票",serviceType = ServiceType.COMMON_SERVICE)
    private void saveCommonInvoice(List<CreateInvoiceDTO> createInvoiceDTOList, Long userId, OrderMasterDO orderMasterDO){
        if(CollectionUtils.isEmpty(createInvoiceDTOList)){
            return;
        }

        List<InvoiceDTO> invoiceDTOList = createInvoiceDTOList.stream().map(invoice->{
            InvoiceDTO invoiceDTO = new InvoiceDTO();
            invoiceDTO.setId(invoice.getId());
            invoiceDTO.setAmount(invoice.getAmount());
            invoiceDTO.setRemark(invoice.getRemark());
            invoiceDTO.setDrawer(invoice.getDrawer());
            invoiceDTO.setBankNameCompany(invoice.getBankNameCompany());
            invoiceDTO.setBankName(invoice.getBankName());
            invoiceDTO.setBankNum(invoice.getBankNum());
            invoiceDTO.setTaxpayerNo(invoice.getTaxpayerNo());
            invoiceDTO.setInvoiceDate(invoice.getInvoiceDate());
            invoiceDTO.setUrl(invoice.getUrl());
            invoiceDTO.setInvoicePhoto(invoice.getInvoicePhoto());
            invoiceDTO.setSummaryId(invoice.getSummaryId());
            invoiceDTO.setOrderIds(invoice.getOrderIds());
            invoiceDTO.setBankCode(invoice.getBankCode());
            invoiceDTO.setOrgId(orderMasterDO.getFuserid().longValue());
            invoiceDTO.setInvoiceType(invoice.getInvoiceType());
            invoiceDTO.setTaxAmount(invoice.getTaxAmount());
            invoiceDTO.setExcludedAmount(invoice.getExcludedAmount());
            invoiceDTO.setProjectCode(invoice.getProjectCode());
            invoiceDTO.setInvoiceItem(invoice.getInvoiceItem());
            invoiceDTO.setUserId(userId);
            invoiceDTO.setInvoiceNo(invoice.getInvoiceNo());
            if(StringUtils.isNotEmpty(invoice.getInvoiceCode())){
                invoiceDTO.setInvoiceNo(invoice.getInvoiceCode() + "-" + invoice.getInvoiceNo());
            }
            invoiceDTO.setInvoicePhoto(invoice.getInvoicePhoto());
            invoiceDTO.setSourceId(invoice.getOrderId());
            invoiceDTO.setType(InvoiceTypeEnum.ORDER.getType());
            //发票信息转换
            invoiceDTO.setInvoiceFileList(this.convertInvoiceFile(invoice.getInvoiceFileList()));
            return invoiceDTO;
        }).collect(Collectors.toList());
        invoiceClient.saveInvoice(invoiceDTOList);
    }

    /**
     * 通用前置校验
     *
     * @param createInvoiceDTOList
     * return 前置校验成功的订单信息
     */
    private OrderMasterDO performGeneralPreValidation(List<CreateInvoiceDTO> createInvoiceDTOList){
        List<Integer> orderIdList = createInvoiceDTOList.stream().filter(createInvoiceDTO -> createInvoiceDTO.getOrderId() != null).map(createInvoiceDTO -> createInvoiceDTO.getOrderId().intValue()).distinct().collect(Collectors.toList());
        BusinessErrUtil.isTrue(orderIdList.size() == 1, ExecptionMessageEnum.ONE_INVOICE_AT_A_TIME);
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderIdList.get(0));
        createInvoiceDTOList.forEach(invoice->{
            if(invoice.getInvoiceItem() == 1){
                // 全电发票，无枚举
                BusinessErrUtil.isTrue(invoice.getInvoiceNo() != null && invoice.getInvoiceNo().length() == 20, ExecptionMessageEnum.INVOICE_NUMBER_REQUIRED);
            }else{
                BusinessErrUtil.isTrue(invoice.getInvoiceCode() != null && 10 <= invoice.getInvoiceCode().length() && invoice.getInvoiceCode().length() <= 12, ExecptionMessageEnum.INVOICE_CODE_REQUIRED);
                BusinessErrUtil.isTrue(invoice.getInvoiceNo() != null && invoice.getInvoiceNo().length() == 8, ExecptionMessageEnum.INVOICE_NUMBER_8_DIGITS);
            }
            Preconditions.notNull(invoice.getInvoiceDate(), "发票日期不可空");
            Preconditions.notNull(invoice.getDrawer(), "开票单位不可空");
            Preconditions.notNull(invoice.getBankName(), "开户银行不可空");
            Preconditions.notNull(invoice.getBankNameCompany(), "银行名不可空");
            Preconditions.notNull(invoice.getBankNum(), "银行卡号不可空");
        });
        return orderMasterDO;
    }

    /**
     * 单位前置校验
     */
    private void performOrgPreValidation(String orderNo, Integer orgId){
        if(UPDATE_INVOICE_NEED_REPUSH_ORG_ID_SET.contains(orgId)){
            OrderEventStatusRequestDTO orderEventStatusRequestDTO = new OrderEventStatusRequestDTO();
            orderEventStatusRequestDTO.setOrderNoList(New.list(orderNo));
            orderEventStatusRequestDTO.setOrderPushEventEnumList(New.list(OrderPushEventEnum.INVOICE_PUSH));
            List<OrderEventStatusResponseDTO> orderEventStatusList = orderPushEventStatusClient.listEventPushStatus(orderEventStatusRequestDTO);
            if(CollectionUtils.isNotEmpty(orderEventStatusList)){
                OrderEventStatusResponseDTO orderEventStatusResponseDTO = orderEventStatusList.get(0);
                BusinessErrUtil.isTrue(!OrderEventStatusEnum.PUSHING.equals(orderEventStatusResponseDTO.getOrderEventStatusEnum()) , "正在保存发票，请稍后重试");
            }
        }
    }

    /**
     * 发票附件信息转换
     * List<AttachmentDTO> 转换 List<UploadFileSimpleDTO>
     *
     * @param attachmentDTOList
     * @return
     */
    private List<UploadFileSimpleDTO> convertInvoiceFile(List<AttachmentDTO> attachmentDTOList){
        List<UploadFileSimpleDTO> uploadFileSimpleDTOList = New.list();
        if(CollectionUtils.isEmpty(attachmentDTOList)){
            return uploadFileSimpleDTOList;
        }
        attachmentDTOList.forEach(attachmentDTO -> {
            UploadFileSimpleDTO uploadFileSimpleDTO = new UploadFileSimpleDTO();
            uploadFileSimpleDTO.setFileName(attachmentDTO.getFileName());
            uploadFileSimpleDTO.setUrl(attachmentDTO.getUrl());
            uploadFileSimpleDTOList.add(uploadFileSimpleDTO);
        });
        return uploadFileSimpleDTOList;
    }
}
