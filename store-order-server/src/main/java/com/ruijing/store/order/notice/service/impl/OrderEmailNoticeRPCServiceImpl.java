package com.ruijing.store.order.notice.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.notice.service.OrderEmailNoticeRPCService;
import com.ruijing.store.order.business.handler.OrderEmailHandler;

import javax.annotation.Resource;

@MSharpService
public class OrderEmailNoticeRPCServiceImpl implements OrderEmailNoticeRPCService {

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Override
    public RemoteResponse<Boolean> orderSplitNotice(OrderBasicParamDTO request) {
        Integer orderId = request.getOrderId();
        Preconditions.notNull(orderId, "orderId must not be null");
        boolean isSuccess = orderEmailHandler.sendSplitOrderDeliveryEmail(orderId);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(isSuccess);
    }

}
