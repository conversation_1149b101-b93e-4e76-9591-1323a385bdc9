package com.ruijing.store.order.business.enums.myorderlist;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 2020/12/28 14:52
 */
public enum OrderAcceptPermitEnum {

    CAN_ACCEPT(0,"显示验收按钮"),
    CAN_NOT_ACCEPT(1,"不显示验收按钮");

    public final Integer value;

    public final String name;

    OrderAcceptPermitEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return name;
    }

    public static final OrderAcceptPermitEnum getByName(String name) {
        for (OrderAcceptPermitEnum orderAcceptPermitEnum : OrderAcceptPermitEnum.values()) {
            if (orderAcceptPermitEnum.name.equals(name)){
                return orderAcceptPermitEnum;
            }
        }
        return null;
    }

    public static final OrderAcceptPermitEnum getByValue(Integer value) {
        for (OrderAcceptPermitEnum orderAcceptPermitEnum : OrderAcceptPermitEnum.values()) {
            if (orderAcceptPermitEnum.value.equals(value)){
                return orderAcceptPermitEnum;
            }
        }
        return null;
    }
}
