package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

/**
 * <AUTHOR>
 * @Date 2020/11/23 21:09
 * @Description
 **/
public class OrderCanInboundBO {

    private Boolean canInbound;

    private Integer canSubmitWarehouse;

    public Boolean getCanInbound() {
        return canInbound;
    }

    public void setCanInbound(Boolean canInbound) {
        this.canInbound = canInbound;
    }

    public Integer getCanSubmitWarehouse() {
        return canSubmitWarehouse;
    }

    public void setCanSubmitWarehouse(Integer canSubmitWarehouse) {
        this.canSubmitWarehouse = canSubmitWarehouse;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderCanInboundBO{");
        sb.append("canInbound=").append(canInbound);
        sb.append(", canSubmitWarehouse=").append(canSubmitWarehouse);
        sb.append('}');
        return sb.toString();
    }
}
