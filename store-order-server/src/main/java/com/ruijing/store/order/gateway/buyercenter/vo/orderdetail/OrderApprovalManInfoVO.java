package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/13 16:30
 * @description
 */
@Model("有审批权限的审批数据体")
public class OrderApprovalManInfoVO implements Serializable {

    private static final long serialVersionUID = -2259788156000009113L;
    
    @ModelProperty("每一级的审批人")
    List<OrderApprovalManItemVO> orderApprovalManItemVOList;

    public List<OrderApprovalManItemVO> getOrderApprovalManItemVOList() {
        return orderApprovalManItemVOList;
    }

    public OrderApprovalManInfoVO setOrderApprovalManItemVOList(List<OrderApprovalManItemVO> orderApprovalManItemVOList) {
        this.orderApprovalManItemVOList = orderApprovalManItemVOList;
        return this;
    }

    @Override
    public String toString() {
        return "OrderApprovalManInfoVO{" +
                "orderApprovalManItemVOList=" + orderApprovalManItemVOList +
                '}';
    }
}
