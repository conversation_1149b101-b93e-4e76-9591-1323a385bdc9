<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.timeoutstatistics.mapper.TimeoutStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO">
    <!--@mbg.generated-->
    <!--@Table t_timeout_statistics-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="dep_id" jdbcType="INTEGER" property="depId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="old_config_day" jdbcType="INTEGER" property="oldConfigDay" />
    <result column="old_config_amount" jdbcType="INTEGER" property="oldConfigAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, dep_id, `type`, amount, creation_time, update_time, old_config_day, old_config_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_timeout_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_timeout_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="batchDeleteById">
    <!--@mbg.generated-->
    delete from t_timeout_statistics
    where id in
    <foreach item="id" index="index" collection="idList"
             open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_timeout_statistics (org_id, dep_id, `type`, 
      amount, creation_time, update_time, 
      old_config_day, old_config_amount)
    values (#{orgId,jdbcType=INTEGER}, #{depId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{amount,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{oldConfigDay,jdbcType=INTEGER}, #{oldConfigAmount,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_timeout_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="depId != null">
        dep_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="oldConfigDay != null">
        old_config_day,
      </if>
      <if test="oldConfigAmount != null">
        old_config_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="depId != null">
        #{depId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldConfigDay != null">
        #{oldConfigDay,jdbcType=INTEGER},
      </if>
      <if test="oldConfigAmount != null">
        #{oldConfigAmount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO">
    <!--@mbg.generated-->
    update t_timeout_statistics
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="depId != null">
        dep_id = #{depId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldConfigDay != null">
        old_config_day = #{oldConfigDay,jdbcType=INTEGER},
      </if>
      <if test="oldConfigAmount != null">
        old_config_amount = #{oldConfigAmount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.timeoutstatistics.model.TimeoutStatisticsDO">
    <!--@mbg.generated-->
    update t_timeout_statistics
    set org_id = #{orgId,jdbcType=INTEGER},
      dep_id = #{depId,jdbcType=INTEGER},
      `type` = #{type,jdbcType=INTEGER},
      amount = #{amount,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      old_config_day = #{oldConfigDay,jdbcType=INTEGER},
      old_config_amount = #{oldConfigAmount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2019-10-23-->
  <insert id="insertList">
        INSERT INTO t_timeout_statistics(
        id,
        org_id,
        dep_id,
        type,
        amount,
        creation_time,
        update_time,
        old_config_day,
        old_config_amount
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=BIGINT},
            #{element.orgId,jdbcType=INTEGER},
            #{element.depId,jdbcType=INTEGER},
            #{element.type,jdbcType=INTEGER},
            #{element.amount,jdbcType=INTEGER},
            #{element.creationTime,jdbcType=TIMESTAMP},
            #{element.updateTime,jdbcType=TIMESTAMP},
            #{element.oldConfigDay,jdbcType=INTEGER},
            #{element.oldConfigAmount,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2019-10-25-->
  <delete id="delete">
    delete from  t_timeout_statistics
  </delete>

<!--auto generated by MybatisCodeHelper on 2019-10-30-->
  <select id="findAll" resultMap="BaseResultMap">
    select
    org_id, dep_id, `type`
    from t_timeout_statistics
  </select>

<!--auto generated by MybatisCodeHelper on 2019-11-26-->
  <select id="queryOneByOrgIdAndDepIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_timeout_statistics
        where 1 = 1
        <if test="orgId != null">
          and org_id = #{orgId,jdbcType=INTEGER}

        </if>
        <if test="depId != null">
          and dep_id=#{depId,jdbcType=INTEGER}

        </if>
        <if test="type != null">
          and `type`=#{type,jdbcType=INTEGER}
        </if>
    </select>

<!--auto generated by MybatisCodeHelper on 2019-12-04-->
  <delete id="deleteByOrgIdAndDepIdAndType">
        delete from t_timeout_statistics
        where org_id=#{orgId,jdbcType=INTEGER} and dep_id=#{depId,jdbcType=INTEGER} and `type`=#{type,jdbcType=INTEGER}
    </delete>

<!--auto generated by MybatisCodeHelper on 2020-06-29-->
  <select id="queryByOrgIdAndDepIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_timeout_statistics
    where 1 = 1
    <if test="orgId != null">
      and org_id = #{orgId,jdbcType=INTEGER}

    </if>

    <if test="depIdCollection != null">
      and dep_id in
      <foreach item="item" index="index" collection="depIdCollection"
               open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>

    </if>
  </select>

  <update id="addAmountById">
    <!--@mbg.generated-->
    update t_timeout_statistics
    set
    amount =
    case
    when amount > (#{amount,jdbcType=INTEGER} * -1) then amount + #{amount,jdbcType=INTEGER}
    else 0 end
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>