package com.ruijing.store.order.base.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.RefOrderFundCardDTO;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.api.dto.UserDTO;
import com.reagent.research.financial.docking.dto.order.OrderDetailDTO;
import com.reagent.research.fundcard.api.rule.dto.*;
import com.reagent.research.fundcard.api.rule.enums.ZhongZhongCategoryEnum;
import com.reagent.research.fundcard.dto.*;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.fundcard.dto.v2.SelectFundCardDTO;
import com.reagent.research.fundcard.enums.*;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.reagent.research.statement.api.statement.dto.StatementLogRequestDTO;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.shop.crm.api.pojo.dto.bank.BankAccountDTO;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderChangeCommonDTO;
import com.ruijing.store.order.api.base.other.dto.OrderOperatorDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.api.base.other.service.OrderRelatedRPCService;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.FundCardFreezeCommonService;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.util.FundCardUtils;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.OldDateConfigBO;
import com.ruijing.store.order.constant.*;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.FundCardProjectRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.FundCardSubjectRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderFundCardRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.OrderDetailFeeTypeVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2019/10/9 15:19
 **/
@Service
public class RefFundcardOrderServiceImpl implements RefFundcardOrderService {

    /**
     * 没有换卡接口但允许待结算换卡的单位（先解冻再冻结）
     */
    private static final List<String> ORG_LIST_CHANGE_CARD_WITHOUT_CHANGE_CARD_IMPL = New.list(OrgConst.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN);

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderRelatedRPCService orderRelatedRPCService;

    @Resource
    private UserClient userClient;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private FundCardFreezeCommonService fundCardFreezeCommonService;

    @Resource
    private CategoryDirectoryClient categoryDirectoryClient;

    @Resource
    private RefFundCardOrderClient refFundCardOrderClient;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    /**
     * 经费卡非必填的oms配置值
     */
    private static final String MUST_HAVE_FUNDCARD_FALSE = "0";

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public RemoteResponse insertRefFundcardOrder(RefFundcardOrderDTO refFundcardOrderDTO) {
        RefFundcardOrderDO entity = RefFundcardOrderTranslator.dto2DO(refFundcardOrderDTO);
        int result = refFundcardOrderMapper.insertSelective(entity);
        return RemoteResponse.custom().setData(result).setSuccess().build();
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public RemoteResponse updateRefFundcardOrderByOrderId(RefFundcardOrderDTO refFundcardOrderDTO) {
        Assert.notNull(refFundcardOrderDTO.getOrderId(), "orderId不能为空！");
        RefFundcardOrderDO entity = RefFundcardOrderTranslator.dto2DO(refFundcardOrderDTO);
        int result = refFundcardOrderMapper.updateByOrderId(entity);
        return RemoteResponse.custom().setData(result).setSuccess().build();
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public RemoteResponse deleteRefFundcardOrderByOrderId(String orderId) {
        int result = refFundcardOrderMapper.deleteByOrderId(orderId);
        return RemoteResponse.custom().setData(result).setSuccess().build();

    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE,description = "换卡通用方法")
    public RemoteResponse changeFundCardCommon(List<RefFundcardOrderDTO> refFundCardOrderDTOS, OrderOperatorDTO user, boolean isRefreeze , boolean callInterfaceFlag,String changeReason) {
        if (CollectionUtils.isEmpty(refFundCardOrderDTOS)) {
            throw new CallRpcException("更新经费卡失败！经费卡为空！");
        }
        
        if (user != null && user.getOrgCode() != null) {
            // 保存经费卡冻结解冻信息到预算系统
            changeAndUpdateFundCard(refFundCardOrderDTOS, user, isRefreeze,callInterfaceFlag,changeReason);
        }

        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    public List<RefFundcardOrderDTO> findByOrderId(List<String> orderIdList) {
        List<RefFundcardOrderDO> fundcardOrderDOList = refFundcardOrderMapper.findByOrderIdIn(orderIdList);
        if (CollectionUtils.isEmpty(fundcardOrderDOList)) {
            return New.emptyList();
        }

        return fundcardOrderDOList.stream().map(f -> RefFundcardOrderTranslator.doToDto(f)).collect(Collectors.toList());
    }

    /**
     * 更改订单经费卡关联关系，并且同步更新待结算业务表记录
     * @param newRefFundCardOrderDTOS 绑定的新卡数组
     */
    private List<RefFundcardOrderDO> changeAndUpdateFundCard(List<RefFundcardOrderDTO> newRefFundCardOrderDTOS, OrderOperatorDTO user, boolean isRefreeze, boolean callInterfaceFlag,String changeReason) {
        if (CollectionUtils.isEmpty(newRefFundCardOrderDTOS)) {
            return Collections.emptyList();
        }
        // 获取是否需要处理旧卡，这里保证准确性，取实时数据
        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(user.getOrgCode(), New.list(ConfigCodeEnum.OLD_DATA_PROCESS_CHANGE_CARD.name(), ConfigCodeEnum.OLD_DATA_PROCESS_TIME.name(), ConfigCodeEnum.OLD_DATA_PROCESS_BUSINESS_SCOPE.name(), ConfigCodeEnum.OLD_DATA_PROCESS_OPEN.name()));
        OldDateConfigBO oldDateConfigBO = OldDateConfigBO.getInstance(user.getOrgCode(), configList, true);

        final List<String> cardIds = newRefFundCardOrderDTOS.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        final List<FundCardDTO> allCardInfo = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(user.getOrgCode(), cardIds);
        BusinessErrUtil.notEmpty(allCardInfo, ExecptionMessageEnum.NO_CARD_FOUND, cardIds);
        // 直接拿到最末级的
        List<FundCardDTO> currentLevelCards = researchFundCardServiceClient.getFundCardListByCardIds(cardIds, user.getOrgCode());

        // 设置经费负责人姓名
        user.setFundManagerName(Optional.ofNullable(allCardInfo.get(0)).map(FundCardDTO::getFundCardManagerDTOs).map(manager -> manager.get(0)).map(FundCardManagerDTO::getManagerName).orElse(StringUtils.EMPTY));
        // 是否为发起结算才冻结解冻的单位
        boolean isSubmitStatementFreeze = this.isSubmitStatementFreeze(user.getOrgCode());
        // 非对接单，但仍需修改绑卡记录的单
        List<Integer> notFundDockingOrderIdList = this.getNotFundDockingOrderIdList(user.getOrgCode(), newRefFundCardOrderDTOS);
        // 非对接卡，但仍需修改绑卡记录的卡
        List<String> notFundDockingCardIdList = this.getNotFundDockingCardIdList(user.getOrgCode(), currentLevelCards, FundCardUtils.getAllLevelCardIdCardMap(allCardInfo));

        // 经费卡关联关系对象，插入t_ref_fundcard_order中
        List<RefFundcardOrderDO> refFundCardOrderList = new ArrayList<>(newRefFundCardOrderDTOS.size());
        // 订单 id 对应经费卡字典, string -> string, 后面再 orderId 再转 Integer
        Map<String, String> orderRefFundCardMap = new HashMap<>(newRefFundCardOrderDTOS.size());
        
        // 是否调用冻结而非换卡？ 判断条件：重新冻结或没有待结算换卡接口的单位
        boolean callFreeze = isRefreeze || ORG_LIST_CHANGE_CARD_WITHOUT_CHANGE_CARD_IMPL.contains(user.getOrgCode());
        // 调用换卡失败的状态。根据是否调用冻结接口特殊处理
        OrderFundStatusEnum callChangeCardFailStatus = callFreeze ? OrderFundStatusEnum.FreezedFail : OrderFundStatusEnum.ChangedCardFail;
        
        
        // 1.初始化 绑卡数据DO 和 订单id-对应经费卡字典
        for (RefFundcardOrderDTO fundCardOrderDTO : newRefFundCardOrderDTOS) {
            if (orderRefFundCardMap.containsKey(fundCardOrderDTO.getOrderId())) {
                String fundCardNo = orderRefFundCardMap.get(fundCardOrderDTO.getOrderId());
                orderRefFundCardMap.put(fundCardOrderDTO.getOrderId(), fundCardNo + ";" + fundCardOrderDTO.getCardNo());
            } else {
                orderRefFundCardMap.put(fundCardOrderDTO.getOrderId(), fundCardOrderDTO.getCardNo());
            }

            // 中肿旧的经费属性为null时，要适配新预算系统的新的属性
            if (isSubmitStatementFreeze && fundCardOrderDTO.getFundType() == null) {
                fundCardOrderDTO.setFundType(FundTypeEnum.NULL.getValue());
            }

            RefFundcardOrderDO entity = RefFundcardOrderTranslator.dto2DO(fundCardOrderDTO);
            entity.setId(UUID.randomUUID().toString());
            refFundCardOrderList.add(entity);
        }
        // 调用换卡接口的经费卡，过滤掉非对接单和非对接卡
        List<RefFundcardOrderDTO> callChangeCardImplFundCardList = newRefFundCardOrderDTOS.stream()
                .filter(item-> !(notFundDockingOrderIdList.contains(Integer.parseInt(item.getOrderId())) || notFundDockingCardIdList.contains(RefFundcardOrderTranslator.getLastLevelCardId(item)))).collect(Collectors.toList());

        List<Integer> orderIdList = newRefFundCardOrderDTOS.stream().map(RefFundcardOrderDTO::getOrderId).map(Integer::parseInt).collect(Collectors.toList());
        List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(orderIdList);
        if(oldDateConfigBO != null){
            // 配置了需要删除旧单，且有配置旧单时间，则先返还政采目录，再删除旧卡
            List<Integer> needDeleteOldFundCardOrderIdList = orderList.stream()
                    .filter(orderMasterDO -> oldDateConfigBO.judgeOldCardNeedDel(orderMasterDO.getForderdate(), orderMasterDO.getFundStatus(), orderMasterDO.getSpecies().intValue()) )
                    .map(OrderMasterDO::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(needDeleteOldFundCardOrderIdList)){
                categoryDirectoryClient.cancelOrderStatistics(orderList.get(0).getFuserid(), needDeleteOldFundCardOrderIdList, null);
                refFundcardOrderMapper.deleteByOrderIdIn(needDeleteOldFundCardOrderIdList.stream().map(Objects::toString).collect(Collectors.toList()));
            }
        }
        List<OrderDetailDO> detailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        List<RefFundcardOrderDO> oldCardList = refFundcardOrderMapper.findByOrderIdIn(orderIdList.stream().map(Object::toString).collect(Collectors.toList()));

        boolean isCategoryDirectoryOn = categoryDirectoryClient.isCategoryDirectoryOn(user.getOrgCode(), true, true);

        // 这里按了线上线下单做了分组，分批做处理（防止线上线下单一同换卡，而线上调用冻结解冻，线下要调用的情况，这种情况下线上会回调，线下不会）
        Map<Byte, List<OrderMasterDO>> speciesOrderListMap = DictionaryUtils.groupBy(orderList, OrderMasterDO::getSpecies);
        for (Map.Entry<Byte, List<OrderMasterDO>> entry : speciesOrderListMap.entrySet()) {
            // 是否依赖回调判断成功
            boolean successDependOnCallback = true;
            SaveCardResult saveCardResult = null;

            List<OrderMasterDO> matchOrderList = entry.getValue();
            List<Integer> matchOrderIdList = matchOrderList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
            List<String> matchOrderIdStrList = matchOrderList.stream().map(orderMasterDO -> orderMasterDO.getId().toString()).collect(Collectors.toList());
            List<RefFundcardOrderDTO> matchNewCardList = newRefFundCardOrderDTOS.stream().filter(card->matchOrderIdStrList.contains(card.getOrderId())).collect(Collectors.toList());
            List<RefFundcardOrderDO> matchOldCardList = oldCardList.stream().filter(card->matchOrderIdStrList.contains(card.getOrderId())).collect(Collectors.toList());

            // 2.如果开启了政采目录配置，则需要校验并管控
            if(isCategoryDirectoryOn){
                // 如果开启了政采目录限额
                // 校验新卡的政采目录余额是否充足
                categoryDirectoryClient.checkPurchaseLimitForDetails(matchOrderList, detailList, matchNewCardList);
                // 不管是冻结还是换卡，都是先调用新卡的统计接口
                categoryDirectoryClient.saveOverStatistics(matchOrderList, detailList, matchNewCardList, user.getUserId());
            }

            // 3.调用科研的换卡接口
            // 中肿, 眼科, 浙江肿瘤没有换卡业务，其他单位需要保存新卡冻结对象，旧卡解冻到预算系统。
            // 有需要调用换卡的卡才去调用
            if (CollectionUtils.isNotEmpty(callChangeCardImplFundCardList) && !isSubmitStatementFreeze && callInterfaceFlag) {
                List<RefFundcardOrderDTO> matchCallChangeFundCardList = callChangeCardImplFundCardList
                        .stream().filter(card->matchOrderIdStrList.contains(card.getOrderId())).collect(Collectors.toList());
                List<UpdateOrderParamDTO> orderUpdatedList = matchCallChangeFundCardList.stream().map(ref -> {
                    UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
                    updated.setOrderId(Integer.parseInt(ref.getOrderId()));
                    updated.setFundStatus(OrderFundStatusEnum.ChangingCard.getValue());
                    return updated;
                }).collect(Collectors.toList());

                orderMasterMapper.updateFieldByIdIn(orderUpdatedList);

                try {
                    saveCardResult = this.changeFundCard(matchOrderList, detailList, matchCallChangeFundCardList, user, callFreeze, isCategoryDirectoryOn, changeReason);
                    Integer handleResult = saveCardResult.getFundCardResultDTO().getHandleResult();
                    Integer fundStatus = null;
                    if (HandleResultEnum.UN_HANDLE.getCode().equals(handleResult)) {
                        // 处理历史订单，未对接预算系统又调了换卡的订单叫无需解冻状态
                        fundStatus = OrderFundStatusEnum.UN_FREEZE.value;
                        successDependOnCallback = false;
                    } else if (FundCardAsyCallBackEnum.NO_NEED.getValue().equals(saveCardResult.getFundCardResultDTO().getRelyAsyCallback())) {
                        // 是否依赖回调结果，依赖回调则将经费状态改为换卡中，否则经费状态为冻结成功
                        fundStatus = OrderFundStatusEnum.Freezed.value;
                        successDependOnCallback = false;
                    }
                    if (fundStatus != null) {
                        Integer finalFundStatus = fundStatus;
                        orderUpdatedList.forEach(updated -> updated.setFundStatus(finalFundStatus));
                        // 更新订单的经费状态
                        orderMasterMapper.updateFieldByIdIn(orderUpdatedList);
                    }
                } catch (Exception e) {
                    orderUpdatedList.forEach(updated -> {
                        updated.setFundStatus(callChangeCardFailStatus.getValue());
                        updated.setFailedReason(e.getMessage());
                    });
                    orderMasterMapper.updateFieldByIdIn(orderUpdatedList);
                    if (isCategoryDirectoryOn) {
                        // 如果前面做过管控，这里需要做回滚。
                        if (customSelectFundCard(matchOrderList) || CollectionUtils.isEmpty(matchOldCardList) || callFreeze) {
                            // 条件copy from 判断冻结，如果是走冻结接口的，oldCard置空做回滚
                            matchOldCardList = null;
                        }
                        categoryDirectoryClient.rollbackSaveStatisticsWhenFail(matchOrderList, detailList, matchOldCardList, matchNewCardList, user.getUserId());
                    }
                    throw new IllegalStateException(e);
                }
            }

            boolean isChangeCard = saveCardResult != null && saveCardResult.getSaveMode() == SaveCardResult.CHANGE_CARD;
            // 只有换卡异步操作需要等待回调成功才进行经费卡更新，其他的时候直接写入新的绑卡数据
            if(!(successDependOnCallback && isChangeCard)){
                // 3.修改绑卡数据
                List<RefFundcardOrderDO> matchCardRecordList = refFundCardOrderList.stream().filter(record->matchOrderIdStrList.contains(record.getOrderId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(refFundCardOrderList)) {
                    // 删除旧的绑卡记录
                    refFundcardOrderMapper.deleteByOrderIdIn(matchOrderIdStrList);
                    // 插入新的绑卡记录
                    refFundcardOrderMapper.insertList(matchCardRecordList);
                    // 删除及插入记录到新的经费卡关联表中
                    refFundCardOrderClient.deleteInOrderId(orderIdList);
                    refFundCardOrderClient.batchInsertSelective(newRefFundCardOrderDTOS.stream().map(RefFundcardOrderTranslator::fromDTO).collect(toList()));
                }
                // 4.更新新待结算单的经费卡信息
                Map<String, String> matchOrderFundMap = new HashMap<>(matchOrderList.size());
                matchOrderIdStrList.forEach(orderIdStr->matchOrderFundMap.put(orderIdStr, orderRefFundCardMap.get(orderIdStr)));
                updateNewWaitingStatement(matchOrderFundMap);
            }
            if(!successDependOnCallback && isChangeCard){
                // 不需要回调且为换卡，即换卡已经成功，需要释放旧卡的政采管控
                categoryDirectoryClient.cancelOrderStatistics(orderList.get(0).getFuserid(), matchOrderIdList, matchOldCardList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList()));
                // 记录换卡成功日志
                matchOrderList.forEach(orderMasterDO -> {
                    // 获取该订单的新旧卡 拼接
                    String oldValue = oldCardList.stream()
                            .filter(card -> String.valueOf(orderMasterDO.getId()).equals(card.getOrderId()))
                            .map(RefFundcardOrderDO::getCardNo)
                            .collect(Collectors.joining("，"));

                    String newValue = matchNewCardList.stream()
                            .filter(card -> String.valueOf(orderMasterDO.getId()).equals(card.getOrderId()))
                            .map(RefFundcardOrderDTO::getCardNo)
                            .collect(Collectors.joining("，"));

                    OrderApprovalEnum approvalEnum= OrderStatusEnum.Statementing_1.getValue().equals(orderMasterDO.getStatus())?
                            OrderApprovalEnum.SETTLEMENT_CHANGE_FUND_CARD_SUCCESS : OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS;
                    saveChangeCardLog(orderMasterDO.getId(), approvalEnum, user, changeReason, newValue, oldValue, StringUtils.EMPTY);
                });
            }
        }
        return refFundCardOrderList;
    }

    /**
     * 记录换卡日志
     * @param orderId 订单ID
     * @param approvalEnum 日志枚举
     * @param user 操作用户
     * @param changeReason 换卡原因
     * @param newValue 新卡号
     * @param oldValue 旧卡号
     */
    private void saveChangeCardLog(Integer orderId, OrderApprovalEnum approvalEnum, OrderOperatorDTO user,
                                   String changeReason, String newValue, String oldValue,String failReason) {
        // 构建备注信息
        String remark;
        if (OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS.equals(approvalEnum)
        || OrderApprovalEnum.SETTLEMENT_CHANGE_FUND_CARD_SUCCESS.equals(approvalEnum)) {
            // 换卡成功的情况
            remark = StrUtil.format("原值为“{}”，新值为“{}”", oldValue, newValue);
            if (StringUtils.isNotBlank(changeReason)) {
                remark = StrUtil.format("原因：{}，{}", changeReason, remark);
            }
        } else {
            // 换卡失败的情况
            remark = StrUtil.format("{}，失败原因：{}", approvalEnum.getName(), failReason);
        }
        // 调用日志服务记录
        orderApprovalLogService.createOrderOperateLog(orderId, approvalEnum.getValue(), user.getUserId(), user.getUserName(), remark);
    }



    /**
     * 判断是否发起结算才冻结经费的单位
     * @param orgCode
     * @return
     */
    private boolean isSubmitStatementFreeze(String orgCode) {
        return OrderStatementConstant.SUBMIT_STATEMENT_FREEZE_COLLECT.contains(orgCode);
    }

    /**
     * 获取非经费对接的卡
     *
     * @param orgCode     单位代码
     * @param currentLevelCards 经费卡数据
     * @return 不需要调用冻结的卡id
     */
    private List<String> getNotFundDockingCardIdList(String orgCode, List<FundCardDTO> currentLevelCards, Map<String, FundCardDTO> allLevelCardIdCardMap) {
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode)) {
            Set<String> notFundDockingCardIdList = New.set();
            for(FundCardDTO card : currentLevelCards){
                FundCardDTO temp = card;
                FundCardDTO projectCard = temp;
                while (temp != null){
                    // 找到最上级的那张卡
                    projectCard = temp;
                    temp = allLevelCardIdCardMap.get(temp.getParentId());
                }
                if(projectCard != null && projectCard.getCode() != null && projectCard.getCode().startsWith("Q")){
                    notFundDockingCardIdList.add(card.getId());
                }
            }
            Set<String> notFinancialCardIds = currentLevelCards.stream().filter(card -> FundTypeEnum.NOT_FINANCIAL.getValue() == card.getFundType()).map(FundCardDTO::getId).collect(Collectors.toSet());
            notFundDockingCardIdList.addAll(notFinancialCardIds);
            // 中大附一特殊处理，非对接卡不冻结经费
            return New.list(notFundDockingCardIdList);
        }
        return New.emptyList();
    }

    /**
     * 判断是否非经费对接的订单
     *
     * @param orgCode                 单位
     * @param newRefFundCardOrderDTOS 要更换的绑卡数据
     * @return 获取不进行经费冻结的订单id
     */
    private List<Integer> getNotFundDockingOrderIdList(String orgCode, List<RefFundcardOrderDTO> newRefFundCardOrderDTOS) {
        List<Integer> orderIdList = newRefFundCardOrderDTOS.stream().map(RefFundcardOrderDTO::getOrderId).distinct().map(Integer::valueOf).collect(Collectors.toList());
        Set<Integer> notFreezeOrderIdSet = New.set();
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode)) {
            // 中大附一特殊处理，服务类(除生物医学服务/动物实验服务/化工技术服务)，不冻结经费
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
            Map<Integer, List<OrderDetailDO>> orderIdDetailMap = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFmasterid);
            for(Map.Entry<Integer, List<OrderDetailDO>> entry : orderIdDetailMap.entrySet()){
                // 只要包含非特殊分类的服务类，就不允许冻结
                boolean containsNormalService = orderDetailDOList.stream()
                        .anyMatch(d -> {
                            boolean isService = CategoryConstant.SCIENCE_SERVICE_ID.equals(d.getFirstCategoryId());
                            boolean isSpecialService = CategoryConstant.BIO_SCI_SERVICE_ID.equals(d.getSecondCategoryId())
                                    || CategoryConstant.CHEMICAL_TECH_SERVICE_ID.equals(d.getSecondCategoryId())
                                    || CategoryConstant.ANIMAL_EXPERIMENT_SERVICE_ID.equals(d.getSecondCategoryId());
                            return isService && !isSpecialService;
                        });
                if(containsNormalService){
                    notFreezeOrderIdSet.add(entry.getKey());
                }
            }
            notFreezeOrderIdSet.addAll(orderIdList.stream().filter(DockingConstant.NOT_FREEZE_ORDER_ID_LIST::contains).collect(Collectors.toList()));
        }
//        // 不对接财务系统的单，为非对接单，不实际调用财务接口
//        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(orderIdList, OrderExtraEnum.STATEMENT_WAY_ENABLE_FUND_DOCKING.getValue());
//        notFreezeOrderIdSet.addAll(orderExtraDTOList.stream().filter(orderExtra->CommonValueUtils.FALSE_NUMBER_STR.equals(orderExtra.getExtraValue()))
//                .map(BaseOrderExtraDTO::getOrderId).collect(Collectors.toList()));
        return New.list(notFreezeOrderIdSet);
    }

    /**
     * 更新 新待结算单的经费卡信息
     * @param orderRefFundCardMap
     */
    private void updateNewWaitingStatement(Map<String, String> orderRefFundCardMap) {
        // 更新待结算单列表
        List<WaitingStatementOrderRequestDTO> statementOrderRequestDTOList = new ArrayList<>(orderRefFundCardMap.size());
        WaitingStatementOrderRequestDTO statementRequest = null;
        for (Map.Entry<String, String> entry : orderRefFundCardMap.entrySet()) {
            statementRequest = new WaitingStatementOrderRequestDTO();
            statementRequest.setOrderId(NumberUtils.toInt(entry.getKey(), 0));
            statementRequest.setFundCards(entry.getValue());

            statementOrderRequestDTOList.add(statementRequest);
        }
        if (CollectionUtils.isNotEmpty(statementOrderRequestDTOList)) {
            statementPlatformClient.updateWaitingStatement(statementOrderRequestDTOList);
        }
    }

    /**
     * 保存新卡冻结对象，旧卡解冻到预算系统
     * @param newRefFundCardOrderDTOS 绑定的新卡对象
     * @param user                    操作人信息
     * @param callFreeze 是否调用冻结
     */
    private SaveCardResult changeFundCard(List<OrderMasterDO> orderList, List<OrderDetailDO> detailList, List<RefFundcardOrderDTO> newRefFundCardOrderDTOS, OrderOperatorDTO user, boolean callFreeze, boolean isCategoryDirectoryOn, String changeReason) {
        Set<String> orderIdCollection = newRefFundCardOrderDTOS.stream().map(RefFundcardOrderDTO::getOrderId).collect(Collectors.toSet());
        // 旧的经费卡绑定对象数组
        List<RefFundcardOrderDTO> oldRefFundCardList = refFundcardOrderMapper.findByOrderIdIn(orderIdCollection).stream().map(RefFundcardOrderTranslator::doToDto).collect(toList());
        // 填充经费支出申请单号
        if(callFreeze) {
            // 如果是重新冻结，入参中不会有经费支出申请单号信息，需要把旧单的经费支出申请单号填充到新单中
            List<RefFundcardOrderDTO> tempRefFundcardOrderDTOList = new ArrayList<>(oldRefFundCardList);
            oldRefFundCardList.addAll(newRefFundCardOrderDTOS);
            fillWithNewDTO(tempRefFundcardOrderDTOList);
        }else {
            fillWithNewDTO(oldRefFundCardList);
        }

        // 订单id -> 经费卡 map
        Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap = newRefFundCardOrderDTOS.stream().collect(Collectors.groupingBy(r -> Integer.parseInt(r.getOrderId())));
        Map<Integer, List<RefFundcardOrderDTO>> oldOrderIdFundCardMap = oldRefFundCardList.stream().collect(Collectors.groupingBy(r -> Integer.parseInt(r.getOrderId())));
        BusinessErrUtil.notEmpty(orderList, ExecptionMessageEnum.CARD_REPLACEMENT_FAILED_NO_ORDER_INFO);
        final List<String> orderNos = orderList.stream().map(OrderMasterDO::getForderno).collect(Collectors.toList());
        final List<DockingExtraDTO> dockings = dockingExtraService.findDockingExtraByInfo(orderNos);
        final Map<String, String> orderNoDockingNoMapper = dockings.stream()
                .filter(dockingExtraDTO -> dockingExtraDTO.getExtraInfo() != null)
                .collect(Collectors.toMap(DockingExtraDTO::getInfo, DockingExtraDTO::getExtraInfo, (oldValue, newValue) -> newValue));

        String orgCode = user.getOrgCode();
        // 获取经费卡是否对接
        String dockingFundCardConfig = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.RESEARCH_FUNDCARD_ISINTERFACE);
        boolean isDockingFundCard = ConfigConstant.FUND_CARD_DOCKING_VAL.equals(dockingFundCardConfig);
        Date nowDate = new Date();

        List<Integer> departmentIdList = orderList.stream().map(OrderMasterDO::getFbuydepartmentid).collect(Collectors.toList());

        // 无绑卡记录，是待结算环节选卡，使用预算冻结接口。或指定使用重新冻结接口，则调用冻结
        if (customSelectFundCard(orderList) || CollectionUtils.isEmpty(oldRefFundCardList) || callFreeze) {
            FreezeDTO parentFreezeDTO = parsingRefToFreezeDto(user, orderIdFundCardMap, nowDate, orderList.get(0), detailList, isDockingFundCard);
            OrgRequest<FreezeDTO> request = new OrgRequest<>();
            // 多张单换卡时，才需要setFreezeDTOs。API接口设计如此。。
            if (orderList.size() > 1) {
                List<FreezeDTO> freezeOrderList = new ArrayList<>(orderList.size());
                for (OrderMasterDO order : orderList) {
                    FreezeDTO freezeDTO = parsingRefToFreezeDto(user, orderIdFundCardMap, nowDate, order, detailList, isDockingFundCard);
                    freezeOrderList.add(freezeDTO);
                }
                parentFreezeDTO.setFreezeDTOs(freezeOrderList);
            }
            request.setOrgCode(orgCode);
            // 绑卡的时候需要加入部门id，支持多张单换卡的部门暂时不需要这个字段（后续如果要可能要修改这个方法）
            request.setDepartmentIds(departmentIdList);

            UserDTO userInfo = new UserDTO();
            userInfo.setUserId(user.getUserId());
            userInfo.setJobNumber(user.getJobNumber());
            userInfo.setRealName(user.getUserName());
            request.setUserDTO(userInfo);
            request.setData(parentFreezeDTO);
            parentFreezeDTO.getExtraDTOs().add(new ExtraDTO("isCategoryDirectoryOn", CommonValueUtils.parseBoolean2NumberStr(isCategoryDirectoryOn)));
            // 批量冻结经费卡
            return new SaveCardResult(researchFundCardServiceClient.fundCardFreezeBatch(request), SaveCardResult.FREEZE_CARD);
        } else {
            // 有绑卡记录，是待结算换卡，使用预算换卡接口
            ChangeFundCardDTO request = new ChangeFundCardDTO();
            // 最外层的换卡模型塞第一张单的数据，单张订单这么传。API接口设计如此。。
            request.setAppKey(Environment.getAppKey());
            OrderMasterDO firstOrder = orderList.get(0);
            request.setSerialNumber(firstOrder.getForderno());
            request.setSourceType(SourceTypeEnum.ORDER.getValue());
            request.setBusinessType(BusinessTypeEnum.BUY.getValue());
            request.setUserId(user.getUserId());
            request.setFreezeDTO(parsingRefToFreezeDto(user, orderIdFundCardMap, nowDate, firstOrder, detailList, isDockingFundCard));
            request.setUnfreezeDTO(parsingRefToUnFreezeDto(user, oldOrderIdFundCardMap, nowDate, firstOrder, orderNoDockingNoMapper, isDockingFundCard, detailList));

            // 多张单换卡时，才需要setChangeFundCardDTOs。API接口设计如此。。
            if (orderList.size() > 1) {
                List<ChangeFundCardDTO> changeFundCardList = new ArrayList<>(newRefFundCardOrderDTOS.size() + oldRefFundCardList.size());
                for (OrderMasterDO order : orderList) {
                    AtomicReference<ChangeFundCardDTO> changeFundCardItem = new AtomicReference<>();
                    changeFundCardItem.set(new ChangeFundCardDTO());
                    // 新的经费卡生成新的冻结对象
                    newRefFundCardOrderDTOS.forEach(newRef -> {
                        FreezeDTO freezeDTO = parsingRefToFreezeDto(user, orderIdFundCardMap, nowDate, order, detailList, isDockingFundCard);
                        changeFundCardItem.get().setFreezeDTO(freezeDTO);
                        UnfreezeDTO unfreezeDTO = parsingRefToUnFreezeDto(user, oldOrderIdFundCardMap, nowDate, order, orderNoDockingNoMapper, isDockingFundCard, detailList);
                        changeFundCardItem.get().setUnfreezeDTO(unfreezeDTO);
                    });

                    changeFundCardItem.get().setAppKey(Environment.getAppKey());
                    changeFundCardItem.get().setSerialNumber(order.getForderno());
                    changeFundCardItem.get().setSourceType(SourceTypeEnum.ORDER.getValue());
                    changeFundCardItem.get().setBusinessType(BusinessTypeEnum.BUY.getValue());
                    changeFundCardItem.get().setUserId(user.getUserId());

                    changeFundCardList.add(changeFundCardItem.get());
                }
                request.setChangeFundCardDTOs(changeFundCardList);
            }
            // 这里将冻结时的参数传给TPI，让他回调的时候返回给我们
            request.setExtraDTOs(New.list(new ExtraDTO("newCards", JsonUtils.toJson(newRefFundCardOrderDTOS)),
                    new ExtraDTO("operatorUserId", user.getUserId().toString()),
                    new ExtraDTO("isCategoryDirectoryOn", CommonValueUtils.parseBoolean2NumberStr(isCategoryDirectoryOn))
            ));
            if (StringUtils.isNotBlank(changeReason)) {
                request.getExtraDTOs().add(new ExtraDTO("changeReason", changeReason));
            }

            // 预算系统换卡接口
            return new SaveCardResult(researchFundCardServiceClient.changeFundCard(orgCode, request, departmentIdList), SaveCardResult.CHANGE_CARD);
        }
    }

    /**
     * 待结算选卡定制化的单位
     * @param orderList 订单
     * @return          是否可以选卡
     */
    private boolean customSelectFundCard(List<OrderMasterDO> orderList) {
        String orgCode = orderList.get(0).getFusercode();
        // 待结算选卡定制化的单位
        Set<String> customOrgCodes = New.set(
            OrgEnum.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN.getCode(),
            OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode(),
            OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getCode()
        );
        boolean customSelect = customOrgCodes.contains(orgCode);
        if (customSelect) {
            List<Integer> orderIdList = orderList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
            List<OrderDetailDO> orderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
            Map<Integer, List<OrderDetailDO>> orderIdDetailListMap = DictionaryUtils.groupBy(orderDetailList, OrderDetailDO::getFmasterid);
            // 只有浙江肿瘤的旧待结算单, 且无退货中的单可以选卡
            orderList.forEach(o -> {
                if (OrgEnum.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN.getCode().equals(orgCode)){
                    BusinessErrUtil.isTrue(o.getForderdate().before(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT
                            , OrderDateConstant.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN)), ExecptionMessageEnum.ORDER_FROZEN_CANNOT_SELECT_CARD, o.getForderno());
                }
                List<OrderDetailDO> detailList = orderIdDetailListMap.get(o.getId());
                boolean havingReturn = detailList.stream().anyMatch(OrderCommonUtils::havingReturn);
                BusinessErrUtil.isTrue(!havingReturn, ExecptionMessageEnum.ORDER_RETURN_IN_PROGRESS_CANNOT_SELECT_CARD, o.getForderno());
            });
            // 除浙江省中医院外，还需要校验是否经费卡表有数据来判定是否进行换卡
            return OrgEnum.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN.getCode().equals(orgCode) || OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getCode().equals(orgCode);
        }
        return false;
    }

    /**
     * refFundCard 绑卡对象 转换为冻结对象
     * @param user                  用户信息
     * @param orderIdFundCardMap    订单id -> 绑卡记录 字典
     * @param nowDate               当前时间
     * @param order                 订单对象
     * @return                      冻结订单对象
     */
    public static FreezeDTO parsingRefToFreezeDto(OrderOperatorDTO user, Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap, Date nowDate,
                                                  OrderMasterDO order, List<OrderDetailDO> detailList) {
        return parsingRefToFreezeDto(user,  orderIdFundCardMap, nowDate,
                order, detailList, true);
    }

    /**
     * refFundCard 绑卡对象 转换为冻结对象
     * @param user                  用户信息
     * @param orderIdFundCardMap    订单id -> 绑卡记录 字典
     * @param nowDate               当前时间
     * @param order                 订单对象
     * @return                      冻结订单对象
     */
    public static FreezeDTO parsingRefToFreezeDto(OrderOperatorDTO user, Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap, Date nowDate,
                                                      OrderMasterDO order, List<OrderDetailDO> detailList, boolean isDockingFundCard) {
        FreezeDTO freezeDTO = new FreezeDTO();
        freezeDTO.setSerialNumber(order.getForderno());
        // 采购业务，需要维护枚举
        freezeDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
        // 最终支付金额
        BigDecimal payTotalMoney = order.getForderamounttotal().subtract(BigDecimal.valueOf(order.getReturnAmount()));
        freezeDTO.setFreezeAmount(payTotalMoney);
        freezeDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        freezeDTO.setBuyerUserId(order.getFbuyerid());
        freezeDTO.setUserId(user.getUserId());
        Date oprDate = DockingConstant.CUSTOM_FREEZE_TIME_ORDER_LIST.contains(order.getId()) ? order.getForderdate() : nowDate;
        freezeDTO.setOperateDate(oprDate);
        freezeDTO.setAppKey(Environment.getAppKey());
        freezeDTO.setOperatorName(user.getUserName());
        freezeDTO.setOperatorJobNumber(user.getJobNumber());
        freezeDTO.setOperatorTelephone(user.getTelephone());
        freezeDTO.setCallTpiClient(customClient(order));
        freezeDTO.setDepartmentName(order.getFbuydepartment());
        freezeDTO.setSpecies(order.getSpecies().intValue());
        freezeDTO.setManagerName(user.getFundManagerName());
        freezeDTO.setData(customData(order, detailList));

        List<RefFundcardOrderDTO> curFundCardList = orderIdFundCardMap.get(order.getId());
        if (CollectionUtils.isNotEmpty(curFundCardList)) {
            // 冻结的新卡信息
            List<SelectFundCardDTO> selectFundCardDTOS = new ArrayList<>(curFundCardList.size());
            for (RefFundcardOrderDTO fundCard : curFundCardList) {
                // 对接的，暂时不改他，还是取回总额。非对接的取传入的每张卡的冻结金额
                BigDecimal fundCardFreezeMoney = isDockingFundCard ? payTotalMoney : fundCard.getFreezeAmount();
                selectFundCardDTOS.add(RefFundcardOrderTranslator.refToSelectFundCardDto(fundCard, fundCardFreezeMoney));
            }
            freezeDTO.setSelectFundCardDTOS(selectFundCardDTOS);
        }
        // 中山八院冻结需要传供应商和商品信息
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_BA_YI_YUAN.getCode().equals(user.getOrgCode())){
            SupplierDTO supplierDTO = new SupplierDTO();
            supplierDTO.setSupplierCode(order.getFsuppcode());
            supplierDTO.setSupplierName(order.getFsuppname());
            freezeDTO.setSupplierDTO(supplierDTO);
        }
        // 中山六院
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getCode().equals(user.getOrgCode())) {
            freezeDTO.setOperatorTelephone(user.getTelephone());
            // 补充银行账户信息
            SuppClient suppClient = SpringUtil.getBean(SuppClient.class);
            List<BankAccountDTO> bankAccountDTOList = suppClient.listBankAccountBySuppIds(New.list(order.getFsuppid()));
            if (CollectionUtils.isNotEmpty(bankAccountDTOList)) {
                BankAccountDTO bankAccount = bankAccountDTOList.get(0);
                SupplierBankDTO supplierBankDTO = new SupplierBankDTO();
                supplierBankDTO.setBankNumber(bankAccount.getBankNumber());
                supplierBankDTO.setBankBranchName(bankAccount.getBankBranch());
                supplierBankDTO.setBankAccount(bankAccount.getBankAccount());
                SupplierDTO supplierDTO = new SupplierDTO();
                supplierDTO.setSupplierBankDTO(supplierBankDTO);
                freezeDTO.setSupplierDTO(supplierDTO);
            }
            // 补充课题组负责人信息
            UserClient userClientBean = SpringUtil.getBean(UserClient.class);
            UserBaseInfoDTO departmentManager = userClientBean.getDepartmentManager(order.getFuserid(), order.getFbuydepartmentid());
            if (Objects.nonNull(departmentManager)) {
                freezeDTO.setManagerJobNumber(departmentManager.getJobnumber());
            }
        }
        freezeDTO.setOrderDetailDTOs(OrderDetailTranslator.orderDetail2FundCardOrderDetail(detailList));
        List<ExtraDTO> extraDTOList = new ArrayList<>();
        freezeDTO.setExtraDTOs(extraDTOList);
        extraDTOList.add(new ExtraDTO("orderDate", DateUtils.format("yyyy-MM-dd", order.getForderdate())));
        extraDTOList.add(new ExtraDTO("supplierName", order.getFsuppname()));
        Date newFinancialDate = OrderDateConstant.ORG_CODE_NEW_FINANCIAL_DATE_MAP.get(order.getFusercode());
        if(newFinancialDate != null){
            // 如果是存在新旧财务对接的，根据新旧财务对接事件设置isNew作为标记传给TPI
            extraDTOList.add(new ExtraDTO("isNew", order.getForderdate().after(newFinancialDate) ? "1" : "0"));
        }
        if(OrgEnum.SHEN_ZHEN_YI_XUE_KE_XUE_YUAN.getCode().equals(user.getOrgCode())){
            extraDTOList.add(new ExtraDTO("freight", order.getCarryFee().toString()));
        }
        // 广东药科
        if(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode().equals(user.getOrgCode())){
            // 补充课题组负责人信息
            UserClient userClientBean = SpringUtil.getBean(UserClient.class);
            UserBaseInfoDTO departmentManager = userClientBean.getDepartmentManager(order.getFuserid(), order.getFbuydepartmentid());
            if (Objects.nonNull(departmentManager)) {
                freezeDTO.setManagerJobNumber(departmentManager.getJobnumber());
            }
        }
        return freezeDTO;
    }

    /**
     * 定制化换卡冻结对象的 data
     * @param order
     * @return
     */
    private static Object customData(OrderMasterDO order, List<OrderDetailDO> detailList) {
        if (null == detailList) {
            return null;
        }
        return detailList.stream()
                .filter(d -> d.getFmasterid().equals(order.getId()))
                .map(it -> {
                    OrderDetailDTO dto = new OrderDetailDTO();
                    dto.setGoodsName(it.getFgoodname());
                    dto.setPrice(it.getFbidprice());
                    dto.setQuantity(it.getFquantity());
                    dto.setGoodsCode(it.getFgoodcode());
                    return dto;
                }).collect(Collectors.toList());
    }

    /**
     * 旧单, 换卡是否调财务系统
     * @param order
     * @return
     */
    private static Boolean customClient(OrderMasterDO order) {
        if (OrgEnum.NAN_FANG_YI_KE_DA_XUE_ZHU_JIANG_YI_YUAN.getCode().equals(order.getFusercode())) {
            return false;
        }
        // 是否对接财务
        return null;
    }

    /**
     * refFundCard 绑卡对象 转换为解冻对象
     * @param user                  用户信息
     * @param orderIdFundCardMap    订单id -> 绑卡记录 字典
     * @param nowDate               当前时间
     * @param order                 订单对象
     * @return                      解冻订单对象
     */
    public static UnfreezeDTO parsingRefToUnFreezeDto(OrderOperatorDTO user, Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap, Date nowDate, OrderMasterDO order, Map<String, String> orderNoDockingNoMapper, List<OrderDetailDO> detailList) {
        return parsingRefToUnFreezeDto(user, orderIdFundCardMap, nowDate, order, orderNoDockingNoMapper, true, detailList);
    }

    /**
     * refFundCard 绑卡对象 转换为解冻对象
     * @param user                  用户信息
     * @param orderIdFundCardMap    订单id -> 绑卡记录 字典
     * @param nowDate               当前时间
     * @param order                 订单对象
     * @return                      解冻订单对象
     */
    public static UnfreezeDTO parsingRefToUnFreezeDto(OrderOperatorDTO user, Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap, Date nowDate, OrderMasterDO order, Map<String, String> orderNoDockingNoMapper, boolean isDockingFundCard, List<OrderDetailDO> detailList) {
        UnfreezeDTO unfreezeDTO = new UnfreezeDTO();
        unfreezeDTO.setSerialNumber(order.getForderno());
        // 采购业务，需要维护枚举
        unfreezeDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
        // 最终支付金额
        BigDecimal payTotalMoney = order.getForderamounttotal().subtract(new BigDecimal(order.getReturnAmount()));
        unfreezeDTO.setFreezeAmount(payTotalMoney);
        unfreezeDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        unfreezeDTO.setBuyerUserId(order.getFbuyerid());
        unfreezeDTO.setUserId(user.getUserId());
        unfreezeDTO.setOperateDate(nowDate);
        unfreezeDTO.setAppKey(Environment.getAppKey());
        unfreezeDTO.setOperatorName(user.getUserName());
        unfreezeDTO.setOperatorJobNumber(user.getJobNumber());
        unfreezeDTO.setManagerName(user.getFundManagerName());
        unfreezeDTO.setExtraSerialNumber(orderNoDockingNoMapper.get(order.getForderno()));
        unfreezeDTO.setSpecies(order.getSpecies().intValue());
        // 中山八院解冻需要传供应商和商品信息
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_BA_YI_YUAN.getCode().equals(user.getOrgCode())){
            SupplierDTO supplierDTO = new SupplierDTO();
            supplierDTO.setSupplierCode(order.getFsuppcode());
            supplierDTO.setSupplierName(order.getFsuppname());
            unfreezeDTO.setSupplierDTO(supplierDTO);
            List<com.reagent.research.fundcard.dto.OrderDetailDTO> detailDTOS = new ArrayList<>();
        }
        unfreezeDTO.setOrderDetailDTOs(OrderDetailTranslator.orderDetail2FundCardOrderDetail(detailList));
        List<RefFundcardOrderDTO> curFundCardList = orderIdFundCardMap.get(order.getId());
        List<SelectFundCardDTO> selectFundCardDTOS = new ArrayList<>(curFundCardList.size());
        if (CollectionUtils.isNotEmpty(curFundCardList)) {
            // 解冻的旧卡信息
            for (RefFundcardOrderDTO fundCard : curFundCardList) {
                BigDecimal cardUnfreezeAmount = isDockingFundCard ? payTotalMoney : fundCard.getFreezeAmount();
                selectFundCardDTOS.add(RefFundcardOrderTranslator.refToSelectFundCardDto(fundCard, cardUnfreezeAmount));
            }
            unfreezeDTO.setSelectFundCardDTOS(selectFundCardDTOS);
        }
        List<ExtraDTO> extraDTOList = new ArrayList<>();
        unfreezeDTO.setExtraDTOs(extraDTOList);
        if(OrgEnum.SHEN_ZHEN_YI_XUE_KE_XUE_YUAN.getCode().equals(user.getOrgCode())){
            // 换卡解冻，都按取消订单处理
            extraDTOList.add(new ExtraDTO("operateType", "1"));
        }
        return unfreezeDTO;
    }

    @Override
    public List<RefFundcardOrderDTO> findByOrderIdList(List<Integer> orderIdList) {
        List<String> orderIdListForString = orderIdList.stream().map(String::valueOf).collect(Collectors.toList());
        List<RefFundcardOrderDO> refFundCardOrderDOList = refFundcardOrderMapper.selectAllByOrderIdIn(orderIdListForString);

        return RefFundcardOrderTranslator.do2Dto(refFundCardOrderDOList);
    }

    /**
     * 采购单id 批量查询经费卡关联关系
     * @param applicationIdList
     * @return
     */
    @Override
    public List<RefFundcardOrderDTO> findByApplicationIdList(List<Integer> applicationIdList) {
        List<String> applicationIdListForString = applicationIdList.stream().map(String::valueOf).collect(Collectors.toList());
        List<RefFundcardOrderDO> refFundCardOrderDOList = refFundcardOrderMapper.selectAllByApplicationIdIn(applicationIdListForString);
        return RefFundcardOrderTranslator.do2Dto(refFundCardOrderDOList);
    }

    /**
     * 根据经费开id集合查询订单id集合
     *
     * @param cardIds
     * @return
     */
    @Override
    public List<String> findOrderIdsByCardIds(List<String> cardIds) {
        if (CollectionUtils.isEmpty(cardIds)) {
            return Lists.newArrayList();
        }
        return refFundcardOrderMapper.findOrderIdByCardIds(cardIds);
    }

    /**
     * 根据采购申请id查询经费卡
     *
     * @param applicationId
     * @return
     */
    @Override
    public List<RefFundcardOrderDO> findByApplicationId(String applicationId) {
        if (StringUtils.isEmpty(applicationId)) {
            return Lists.newArrayList();
        }
        return refFundcardOrderMapper.findByApplicationId(applicationId);
    }

    /**
     * 根据订单id查询经费卡
     *
     * @param orderId
     * @return
     */
    @Override
    public List<RefFundcardOrderDO> findRefundcardOrderByOrderId(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return Lists.newArrayList();
        }
        return refFundcardOrderMapper.findByOrderId(orderId);
    }

    /**
     * 根据竞价单id查询经费卡
     *
     * @param bidId
     * @return
     */
    @Override
    public List<RefFundcardOrderDO> findByBidId(String bidId) {
        if (StringUtils.isEmpty(bidId)) {
            return Lists.newArrayList();
        }
        return refFundcardOrderMapper.findByBidId(bidId);
    }

    @Override
    @ServiceLog(description = "根据采购申请单id更新绑卡记录", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public Integer updateRefFundCardOrderByApplicationId(RefFundcardOrderDTO refFundcardOrderDTO) {
        Assert.notNull(refFundcardOrderDTO.getApplicationId(), "applicationId不能为空！");
        RefFundcardOrderDO entity = RefFundcardOrderTranslator.dto2DO(refFundcardOrderDTO);
        return refFundcardOrderMapper.updateByApplicationId(entity);
    }

    @Override
    @ServiceLog(description = "根据竞价单id更新绑卡记录", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public Integer updateRefFundCardOrderByBId(RefFundcardOrderDTO refFundcardOrderDTO) {
        Assert.notNull(refFundcardOrderDTO.getBidId(), "Bid不能为空！");
        RefFundcardOrderDO entity = RefFundcardOrderTranslator.dto2DO(refFundcardOrderDTO);
        return refFundcardOrderMapper.updateByBidId(entity);
    }

    @Override
    public Integer deleteByIdList(List<String> idList) {
        Preconditions.notEmpty(idList, "id不能为空！");
        Preconditions.isTrue(idList.size() < 101, "单次更新记录不可超过100条！");
        int affect = refFundcardOrderMapper.deleteByIdIn(idList);
        return affect;
    }

    @Override
    public void validateFundCard(List<RefFundcardOrderDTO> refFundCardOrderDTO,
                                 OrderOperatorDTO user,
                                 BigDecimal consumablesFee,
                                 BigDecimal analysisFee) {
        // 校验经费类型
        judgeFundCardType(refFundCardOrderDTO, user);
        // 校验经费卡是否可用
        judgeUsableFundCard(refFundCardOrderDTO, user, consumablesFee, analysisFee);
    }

    private void judgeFundCardType(List<RefFundcardOrderDTO> refCards, OrderOperatorDTO user) {
        List<Integer> orderIds = refCards.stream().map(it -> Integer.parseInt(it.getOrderId())).collect(Collectors.toList());
        // 取经费卡id, 如果用3级经费卡, 先取subjectId, subjectId为空则用2级经费卡cardId
        List<String> newCardIds = refCards.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).filter(Objects::nonNull).collect(Collectors.toList());
        final Map<String, Integer> newCardIdOrderIdMapper = DictionaryUtils.toMap(refCards, RefFundcardOrderTranslator::getLastLevelCardId, it -> Integer.parseInt(it.getOrderId()));
        final List<FundCardDTO> newCardInfo = researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(user.getOrgCode(), newCardIds);
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIds);
        if(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(user.getOrgCode())){
            boolean isSpecialService = orderDetailDOList.stream().allMatch(d -> CategoryConstant.BIO_SCI_SERVICE_ID.equals(d.getSecondCategoryId())
                    || CategoryConstant.CHEMICAL_TECH_SERVICE_ID.equals(d.getSecondCategoryId())
                    || CategoryConstant.ANIMAL_EXPERIMENT_SERVICE_ID.equals(d.getSecondCategoryId()));
            if(isSpecialService){
                // 上述这三种分类，只能用【测试化验加工费】
                BusinessErrUtil.isTrue(newCardInfo.stream().allMatch(card->"Z03".equals(card.getCode())
                        || "R05".equals(card.getCode())
                        || "H17".equals(card.getCode())), "当前商品分类仅支持使用测试化验加工费");
            }else {
                // 否则，不能使用能用任何【测试化验加工费】
                BusinessErrUtil.isTrue(newCardInfo.stream().noneMatch(card->"Z03".equals(card.getCode())
                        || "R05".equals(card.getCode())
                        || "H17".equals(card.getCode())), "当前商品分类不支持使用测试化验加工费");
            }
        }

        // 校验经费采购规则
        List<FundCardPurchaseRuleGoodsDTO> goodsCateorgyList = orderDetailDOList.stream().map(orderDetail -> {
            FundCardPurchaseRuleGoodsDTO ruleGoodsDTO = new FundCardPurchaseRuleGoodsDTO();
            ruleGoodsDTO.setGoodsCategoryIdLevel1(orderDetail.getFirstCategoryId());
            ruleGoodsDTO.setGoodsCategoryIdLevel2(orderDetail.getSecondCategoryId());
            // 有三级分类才传三级
            if (Objects.nonNull(orderDetail.getCategoryid()) && !Objects.equals(orderDetail.getCategoryid(), orderDetail.getSecondCategoryId())) {
                ruleGoodsDTO.setGoodsCategoryIdLevel3(orderDetail.getCategoryid());
            }
            return ruleGoodsDTO;
        }).collect(Collectors.toList());
        FundCardPurchaseRuleCheckDTO fundCardPurchaseRuleCheckDTO = new FundCardPurchaseRuleCheckDTO();
        fundCardPurchaseRuleCheckDTO.setFundCardIds(newCardIds);
        fundCardPurchaseRuleCheckDTO.setGoodsList(goodsCateorgyList);
        researchFundCardServiceClient.fundCardPurchaseRuleCheck(fundCardPurchaseRuleCheckDTO, user.getOrgCode());

        List<BaseOrderExtraDTO> fundFlows = orderExtraClient.selectByOrderIdInAndExtraKey(orderIds, OrderExtraEnum.FLOW_FUND_TYPE.getValue());
        if (CollectionUtils.isEmpty(fundFlows)) {
            return;
        }
        // orderId -> fundFlow
        final Map<Integer, String> orderIdFundFlowMapper = DictionaryUtils.toMap(fundFlows, BaseOrderExtraDTO::getOrderId, BaseOrderExtraDTO::getExtraValue);
        if (CollectionUtils.isEmpty(newCardInfo)) {
            return;
        }
        newCardInfo.forEach(it -> {
            if (it.getFundType() == null) {
                return;
            }
            final Integer orderId = newCardIdOrderIdMapper.get(it.getId());
            if (orderId == null) {
                return;
            }
            final boolean equals = it.getFundType().toString().equals(orderIdFundFlowMapper.get(orderId));
            BusinessErrUtil.isTrue(equals, ExecptionMessageEnum.MODIFICATION_FAILED_CANNOT_CHANGE_FUNDING_TYPE, orderId);
        });
    }

    private void judgeUsableFundCard(List<RefFundcardOrderDTO> newCards, OrderOperatorDTO user, BigDecimal consumablesFee, BigDecimal analysisFee) {
        // 查出所有订单，按照采购人分组
        List<Integer> allOrderIdList = newCards.stream().map(item -> Integer.parseInt(item.getOrderId())).collect(Collectors.toList());
        List<OrderMasterDO> allOrderMasterDOList = orderMasterMapper.findByIdIn(allOrderIdList);
        if (CollectionUtils.isEmpty(allOrderMasterDOList)) {
            return;
        }
        Map<Integer, List<OrderMasterDO>> buyerIdMasterListMap = DictionaryUtils.groupBy(allOrderMasterDOList, OrderMasterDO::getFbuyerid);
        // 获取所有的采购人信息
        List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(buyerIdMasterListMap.keySet(), allOrderMasterDOList.get(0).getFuserid());
        Map<Integer, UserBaseInfoDTO> userIdInfoMap = DictionaryUtils.toMap(userBaseInfoDTOList, UserBaseInfoDTO::getId, Function.identity());
        // 根据不同的采购人分组获取经费卡是否可用
        for (Map.Entry<Integer, List<OrderMasterDO>> entry : buyerIdMasterListMap.entrySet()) {
            UserBaseInfoDTO userBaseInfoDTO = userIdInfoMap.get(entry.getKey());
            BusinessErrUtil.notNull(userBaseInfoDTO, ExecptionMessageEnum.FAILED_TO_OBTAIN_USER_INFO, entry.getKey());

            // 获取对应采购人分组下需要校验的经费卡
            List<OrderMasterDO> orderMasterDOList = entry.getValue();
            List<Integer> orderIdListToCheck = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
            List<RefFundcardOrderDTO> fundCardListToCheck = newCards.stream().filter(card -> orderIdListToCheck.contains(Integer.parseInt(card.getOrderId()))).collect(Collectors.toList());

            OrgRequest<FundCardUsableDTO> request = new OrgRequest<>();

            FundCardUsableDTO data = new FundCardUsableDTO();
            request.setOrgCode(user.getOrgCode());
            int cardSize = fundCardListToCheck.size();
            List<FundCardDetailDTO> fundCardDetailDTOS = new ArrayList<>(cardSize);
            List<GoodsCategoryDTO> goodsCategoryDTOs = new ArrayList<>(cardSize);
            // 默认的设置经费卡方法
            this.defaultSetFundCard(fundCardListToCheck, user, fundCardDetailDTOS, goodsCategoryDTOs);
            // 定制化设置品类
            this.customSetGoodsCategory(fundCardListToCheck, user, consumablesFee, analysisFee, goodsCategoryDTOs);

            data.setFundCardDetailDTOS(fundCardDetailDTOS);
            data.setGoodsCategoryDTOs(goodsCategoryDTOs);
            // 设置采购人信息
            UserDTO userDTO = new UserDTO();
            userDTO.setJobNumber(userBaseInfoDTO.getJobnumber());
            userDTO.setRealName(userBaseInfoDTO.getName());
            userDTO.setUserId(userBaseInfoDTO.getId());
            request.setUserDTO(userDTO);
            // 设置采购部门id
            List<Integer> departmentIdList = orderMasterDOList.stream().map(OrderMasterDO::getFbuydepartmentid).collect(Collectors.toList());
            request.setDepartmentIds(departmentIdList);
            request.setData(data);
            researchFundCardServiceClient.judgeUsableFundCardByRule(request);
        }
    }

    void defaultSetFundCard(List<RefFundcardOrderDTO> refFundCardOrderDTO, OrderOperatorDTO user, List<FundCardDetailDTO> fundCardDetailDTOS, List<GoodsCategoryDTO> goodsCategoryDTOs) {
        List<String> cardIdList = refFundCardOrderDTO.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        for (RefFundcardOrderDTO dto : refFundCardOrderDTO) {
            if (cardIdList.contains(RefFundcardOrderTranslator.getLastLevelCardId(dto))) {
                FundCardDetailDTO fundCardItem = new FundCardDetailDTO();
                if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode().equals(user.getOrgCode())) {
                    fundCardItem.setId(dto.getProjectId());
                } else {
                    fundCardItem.setId(RefFundcardOrderTranslator.getLastLevelCardId(dto));
                }
                fundCardItem.setUseAmount(dto.getFreezeAmount());
                fundCardItem.setExpenseApplyNo(dto.getExpenseApplyNo());
                fundCardDetailDTOS.add(fundCardItem);
                cardIdList.remove(dto.getCardId());
            }

            GoodsCategoryDTO goodsCategoryItem = new GoodsCategoryDTO();
            goodsCategoryItem.setCategoryAmount(dto.getFreezeAmount());
            goodsCategoryDTOs.add(goodsCategoryItem);
        }
    }

    /**
     * 定制化设置订单对应经费卡的分类
     * @param refFundCardOrderDTO   绑卡信息
     * @param user                  用户
     * @param consumablesFee        实验耗材费，只有中肿用
     * @param analysisFee           测试分析费，只有中肿用
     * @param goodsCategoryDTOs     订单商品
     */
    void customSetGoodsCategory(List<RefFundcardOrderDTO> refFundCardOrderDTO, OrderOperatorDTO user, BigDecimal consumablesFee, BigDecimal analysisFee, List<GoodsCategoryDTO> goodsCategoryDTOs) {
        /**
         *  中肿的校验经费逻辑有点神奇。。
         *  需要对报账类型进行分类，需要分别统计订单商品中的 "测试分析费" 和 "实验耗材费" 的总金额传到预算系统进行报账金额检验
         */
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode().equals(user.getOrgCode())) {
            goodsCategoryDTOs.clear();
            if (consumablesFee != null && consumablesFee.compareTo(BigDecimal.ZERO) > 0) {
                GoodsCategoryDTO goodsCategoryItem = new GoodsCategoryDTO();
                goodsCategoryItem.setFirstCategoryId(ZhongZhongCategoryEnum.REAGENT_CONSUMABLES_FEE.getValue());
                goodsCategoryItem.setCategoryAmount(consumablesFee);
                goodsCategoryDTOs.add(goodsCategoryItem);
            }
            if (analysisFee != null && analysisFee.compareTo(BigDecimal.ZERO) > 0) {
                GoodsCategoryDTO goodsCategoryItem = new GoodsCategoryDTO();
                goodsCategoryItem.setFirstCategoryId(ZhongZhongCategoryEnum.SERVICE_ANALYSIS_FEE.getValue());
                goodsCategoryItem.setCategoryAmount(analysisFee);
                goodsCategoryDTOs.add(goodsCategoryItem);
            }
            return;
        }
        // 南方医、暨大校验订单商品一级分类（经费分类）
        final List<String> needFirstCategoryIdOrgList = New.list(OrgEnum.NAN_FANG_YI_KE.getCode(), OrgEnum.JI_NAN_DA_XUE.getCode());
        if (needFirstCategoryIdOrgList.contains(user.getOrgCode())) {
            List<Integer> orderIdList = refFundCardOrderDTO.stream().map(it -> Integer.parseInt(it.getOrderId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIdList)) {
                return;
            }
            goodsCategoryDTOs.clear();
            List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
            BusinessErrUtil.notEmpty(orderDetailDOList, ExecptionMessageEnum.CARD_REPLACEMENT_FAILED_NO_ORDER_ITEMS);
            Map<Integer, List<OrderDetailDO>> firstCategoryDetailMap = DictionaryUtils.groupBy(orderDetailDOList, OrderDetailDO::getFirstCategoryId);
            firstCategoryDetailMap.forEach((firstCateGoryId, details) -> {
                GoodsCategoryDTO goodsCategoryItem = new GoodsCategoryDTO();
                goodsCategoryItem.setFirstCategoryId(firstCateGoryId);
                goodsCategoryItem.setCategoryAmount(details.stream().map(OrderDetailDO::getFbidamount).reduce(BigDecimal.ZERO, BigDecimal::add));
                goodsCategoryDTOs.add(goodsCategoryItem);
            });
        }
    }

    @Override
    public Boolean fundCardNotRequired(List<Integer> orderIdList, OrderOperatorDTO user) {
        // 如果经费卡传空，则校验经费卡配置是否 "经费卡非必填"，需要兼容
        String orgCode = user.getOrgCode();
        String config = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.MUST_HAVE_FUNDCARD);
        if (config == null) {
            throw new IllegalStateException("获取经费卡oms配置失败！配置项为空！");
        }

        if (MUST_HAVE_FUNDCARD_FALSE.equals(config)) {
            // "经费卡非必填 并且 前端无经费卡传来，则删除经费卡绑定记录，同时还要同步新结算的订单"
            OrderChangeCommonDTO request = new OrderChangeCommonDTO();
            List<RefFundcardOrderDTO> refFundcardOrderDTOList = new ArrayList<>(orderIdList.size());
            for (Integer orderId : orderIdList) {
                RefFundcardOrderDTO refItem = new RefFundcardOrderDTO();
                refItem.setOrderId(orderId.toString());
                refItem.setCardId(StringUtils.EMPTY);
                refItem.setCardNo(StringUtils.EMPTY);
            }
            request.setRefFundCardOrderList(refFundcardOrderDTOList);
            request.setOrderOperatorDTO(user);
            orderRelatedRPCService.saveFundCardCommon(request);
            return true;
        }

        return false;
    }

    @Override
    @ServiceLog(description = "待结算换卡服务", serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void saveFundCardForWaitStatement(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo) {
        Integer orgId = rjSessionInfo.getOrgId();
        UserBaseInfoDTO userInfo = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), orgId);
        List<Integer> orderIds = request.getOrderIds();
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.OPERATION_FAILED_INVALID_USER);
        this.batchChangeFundCardVerify(request, orgId);
        this.saveFundCardVerify(request, rjSessionInfo, OrderStatusEnum.WaitingForStatement_1);
        // 获取旧卡号
        Map<Integer, String> oldCardMap = getFundCardNoByOrderId(orderIds);
        this.changeFundCardCore(userInfo, orgId, request, false, false);
        // 获取新卡号
        Map<Integer, String> newCardMap = getFundCardNoByOrderId(orderIds);
        // 记录未绑卡订单 待结算换卡日志
        saveNoCardChangeLog(orderIds, request.getReason(), oldCardMap, newCardMap, userInfo);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void saveFundCardForStatement(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo) {
        Integer orgId = rjSessionInfo.getOrgId();
        UserBaseInfoDTO userInfo = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), orgId);
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.OPERATION_FAILED_INVALID_USER);
        List<OrderMasterDO> orderMasterDOList = this.saveFundCardVerify(request, rjSessionInfo, OrderStatusEnum.Statementing_1);
        List<Integer> statementIdList = orderMasterDOList.stream().map(OrderMasterDO::getStatementId).collect(Collectors.toList());
        BusinessErrUtil.isTrue(statementIdList.size() == orderMasterDOList.size(), ExecptionMessageEnum.INCORRECT_SETTLEMENT_DATA_CONTACT_SUPPORT);
        List<Integer> orderIds = orderMasterDOList.stream().map(OrderMasterDO::getId).distinct().collect(toList());
        // 获取旧卡号
        Map<Integer, String> oldCardMap = getFundCardNoByOrderId(orderIds);
        this.changeFundCardCore(userInfo, orgId, request, false, false);
        // 获取新卡号
        Map<Integer, String> newCardMap = getFundCardNoByOrderId(orderIds);
        saveStatementChangeCardLog(userInfo, request.getReason(), orderMasterDOList, oldCardMap, newCardMap);
    }

    /**
     * 记录未绑卡待结算换卡日志的方法
     */
    private void saveNoCardChangeLog(List<Integer> orderIds, String reason, Map<Integer, String> oldCardMap,
                                     Map<Integer, String> newCardMap, UserBaseInfoDTO userInfo) {

        // 只记录未绑卡的
        List<Integer> noCardOrderIds = orderIds.stream()
                .filter(orderId -> StringUtils.isBlank(oldCardMap.get(orderId)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(noCardOrderIds)) {
            return;
        }
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(noCardOrderIds);

        for (OrderMasterDO orderMasterDO : orderMasterDOList) {
            Integer orderId = orderMasterDO.getId();
            String orderNo = orderMasterDO.getForderno();
            String oldCardNo = oldCardMap.get(orderId);
            String newCardNo = newCardMap.get(orderId);

            // 判断是否成功获取到新卡号
            String remark;
            if (StringUtils.isNotBlank(newCardNo)) {
                // 如果成功获取到新卡号，则记录成功的换卡日志
                remark = StrUtil.format("订单号：{}，原值为“{}”，新值为“{}”", orderNo, StringUtils.defaultString(oldCardNo), newCardNo);
                if (StringUtils.isNotBlank(reason)) {
                    remark = StrUtil.format("修改原因：{}，{}", reason, remark);
                }
                orderApprovalLogService.createOrderOperateLog(orderId, OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS.getValue(),
                        userInfo.getId(), userInfo.getName(), remark);
            } else {
                // 否则记录失败的换卡日志
                remark = OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_FAILURE.name();
                orderApprovalLogService.createOrderOperateLog(orderId, OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_FAILURE.getValue(),
                        userInfo.getId(), userInfo.getName(), remark);
            }
        }
    }



    /**
     * 保存结算换卡日志
     */
    private void saveStatementChangeCardLog(UserBaseInfoDTO userInfo, String reason,
            List<OrderMasterDO> orderMasterDOList, Map<Integer, String> oldCardMap, Map<Integer, String> newCardMap) {
        List<StatementLogRequestDTO> statementLogRequestDTOList = new ArrayList<>();
        for (OrderMasterDO orderMaster : orderMasterDOList) {
            if (Objects.isNull(orderMaster) || Objects.isNull(orderMaster.getStatementId())) {
                continue;
            }
            StatementLogRequestDTO statementLogRequestDTO = new StatementLogRequestDTO();
            statementLogRequestDTO.setStatementId(orderMaster.getStatementId());
            statementLogRequestDTO.setUserId(userInfo.getId().longValue());
            statementLogRequestDTO.setUserName(userInfo.getName());

            String remark = StrUtil.format("订单号：{}，修改原因：{}，原值为“{}”，新值为“{}”",
                    orderMaster.getForderno(),
                    reason,
                    oldCardMap.getOrDefault(orderMaster.getId(), StringUtils.EMPTY),
                    newCardMap.getOrDefault(orderMaster.getId(), StringUtils.EMPTY));
            statementLogRequestDTO.setReason(remark);
            statementLogRequestDTOList.add(statementLogRequestDTO);
        }
        statementPlatformClient.saveChangeCardLogs(statementLogRequestDTOList);
    }

    /**
     * 查询订单-经费卡号信息，多张逗号拼接
     */
    public Map<Integer,String> getFundCardNoByOrderId(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyMap();
        }
        List<String> orderIds = orderIdList.stream().map(String::valueOf).collect(toList());
        List<RefFundcardOrderDO> fundcardOrderDOList = refFundcardOrderMapper.findByOrderIdIn(orderIds);
        if (CollectionUtils.isEmpty(fundcardOrderDOList)) {
            return New.emptyMap();
        }
        Map<String, List<RefFundcardOrderDO>> orderId2CardMap = DictionaryUtils.groupBy(fundcardOrderDOList, RefFundcardOrderDO::getOrderId);

        Map<Integer, String> result = New.mapWithCapacity(orderIdList.size());
        orderId2CardMap.forEach((orderId, fundcardList) -> {
            String cardNos = fundcardList.stream()
                    .map(RefFundcardOrderDO::getCardNo)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining("，"));
            result.put(Integer.valueOf(orderId), cardNos);
        });
        return result;
    }

    /**
     * 批量换卡校验
     */
    private void batchChangeFundCardVerify(ChangeFundCardRequestDTO request, Integer orgId) {
        // 中肿、中大眼科允许批量换卡 ,因为发起结算后才回去冻结，这里是指修改并定的卡
        final Set<Integer> allowOrgIdSet = New.set(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getValue()
                , OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getValue());
        if (allowOrgIdSet.contains(orgId)) {
            return;
        }
        if (Objects.isNull(request)) {
            return;
        }
        if (CollectionUtils.size(request.getOrderIds()) < 2) {
            return;
        }
        // 财务对接单位暂不支持批量换卡
        OrganizationClient.SimpleOrgDTO orgDTO = organizationClient.findSimpleOrgDTOById(orgId);
        BusinessErrUtil.notNull(orgDTO, ExecptionMessageEnum.CURRENT_UNIT_NOT_FOUND, orgId);
        String dockingFundCardConfig = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgDTO.getCode(), ConfigConstant.RESEARCH_FUNDCARD_ISINTERFACE);
        boolean isDockingFundCard = ConfigConstant.FUND_CARD_DOCKING_VAL.equals(dockingFundCardConfig);
        BusinessErrUtil.isTrue(!isDockingFundCard, ExecptionMessageEnum.FINANCE_ORG_CARD_BATCH_MODIFY_UNSUPPORTED);
    }

    private List<OrderMasterDO> saveFundCardVerify(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo, OrderStatusEnum checkOrderStatus) {
        // 需要校验的权限编码
        String authAccessCode = checkOrderStatus == OrderStatusEnum.WaitingForStatement_1 ? ConfigConstant.BUYER_CENTER_STATEMENT_FUND_CARD_EDIT : ConfigConstant.BUYER_CENTER_PAYMENT_FUND_CARD_EDIT;
        // 查用户在哪些部门下有待结算改经费卡权限
        List<DepartmentDTO> departmentDTOList = userClient.getDeptForUserByAccess(rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), authAccessCode);
        List<Integer> userHaveAuthDeptIdList = departmentDTOList.stream().map(DepartmentDTO::getId).collect(Collectors.toList());
        List<Integer> orderIds = request.getOrderIds();
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIds);
        List<String> statusNotMatchOrderNoList = orderMasterDOList.stream().filter(orderMasterDO -> !checkOrderStatus.getValue().equals(orderMasterDO.getStatus())).map(OrderMasterDO::getForderno).collect(Collectors.toList());
        BusinessErrUtil.isEmpty(statusNotMatchOrderNoList, ExecptionMessageEnum.ORDER_STATUS_NOT_MATCH_CANNOT_CHANGE_CARD, StringUtils.join(statusNotMatchOrderNoList, ","), checkOrderStatus.getName());
        // 校验用户对选择的订单对应的采购部门是否都有修改经费卡的权限
        List<String> withoutAuthOrderNoList = orderMasterDOList.stream().filter(orderMasterDO -> !userHaveAuthDeptIdList.contains(orderMasterDO.getFbuydepartmentid())).map(OrderMasterDO::getForderno).collect(Collectors.toList());
        BusinessErrUtil.isTrue(CollectionUtils.isEmpty(withoutAuthOrderNoList), ExecptionMessageEnum.NO_PERMISSION_CHANGE_FUNDING_CARD, StringUtils.join(withoutAuthOrderNoList, "、"));
        return orderMasterDOList;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void saveFundCardSkipVerify(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo, boolean skipCheckCardEqual) {
        Integer orgId = rjSessionInfo.getOrgId();
        UserBaseInfoDTO userInfo = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), orgId);
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.OPERATION_FAILED_INVALID_USER);
        this.changeFundCardCore(userInfo, orgId, request, false, skipCheckCardEqual);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void reFreezeFundCard(Integer orderId){
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.NO_DATA_FOUND_FOR_ORDER_ID, orderId);
        this.reFreezeFundCard(orderMasterDO);
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public void reFreezeFundCard(OrderMasterDO orderMasterDO) {
        BusinessErrUtil.isTrue(OrderFundStatusEnum.FreezedFail.getValue().equals(orderMasterDO.getFundStatus()), ExecptionMessageEnum.ORDER_NOT_FREEZING_FAILURE_CANNOT_REFREEZE);
        ChangeFundCardRequestDTO changeFundCardRequestDTO = fundCardFreezeCommonService.getReFreezeRequestParam(orderMasterDO);
        UserBaseInfoDTO userBaseInfoDTO = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        this.changeFundCardCore(userBaseInfoDTO, orderMasterDO.getFuserid(), changeFundCardRequestDTO, true, true);
    }


    @Override
    public void changeFundCard(UserBaseInfoDTO userInfo, Integer orgId, ChangeFundCardRequestDTO request, boolean isRefreeze, boolean skipCheckCardEqual, boolean callInterfaceFlag) {
        changeFundCardCore(userInfo, orgId, request, isRefreeze, skipCheckCardEqual, callInterfaceFlag);
    }

    private void changeFundCardCore(UserBaseInfoDTO userInfo, Integer orgId, ChangeFundCardRequestDTO request, boolean isRefreeze, boolean skipCheckCardEqual) {
        changeFundCardCore(userInfo, orgId, request, isRefreeze, skipCheckCardEqual, true);
    }

    private void changeFundCardCore(UserBaseInfoDTO userInfo, Integer orgId, ChangeFundCardRequestDTO request, boolean isRefreeze, boolean skipCheckCardEqual, boolean callInterfaceFlag) {
        // 西南医科大单独校验换卡原因必填
        if (Objects.equals(orgId, OrgEnum.XI_NAN_YI_KE_DA_XUE.getValue())) {
            BusinessErrUtil.isTrue(StringUtils.isNotBlank(request.getReason()), ExecptionMessageEnum.CARD_CHANGE_REASON_EMPTY);
        }
        // 换卡逻辑迁移
        List<Integer> orderIds = request.getOrderIds();
        BusinessErrUtil.notEmpty(orderIds, "订单id不能为空！");
        List<FundCardProjectRequestDTO> saveProjectList = request.getSaveProjectList();
        BusinessErrUtil.notNull(userInfo, ExecptionMessageEnum.OPERATION_FAILED_INVALID_USER);

        OrderOperatorDTO operatorDTO = new OrderOperatorDTO();
        String orgCode;
        try {
            OrgEnum orgEnumById = OrgEnum.getOrgEnumById(userInfo.getOrganizationId());
            orgCode = orgEnumById.getCode();
        } catch (Exception e) {
            OrganizationDTO orgById = userClient.getOrgById(orgId);
            orgCode = orgById.getCode();
        }
        operatorDTO.setOrgCode(orgCode);
        operatorDTO.setUserId(userInfo.getId());
        operatorDTO.setJobNumber(userInfo.getJobnumber());
        operatorDTO.setUserName(userInfo.getName());
        operatorDTO.setOperateDate(new Date());
        operatorDTO.setTelephone(userInfo.getMobile());

        // 如果经费卡传空，则校验经费卡配置是否 "经费卡非必填"，需要兼容
        if (CollectionUtils.isEmpty(saveProjectList)) {
            BusinessErrUtil.isTrue(refFundcardOrderService.fundCardNotRequired(orderIds, operatorDTO), "经费信息不可为空！");
        }
        // 组装经费卡数据
        Pair<List<RefFundcardOrderDTO>, List<OrderMasterDO>> fundCardOrderDTOList = this.packageFundCardOrderDTOList(orderIds, request, orgCode);

        List<String> changingCardOrderNos = fundCardOrderDTOList.getRight().stream()
                .filter(item -> OrderFundStatusEnum.ChangingCard.getValue().equals(item.getFundStatus()))
                .map(OrderMasterDO::getForderno)
                .collect(Collectors.toList());
        BusinessErrUtil.isTrue(CollectionUtils.isEmpty(changingCardOrderNos), ExecptionMessageEnum.ORDER_CHANGING_CARD_CANNOT_CHANGE_CARD_AGAIN, StringUtils.join(changingCardOrderNos, "、"));
        // 检查经费状态 (未冻结、冻结失败、解冻成功、换卡失败 才能换卡)
        if (ORG_LIST_CHANGE_CARD_WITHOUT_CHANGE_CARD_IMPL.contains(orgCode) || isRefreeze) {
            boolean canFreeze = fundCardOrderDTOList.getRight().stream()
                    .allMatch(item -> OrderFundStatusEnum.FreezedFail.getValue().equals(item.getFundStatus())
                            || OrderFundStatusEnum.UN_FREEZE.getValue().equals(item.getFundStatus())
                            || OrderFundStatusEnum.ThrawSuccessed.getValue().equals(item.getFundStatus())
                            || OrderFundStatusEnum.ChangedCardFail.getValue().equals(item.getFundStatus()));
            BusinessErrUtil.isTrue(canFreeze, ExecptionMessageEnum.ORDER_STATUS_NOT_ALLOW_FREEZE);
        }
        // 是否有冻结失败的订单
        boolean haveAnyFreezeFail = fundCardOrderDTOList.getRight().stream()
                .anyMatch(item -> OrderFundStatusEnum.FreezedFail.getValue().equals(item.getFundStatus()));
        // 如果跳过校验，就直接不判断卡是否一致
        // 如果是冻结失败的订单，如果待结算换的是同一张卡，就当是要做重新冻结操作，不需校验的卡是否相同。
        // 但如果是冻结成功，就校验是否是同一张卡，同一张卡不执行换卡逻辑
        if (!skipCheckCardEqual && !haveAnyFreezeFail && validateIsEq(fundCardOrderDTOList.getLeft())) {
            return;
        }
        if (!isCustomOldOrder(fundCardOrderDTOList)) {
            // 检验经费卡
            refFundcardOrderService.validateFundCard(fundCardOrderDTOList.getLeft(), operatorDTO, request.getConsumablesFee(), request.getAnalysisFee());
            // 换卡
            refFundcardOrderService.changeFundCardCommon(fundCardOrderDTOList.getLeft(), operatorDTO, isRefreeze, callInterfaceFlag, request.getReason());
        }
    }

    /**
     * 旧单自结算, 未冻结经费的单, 只记录绑卡信息 2021-12-29, 高度定制的换卡需求
     * @param fundCardOrderDTOList
     * @return
     */
    private boolean isCustomOldOrder(Pair<List<RefFundcardOrderDTO>, List<OrderMasterDO>> fundCardOrderDTOList) {
        final String orgCode = fundCardOrderDTOList.getRight().get(0).getFusercode();
        // 广妇幼换卡不冻结经费，但实际上调了经费卡那边的换卡和冻结接口，经费卡说就算调用了实际上也没有调用TPI的冻结接口 很神奇。先维持现状
        // 如果是孙逸仙，旧单不能走冻结，宇雷用的是经费状态作为判断条件，旧单全部改为自结算。这边改为如果是孙逸仙还需要继续下面的判断
        if (!OrgEnum.GUANG_ZHOU_SHI_FU_NV_ER_TONG_YI_LIAO_ZHONG_XIN.getCode().equals(orgCode) && 
                !OrgEnum.ZHONG_SHAN_DA_XUE_SUN_YI_XIAN_JI_NIAN_YI_YUAN.getCode().equals(orgCode)) {
            return false;
        }
        final List<RefFundcardOrderDTO> cardList = fundCardOrderDTOList.getLeft();
        final List<OrderMasterDO> orderList = fundCardOrderDTOList.getRight();
        if (CollectionUtils.isEmpty(cardList)) {
            return true;
        }
        // 孙逸仙旧单是自结算，只需要修改绑卡记录，不用实际上调用换卡/冻结接口
        // get(0)是因为只有孙逸仙用，界面恰好只有单个单的换卡接口
        Integer fundStatus = orderList.get(0).getFundStatus();
        if (OrderFundStatusEnum.DISTRIBUTE_STATEMENT_NO_TAG.value.equals(fundStatus) || OrderFundStatusEnum.DISTRIBUTE_STATEMENT.value.equals(fundStatus)) {
            // 删除旧的绑卡记录
            refFundcardOrderMapper.deleteByOrderIdIn(cardList.stream().map(RefFundcardOrderDTO::getOrderId).collect(Collectors.toList()));
            // 插入新的绑卡记录
            List<RefFundcardOrderDO> newRefCardList = cardList.stream().map(f -> {
                RefFundcardOrderDO ref = RefFundcardOrderTranslator.dto2DO(f);
                ref.setId(UUID.randomUUID().toString());
                return ref;
            }).collect(Collectors.toList());
            refFundcardOrderMapper.insertList(newRefCardList);
            // 更新新待结算单的经费卡信息
            updateNewWaitingStatement(DictionaryUtils.toMap(cardList, RefFundcardOrderDTO::getOrderId, RefFundcardOrderDTO::getCardNo));
            return true;
        } else {
            return false;
        }
    }

    /**
     * 校验换的卡和旧卡是否一致
     *
     * @param fundCardOrderDTOList 换的新卡
     * @return 是否一致
     */
    private boolean validateIsEq(List<RefFundcardOrderDTO> fundCardOrderDTOList) {
        if (CollectionUtils.isEmpty(fundCardOrderDTOList)) {
            return false;
        }

        // 获得需要绑定的新卡的卡的cardId与subjectId列表。因为待结算选卡只能选一样的卡，就给它distinct回入参，经费卡层级转换为对应的cardId耦合在了前面的封装数据里面，这里暂时这么处理
        List<Pair<String, String>> newCardSubjectIdPairList = fundCardOrderDTOList.stream()
                .map(dto -> Pair.of(dto.getCardId(), dto.getSubjectId())).distinct().collect(Collectors.toList());

        // 眼科是自由选哪张经费卡用多少钱，且待结算可以选卡，因此当多选了经费卡的时候，就当它是不一致就好了
        if (newCardSubjectIdPairList.size() > 1) {
            return false;
        }

        final List<String> orderIds = fundCardOrderDTOList.stream().map(RefFundcardOrderDTO::getOrderId).collect(Collectors.toList());
        final List<RefFundcardOrderDO> oldRefFundCards = refFundcardOrderMapper.findByOrderIdIn(orderIds);
        // 如果没有查到任何旧卡数据,那肯定不一致了
        if (CollectionUtils.isEmpty(oldRefFundCards)) {
            return false;
        }

        // 将旧卡按order_id分组
        final Map<String, List<Pair<String, String>>> oldOrderIdCardSubjectIdPairListMap =
                oldRefFundCards.stream().collect(Collectors.groupingBy(
                        RefFundcardOrderDO::getOrderId, Collectors.mapping(item -> Pair.of(item.getCardId(), item.getSubjectId()), Collectors.toList())));

        // 循环每个订单，新旧卡比对，如果新旧卡集合数据不一致，那就是不一致。
        for (String orderId : orderIds) {
            List<Pair<String, String>> oldCardSubjectIdPairList = oldOrderIdCardSubjectIdPairListMap.get(orderId);
            // 如果旧的数据是空的，或者卡数量不等，那就是不一致
            if (oldCardSubjectIdPairList == null || oldCardSubjectIdPairList.size() != newCardSubjectIdPairList.size()) {
                return false;
            }
            // 判断列表的第一个pair是否相同，前面限制了数量>1就是不一致。equals判断pair的cardId和subjectId是否一致
            if (!oldCardSubjectIdPairList.get(0).equals(newCardSubjectIdPairList.get(0))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 组装经费卡数据
     * @param orderIds 订单id
     * @param request  换卡请求参数
     * @param orgCode  机构编码
     * @return 经费卡绑卡信息
     */
    private Pair<List<RefFundcardOrderDTO>, List<OrderMasterDO>> packageFundCardOrderDTOList(List<Integer> orderIds, ChangeFundCardRequestDTO request, String orgCode) {
        List<FundCardProjectRequestDTO> saveProjectList = request.getSaveProjectList();
        List<OrderMasterDO> orderList = orderMasterMapper.findByIdIn(orderIds);
        Set<Integer> fundStatusCollect = orderList.stream().map(OrderMasterDO::getFundStatus).collect(Collectors.toSet());
        BusinessErrUtil.isTrue(fundStatusCollect.size() == 1, ExecptionMessageEnum.CANNOT_CHANGE_CARD_FOR_DIFFERENT_SETTLEMENTS);
        Map<Integer, OrderMasterDO> orderIdIdentityMap = orderList.stream().collect(Collectors.toMap(OrderMasterDO::getId, Function.identity()));
        // 经费卡绑卡信息
        List<RefFundcardOrderDTO> fundCardOrderDTOList = new ArrayList<>(saveProjectList.size());

        // 中肿换卡比较特殊绑定一级经费卡，且要设置serialNumber
        if (isCustomWrapCard(orderIds, orgCode, saveProjectList, orderIdIdentityMap, fundCardOrderDTOList)) {
            return Pair.of(fundCardOrderDTOList, orderList);
        }
        // 获取单位经费卡层级、经费卡是否对接
        Map<String, String> configMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orgCode, New.list(ConfigConstant.RESEARCH_FUNDCARD_LEVEL, ConfigConstant.RESEARCH_FUNDCARD_ISINTERFACE));
        // 获取单位经费卡层级
        int cardLevel = NumberUtils.toInt(configMap.get(ConfigConstant.RESEARCH_FUNDCARD_LEVEL));
        boolean isDockingFundCard = ConfigConstant.FUND_CARD_DOCKING_VAL.equals(configMap.get(ConfigConstant.RESEARCH_FUNDCARD_ISINTERFACE));
        
        if(!isDockingFundCard){
            // 非对接单位绑卡
            wrapNotDockingOrgCard(orderIds, cardLevel, saveProjectList, orderIdIdentityMap, fundCardOrderDTOList);
        }else {
            wrapCardCore(orderIds, orgCode, saveProjectList, orderIdIdentityMap, fundCardOrderDTOList, cardLevel);
        }
        return Pair.of(fundCardOrderDTOList, orderList);
    }

    /**
     * 非对接单位绑卡，允许单个订单绑多经费卡
     * @param orderIds 需要修改经费的订单
     * @param cardLevel 当前单位配置的经费卡等级
     * @param saveProjectList 需要换绑的经费卡
     * @param orderIdIdentityMap 订单id-订单数据映射
     * @param fundCardOrderDTOList 最终产生的绑卡数据
     */
    private void wrapNotDockingOrgCard(List<Integer> orderIds, int cardLevel, List<FundCardProjectRequestDTO> saveProjectList, Map<Integer, OrderMasterDO> orderIdIdentityMap, List<RefFundcardOrderDTO> fundCardOrderDTOList){
        BusinessErrUtil.isTrue(!(orderIds.size() > 1 && saveProjectList.size() > 1), ExecptionMessageEnum.MULTIPLE_FUNDS_CANNOT_BULK_MODIFY);
        for (FundCardProjectRequestDTO fundCardProjectDTO : saveProjectList) {
            // 获取项目经费卡信息
            List<OrderFundCardRequestDTO> fundCardList = fundCardProjectDTO.getSaveFundCardList();
            // 经费卡为空则该单位只有一层经费卡
            if (FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == cardLevel) {
                orderIds.forEach(orderId -> {
                    BigDecimal freezeAmount = this.getFreezeAmount(orderIdIdentityMap, orderId, saveProjectList.size() > 1, fundCardProjectDTO.getUseAmount());
                    this.setFundCardRequestDataIntoList(fundCardProjectDTO.getProjectCode(), fundCardProjectDTO.getId(), null, freezeAmount, orderId, fundCardProjectDTO.getExpenseApplyNo(), fundCardOrderDTOList);
                });
            } else if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel) {
                for (OrderFundCardRequestDTO orderFundCardDTO : fundCardList) {
                    // 设置二级经费卡
                    orderIds.forEach(orderId -> {
                        BigDecimal freezeAmount = this.getFreezeAmount(orderIdIdentityMap, orderId, saveProjectList.size() > 1, orderFundCardDTO.getUseAmount());
                        this.setFundCardRequestDataIntoList(orderFundCardDTO.getCardNo(), orderFundCardDTO.getCardId(), null, freezeAmount, orderId, orderFundCardDTO.getExpenseApplyNo(), fundCardOrderDTOList);
                    });
                }
            } else {
                for (OrderFundCardRequestDTO orderFundCardDTO : fundCardList) {
                    // 获取经费卡科目信息
                    List<FundCardSubjectRequestDTO> fundCardSubjectList = orderFundCardDTO.getSaveFundCardSubjectList();
                    // 设置经费卡科目信息
                    for (FundCardSubjectRequestDTO fundCardSubjectDTO : fundCardSubjectList) {
                        orderIds.forEach(orderId -> {
                            BigDecimal freezeAmount = this.getFreezeAmount(orderIdIdentityMap, orderId, saveProjectList.size() > 1, fundCardSubjectDTO.getUseAmount());
                            this.setFundCardRequestDataIntoList(orderFundCardDTO.getCardNo(), orderFundCardDTO.getCardId(), fundCardSubjectDTO.getId(), freezeAmount, orderId, fundCardSubjectDTO.getExpenseApplyNo(), fundCardOrderDTOList);
                        });
                    }
                }
            }
        }
        if(orderIds.size() == 1){
            // 只有一个单换卡时，校验选择金额是否等于将要冻结的金额，用于校验多经费卡冻结金额总额
            BigDecimal totalFreezeAmount = fundCardOrderDTOList.stream().map(RefFundcardOrderDTO::getFreezeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            OrderMasterDO orderMasterDO = orderIdIdentityMap.get(orderIds.get(0));
            BigDecimal amountShouldBeFreeze = orderMasterDO != null ? orderMasterDO.getForderamounttotal().subtract(BigDecimal.valueOf(orderMasterDO.getReturnAmount())) : BigDecimal.ZERO;
            BusinessErrUtil.isTrue(totalFreezeAmount.compareTo(amountShouldBeFreeze) == 0, ExecptionMessageEnum.CARD_AMOUNT_NOT_EQUAL_ORDER_AMOUNT);
        }
    }
    
    private void setFundCardRequestDataIntoList(String cardNo, String cardId, String subjectId, BigDecimal freezeAmount, Integer orderId, String expenseApplyNo, List<RefFundcardOrderDTO> fundCardOrderDTOList){
        RefFundcardOrderDTO item = new RefFundcardOrderDTO();
        item.setCardNo(cardNo);
        item.setCardId(cardId);
        item.setSubjectId(subjectId);
        item.setUsemoney(freezeAmount);
        item.setFreezeAmount(freezeAmount);
        item.setOrderId(orderId.toString());
        item.setExpenseApplyNo(expenseApplyNo);
        fundCardOrderDTOList.add(item);
    }
    
    private BigDecimal getFreezeAmount(Map<Integer, OrderMasterDO> orderIdIdentityMap, Integer orderId, boolean freezeUseMoney, BigDecimal useAmount){
        OrderMasterDO orderInfo = orderIdIdentityMap.get(orderId);
        if(freezeUseMoney){
            Preconditions.notNull(useAmount, "多选经费卡下使用金额不可以为空");
            return useAmount;
        }else{
            return orderInfo != null ? orderInfo.getForderamounttotal().subtract(BigDecimal.valueOf(orderInfo.getReturnAmount())) : BigDecimal.ZERO;
        }
    }

    /**
     * 绑卡核心代码，用于处理对接卡，由于预算迭代3.9仅涉及非对接单位选卡，原获取绑卡数据的方法不动，用于绑定对接卡
     * @param orderIds 需要进行绑卡的订单
     * @param orgCode 机构代码
     * @param saveProjectList 需要换绑的经费卡
     * @param orderIdIdentityMap 订单id-订单数据映射
     * @param fundCardOrderDTOList 最终产生的绑卡数据
     * @param cardLevel 当前单位经费卡等级
     */
    private void wrapCardCore(List<Integer> orderIds, String orgCode, List<FundCardProjectRequestDTO> saveProjectList, Map<Integer, OrderMasterDO> orderIdIdentityMap, List<RefFundcardOrderDTO> fundCardOrderDTOList, int cardLevel) {
        int orderSize = orderIds.size();
        int projectSize = saveProjectList.size();
        for (FundCardProjectRequestDTO fundCardProjectDTO : saveProjectList) {
            // 获取项目经费卡信息
            List<OrderFundCardRequestDTO> fundCardList = fundCardProjectDTO.getSaveFundCardList();
            // 经费卡为空则该单位只有一层经费卡
            if (FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == cardLevel) {
                orderIds.forEach(orderId -> {
                    RefFundcardOrderDTO item = new RefFundcardOrderDTO();
                    item.setCardNo(fundCardProjectDTO.getProjectCode());
                    item.setCardId(fundCardProjectDTO.getId());
                    OrderMasterDO orderInfo = orderIdIdentityMap.get(orderId);
                    item.setFreezeAmount(orderInfo != null ? orderInfo.getForderamounttotal().subtract(BigDecimal.valueOf(orderInfo.getReturnAmount())) : BigDecimal.ZERO);
                    item.setOrderId(orderId.toString());
                    item.setExpenseApplyNo(fundCardProjectDTO.getExpenseApplyNo());
                    fundCardOrderDTOList.add(item);
                });
            } else if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel) {
                for (OrderFundCardRequestDTO orderFundCardDTO : fundCardList) {
                    // 设置二级经费卡
                    orderIds.forEach(orderId -> {
                        RefFundcardOrderDTO item = new RefFundcardOrderDTO();
                        item.setCardNo(orderFundCardDTO.getCardNo());
                        item.setCardId(orderFundCardDTO.getCardId());
                        OrderMasterDO orderInfo = orderIdIdentityMap.get(orderId);
                        item.setFreezeAmount(orderInfo != null ? orderInfo.getForderamounttotal().subtract(BigDecimal.valueOf(orderInfo.getReturnAmount())) : BigDecimal.ZERO);
                        item.setOrderId(orderId.toString());
                        item.setExpenseApplyNo(orderFundCardDTO.getExpenseApplyNo());
                        fundCardOrderDTOList.add(item);
                    });
                }
            } else {
                for (OrderFundCardRequestDTO orderFundCardDTO : fundCardList) {
                    // 获取经费卡科目信息
                    List<FundCardSubjectRequestDTO> fundCardSubjectList = orderFundCardDTO.getSaveFundCardSubjectList();
                    // 设置经费卡科目信息
                    for (FundCardSubjectRequestDTO fundCardSubjectDTO : fundCardSubjectList) {
                        // 眼科必须手动输入
                        if (OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getCode().equals(orgCode)) {
                            BusinessErrUtil.notNull(fundCardSubjectDTO.getUseAmount(), ExecptionMessageEnum.PLEASE_ENTER_FUNDING_CARD_AMOUNT);
                            BusinessErrUtil.isTrue(!(orderSize > 1 && projectSize > 1), ExecptionMessageEnum.MULTI_ORDER_MULTI_CARD_NOT_SUPPORTED);
                            if (orderSize == 1) {
                                OrderMasterDO orderInfo = orderIdIdentityMap.get(orderIds.get(0));
                                // 输入使用金额的总数
                                BigDecimal inputUseMoneyAmount = CollectionUtils.isNotEmpty(fundCardSubjectList)
                                        ? saveProjectList.stream().map(FundCardProjectRequestDTO::getSaveFundCardList)
                                            .flatMap(List::stream).map(OrderFundCardRequestDTO::getSaveFundCardSubjectList)
                                            .flatMap(List::stream).filter(f -> f.getUseAmount() != null)
                                            .map(FundCardSubjectRequestDTO::getUseAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                        : BigDecimal.ZERO;
                                if (orderInfo != null) {
                                    BusinessErrUtil.isTrue(inputUseMoneyAmount.compareTo(orderInfo.getForderamounttotal().subtract(BigDecimal.valueOf(orderInfo.getReturnAmount()))) <= 0, ExecptionMessageEnum.INPUT_AMOUNT_EXCEEDS_ORDER_AMOUNT);
                                }
                            }

                        }
                        orderIds.forEach(orderId -> {
                            RefFundcardOrderDTO item = new RefFundcardOrderDTO();
                            item.setCardNo(orderFundCardDTO.getCardNo());
                            item.setCardId(orderFundCardDTO.getCardId());
                            item.setSubjectId(fundCardSubjectDTO.getId());

                            OrderMasterDO orderInfo = orderIdIdentityMap.get(orderId);
                            BigDecimal freezeAmount = (orderInfo != null ? orderInfo.getForderamounttotal().subtract(BigDecimal.valueOf(orderInfo.getReturnAmount())) : BigDecimal.ZERO);
                            item.setFreezeAmount(freezeAmount);
                            // 检验是否批量订单换同一张卡，是则每张卡的useMoney用订单冻结金额，否则直接用用户填写的useMoney
                            item.setUsemoney(orderSize > 1 ? freezeAmount : fundCardSubjectDTO.getUseAmount());
                            item.setOrderId(orderId.toString());
                            item.setExpenseApplyNo(fundCardSubjectDTO.getExpenseApplyNo());
                            fundCardOrderDTOList.add(item);
                        });
                    }
                }
            }
        }
    }

    private boolean isCustomWrapCard(List<Integer> orderIds, String orgCode, List<FundCardProjectRequestDTO> saveProjectList, Map<Integer, OrderMasterDO> orderIdIdentityMap, List<RefFundcardOrderDTO> fundCardOrderDTOList) {
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_ZHONG_LIU_YI_YUAN.getCode().equals(orgCode)) {
            // 这里卡id集合去重下，因为中肿同一个卡号会根据费用类型传两张一样的卡号过来
            Set<String> cardIdSet = saveProjectList.stream().map(FundCardProjectRequestDTO::getId).collect(Collectors.toSet());
            AtomicInteger serialNumber = new AtomicInteger(1);
            saveProjectList.stream().forEach(fundCardProjectDTO -> {
                int orderIdSize = orderIds.size();
                if (!cardIdSet.contains(fundCardProjectDTO.getId())) {
                    return;
                }
                for (int i = 0; i < orderIdSize; i++) {
                    RefFundcardOrderDTO item = new RefFundcardOrderDTO();
                    item.setProjectId(fundCardProjectDTO.getId());
                    item.setCardNo(fundCardProjectDTO.getProjectCode());
                    item.setCardId(fundCardProjectDTO.getId());
                    OrderMasterDO orderInfo = orderIdIdentityMap.get(orderIds.get(i));
                    item.setFreezeAmount(orderInfo != null ? orderInfo.getForderamounttotal().subtract(BigDecimal.valueOf(orderInfo.getReturnAmount())) : BigDecimal.ZERO);
                    item.setOrderId(orderIds.get(i).toString());
                    item.setFundType(fundCardProjectDTO.getFundType());
                    item.setSerialNumber(serialNumber.getAndIncrement());
                    fundCardOrderDTOList.add(item);
                }
                cardIdSet.remove(fundCardProjectDTO.getId());
            });
            return true;
        }
        return false;
    }

    @Override
    public List<OrderDetailFeeTypeVO> getOrderDetailFeeType(List<Integer> orderIdList) {
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return Collections.emptyList();
        }

        Map<Integer, Map<String, List<OrderDetailDO>>> orderIdFeeTagListMap = orderDetailList.stream().collect(Collectors.groupingBy(OrderDetailDO::getFmasterid, Collectors.groupingBy(OrderDetailDO::getFeeTypeTag)));
        List<OrderDetailFeeTypeVO> result = new ArrayList<>(orderIdFeeTagListMap.keySet().size());
        orderDetailList.stream().forEach(detail -> {
            OrderDetailFeeTypeVO item = new OrderDetailFeeTypeVO();
            item.setOrderId(detail.getFmasterid());
            item.setFeeTypeTag(detail.getFeeTypeTag());
            item.setCategoryTag(detail.getCategoryTag());
            item.setFeePrice(detail.getFbidprice());
            item.setQuantity(detail.getFquantity());
            item.setTotalPrice(detail.getFbidamount());
            result.add(item);
        });

        return result;
    }

    @Override
    public Integer orderFundCardFreeze(OrderMasterDO order, List<OrderDetailDO> orderDetailList, LoginUserInfoBO operator) {
        if (order == null) {
            return 0;
        }

        List<RefFundcardOrderDO> refFundCardOrderDOList = refFundcardOrderMapper.findByOrderId(order.getId().toString());
        if (CollectionUtils.isEmpty(refFundCardOrderDOList)) {
            return 0;
        }
        UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
        updated.setOrderId(order.getId());
        updated.setFundStatus(OrderFundStatusEnum.FROZE.getValue());
        List<DefrayDTO> request2 = new ArrayList<>();
        DefrayDTO defrayDTO = new DefrayDTO();
        defrayDTO.setSerialNumber(order.getForderno());
        defrayDTO.setExtraSerialNumber(order.getForderno());
        defrayDTO.setAppKey(Environment.getAppKey());
        defrayDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
        defrayDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
        defrayDTO.setUserId(order.getFbuyerid());
        defrayDTO.setBuyerUserId(order.getFbuyerid());
        // 订单实际支付金额
        BigDecimal defrayMoney = order.getForderamounttotal().subtract(BigDecimal.valueOf(order.getReturnAmount()));
        defrayDTO.setFreezeAmount(defrayMoney);
        defrayDTO.setOperateDate(new Date());
        defrayDTO.setOperatorName(operator.getUserName());
        defrayDTO.setOperatorJobNumber(operator.getJobNumber());

        List<String> cardIdList = refFundCardOrderDOList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        List<FundCardDTO> fundProjectList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(order.getFusercode(), cardIdList);
        BusinessErrUtil.notEmpty(fundProjectList, ExecptionMessageEnum.DEDUCTION_FAILED_NO_FUNDING_CARD);
        BusinessErrUtil.notEmpty(fundProjectList.get(0).getFundCardManagerDTOs(), ExecptionMessageEnum.DEDUCTION_FAILED_NO_TEAM_LEADER);
        FundCardManagerDTO fundCardManagerDTO = fundProjectList.get(0).getFundCardManagerDTOs().get(0);
        defrayDTO.setManagerJobNumber(fundCardManagerDTO.getManagerCode());
        defrayDTO.setManagerName(fundCardManagerDTO.getManagerName());
        List<ExtraDTO> extraList = orderDetailList.stream().map(it -> {
            ExtraDTO extraDTO = new ExtraDTO();
            extraDTO.setField(it.getFgoodname());
            extraDTO.setValue(it.getFbidamount().toPlainString());
            return extraDTO;
        }).collect(Collectors.toList());
        defrayDTO.setExtraDTOs(extraList);

        List<SelectFundCardDTO> fundCardDtoList = refFundCardOrderDOList.stream().map(ref -> RefFundcardOrderTranslator.refToSelectFundCardDto(ref, defrayMoney)).collect(Collectors.toList());
        defrayDTO.setSelectFundCardDTOS(fundCardDtoList);
        request2.add(defrayDTO);
        // 冻结经费卡 调用解冻结果
        FundCardResultDTO result = null;
        Integer handleResult = null;
        try {
            result = researchFundCardServiceClient.orderDefray(order.getFusercode(), request2);
            handleResult = result.getHandleResult();
        } catch (Exception e) {
            updated.setFailedReason(order.getForderno() + "冻结失败：" + e.getMessage());
            updated.setFundStatus(OrderFundStatusEnum.DEDUCT_FAILED.getValue());
            orderMasterMapper.updateOrderById(updated);
            return 0;
        }
        if (HandleResultEnum.UN_HANDLE.getCode().equals(handleResult)) {
            // 处理历史订单，未对接预算系统又调了解冻的订单叫无需解冻状态
            updated.setFundStatus(OrderFundStatusEnum.UN_FREEZE.value);
        } else if (FundCardAsyCallBackEnum.NO_NEED.getValue().equals(result.getRelyAsyCallback())) {
            // 是否依赖回调结果，依赖回调则将经费状态改为释放中，否则经费状态为释放成功
            updated.setFundStatus(OrderFundStatusEnum.Deducted.value);
        }
        if (updated.getFundStatus() != null) {
            // 更新订单的经费状态
            orderMasterMapper.updateOrderById(updated);
        }
        return 1;
    }

    @Override
    public Integer orderFundCardFreeze(OrderBasicParamDTO request) {
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        Integer orderId = request.getOrderId();
        Preconditions.notNull(orderId, "操作失败，orderId为空");
        OrderMasterDO order = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(order, ExecptionMessageEnum.OPERATION_FAILED_NO_ORDER_FOUND, orderId);
        List<OrderDetailDO> orderDetail = orderDetailMapper.findByFmasterid(orderId);
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());

        return this.orderFundCardFreeze(order, orderDetail, loginUserInfo);
    }

    @Override
    public int deleteByOrderIds(List<Integer> orderIdList) {
        Preconditions.notEmpty(orderIdList,"orderId must not be empty");
        Preconditions.isTrue(orderIdList.size() <= 200, "orderId length should be less than 200");
        List<String> orderIds = orderIdList.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.toList());
        return refFundcardOrderMapper.deleteByOrderIdIn(orderIds);
    }

    /**
     * 查询新表中有，旧表中没有的字段，并填充到DTO中
     * @param refFundCardOrderDTOS
     */
    @Override
    public void fillWithNewDTO(List<RefFundcardOrderDTO> refFundCardOrderDTOS) {
        if(CollectionUtils.isEmpty(refFundCardOrderDTOS)){
            return;
        }
        List<Integer> orderIdList = refFundCardOrderDTOS.stream().map(RefFundcardOrderDTO::getOrderId).map(Integer::parseInt).distinct().collect(toList());
        Map<Integer, Map<String, String>> orderExpenseApplyNoMap = New.mapWithCapacity(orderIdList.size());
        List<RefOrderFundCardDTO>  refOrderFundCardDTOList = refFundCardOrderClient.listInOrderId(orderIdList);
        if(CollectionUtils.isNotEmpty(refOrderFundCardDTOList)){
            // 关联每个订单的经费支出申请单号
            refOrderFundCardDTOList.forEach(refOrderFundCardDTO -> {
                if(StringUtils.isNotBlank(refOrderFundCardDTO.getExpenseApplyNo())){
                    Map<String, String> expenseApplyNoMap = orderExpenseApplyNoMap.computeIfAbsent(refOrderFundCardDTO.getOrderId(), k -> New.map());
                    expenseApplyNoMap.put(refOrderFundCardDTO.getFundCardId(), refOrderFundCardDTO.getExpenseApplyNo());
                }
            });
            refFundCardOrderDTOS.forEach(refFundcardOrderDTO -> {
                Map<String, String> expenseApplyNoMap = orderExpenseApplyNoMap.get(Integer.parseInt(refFundcardOrderDTO.getOrderId()));
                if(MapUtils.isNotEmpty(expenseApplyNoMap)){
                    refFundcardOrderDTO.setExpenseApplyNo(expenseApplyNoMap.get(RefFundcardOrderTranslator.getLastLevelCardId(refFundcardOrderDTO)));
                }
            });
        }
    }

    /**
     * 换卡结果
     */
    private class SaveCardResult{

        /**
         * 冻结
         */
        public final static int FREEZE_CARD = 1;

        /**
         * 换卡
         */
        public final static int CHANGE_CARD = 2;

        /**
         * tpi的返回结果
         */
        private FundCardResultDTO fundCardResultDTO;

        /**
         * 换卡模式， FREEZE_CARD 或 CHANGE_CARD
         */
        private int saveMode;

        public SaveCardResult(FundCardResultDTO fundCardResultDTO, int saveMode) {
            this.fundCardResultDTO = fundCardResultDTO;
            this.saveMode = saveMode;
        }

        public FundCardResultDTO getFundCardResultDTO() {
            return fundCardResultDTO;
        }

        public int getSaveMode() {
            return saveMode;
        }
    }
}
