package com.ruijing.store.order.other.service;

import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 商品库存处理
 * @Date: 2024/06/20 15:20
 **/
public interface ProductStockService {

    /**
     * 订单商品返回库存
     * @param suppId        供应商id
     * @param orderMasterDO 订单DO
     */
    boolean addSku(Integer suppId, OrderMasterDO orderMasterDO);

    /**
     * 完成退货的商品
     * @param suppId 供应商id
     * @param completeReturnGoods 完成退货商品
     * @param orderMasterDO 订单DO
     * @return 完成退货是否成功
     */
    boolean addSku(Integer suppId, List<GoodsReturnInfoDetailVO> completeReturnGoods, String returnNo, OrderMasterDO orderMasterDO);

}
