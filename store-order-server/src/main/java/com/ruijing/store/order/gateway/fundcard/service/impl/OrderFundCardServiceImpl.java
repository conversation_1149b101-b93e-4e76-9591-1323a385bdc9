package com.ruijing.store.order.gateway.fundcard.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.log.enums.OrderDockingOperationEnum;
import com.reagent.order.base.log.enums.OrderDockingResultEnum;
import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.order.dto.OrderDockingNumberDTO;
import com.reagent.order.dto.inner.buyer.FetchDataResponseItemDTO;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.reagent.order.enums.DockingDataTypeEnum;
import com.reagent.order.enums.DockingNumberTypeEnum;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.research.api.dto.CallbackRequest;
import com.reagent.research.api.dto.ExtraDTO;
import com.reagent.research.api.dto.OrgRequest;
import com.reagent.research.fundcard.dto.FundCardResultDTO;
import com.reagent.research.fundcard.dto.SupplierDTO;
import com.reagent.research.fundcard.dto.UnfreezeCallbackResult;
import com.reagent.research.fundcard.dto.UnfreezeDTO;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.SelectFundCardDTO;
import com.reagent.research.fundcard.enums.*;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.reagent.tpi.tpiclient.api.order.enums.TPIClientOrderStatusEnum;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.docking.dto.DockingExtraDTO;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.*;
import com.ruijing.store.order.api.base.other.service.OrderRelatedRPCService;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderFundCardCacheTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.base.util.FundCardUtils;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;
import com.ruijing.store.order.business.service.RiskRuleService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.*;
import com.ruijing.store.order.gateway.fundcard.request.FundCardSpecialRequestDTO;
import com.ruijing.store.order.gateway.fundcard.request.OrderFundCardListParam;
import com.ruijing.store.order.gateway.fundcard.request.OrderFundCardParam;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardUnfreezeService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.order.service.ResearchBaseService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.supp.include.api.dto.OrgSuppSynStatusDataDTO;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Date: 2021/5/21 12:48
 * @Description: 经费卡换卡入参DTO
 */
@MSharpService
public class OrderFundCardServiceImpl implements OrderFundCardService {

    /**
     * 透传到TPI，作为改为自结算的标识的KEY
     */
    private final String CHANGE_TO_SELF_STATEMENT_EXTRA_KEY = "changeToSelfStatement";

    /**
     * 操作人
     */
    private final String OPERATE_USER_ID = "operateUserId";

    /**
     * 失败原因最大长度
     */
    private static final int FAIL_REASON_LENGTH_LIMIT = 500;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderFundCardServiceImpl.class);

    /**
     * 对接、通过推送订单相关数据的方式在对方平台解冻的订单（非传统通过预算那边调用）
     */
    private final List<String> ORG_CODE_UN_FREEZE_BY_DOCKING_PUSH = New.list(OrgConst.NING_BO_ER_YUAN);

    @Resource
    private OrderMasterMapper orderMasterMapper;
    
    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private OrderFundCardCacheClient orderFundCardCacheClient;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private ResearchBaseService researchBaseService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private ApplicationBaseService applicationBaseService;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private TPIOrderClient tpiOrderClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private OrderPushEventStatusClient orderPushEventStatusClient;

    @Resource
    private CategoryDirectoryClient categoryDirectoryClient;

    @Resource
    RiskRuleService riskRuleService;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderRelatedRPCService orderRelatedRPCService;


    @Resource
    private RefFundCardOrderClient refFundCardOrderClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderFundCardUnfreezeService orderFundCardUnfreezeService;

    @Resource
    private OrderDockingNumberRpcClient orderDockingNumberRpcClient;

    @Resource
    private FetchOrderDockingDataServiceClient fetchOrderDockingDataServiceClient;

    /**
     * 保存修改经费卡缓存的信息
     *
     * @param request
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse saveFundCardCache(FundCardSpecialRequestDTO request) {
        // 校验入参
        List<OrderFundCardParam> orderFundCardParams = request.getOrderFundCardParams();
        Preconditions.notEmpty(orderFundCardParams, "修改经费卡失败，修改的经费卡为空");
        OrderFundCardParam missingParamFundCard = orderFundCardParams.stream()
                .filter(p -> (p.getProjectId() == null) || (p.getOrderId() == null)).findFirst()
                .orElse(null);

        BusinessErrUtil.isTrue(Objects.isNull(missingParamFundCard), ExecptionMessageEnum.MODIFY_FUNDING_CARD_FAILED);
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        // 查询OMS经费卡配置层级
        String fundCardLevel = sysConfigClient.getConfigByOrgCodeAndConfigCode(loginUserInfo.getOrgCode(), ConfigConstant.RESEARCH_FUNDCARD_LEVEL);
        List<OrderFundCardCacheRequestDTO> fundCardList = OrderFundCardCacheTranslator.orderFundCardParamsToDTO(orderFundCardParams, fundCardLevel);
        List<RefFundcardOrderDTO> refFundCardOrderDTO = OrderFundCardCacheTranslator.orderFundCardParamsToRefDTO(orderFundCardParams, fundCardLevel);
        // 1.检验分类
        OrderOperatorDTO operatorDTO = new OrderOperatorDTO();
        operatorDTO.setOrgCode(loginUserInfo.getOrgCode());
        refFundcardOrderService.validateFundCard(refFundCardOrderDTO, operatorDTO, null, null);
        // 2.余额校验 另外设置每张卡的冻结金额（订单总金额 - 退货金额）
        List<Integer> orderIdList = ListUtils.toList(fundCardList, OrderFundCardCacheRequestDTO::getOrderId);
        List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByIdIn(orderIdList);
        Map<Integer, OrderMasterDO> orderIdToOrderMasterMap = orderMasterDOList.stream().collect(Collectors.toMap(OrderMasterDO::getId, Function.identity()));
        this.checkFundCardSufficient(fundCardList, orderMasterDOList);
        try {
            // 3.判断是不是第一次改卡，是就把新旧卡都推进缓存表，否则只存新卡
            cacheClient.lockRetry(OrderOperationConstant.ORDER_APPROVAL_CHANGE_FUND_CARD + orderIdList, 10);
            // 3.1 查询经费卡缓存数据 注：是每个订单中最新的一条缓存记录
            List<OrderFundCardDTO> fundCardCache = orderFundCardCacheClient.findOrderFundCardCache(orderIdList);
            Map<Integer, List<OrderFundCardDTO>> orderIdCacheMap = DictionaryUtils.groupBy(fundCardCache, OrderFundCardDTO::getOrderId);
            // 没换卡操作之前绑定的经费卡(已冻结)
            List<String> orderIdString = orderIdList.stream().map(String::valueOf).collect(Collectors.toList());
            List<RefFundcardOrderDTO> oldFundCardList = refFundcardOrderService.findByOrderId(orderIdString);
            Map<String, List<RefFundcardOrderDTO>> oldFundCardMap = DictionaryUtils.groupBy(oldFundCardList, RefFundcardOrderDTO::getOrderId);
            // 3.2 换卡时对新卡序列 + 1
            List<OrderFundCardDTO> finalFundCardList = new ArrayList<>();
            fundCardList.forEach(
                    changeCard -> {
                        // 上一张卡id(A更换B，这里指A)
                        String lastCardId = null;
                        // 如果该订单没有换过卡，那么缓存中将不存在数据，则从从t_ref_fundcard_order取记录(当前绑定的经费卡)
                        if (!orderIdCacheMap.containsKey(changeCard.getOrderId())) {
                            List<OrderFundCardDTO> oldFundCardListDto = OrderFundCardCacheTranslator.refToCacheDto(oldFundCardMap.get(changeCard.getOrderId().toString()));
                            if (CollectionUtils.isNotEmpty(oldFundCardListDto)) {
                                // 目前采购金额统计换卡业务只有暨大且只绑定单张卡
                                lastCardId = oldFundCardListDto.get(0).getFundCardId();
                                finalFundCardList.addAll(oldFundCardListDto);
                            }
                        } else {
                            // 获取该订单之前的换卡次数+1
                            OrderFundCardDTO oldCardCache = orderIdCacheMap.get(changeCard.getOrderId()).get(0);
                            lastCardId = oldCardCache.getFundCardId();
                            changeCard.setSequence(oldCardCache.getSequence() + 1);
                        }
                        // 采购金额管控当前卡加金额(A换B，这里指的是B) 注:加金额放减金额前面 无批量接口....
                        applicationBaseService.updateApplyManageProductUsage(orderIdToOrderMasterMap.get(changeCard.getOrderId()), ApplyManageOperationEnum.ADD_PURCHASE_APPLY.getValue(), changeCard.getFundCardId(), null);
                        // 采购金额管控将上张卡减金额(A换B，这里指的是A)
                        applicationBaseService.updateApplyManageProductUsage(orderIdToOrderMasterMap.get(changeCard.getOrderId()), ApplyManageOperationEnum.UNFREEZE_FUND.getValue(), lastCardId, null);
                        // 放入当前更换卡
                        finalFundCardList.add(OrderFundCardCacheTranslator.dtoToCacheDto(changeCard));
                    });

            orderFundCardCacheClient.saveFundCardCache(finalFundCardList);
        } catch (Exception e) {
            throw new BusinessInterceptException(ExecptionMessageEnum.APPROVAL_CARD_REPLACEMENT_FAILED, e.getMessage());
        } finally {
            cacheClient.removeCache(OrderOperationConstant.ORDER_APPROVAL_CHANGE_FUND_CARD + orderIdList);
        }

        for (Integer orderId : orderIdList) {
            orderApprovalLogService.createOrderOperateLog(orderId, OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD.getValue(), loginUserInfo.getUserId(), loginUserInfo.getUserName(), OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD.getName());
        }

        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 检查经费是否充足：会逐一检查order和经费卡是否存在，对比需要冻结的金额和经费卡余额
     *
     * @param fundCardCacheList
     */
    private void checkFundCardSufficient(List<OrderFundCardCacheRequestDTO> fundCardCacheList, List<OrderMasterDO> orderMasterDOList) {
        // 1.筛选出旧单
        orderMasterDOList.forEach(orderMasterDO -> {
            String orgCode = orderMasterDO.getFusercode();
            boolean isNewOrder = researchBaseService.isNewOrder(orderMasterDO, OrderDateConstant.ORG_CODE_OLD_ORDER_DATE_CHANGE_CARD_MAP.get(orgCode));
            Preconditions.isTrue(isNewOrder, "旧订单" + orderMasterDO.getForderno() + "不支持换卡!");
        });

        // 2.获取经费卡list
        // 获取当前用户orgCode
        RjSessionInfo sessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment("RJ_SESSION_INFO");
        Integer orgId = sessionInfo.getOrgId();
        String orgCode = null;
        try {
            orgCode = OrgEnum.getOrgEnumById(orgId).getCode();
        } catch (Exception e) {
            OrganizationDTO organizationDTO = userClient.getOrgById(orgId);
            Preconditions.notNull(organizationDTO, "根据orgId:" + orgId + "获取机构信息为空！");
            orgCode = organizationDTO.getCode();
        }

        List<String> fundCardIdList = ListUtils.toList(fundCardCacheList, OrderFundCardCacheRequestDTO::getFundCardId);
        // 根据经费卡id获取对应的经费卡
        List<FundCardDTO> fundCardList =
                researchFundCardServiceClient.getFundCardListByCardIds(fundCardIdList, orgCode);

        // 3.逐一判断经费卡余额是否足够支付订单需要冻结的金额
        // 订单id 映射对应 订单
        Map<Integer, OrderMasterDO> orderMasterDOMap = orderMasterDOList.stream()
                .collect(Collectors.toMap(OrderMasterDO::getId, singleOrder -> singleOrder, (orderOne, orderTwo) -> orderOne));

        // 经费卡id 映射对应 经费卡
        Map<String, FundCardDTO> orderFundCardMap = fundCardList.stream()
                .collect(Collectors.toMap(FundCardDTO::getId, singleFunCard -> singleFunCard, (funCardOne, funCardTwo) -> funCardOne));

        // fundCardCache 可找到对应经费卡，也可找到对应订单,依次对比金额
        fundCardCacheList.forEach(fundCardCache -> {
            // 校验FundCardDTO是否存在
            FundCardDTO fundCardDTO = orderFundCardMap.get(fundCardCache.getFundCardId());
            BusinessErrUtil.notNull(fundCardDTO, ExecptionMessageEnum.FUND_CARD_NOT_EXIST, fundCardCache.getId());

            // 校验order是否存在
            OrderMasterDO orderMasterDO = orderMasterDOMap.get(fundCardCache.getOrderId());
            Preconditions.notNull(orderMasterDO, "订单" + fundCardCache.getOrderId() + "不存在，请联系管理员！");

            // 订单总价格
            BigDecimal forderamounttotal = orderMasterDO.getForderamounttotal();
            // 退货金额
            Double returnAmount = orderMasterDO.getReturnAmount();
            // 需要冻结的金额
            BigDecimal freezeAmount = returnAmount == null ? forderamounttotal : forderamounttotal.subtract(new BigDecimal(returnAmount));
            researchFundCardServiceClient.checkFundCardSufficient(fundCardDTO, freezeAmount);
            // 如果校验通过，则设置冻结金额
            fundCardCache.setFreezeAmount(freezeAmount);
        });
    }

    private void orderFundCardUnFreeze(OrderMasterDO orderMasterDO) {
        orderFundCardUnFreeze(orderMasterDO, null);
    }

    @Override
    @ServiceLog(description = "经费卡解冻", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean unfreezeFundCard(OrderBasicParamDTO request) {
        Integer orderId = request.getOrderId();
        Preconditions.notNull(orderId, "orderId must not be null");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        // 中大附一，结算中不可解冻(放在这里为了只控制用户不能解冻）
        boolean cannotUnfreeze = orderMasterDO.getFuserid() == OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() && OrderStatusEnum.Statementing_1.getValue().equals(orderMasterDO.getStatus());
        BusinessErrUtil.isTrue(!cannotUnfreeze, ExecptionMessageEnum.ORDER_SUBMITTED_SETTLEMENT_NO_UNFREEZE);
        this.orderFundCardUnFreeze(orderMasterDO);
        return true;
    }

    @Override
    @ServiceLog(description = "重新解冻经费", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public boolean reUnfreezeFundCard(OrderBasicParamDTO request) {
        Integer orderId = request.getOrderId();
        Preconditions.notNull(orderId, "orderId must not be null");
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        // 重新解冻按钮-整单退货且为解冻失败时才能重新解冻
        boolean isAllReturn = CommonValueUtils.parseNumberStrToBoolean(
                sysConfigClient.getConfigByOrgCodeAndConfigCode(orderMasterDO.getFusercode(), ConfigCodeEnum.ORDER_RETURN_ONLY_WHOLE.name()));
        boolean showReUnfreeze = isAllReturn && OrderFundStatusEnum.ThrawFailed.value.equals(orderMasterDO.getFundStatus());
        BusinessErrUtil.isTrue(isAllReturn && showReUnfreeze, ExecptionMessageEnum.REFREEZE_SUPPORTS_FULL_RETURN);
        this.orderFundCardUnFreeze(orderMasterDO);
        return true;
    }

    @Override
    public void orderFundCardUnFreeze(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO){
        orderFundCardUnFreeze(orderMasterDO, unFreezeRequestDTO, true);
    }

    /**
     * 订单解冻经费卡
     *
     * @param orderMasterDO
     */
    @Override
    public void orderFundCardUnFreeze(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO, boolean callInterfaceFlag) {
        String orgCode = orderMasterDO.getFusercode();
        Integer orderId = orderMasterDO.getId();
        Preconditions.notNull(orgCode, "组织code参数为null");
        // 是否退货完成（非第一次退货，但退货后订单关闭也包含在内）
        boolean isAllReturn = unFreezeRequestDTO != null && OrderUnFreezeTypeEnum.RETURN == unFreezeRequestDTO.getOrderUnFreezeTypeEnum();

        // 获取订单绑卡数据
        List<RefFundcardOrderDO> refFundCardList = refFundcardOrderMapper.findByOrderId(orderId.toString());
        // 退货前置处理
        this.unfreezePreHandle(orderMasterDO, unFreezeRequestDTO, refFundCardList);

        Supplier<ListenableFuture<?>> removeFunction = () ->
                // 异步删除新待结算单信息,当且仅当是整单退货且为订单在待结算状态的时候。待结算换卡(解绑)和取消订单不在此行列中
                AsyncExecutor.listenableRunAsync(() -> {
                    // 如果是新结算单位，要删除待结算单信息
                    if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus()) && isAllReturn) {
                        statementPlatformClient.deleteWaitingStatementByOrderId(Collections.singletonList(orderId));
                    }
                }).addFailureCallback(throwable -> {
                    LOGGER.error("整单退货删除待结算单信息失败！", throwable);
                    Cat.logError(throwable);
                });

        if (this.disposeUnFreezeOldOrder(orderMasterDO) || ORG_CODE_UN_FREEZE_BY_DOCKING_PUSH.contains(orgCode)) {
            // 通过财务未对接前的旧单/对接了管理平台，管理平台自己解冻的单位不调用解冻接口
            removeFunction.get();
            return;
        }

        if(this.judgeIfNotFundDocking(orderMasterDO,refFundCardList) || BooleanUtils.isFalse(callInterfaceFlag)){
            // 如果是非经费对接（即无需调用经费卡的解冻）的单据，则仅需要删除绑卡数据，重置待结算数据即可
            LOGGER.info("订单解绑经费卡，删除绑卡记录DO: {}", JsonUtils.toJson(refFundCardList));
            refFundcardOrderMapper.deleteByOrderId(orderId.toString());
            if(OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus()) && !isAllReturn){
                // 整单退货将删除待结算数据
                this.clearWaitingStatementFundCardData(orderId);
            }
        } else {
            // 不是非对接单据，调用经费卡接口
            orderFundCardUnFreezeCore(orderMasterDO, refFundCardList, unFreezeRequestDTO, orgCode);
        }
        
        // 解冻时贵医大订单对接医院需要推送作废单信息
        if (OrgEnum.GUI_ZHOU_YI_KE_DA_XUE.getCode().equals(orderMasterDO.getFusercode())) {
            updateThirdPartyOrder(orderMasterDO);
        }

        removeFunction.get();
    }

    /**
     * 退货前置任务
     * @param orderMasterDO 订单数据
     * @param unFreezeRequestDTO 解冻数据
     * @param refFundCardList 经费数据
     */
    private void unfreezePreHandle(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO, List<RefFundcardOrderDO> refFundCardList){
        // 是否退货操作
        boolean isReturn = unFreezeRequestDTO != null
                && (unFreezeRequestDTO.getOrderUnFreezeTypeEnum() == OrderUnFreezeTypeEnum.RETURN || unFreezeRequestDTO.getOrderUnFreezeTypeEnum() == OrderUnFreezeTypeEnum.RETURN_PARTIALLY);

        // 是否发起的部分退货
        boolean partReturn = isReturn && orderMasterDO.getForderamounttotal().compareTo(unFreezeRequestDTO.getFreezeAmount()) != 0;

        Integer orderId = orderMasterDO.getId();
        List<CompletableFuture<?>> preHandleBeforeUnfreeze = New.list();
        // 1.经费授权审批金额返还
        if(refFundCardList.size() == 1){
            preHandleBeforeUnfreeze.add(AsyncExecutor.listenableRunAsync(()->{
                // 部分退货，用退货id做返还单号，否则都用订单号，让经费卡做幂等处理
                BigDecimal changeAmount = partReturn ? unFreezeRequestDTO.getFreezeAmount() : orderMasterDO.getForderamounttotal();
                String returnNo = partReturn ? unFreezeRequestDTO.getReturnId() : orderMasterDO.getForderno();
                researchFundCardServiceClient.reduceAuthUsedApproveAmount(orderMasterDO, RefFundcardOrderTranslator.getLastLevelCardId(refFundCardList.get(0)), changeAmount, returnNo);
            }).addFailureCallback(throwable -> {
                LOGGER.error("返还授权审批额度失败,orderId:{},unFreezeRequestDTO:{}", orderId, unFreezeRequestDTO, throwable);
                Cat.logError(throwable);
            }).completable());
        }
        if (!partReturn || OrderStatusEnum.Close.getValue().equals(orderMasterDO.getStatus())) {
            // 2.政采目录返还
            // 非部分退货都调用释放政采目录接口
            // 两种情况：unFreezeRequestDTO为空，即非退货操作。或者不为空且订单状态为关闭，即全部退货完成。这时候才释放政采统计
            preHandleBeforeUnfreeze.add(
                    AsyncExecutor.listenableRunAsync(()->categoryDirectoryClient.cancelOrderStatistics(orderMasterDO.getFuserid(), orderId))
                            .addFailureCallback(throwable -> {
                                LOGGER.error("释放政采目录失败,orderId:{},unFreezeRequestDTO:{}", orderId, unFreezeRequestDTO, throwable);
                                Cat.logError(throwable);
                            }).completable());
        }
        // unFreezeRequestDTO 不为空则为退货
        if (isReturn){
            // 3.风控规则限额管控返还
            preHandleBeforeUnfreeze.add(AsyncExecutor.listenableRunAsync(()->{
                Preconditions.isTrue(StringUtils.isNotEmpty(unFreezeRequestDTO.getReturnId()), "退货单Id为空");
                // 退货完成，归还竞价和采购限额
                GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(Integer.valueOf(unFreezeRequestDTO.getReturnId()));
                String returnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
                List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = GoodsReturnTranslator.parseJSONToInfoDetailVO(returnDetailJSON);
                List<Integer> orderDetailList = goodsReturnInfoDetailVOS.stream().map(
                        goodsReturnInfoDetailVO -> Integer.parseInt(goodsReturnInfoDetailVO.getDetailId())
                ).collect(Collectors.toList());
                List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByIdIn(orderDetailList);
                riskRuleService.amountLimitBackForGoodsReturn(goodsReturnInfoDetailVOS, orderDetailDOList, orderMasterDO);
            }).addFailureCallback(throwable -> {
                LOGGER.error("风控规则限额管控返还失败,orderId:{},unFreezeRequestDTO:{}", orderId, unFreezeRequestDTO, throwable);
                Cat.logError(throwable);
            }).completable());
        }
        if(!preHandleBeforeUnfreeze.isEmpty()){
            CompletableFuture.allOf(preHandleBeforeUnfreeze.toArray(new CompletableFuture[]{}))
                    .exceptionally(throwable -> {
                        LOGGER.error("退货前置任务执行失败,orderId:{},unFreezeRequestDTO:{}", orderId, unFreezeRequestDTO, throwable);
                        return null;
                    }).join();
        }
    }

    /**
     * 判断是否非对接单的解冻
     * @param orderMasterDO 订单数据
     * @param refFundCardList 经费卡数据
     * @return 是否不调用经费卡解冻，仅处理订单绑卡数据
     */
    private boolean judgeIfNotFundDocking(OrderMasterDO orderMasterDO, List<RefFundcardOrderDO> refFundCardList){
        String orgCode = orderMasterDO.getFusercode();
        Integer orderId = orderMasterDO.getId();
        // 中大附一
        if(OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orderMasterDO.getFusercode())){
            if(DockingConstant.NOT_FREEZE_ORDER_ID_LIST.contains(orderMasterDO.getId())){
                return true;
            }
            // 经费状态是未冻结
            if(OrderFundStatusEnum.UN_FREEZE.getValue().equals(orderMasterDO.getFundStatus())){
                // 中大附一，绑卡时间在这个时间之前的且经费状态为未冻结的，仅做删卡处理
                Date versionTime = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, "2022-06-14 18:23:41");
                if(refFundCardList.stream().allMatch(refFundcardOrderDO -> refFundcardOrderDO.getCreationTime().before(versionTime))){
                    return true;
                }
                // 如果含服务类商品，则为非对接单，不调用经费卡接口
                List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
                boolean existServiceProduct = orderDetailDOList.stream().anyMatch(d -> CategoryConstant.SCIENCE_SERVICE_ID.equals(d.getFirstCategoryId()));
                if(existServiceProduct){
                    return true;
                }
                // 如果含非对接卡，也为非对接单
                List<String> cardIdList = refFundCardList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(toList());
                List<FundCardDTO> currentLevelCards = researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(orgCode, cardIdList);
                List<FundCardDTO> allLevelFundCardList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
                Map<String, FundCardDTO> allLevelCardIdCardMap = FundCardUtils.getAllLevelCardIdCardMap(allLevelFundCardList);
                for(FundCardDTO card : currentLevelCards){;
                    FundCardDTO temp = card;
                    FundCardDTO projectCard = temp;
                    while (temp != null){
                        // 找到最上级的那张卡
                        projectCard = temp;
                        temp = allLevelCardIdCardMap.get(temp.getParentId());
                    }
                    if(projectCard != null && projectCard.getCode() != null && projectCard.getCode().startsWith("Q")){
                        return true;
                    }
                }
                return currentLevelCards.stream().anyMatch(card-> FundTypeEnum.NOT_FINANCIAL.getValue() == card.getFundType());
            }
        }
//        // 判断结算方式是否配置了不使用财务系统
//        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.STATEMENT_WAY_ENABLE_FUND_DOCKING.getValue());
//        return CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.FALSE_NUMBER_STR.equals(orderExtraDTOList.get(0).getExtraValue());

        return false;
    }

    /**
     * 处理之前未对接财务的旧单
     *
     * @param orderMasterDO
     * @return
     */
    private boolean disposeUnFreezeOldOrder(OrderMasterDO orderMasterDO) {
        String orgCode = orderMasterDO.getFusercode();
        String oldOrderDate = OrderDateConstant.ORG_CODE_OLD_ORDER_DATE_UNFREEZE_MAP.get(orgCode);
        if (oldOrderDate != null) {
            return orderMasterDO.getForderdate().before(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, oldOrderDate));
        }
        return false;
    }

    /**
     * 经费解冻核心接口
     *
     * @param orderMasterDO      订单信息
     * @param unFreezeRequestDTO g
     * @param orgCode            机构编码
     */
    private void orderFundCardUnFreezeCore(OrderMasterDO orderMasterDO, List<RefFundcardOrderDO> refFundCardList, OrderUnFreezeRequestDTO unFreezeRequestDTO, String orgCode) {
        // 有绑卡数据，且冻结成功（换卡成功）才去解冻
        if (CollectionUtils.isNotEmpty(refFundCardList)
                && (OrderFundStatusEnum.Freezed.getValue().equals(orderMasterDO.getFundStatus())
                || OrderFundStatusEnum.ThrawFailed.getValue().equals(orderMasterDO.getFundStatus())
                || OrderFundStatusEnum.ChangedCardSuccess.getValue().equals(orderMasterDO.getFundStatus())
                || OrderFundStatusEnum.ChangedCardFail.getValue().equals(orderMasterDO.getFundStatus()))) {
            // 根据机构编码判断是否新旧预算单位
            boolean newBudgetByOrgCode = researchFundCardServiceClient.isNewBudgetByOrgCode(orgCode);

            // 只有上海九院，中山六院，暨南大学、江西中医药大学和新对接预算的单位，才会去调用经费实时扣减并解冻的预算接口
            if ((OrgConst.SHANG_HAI_JIU_YUAN.equals(orgCode) || OrgConst.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.equals(orgCode)
                    || OrgConst.JI_NAN_DA_XUE.equals(orgCode) || OrgConst.JIANG_XI_ZHONG_YI_YAO_DA_XUE.equals(orgCode) || newBudgetByOrgCode)) {
                // 获取冻结编号
                DockingExtraDTO dockingRequest = new DockingExtraDTO();
                String orderNo = orderMasterDO.getForderno();
                OrganizationDTO organization = userClient.getOrgById(orderMasterDO.getFuserid());
                dockingRequest.setInfo(orderNo);
                List<DockingExtraDTO> dockingExtra = dockingExtraService.findDockingExtra(dockingRequest);
                String dockingInfo = null;
                // 如果该医院的冻结信息不为空，则设置冻结编号
                if (CollectionUtils.isNotEmpty(dockingExtra)) {
                    dockingInfo = dockingExtra.get(0).getExtraInfo();
                }

                OrgRequest<UnfreezeDTO> orgRequest = new OrgRequest<>();
                orgRequest.setOrgCode(organization.getCode());
                UnfreezeDTO unfreezeDTO = new UnfreezeDTO();
                unfreezeDTO.setSerialNumber(orderNo);
                unfreezeDTO.setAppKey(Environment.getAppKey());
                unfreezeDTO.setExtraSerialNumber(dockingInfo);
                unfreezeDTO.setSourceType(SourceTypeEnum.ORDER.getValue());
                unfreezeDTO.setBusinessType(BusinessTypeEnum.BUY.getValue());
                Integer buyserId = orderMasterDO.getFbuyerid();
                unfreezeDTO.setUserId(buyserId);
                unfreezeDTO.setBuyerUserId(buyserId);
                unfreezeDTO.setOperateDate(new Date());
                unfreezeDTO.setFreezeAmount(orderMasterDO.getForderamounttotal());
                unfreezeDTO.setSpecies(orderMasterDO.getSpecies().intValue());
                // 返查下操作人信息
                UserBaseInfoDTO operator = userClient.getUserInfo(buyserId, organization.getId());
                unfreezeDTO.setOperatorJobNumber(operator.getJobnumber());
                unfreezeDTO.setOperatorName(operator.getName());

                List<DepartmentDTO> departmentDTOS = departmentRpcClient.getDepartmentsByIds(Arrays.asList(orderMasterDO.getFbuydepartmentid().longValue()));
                Integer managerId = departmentDTOS.get(0).getManagerId();
                if (Objects.nonNull(managerId)) {
                    List<UserBaseInfoDTO> userBaseInfoDTOS = userClient.getUserByIdsAndOrgId(Arrays.asList(managerId), orderMasterDO.getFuserid());
                    UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOS.get(0);
                    unfreezeDTO.setManagerJobNumber(userBaseInfoDTO.getJobnumber());
                    unfreezeDTO.setManagerName(userBaseInfoDTO.getName());
                }

                // 中山八院冻结需要传供应商和商品信息
                if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_BA_YI_YUAN.getCode().equals(orgCode)){
                    SupplierDTO supplierDTO = new SupplierDTO();
                    supplierDTO.setSupplierCode(orderMasterDO.getFsuppcode());
                    supplierDTO.setSupplierName(orderMasterDO.getFsuppname());
                    unfreezeDTO.setSupplierDTO(supplierDTO);
                }
                List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
                // 忽略退货：目前用到该参数的只有中山八院，他是对接单位，没有部分退货的说法，而计算金额用到了这个对象。后续有单位用到部分退货的，或是需要用到商品信息的，需谨慎重构此处代码！！！
                List<com.reagent.research.fundcard.dto.OrderDetailDTO> fundCardOrderDetailList = OrderDetailTranslator.orderDetail2FundCardOrderDetail(orderDetailDOList, true);
                unfreezeDTO.setOrderDetailDTOs(fundCardOrderDetailList);
                if(OrgEnum.HUA_ZHONG_KE_JI_DA_XUE_XIE_HE_SHEN_ZHEN_YI_YUAN.getValue() == orderMasterDO.getFuserid()){
                    // 华中科技大学，商品级别的冻结单号
                    List<String> detailIdStrList = orderDetailDOList.stream().map(d->d.getId().toString()).collect(toList());
                    List<OrderDockingNumberDTO> orderDockingNumberDTOList = orderDockingNumberRpcClient.listByParam(detailIdStrList, DockingNumberTypeEnum.DETAIL_FINANCIAL);
                    if(CollectionUtils.isNotEmpty(orderDockingNumberDTOList)){
                        Map<String, String> detailIdDockingNumberMap = DictionaryUtils.toMap(orderDockingNumberDTOList, OrderDockingNumberDTO::getOrderNo, OrderDockingNumberDTO::getDockingNumber);
                        for(com.reagent.research.fundcard.dto.OrderDetailDTO fundCardOrderDetail : fundCardOrderDetailList){
                            fundCardOrderDetail.getExtraDTOs().add(new ExtraDTO("extraFreezeId", detailIdDockingNumberMap.get(fundCardOrderDetail.getId())));
                        }
                    }
                }

                unfreezeDTO.setExtraDTOs(this.getUnfreezeExtraDTO(orderMasterDO, unFreezeRequestDTO));

                // 退货金额
                BigDecimal returnFreezeAmount = orderMasterDO.getForderamounttotal();
                // 订单是否取消，全部退货而关闭
                boolean isOrderClosed = true;
                if (unFreezeRequestDTO != null) {
                    // 如果是退货操作，则设置解冻金额为退货金额
                    returnFreezeAmount = unFreezeRequestDTO.getFreezeAmount();
                    BusinessErrUtil.notNull(returnFreezeAmount, ExecptionMessageEnum.FUND_UNFREEZE_FAILED);
                    unfreezeDTO.setFreezeAmount(returnFreezeAmount);
                    unfreezeDTO.setReturnSerialNumber(unFreezeRequestDTO.getReturnId());
                    isOrderClosed = OrderStatusEnum.Close.getValue().equals(orderMasterDO.getStatus());
                }

                // 目前经费卡进行了对接或允许使用多经费卡的单位，只允许整单退货。部分退货的单位只允许使用一张经费卡。
                // 部分退货的时候，由于只用一张经费卡 退货金额即传入的金额。但如果整单退货或者取消订单，使用这张卡的冻结金额作为解冻金额。防止A B C三张卡各冻了一部分金额，但解冻解每张卡的金额为商品总额问题
                final BigDecimal finalReturnFreezeAmount = returnFreezeAmount;
                final OrderUnFreezeTypeEnum orderUnFreezeTypeEnum = Objects.nonNull(unFreezeRequestDTO) ? unFreezeRequestDTO.getOrderUnFreezeTypeEnum() : null;
                List<FundCardDTO> fundCardDTOList = refFundCardList.stream().map(ref -> {
                    FundCardDTO fundCardDTO = RefFundcardOrderTranslator.refToFundCardDto(ref, finalReturnFreezeAmount);
                    if (!OrderUnFreezeTypeEnum.RETURN_PARTIALLY.equals(orderUnFreezeTypeEnum)) {
                        fundCardDTO.setFreezeAmount(ref.getFreezeAmount());
                    }
                    return fundCardDTO;
                }).collect(toList());
                unfreezeDTO.setFundCardDTOs(fundCardDTOList);

                List<RefFundcardOrderDTO> refFundcardOrderDTOList = refFundCardList.stream().map(RefFundcardOrderTranslator::doToDto).collect(toList());
                refFundcardOrderService.fillWithNewDTO(refFundcardOrderDTOList);
                unfreezeDTO.setSelectFundCardDTOS(refFundcardOrderDTOList.stream().map(ref -> {
                    SelectFundCardDTO selectFundCardDTO = RefFundcardOrderTranslator.refToSelectFundCardDto(ref, finalReturnFreezeAmount);
                    if (!OrderUnFreezeTypeEnum.RETURN_PARTIALLY.equals(orderUnFreezeTypeEnum)) {
                        selectFundCardDTO.setFreezeAmount(ref.getFreezeAmount());
                    }
                    return selectFundCardDTO;
                }).collect(toList()));
                orgRequest.setData(unfreezeDTO);

                this.orderUnFreezeRefreshStatus(orderMasterDO, orgRequest, isOrderClosed);
            }
        }
    }

    private List<ExtraDTO> getUnfreezeExtraDTO(OrderMasterDO orderMasterDO, OrderUnFreezeRequestDTO unFreezeRequestDTO){
        String orgCode = orderMasterDO.getFusercode();
        String orderNo = orderMasterDO.getForderno();
        List<ExtraDTO> extraDTOList = new ArrayList<>();
        extraDTOList.add(new ExtraDTO("orderDate", DateUtils.format("yyyy-MM-dd", applicationBaseService.getCreateTimeByOrderType(orderMasterDO))));
        if(unFreezeRequestDTO != null && unFreezeRequestDTO.getOperateUserId() != null){
            extraDTOList.add(new ExtraDTO(OPERATE_USER_ID, unFreezeRequestDTO.getOperateUserId().toString()));
        }
        //温州医科大学解冻 和 温医大仁济学院 需要 传注册带学校财务的供应商编码
        if (OrgEnum.WEN_ZHOU_YI_KE_DA_XUE.getCode().equals(orgCode) || OrgEnum.WEN_YI_DA_REN_JI_XUE_YUAN.getCode().equals(orgCode) || OrgEnum.HANG_ZHOU_YI_XUE_YUAN.getCode().equals(orgCode)) {
            OrgSuppSynStatusDataDTO suppCodeAndSynOrg = userClient.findIncludeSuppCodeAndSynOrg(orderMasterDO.getFuserid(), orderMasterDO.getFsuppid());
            BusinessErrUtil.isTrue(suppCodeAndSynOrg != null, ExecptionMessageEnum.SUPPLIER_NOT_REGISTERED_FINANCE, orderMasterDO.getFsuppid());
            extraDTOList.add(new ExtraDTO("extraSuppCode", suppCodeAndSynOrg.getCode()));
        }
        Date newFinancialDate = OrderDateConstant.ORG_CODE_NEW_FINANCIAL_DATE_MAP.get(orgCode);
        if(newFinancialDate != null){
            // 如果是存在新旧财务对接的，根据新旧财务对接事件设置isNew作为标记传给TPI
            extraDTOList.add(new ExtraDTO("isNew", orderMasterDO.getForderdate().after(newFinancialDate) ? "1" : "0"));
        }
        if(OrgEnum.WEN_ZHOU_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode)){
            // 温医附一，如果已经推送入库，则需要增加参数
            OrderEventStatusRequestDTO orderEventStatusRequestDto = new OrderEventStatusRequestDTO();
            orderEventStatusRequestDto.setOrderNoList(New.list(orderNo));
            orderEventStatusRequestDto.setOrderPushEventEnumList(New.list(OrderPushEventEnum.WAREHOUSE));
            List<OrderEventStatusResponseDTO> orderEventStatusResponseDTOList = orderPushEventStatusClient.listEventPushStatus(orderEventStatusRequestDto);
            if(CollectionUtils.isNotEmpty(orderEventStatusResponseDTOList) && OrderEventStatusEnum.COMPLETE.equals(orderEventStatusResponseDTOList.get(0).getOrderEventStatusEnum())){
                extraDTOList.add(new ExtraDTO("isReturnWarehouse", "1"));
            }
        }
        // 瓯江实验室根据采购单解冻
        if (OrgEnum.OU_JIANG_SHI_YAN_SHI.getCode().equals(orgCode) || OrgEnum.NING_BO_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode)) {
            if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType())) {
                ApplicationMasterDTO applicationMasterDTO = applicationBaseClient.getApplicationMasterByApplyId(orderMasterDO.getFtbuyappid(), false);
                extraDTOList.add(new ExtraDTO("purchaseNo", applicationMasterDTO.getApplyNumber()));
                extraDTOList.add(new ExtraDTO("parentSerialNumber", applicationMasterDTO.getApplyNumber()));
            }else if(OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterDO.getOrderType())){
                extraDTOList.add(new ExtraDTO("bidOrderNo", orderMasterDO.getBidOrderId()));
                extraDTOList.add(new ExtraDTO("parentSerialNumber", orderMasterDO.getBidOrderId()));
            }
        }
        if(OrgEnum.SHEN_ZHEN_YI_XUE_KE_XUE_YUAN.getCode().equals(orgCode)){
            OrderUnFreezeTypeEnum orderUnFreezeTypeEnum = unFreezeRequestDTO != null ? unFreezeRequestDTO.getOrderUnFreezeTypeEnum() : null;
            boolean isReturn = orderUnFreezeTypeEnum == OrderUnFreezeTypeEnum.RETURN || orderUnFreezeTypeEnum == OrderUnFreezeTypeEnum.RETURN_PARTIALLY;
            extraDTOList.add(new ExtraDTO("operateType", isReturn ? "4" : "1"));
        }
        boolean changeToSelfStatement = unFreezeRequestDTO != null && OrderUnFreezeTypeEnum.CHANGE_TO_SELF_STATEMENT == unFreezeRequestDTO.getOrderUnFreezeTypeEnum();
        if(changeToSelfStatement){
            // 如果需要改到自结算，则打上标记
            extraDTOList.add(new ExtraDTO(CHANGE_TO_SELF_STATEMENT_EXTRA_KEY, CommonValueUtils.TRUE_NUMBER_STR));
        }

        if(OrgEnum.QING_YUAN_SHI_REN_MIN_YI_YUAN.getCode().equals(orgCode)){
            OrderUnFreezeTypeEnum orderUnFreezeTypeEnum = unFreezeRequestDTO != null ? unFreezeRequestDTO.getOrderUnFreezeTypeEnum() : null;
            boolean isWholeReturn = orderUnFreezeTypeEnum == OrderUnFreezeTypeEnum.RETURN;
            extraDTOList.add(new ExtraDTO("updateOrderType", isWholeReturn ? "24" : "23"));
        }
        // 财务对接拓展数据，会从前端那边传给采购，采购直接透传到订单，解冻时使用
        List<FetchDataResponseItemDTO> financialExtraDataList = fetchOrderDockingDataServiceClient.fetchOrderExtraBigData(New.list(orderNo), DockingDataTypeEnum.FINANCIAL_DOCKING_EXTRA_DATA);
        if(CollectionUtils.isNotEmpty(financialExtraDataList)){
            List<ExtraDTO> financialExtraList = JsonUtils.parseList(financialExtraDataList.get(0).getData(), ExtraDTO.class);
            extraDTOList.addAll(financialExtraList);
        }
        return extraDTOList;
    }

    /**
     * 订单调解冻RPC接口，并刷新订单经费状态
     *
     * @param orderMasterDO 订单信息
     * @param orgRequest    解冻入参
     * @param isOrderClosed 订单是否关闭
     */
    private void orderUnFreezeRefreshStatus(OrderMasterDO orderMasterDO, OrgRequest<UnfreezeDTO> orgRequest, boolean isOrderClosed) {
        UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
        updated.setOrderId(orderMasterDO.getId());
        Integer fundStatus = OrderFundStatusEnum.Thrawing.value;
        if (isOrderClosed) {
            updated.setFundStatus(fundStatus);
            orderMasterMapper.updateOrderById(updated);
            // 此处置空的目的是防止代码最后的update将回调的经费卡状态覆盖（不走回调，就一定会走下面那些UN_HANDLE的if分支）
            updated.setFundStatus(null);
        }

        // 调用解冻结果
        FundCardResultDTO result = null;
        Integer handleResult = null;
        try {
            result = researchFundCardServiceClient.fundCardUnFreezeBatch(orgRequest);
            handleResult = result.getHandleResult();
        } catch (Exception e) {
            if (isOrderClosed) {
                fundStatus = OrderFundStatusEnum.ThrawFailed.value;
                updated.setFundStatus(fundStatus);
            }
            updated.setFailedReason(StringUtils.truncate("解冻失败：" + e.getMessage(), FAIL_REASON_LENGTH_LIMIT));
            orderMasterMapper.updateOrderById(updated);
            return;
        }

        if (HandleResultEnum.UN_HANDLE.getCode().equals(handleResult) || FundCardAsyCallBackEnum.NO_NEED.getValue().equals(result.getRelyAsyCallback())) {
            // 不依赖回调，则直接更新订单-经费卡关联信息表数据
            this.releaseRefFundCardOrderAmount(orderMasterDO, orgRequest.getData().getFreezeAmount(), isOrderClosed);
        }

        if (HandleResultEnum.UN_HANDLE.getCode().equals(handleResult) && isOrderClosed) {
            // 处理历史订单，未对接预算系统又调了解冻的订单叫无需解冻状态
            fundStatus = OrderFundStatusEnum.UN_FREEZE.value;
            updated.setFundStatus(fundStatus);
        } else if (FundCardAsyCallBackEnum.NO_NEED.getValue().equals(result.getRelyAsyCallback()) && isOrderClosed) {
            // 是否依赖回调结果，依赖回调则将经费状态改为释放中，否则经费状态为释放成功
            fundStatus = OrderFundStatusEnum.ThrawSuccessed.value;
            updated.setFundStatus(fundStatus);
        }
        if (updated.getFundStatus() != null) {
            // 更新订单的经费状态
            orderMasterMapper.updateOrderById(updated);
        }
    }


    /**
     * 更新第三方订单推送信息
     *
     * @param orderMasterDO
     */
    public void updateThirdPartyOrder(OrderMasterDO orderMasterDO) {
        ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO = new ThirdPartyPlatformOrderBO();
        thirdPartyPlatformOrderBO.setReagentOrderNo(orderMasterDO.getForderno());
        thirdPartyPlatformOrderBO.setStatus(TPIClientOrderStatusEnum.Close.getValue());
        thirdPartyPlatformOrderBO.setOrgCode(orderMasterDO.getFusercode());
        tpiOrderClient.updateOrderStatusAsync(thirdPartyPlatformOrderBO);
    }

    /**
     * 解冻回调
     *
     * @param callbackRequest
     */
    @Override
    public void unFrozenCallback(CallbackRequest<UnfreezeCallbackResult> callbackRequest) {
        String orgCode = callbackRequest.getOrgCode();
        UnfreezeCallbackResult unfreezeCallbackResult = callbackRequest.getData();
        Preconditions.notNull(unfreezeCallbackResult, "回调数据异常！");
        String orderNo = unfreezeCallbackResult.getSerialNumber();
        Preconditions.notNull(orderNo, "回调数据异常！");

        OrderMasterDO order = orderMasterMapper.findByForderno(orderNo);

        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderNo(orderNo);
        // 浙肿的旧单 和 中大附一要特殊处理解冻回调
        boolean isZheZhongOld = OrgEnum.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN.getCode().equals(orgCode) && order.getForderdate().before(DateUtils.parse(OrderDateConstant.ZHE_JIANG_SHENG_ZHONG_LIU_YI_YUAN));
        if (isZheZhongOld || OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode().equals(orgCode)) {
            if (CallbackRequest.SUCCESS == callbackRequest.getCode()) {
                LOGGER.info("{} 解冻成功--回调！", orderNo);
                // 删除绑卡信息...
                refFundcardOrderMapper.deleteByOrderId(order.getId().toString());
                this.clearWaitingStatementFundCardData(order.getId());
                //修改经费状态未 解冻成功
                updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.ThrawSuccessed.value);
            } else {
                LOGGER.info("{} 解冻失败--回调！", orderNo);
                updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.ThrawFailed.value);
                updateOrderParamDTO.setFailedReason(StringUtils.truncate(callbackRequest.getMsg(), FAIL_REASON_LENGTH_LIMIT));
            }
            orderMasterMapper.updateOrderByOrderNo(updateOrderParamDTO);
            LOGGER.info("更新经费状态成功！", JsonUtils.toJson(updateOrderParamDTO));
            return;
        }

        if (CallbackRequest.SUCCESS == callbackRequest.getCode()) {
            LOGGER.info("{} 解冻成功--回调！", orderNo);
            //记录日志
            orderOtherLogClient.createOrderDockingLog(orderNo, orgCode, null, JsonUtils.toJsonIgnoreNull(callbackRequest),
                    OrderDockingOperationEnum.UNFREEZE_FUND_CARD_CALLBACK.operation, OrderDockingResultEnum.SUCCESS.result);
            //修改经费状态未 解冻成功
            updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.ThrawSuccessed.value);
            orderMasterMapper.updateOrderByOrderNo(updateOrderParamDTO);
            // 是否需要删卡
            boolean needDeleteCard = true;
            if(callbackRequest.getData() != null && CollectionUtils.isNotEmpty(callbackRequest.getData().getExtraDTOs())){
                Map<String, String> extraKeyValueMap = DictionaryUtils.toMap(callbackRequest.getData().getExtraDTOs(), ExtraDTO::getField, ExtraDTO::getValue);
                boolean changeToSelfStatement = CommonValueUtils.TRUE_NUMBER_STR.equals(extraKeyValueMap.get(CHANGE_TO_SELF_STATEMENT_EXTRA_KEY));
                if(changeToSelfStatement){
                    // 若转换到自结算，则保留原卡
                    needDeleteCard = false;
                    // 如果发起解冻时，透传了需要转换到自结算，则转换到自结算
                    Integer operateUserId = extraKeyValueMap.get(OPERATE_USER_ID) == null ? -1 : Integer.parseInt(extraKeyValueMap.get(OPERATE_USER_ID));
                    orderFundCardUnfreezeService.changeOrderToSelfStatement(operateUserId, order.getId());
                }
            }
            if(needDeleteCard){
                // 目前对接了经费卡的单都是整单退货，只需要清空表即可。如果有对接了财务系统能支持部分退货的，需要另外改造
                this.releaseRefFundCardOrderAmount(order, null, true);
            }
        } else {
            LOGGER.info("{} 解冻失败--回调！", orderNo);
            //记录日志
            orderOtherLogClient.createOrderDockingLog(orderNo, orgCode, null, JsonUtils.toJsonIgnoreNull(callbackRequest),
                    OrderDockingOperationEnum.UNFREEZE_FUND_CARD_CALLBACK.operation, OrderDockingResultEnum.FAIL.result);
            updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.ThrawFailed.value);
            updateOrderParamDTO.setFailedReason(StringUtils.truncate(callbackRequest.getMsg(), FAIL_REASON_LENGTH_LIMIT));
            orderMasterMapper.updateOrderByOrderNo(updateOrderParamDTO);
        }
        LOGGER.info("更新经费状态成功！", JsonUtils.toJson(updateOrderParamDTO));

    }

    @Override
    @ServiceLog(description = "查找课题组的经费卡")
    public List<OrderFundCardResponseDTO> findFundCardByDepartmentId(RjSessionInfo rjSessionInfo, OrderFundCardListParam param) {
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginUserInfoBO = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        OrderFundCardRequestDTO request = new OrderFundCardRequestDTO();
        request.setOrgCode(loginUserInfoBO.getOrgCode());
        request.setOrgId(orgId);
        request.setOrderId(param.getOrderId());
        if (param.getPageNo() != null) {
            request.setPageNo(param.getPageNo() );
        }
        if (param.getPageSize() != null) {
            request.setPageSize(param.getPageSize());
        }
        RemoteResponse<List<OrderFundCardResponseDTO>> response = orderRelatedRPCService.findFundCardByDepartmentId(request);
        BusinessErrUtil.isTrue(response.isSuccess(), ExecptionMessageEnum.SEARCH_FUND_CARD_FAILED, response.getMsg());
        return response.getData();
    }

    /**
     * 更新订单与经费卡关联表数据
     *
     * @param orderMasterDO  订单主表
     * @param unfreezeAmount 解冻金额
     * @param isAllReturn    全部退货
     */
    private void releaseRefFundCardOrderAmount(OrderMasterDO orderMasterDO, BigDecimal unfreezeAmount, boolean isAllReturn) {
        Integer orderId = orderMasterDO.getId();
        // 控制这个退货单不能同时释放冻结金额
        String lockKey = "release_refFundCardOrder_amount_" + orderId;
        cacheClient.lockRetry(lockKey, 3);
        try {
            RefFundcardOrderDO updateParam = new RefFundcardOrderDO();
            updateParam.setOrderId(orderId.toString());
            if (isAllReturn) {
                // 全部退货 删除绑卡数据
                refFundcardOrderMapper.deleteByOrderId(orderId.toString());
            } else {
                // 部分退货，做金额运算
                List<RefFundcardOrderDO> refFundcardOrderDOList = refFundcardOrderMapper.findByOrderIdIn(New.list(orderId.toString()));
                if (CollectionUtils.isEmpty(refFundcardOrderDOList)) {
                    return;
                }
                RefFundcardOrderDO refFundcardOrderDO = refFundcardOrderDOList.get(0);
                BigDecimal useMoney = refFundcardOrderDO.getFreezeAmount().subtract(unfreezeAmount);
                BigDecimal freezeAmount = refFundcardOrderDO.getFreezeAmount().subtract(unfreezeAmount);
                updateParam.setUsemoney(useMoney);
                updateParam.setFreezeAmount(freezeAmount);
                refFundcardOrderMapper.updateByOrderId(updateParam);
            }
        } catch (Exception e) {
            LOGGER.error("更新订单{}与经费卡关联表的金额失败！", orderMasterDO.getForderno(), e);
        } finally {
            cacheClient.removeCache(lockKey);
        }
    }

    /**
     * 清除待结算的绑卡数据
     *
     * @param orderId 订单id
     */
    private void clearWaitingStatementFundCardData(Integer orderId) {
        WaitingStatementOrderRequestDTO statementOrderRequestDTO = new WaitingStatementOrderRequestDTO();
        statementOrderRequestDTO.setOrderId(orderId);
        statementOrderRequestDTO.setFundCards(StringUtils.EMPTY);
        statementPlatformClient.updateWaitingStatement(Collections.singletonList(statementOrderRequestDTO));
    }
}
