package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.shop.goods.api.dto.BaseProductDTO;
import com.ruijing.shop.goods.api.dto.ProductUniqDTO;
import com.ruijing.shop.goods.api.service.BizShopProductService;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/5 16:59
 * @desc
 */
@Service
public class BizShopProductServiceClient {

    private static final String CAT_TYPE = "BizShopProductServiceClient";

    private static final Logger LOGGER = LoggerFactory.getLogger(BizShopProductServiceClient.class);

    @MSharpReference(remoteAppkey = "shop-goods-service")
    private BizShopProductService bizShopProductService;

    @ServiceLog(description = "更新库存接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public boolean batchUpdateStock(List<BaseProductDTO> productDTOs) {
        ApiResult apiResult = bizShopProductService.batchUpdateStock(productDTOs);
        Preconditions.isTrue(apiResult.successful(), "更新库存失败：" + JsonUtils.toJsonIgnoreNull(apiResult));
        return apiResult.successful();
    }

    public List<BaseProductDTO> getProductByIds(List<ProductUniqDTO> productUniqDTOS) throws Exception {

        Transaction transaction = Cat.newTransaction(CAT_TYPE, "sumGoodsAmountQuantity");
        ApiResult<List<BaseProductDTO>> remoteResponse = null;
        try {
            remoteResponse = bizShopProductService.getByIds(productUniqDTOS);
            List<BaseProductDTO> resultList = remoteResponse.getData();
            return resultList;
        } catch (Exception e) {
            String jsonLog = JsonUtils.toJson(remoteResponse);
            LOGGER.error("汇总商品接口异常：{}", jsonLog,e);
            Cat.logWarn(CAT_TYPE, "sumProductAmountAndQuantity", "汇总商品接口异常" + jsonLog);
            throw new Exception("汇总商品接口异常");
        }finally {
            transaction.complete();
        }


    }
}
