package com.ruijing.store.order.base.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.log.dto.DataOperationLogDTO;
import com.reagent.order.base.log.request.DataOperationLogQueryRequest;
import com.reagent.order.base.order.dto.OrderFileOperationLogDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogBatchQueryRequestDTO;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.research.statement.api.enums.OperatorChannelEnum;
import com.reagent.research.statement.api.enums.OperatorTypeEnum;
import com.reagent.research.statement.api.enums.StatementStatusEnum;
import com.reagent.research.statement.api.statement.dto.OrderRefStatementLogDTO;
import com.reagent.research.statement.api.statement.dto.StatementLogResultDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.audit.sampling.dto.OrderAuditSamplingDTO;
import com.ruijing.order.saturn.api.audit.sampling.enums.AuditSamplingResultEnum;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationRecordDTO;
import com.ruijing.store.electronicsign.api.dto.OperationListDTO;
import com.ruijing.store.electronicsign.api.enums.BusinessTypeEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderOperateLogService;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.bo.buyercenter.operationlog.OrderLogApproveBO;
import com.ruijing.store.order.business.enums.OmsFixDataEnum;
import com.ruijing.store.order.business.enums.OrderOperationTypeEnum;
import com.ruijing.store.order.constant.OrderOperateLogConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOperationLogVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.PurchaseOrderAllRelateLogVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.LocalI18nUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class OrderOperateLogServiceImpl implements OrderOperateLogService {

    /**
     * 代表系统的操作id
     */
    private final static Integer SYSTEM_OPERATOR_ID = -1;

    /**
     * 代表系统的操作名
     */
    private final static String SYSTEM_OPERATOR_NAME = "系统";

    /**
     * 使用了电子签名的验收审批日志额外文案
     */
    private final static String USE_ELECTRONIC_SIGN_APPROVAL_LOG_EXTRA_TEXT = "（电子签名审批）";

    // 很久以前遗留下来的数据问题，因此经讨论约定这个1540 为供应商特别的标志
    private final static Integer PROMISE_SUPPLIER_FLAG = 1540;
    private final static String AUTO_CANCEL = "超过15天未确认订单，订单自动关闭";
    private final static String AGREE_CANCEL = "同意取消";
    private final static Integer STATEMENT_COMPLETE_STATUS = 3;

    /**
     * 采购人中心日志，需要展示供应商名作为userName的枚举
     */
    private final List<Integer> SHOW_SUPP_NAME_AS_USER_ENUMS = New.list(
            OrderApprovalEnum.ORDER_SPLIT_UP.getValue(),
            OrderApprovalEnum.DELIVERY_PROXY_TURN_ON.getValue(),
            OrderApprovalEnum.DELIVERY_PROXY_TURN_OFF.getValue(),
            OrderApprovalEnum.SUPPLIER_AGREE_CANCEL_ORDER.getValue(),
            OrderApprovalEnum.SUPPLIER_REFUSE_CANCEL_ORDER.getValue(),
            OrderApprovalEnum.SUPP_APPLY_CANCEL_DELIVERY_PROXY.getValue(),
            OrderApprovalEnum.CANCEL_DELIVERY.getValue()
    );

    private final List<OrderApprovalEnum> LOG_SHOW_IN_BUYER_CENTER = New.list(
            OrderApprovalEnum.GOODS_RETURN,
            OrderApprovalEnum.RECEIPT,
            OrderApprovalEnum.REJECT_DURING_RECEIVE_BY_BUYER,
            OrderApprovalEnum.CANCEL,
            OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_PICTURE,
            OrderApprovalEnum.TIMEOUT_AUTO_APPROVAL_ORDER,
            OrderApprovalEnum.TIMEOUT_AUTO_APPROVAL_ORDER_FAILURE,
            OrderApprovalEnum.SETTLEMENT_INVALID,
            // 华农，要展示这几个定制化的通知基里的审批状态
            OrderApprovalEnum.NOTICE_JILI_DISPATCH,
            OrderApprovalEnum.NOTICE_JILI_APPROVE_RETURN,
            OrderApprovalEnum.NOTICE_JILI_REJECT_RETURN,

            OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD,
            OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD_SUCCESS,
            OrderApprovalEnum.APPROVAL_CHANGE_FUND_CARD_FAILURE,
            OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_FAILURE,
            OrderApprovalEnum.WATING_STATEMENT_CHANGE_FUND_CARD_SUCCESS,
            OrderApprovalEnum.SETTLEMENT_CHANGE_FUND_CARD_SUCCESS,
            OrderApprovalEnum.SETTLEMENT_CHANGE_FUND_CARD_FAILURE,
            OrderApprovalEnum.ORDER_SPLIT_UP,
            OrderApprovalEnum.PUSH_ORDER_TO_THIRD_FAILURE,
            OrderApprovalEnum.MODIFIED_DELIVERY_ADDRESS,
            OrderApprovalEnum.DELIVERY_PROXY_TURN_ON,
            OrderApprovalEnum.DELIVERY_PROXY_TURN_OFF,
            OrderApprovalEnum.SUPPLIER_AGREE_CANCEL_ORDER,
            OrderApprovalEnum.BUYER_AGREE_CANCEL_ORDER,
            OrderApprovalEnum.SUPPLIER_REFUSE_CANCEL_ORDER,
            OrderApprovalEnum.FINISH_ORDER,
            OrderApprovalEnum.PUSH_INVOICE,
            OrderApprovalEnum.BUYER_REFUSE_CANCEL_ORDER,
            OrderApprovalEnum.ACCEPT_APPROVE_ERROR,
            OrderApprovalEnum.CANCEL_RECEIPT,
            OrderApprovalEnum.SYSTEM_INVALID_SETTLEMENT,
            OrderApprovalEnum.PUSHING_WAREHOUSE_TO_THIRD,
            OrderApprovalEnum.PUSH_WAREHOUSE_TO_THIRD_SUCCESS,
            OrderApprovalEnum.PUSH_WAREHOUSE_TO_THIRD_FAILURE,
            OrderApprovalEnum.ORDER_CLOSE,
            OrderApprovalEnum.PUSHING_ORDER_STATUS_TO_THIRD,
            OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_SUCCESS,
            OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_FAILURE,
            OrderApprovalEnum.PUSH_INVOICE_SUCCESS,
            OrderApprovalEnum.PUSH_INVOICE_FAIL,
            OrderApprovalEnum.MODIFY_INVOICE,
            OrderApprovalEnum.APPLY_PLATFORM_OPERATOR_APPROVAL,
            OrderApprovalEnum.SUPP_MODIFY_BATCHES,
            OrderApprovalEnum.DELETE_AND_UPDATE_ACCEPTANCE_PHOTO,
            OrderApprovalEnum.SUPP_APPLY_CANCEL_DELIVERY_PROXY,
            OrderApprovalEnum.BUYER_AGREE_CANCEL_DELIVERY_PROXY,
            OrderApprovalEnum.BUYER_REJECT_CANCEL_DELIVERY_PROXY,
            OrderApprovalEnum.CANCEL_ACCEPT_APPROVE,
            OrderApprovalEnum.DELETE_ACCEPTANCE_PHOTO,
            OrderApprovalEnum.DELETE_ACCEPTANCE_ATTACHMENT,
            OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_ATTACHMENT,
            OrderApprovalEnum.CANCEL_DELIVERY,
            OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL
    );

    @Resource(name = "defaultIoExecutor")
    private Executor defaultIoExecutor;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Resource
    private OrderAuditSamplingClient orderAuditSamplingClient;

    @Resource
    private DataOperationLogRpcClient dataOperationLogRpcClient;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private BidClient bidClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private ClaimServiceClient claimServiceClient;

    /**
     * 批量查询订单操作日志
     *
     * @return 订单ID到操作日志列表的映射
     */
    @Override
    public List<OrderOperationLogVO> batchOrderOperationLog(List<OrderMasterDO> orderMasterList) {
        return this.batchOrderOperationLog(orderMasterList, false);
    }

    private List<OrderOperationLogVO> batchOrderOperationLog(List<OrderMasterDO> orderMasterList, boolean getAllRelatedLog){
        // 1. 参数校验与准备
        if (CollectionUtils.isEmpty(orderMasterList)) {
            return New.emptyList();
        }

        // 2. 处理订单ID和订单号
        List<Integer> finalOrderIds = orderMasterList.stream()
                .map(OrderMasterDO::getId)
                .collect(toList());

        List<String> finalOrderNos = orderMasterList.stream()
                .map(OrderMasterDO::getForderno)
                .collect(toList());

        // 3. 批量查询各类日志
        // 3.1 查询订单审批日志
        Map<Integer, List<OrderLogApproveBO>> orderIdApproveLogMap = this.batchGetOrderApproveLog(orderMasterList);

        // 3.2 查询订单电子签名
        List<String> orderIdStrList = finalOrderIds.stream()
                .map(Object::toString)
                .collect(toList());
        List<ElectronicSignOperationRecordDTO> acceptApproveESignList = this.batchGetAcceptApproveElectronicSign(orderIdStrList);
        Map<String, List<ElectronicSignOperationRecordDTO>> orderIdESignMap = acceptApproveESignList.stream()
                .collect(Collectors.groupingBy(ElectronicSignOperationRecordDTO::getBusinessId));

        // 3.3 查询结算日志
        Map<Integer, List<StatementLogResultDTO>> orderStatementLogMap = this.batchGetStatementLogs(finalOrderIds);

        // 3.4 查询退货信息
        Map<Integer, List<GoodsReturn>> orderIdGoodsReturnMap = this.batchGetGoodsReturns(finalOrderIds);

        // 收集所有退货单ID
        List<Integer> allReturnIds = New.list();
        for (List<GoodsReturn> goodsReturnList : orderIdGoodsReturnMap.values()) {
            if (CollectionUtils.isNotEmpty(goodsReturnList)) {
                allReturnIds.addAll(goodsReturnList.stream()
                        .map(GoodsReturn::getId)
                        .collect(toList()));
            }
        }

        // 3.5 查询退货日志
        Map<Integer, List<GoodsReturnLogDO>> returnIdLogMap = this.batchGetGoodsReturnLogs(allReturnIds);

        // 3.6 查询订单抽检数据
        Map<String, List<OrderAuditSamplingDTO>> orderNoAuditMap = this.batchGetOrderAuditSampling(finalOrderNos);

        // 3.7 查询OMS数据操作日志
        Map<String, List<DataOperationLogDTO>> orderNoOmsLogMap = this.batchGetOmsDateOperationLog(finalOrderNos);

        Map<String, List<BizWarehouseReceiceDTO>> orderNoClaimDataMap = New.emptyMap();
        List<String> needClaimDataOrderNoList = orderMasterList.stream().filter(orderMasterDO -> OrgEnum.ZHONG_QING_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() == orderMasterDO.getFuserid()).map(OrderMasterDO::getForderno).collect(toList());
        if (CollectionUtils.isNotEmpty(needClaimDataOrderNoList)) {
            List<BizWarehouseReceiceDTO> claimDataList = claimServiceClient.queryClaimByOrderNos(needClaimDataOrderNoList);
            if (CollectionUtils.isNotEmpty(claimDataList)) {
                orderNoClaimDataMap = DictionaryUtils.groupBy(claimDataList, BizWarehouseReceiceDTO::getOrderNo);
            }
        }

        // 4. 组装结果
        List<OrderOperationLogVO> result = New.list();

        for (OrderMasterDO orderMaster : orderMasterList) {
            Integer orderId = orderMaster.getId();
            String orderNo = orderMaster.getForderno();
            Integer orgId = orderMaster.getFuserid();
            // 该订单的所有操作日志
            List<OrderOperationLogVO> operationLogList = New.list();

            // 4.1 添加订单创建和供应商确认日志
            this.addOrderCreateAndSupplierConfirmLog(operationLogList, orderMaster);

            // 4.2 添加供应商发货日志
            this.addSupplierDeliveryLog(operationLogList, orderMaster);

            // 4.3 添加订单审批日志
            this.addOrderApprovalLogs(operationLogList, orderId, orderIdApproveLogMap,
                    orderIdESignMap.getOrDefault(orderId.toString(), New.emptyList()), getAllRelatedLog);

            // 4.4 添加OMS数据操作日志
            this.addOmsOperationLogs(operationLogList, orderId, orderNoOmsLogMap.get(orderNo));

            // 4.5 添加结算日志
            this.addStatementLogs(operationLogList, orderId, orderStatementLogMap.get(orderId), orgId);

            // 4.6 添加退货日志
            this.addGoodsReturnLogs(operationLogList, orderId, orderIdGoodsReturnMap.getOrDefault(orderId, New.emptyList()), returnIdLogMap);

            // 4.7 添加订单抽检日志
            this.addAuditSamplingLogs(operationLogList, orderId, orderNo, orderNoAuditMap.getOrDefault(orderNo, New.emptyList()));

            //4.8 增加申领日志
            this.addClaimLogs(operationLogList, orderId, orderNoClaimDataMap.get(orderNo));

            // 4.9 按时间排序
            this.sortOperationLogs(operationLogList);

            result.addAll(operationLogList);
        }

        return result;
    }

    /**
     * 添加订单创建和供应商确认日志
     */
    private void addOrderCreateAndSupplierConfirmLog(List<OrderOperationLogVO> operationLogList, OrderMasterDO orderMaster) {
        // 生成订单
        Integer orderId = orderMaster.getId();
        Integer businessType = OperateLogBussinessTypeEnum.ORDER.getCode();
        if (OrderCommonUtils.isSplitOrder(orderMaster.getForderno())) {
            String parentOrderNo = orderMaster.getForderno().substring(0, orderMaster.getForderno().length() - 1);
            OrderMasterDO parentOrder = orderMasterMapper.findByForderno(parentOrderNo);
            this.setOperation(operationLogList, orderId, null, parentOrder.getShutDownDate(), orderMaster.getFsuppname(),
                    OrderOperateLogConstant.CREATE_ORDER, parentOrderNo + "拆分订单生成", businessType);
        } else {
            this.setOperation(operationLogList, orderId, null, orderMaster.getForderdate(), "系统", OrderOperateLogConstant.CREATE_ORDER,
                    "系统自动生成订单", businessType);
            // 供应商确认订单
            if (orderMaster.getFconfirmdate() != null) {
                this.setOperation(operationLogList, orderId, null, orderMaster.getFconfirmdate(), orderMaster.getFsuppname(),
                        OrderOperateLogConstant.SUPP_VERIFY_ORDER, "", businessType);
            }
        }
    }

    /**
     * 添加供应商发货日志
     */
    private void addSupplierDeliveryLog(List<OrderOperationLogVO> operationLogList, OrderMasterDO orderMaster) {
        // 供应商发货
        Integer orderId = orderMaster.getId();
        if (orderMaster.getFdeliverydate() != null) {
            this.setOperation(operationLogList, orderId, null, orderMaster.getFdeliverydate(), orderMaster.getFsuppname(),
                    OrderOperateLogConstant.SUPP_SHIPMENTS, "", OperateLogBussinessTypeEnum.ORDER.getCode());
        }
    }

    /**
     * 添加订单审批日志
     */
    private void addOrderApprovalLogs(List<OrderOperationLogVO> operationLogList, Integer orderId,
                                      Map<Integer, List<OrderLogApproveBO>> orderApproveLogMap,
                                      List<ElectronicSignOperationRecordDTO> acceptApproveESignList, boolean getAllRelatedLog) {
        if (orderApproveLogMap == null || !orderApproveLogMap.containsKey(orderId)) {
            return;
        }

        List<OrderLogApproveBO> orderLogApproveList = orderApproveLogMap.get(orderId);
        if (CollectionUtils.isEmpty(orderLogApproveList)) {
            return;
        }

        // 构建电子签名ID映射
        Map<String, ElectronicSignOperationRecordDTO> signIdMap = New.map();
        if (CollectionUtils.isNotEmpty(acceptApproveESignList)) {
            signIdMap = acceptApproveESignList.stream()
                    .collect(Collectors.toMap(ElectronicSignOperationRecordDTO::getInteractionId, Function.identity(), (a, b) -> a));
        }

        for (OrderLogApproveBO orderLogApprove : orderLogApproveList) {
            boolean needShow = false;
            for(OrderApprovalEnum e : LOG_SHOW_IN_BUYER_CENTER){
                if(e.getValue().equals(orderLogApprove.getOrderApprovalStatus())){
                    needShow = true;
                    break;
                }
            }
            if (needShow || OmsFixDataEnum.FUND_UNFREEZE.getValue().equals(orderLogApprove.getOrderApprovalStatus())
                    || OmsFixDataEnum.FUND_FREEZE.getValue().equals(orderLogApprove.getOrderApprovalStatus())
                    || OmsFixDataEnum.FUND_RE_FREEZE.getValue().equals(orderLogApprove.getOrderApprovalStatus())
                    || OmsFixDataEnum.FUND_RE_UNFREEZE.getValue().equals(orderLogApprove.getOrderApprovalStatus())) {
                this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                        orderLogApprove.getUserName(), orderLogApprove.getStatusDesc(), orderLogApprove.getReason(),
                        OperateLogBussinessTypeEnum.ORDER.getCode());
            }

            if(getAllRelatedLog){
                if(OrderApprovalEnum.COMPLETE_REPORT_EXPENSE.getValue().equals(orderLogApprove.getOrderApprovalStatus())){
                    this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                            orderLogApprove.getUserName(), orderLogApprove.getStatusDesc(), orderLogApprove.getReason(),
                            OperateLogBussinessTypeEnum.ORDER.getCode());
                }
            }

            if (OrderApprovalEnum.REJECT.getValue().equals(orderLogApprove.getOrderApprovalStatus())
                    || OrderApprovalEnum.PASS.getValue().equals(orderLogApprove.getOrderApprovalStatus())) {
                // 订单验收审批通过/驳回，如果在电子签名表里面有匹配项，则标注为使用了电子签名
                boolean useESign = signIdMap.containsKey(orderLogApprove.getId().toString());
                String actionName = useESign ? orderLogApprove.getStatusDesc() + USE_ELECTRONIC_SIGN_APPROVAL_LOG_EXTRA_TEXT : orderLogApprove.getStatusDesc();
                this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                        orderLogApprove.getUserName(), actionName, orderLogApprove.getReason(),
                        OperateLogBussinessTypeEnum.ORDER.getCode());
            }

            if (OrderApprovalEnum.PLATFORM_OPERATOR_APPROVE_PASS.getValue().equals(orderLogApprove.getOrderApprovalStatus())
                    || OrderApprovalEnum.PLATFORM_OPERATOR_APPROVE_REJECT.getValue().equals(orderLogApprove.getOrderApprovalStatus())) {
                // 运营商核实操作，操作人展示为运营商
                this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                        "运营商", orderLogApprove.getStatusDesc(), orderLogApprove.getReason(),
                        OperateLogBussinessTypeEnum.ORDER.getCode());
            }

            if (OmsFixDataEnum.FIX_ORDER_STATUS.getValue().equals(orderLogApprove.getOrderApprovalStatus())
                    || OmsFixDataEnum.FIX_FUND_STATUS.getValue().equals(orderLogApprove.getOrderApprovalStatus())) {
                // 修改订单/经费状态操作，特殊处理
                this.setFixStatusLog(operationLogList, orderId, orderLogApprove, OmsFixDataEnum.getByValue(orderLogApprove.getOrderApprovalStatus()));
            }
        }
    }

    /**
     * 添加OMS数据操作日志
     */
    private void addOmsOperationLogs(List<OrderOperationLogVO> operationLogList, Integer orderId, List<DataOperationLogDTO> omsLogList) {
        if (CollectionUtils.isEmpty(omsLogList)) {
            return;
        }

        Set<Integer> showOperationTypes = New.set(OmsFixDataEnum.FUND_FREEZE.getValue(), OmsFixDataEnum.FUND_UNFREEZE.getValue());

        for (DataOperationLogDTO omsDateOperationLog : omsLogList) {
            if (showOperationTypes.contains(omsDateOperationLog.getOperationType())) {
                this.setOperation(operationLogList, orderId, omsDateOperationLog.getId(), omsDateOperationLog.getOperateTime()
                        , "系统", OmsFixDataEnum.getNameByValue(omsDateOperationLog.getOperationType()), omsDateOperationLog.getFixReason(),
                        OperateLogBussinessTypeEnum.ORDER.getCode());
            }
        }
    }

    /**
     * 添加结算日志
     */
    private void addStatementLogs(List<OrderOperationLogVO> operationLogList, Integer orderId, List<StatementLogResultDTO> statementLogResultDTOList, Integer currentOrderOrgId) {
        if (CollectionUtils.isEmpty(statementLogResultDTOList)) {
            return;
        }

        for (StatementLogResultDTO statementLog : statementLogResultDTOList) {
            if (statementLog == null) {
                continue;
            }
            Integer businessType = OperateLogBussinessTypeEnum.SETTLEMENT.getCode();
            // 判断-开始结算
            if (OperatorTypeEnum.CREATE_STATEMENT.getType().equals(statementLog.getOperatorType())) {
                // 中大特殊，不显示自动发起结算日志
                if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(currentOrderOrgId) && StringUtils.isBlank(statementLog.getUserName())) {
                    continue;
                }
                String userName = StringUtils.isBlank(statementLog.getUserName()) ? SYSTEM_OPERATOR_NAME : statementLog.getUserName();
                this.setOperation(operationLogList, orderId, statementLog.getId().intValue(),
                        statementLog.getCreationTime(), userName, OrderOperationTypeEnum.START_STATEMENT.desc, statementLog.getReason(),
                        businessType);
            }

            // 判断-完成结算
            if (StatementStatusEnum.Completed.getStatus().equals(statementLog.getStatus())
                    && OperatorChannelEnum.SUPP.getChannel().equals(statementLog.getOperatorChannel())) {
                this.setOperation(operationLogList, orderId, statementLog.getId().intValue(),
                        statementLog.getCreationTime(), statementLog.getUserName(), OrderOperationTypeEnum.FINISH_STATEMENT.desc, StringUtils.defaultString(statementLog.getReason()),
                        businessType);
            }
            // 判断-撤销
            if (OperatorTypeEnum.CANCEL_STATEMENT.getType().equals(statementLog.getOperatorType())) {
                this.setOperation(operationLogList, orderId, statementLog.getId().intValue(),
                        statementLog.getCreationTime(), statementLog.getUserName(), OperatorTypeEnum.CANCEL_STATEMENT.getTypeName(), StringUtils.defaultString(statementLog.getReason()),
                        businessType);
            }
            // 判断-作废
            if (OperatorTypeEnum.CANCELED.getType().equals(statementLog.getOperatorType())) {
                this.setOperation(operationLogList, orderId, statementLog.getId().intValue(),
                        statementLog.getCreationTime(), statementLog.getUserName(), OperatorTypeEnum.CANCELED.getTypeName(), StringUtils.defaultString(statementLog.getReason()),
                        businessType);
            }
            // 判断-移除
            if (OperatorTypeEnum.REMOVE_SUMMARY.getType().equals(statementLog.getOperatorType())) {
                this.setOperation(operationLogList, orderId, statementLog.getId().intValue(),
                        statementLog.getCreationTime(), statementLog.getUserName(), OperatorTypeEnum.REMOVE_SUMMARY.getTypeName(), StringUtils.defaultString(statementLog.getReason()),
                        businessType);
            }
        }
    }

    /**
     * 添加退货日志
     */
    private void addGoodsReturnLogs(List<OrderOperationLogVO> operationLogList, Integer orderId, List<GoodsReturn> goodsReturnList, Map<Integer, List<GoodsReturnLogDO>> returnIdLogMap) {
        if (CollectionUtils.isEmpty(goodsReturnList) || returnIdLogMap == null) {
            return;
        }

        Map<Integer, GoodsReturn> returnIdMap = goodsReturnList.stream().collect(Collectors.toMap(GoodsReturn::getId, Function.identity(), (a, b) -> a));

        // 收集该订单所有退货单的日志
        for (Map.Entry<Integer, List<GoodsReturnLogDO>> entry : returnIdLogMap.entrySet()) {
            Integer returnId = entry.getKey();
            List<GoodsReturnLogDO> logList = entry.getValue();

            if (!returnIdMap.containsKey(returnId) || CollectionUtils.isEmpty(logList)) {
                continue;
            }

            GoodsReturn goodsReturn = returnIdMap.get(returnId);

            for (GoodsReturnLogDO goodsReturnLogDO : logList) {
                GoodsReturnOperationTypeEnum operationType = GoodsReturnOperationTypeEnum.getByCode(goodsReturnLogDO.getOperationType());
                if (GoodsReturnOperationTypeEnum.needOrderLogList.contains(operationType)) {
                    String remark = LocalI18nUtils.translate("退货单号") + "：" + goodsReturn.getReturnNo();
                    this.setOperation(operationLogList, orderId, goodsReturnLogDO.getId(), goodsReturnLogDO.getCreateTime(),
                            goodsReturnLogDO.getOperatorName(), operationType.getDescription(), remark,
                            OperateLogBussinessTypeEnum.ORDER.getCode());
                }
            }
        }
    }

    /**
     * 添加订单抽检日志
     */
    private void addAuditSamplingLogs(List<OrderOperationLogVO> operationLogList, Integer orderId, String orderNo, List<OrderAuditSamplingDTO> samplingList) {
        if (CollectionUtils.isEmpty(samplingList)) {
            return;
        }

        for (OrderAuditSamplingDTO orderAuditSamplingDTO : samplingList) {
            String oprName = AuditSamplingResultEnum.PASS.getCode().equals(orderAuditSamplingDTO.getAuditResult()) ?
                    "订单抽检通过" : "订单抽检不通过";
            this.setOperation(operationLogList, orderId, null, orderAuditSamplingDTO.getAuditDate(),
                    orderAuditSamplingDTO.getAuditUserName(), oprName,
                    orderAuditSamplingDTO.getAuditRemark(), OperateLogBussinessTypeEnum.ORDER.getCode());
        }
    }

    /**
     * 按时间排序操作日志
     */
    private void sortOperationLogs(List<OrderOperationLogVO> operationLogList) {
        if (CollectionUtils.isEmpty(operationLogList)) {
            return;
        }

        Collections.sort(operationLogList, (o1, o2) -> {
            // 先按创建时间排，如果时间不一致，则返回时间排序
            int createTimeCmp = o1.getCreateTime().compareTo(o2.getCreateTime());
            if (createTimeCmp != 0) {
                return createTimeCmp;
            }
            // 时间一致，则用id排
            if (Objects.nonNull(o1.getId()) && Objects.nonNull(o2.getId())) {
                return o1.getId().compareTo(o2.getId());
            }
            // 都一致则视为相等
            return 0;
        });
    }

    /**
     * 设置修改订单状态日志
     */
    private void setFixStatusLog(List<OrderOperationLogVO> operationLogList, Integer orderId, OrderLogApproveBO orderLogApprove, OmsFixDataEnum omsFixDataEnum) {
        Integer businessType = OperateLogBussinessTypeEnum.ORDER.getCode();
        Integer operationCode = orderLogApprove.getOrderApprovalStatus();
        if (StringUtils.isEmpty(orderLogApprove.getReason())) {
            // 异常日志，没有原因。用"修改订单/经费状态"文案来代替
            this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                    orderLogApprove.getUserName(), omsFixDataEnum.getName(), orderLogApprove.getReason(), businessType);
            return;
        }
        String[] reasonArr = orderLogApprove.getReason().split("\\|");
        if (reasonArr.length < 2) {
            // 异常日志，文案格式不对。用"修改订单/经费状态"文案来代替
            this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                    orderLogApprove.getUserName(), omsFixDataEnum.getName(), orderLogApprove.getReason(), businessType);
            return;
        }
        Integer fixStatus = Integer.parseInt(reasonArr[0]);
        String statusDesc = null;
        if (OmsFixDataEnum.FIX_ORDER_STATUS.equals(omsFixDataEnum)) {
            OrderStatusEnum orderStatusEnum = OrderStatusEnum.get(fixStatus);
            statusDesc = orderStatusEnum == null ? null : orderStatusEnum.getName();
        } else if (OmsFixDataEnum.FIX_FUND_STATUS.equals(omsFixDataEnum)) {
            OrderFundStatusEnum orderFundStatusEnum = OrderFundStatusEnum.get(fixStatus);
            statusDesc = orderFundStatusEnum == null ? null : orderFundStatusEnum.getName();
        }

        if (statusDesc == null) {
            // 异常日志，没有对应的订单状态。用"修改订单/经费状态"文案来代替
            this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                    orderLogApprove.getUserName(), omsFixDataEnum.getName(), orderLogApprove.getReason(), businessType);
            return;
        }
        String reason = reasonArr[1];
        this.setOperation(operationLogList, orderId, orderLogApprove.getId(), new Date(orderLogApprove.getCreationTime()),
                orderLogApprove.getUserName(), statusDesc, reason, businessType);
    }

    /**
     * 设置操作日志
     */
    private void setOperation(List<OrderOperationLogVO> operationLogList, Integer orderId, Integer id, Date date, String name, String action, String remark, Integer businessType) {
        OrderOperationLogVO op = this.createOperation(orderId, date, name, action, remark, businessType);
        op.setId(id);
        operationLogList.add(op);
    }

    /**
     * 创建操作日志记录
     */
    private OrderOperationLogVO createOperation(Integer orderId, Date date, String name, String action, String remark, Integer businessType) {
        OrderOperationLogVO op = new OrderOperationLogVO();
        op.setOrderId(orderId);
        if (date != null) {
            op.setCreateTime(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, date));
            op.setTimeStamp(String.valueOf(date.getTime()));
        } else {
            op.setCreateTime("--");
            op.setTimeStamp("--");
        }
        op.setOperatorName(name);
        op.setOperationType(action);
        if (StringUtils.isNotBlank(remark)) {
            op.setRemark(remark);
        }
        op.setBusinessType(businessType);
        return op;
    }

    /**
     * 批量查询订单主表信息
     *
     * @param orderIds 订单ID列表
     * @param orderNos 订单号列表
     * @return 订单主表信息列表
     */
    private List<OrderMasterDO> batchQueryOrderMaster(List<Integer> orderIds, List<String> orderNos) {
        List<OrderMasterDO> result = New.list();

        // 根据订单ID查询
        if (CollectionUtils.isNotEmpty(orderIds)) {
            List<OrderMasterDO> orderMasterByIds = orderMasterMapper.findByIdIn(orderIds);
            if (CollectionUtils.isNotEmpty(orderMasterByIds)) {
                result.addAll(orderMasterByIds);
            }
        }

        // 根据订单号查询
        if (CollectionUtils.isNotEmpty(orderNos)) {
            List<OrderMasterDO> orderMasterByNos = orderMasterMapper.findByFordernoIn(orderNos);
            if (CollectionUtils.isNotEmpty(orderMasterByNos)) {
                result.addAll(orderMasterByNos);
            }
        }

        return result.stream().distinct().collect(toList());
    }

    /**
     * 批量查询订单审批日志
     *
     * @param orderMasterList 订单主表信息列表
     * @return 订单ID到审批日志的映射
     */
    public Map<Integer, List<OrderLogApproveBO>> batchGetOrderApproveLog(List<OrderMasterDO> orderMasterList) {
        if (CollectionUtils.isEmpty(orderMasterList)) {
            return New.emptyMap();
        }

        Integer orgId = orderMasterList.get(0).getFuserid();

        // 获取所有订单ID
        List<Integer> orderIds = orderMasterList.stream()
                .map(OrderMasterDO::getId)
                .collect(toList());

        // 批量查询审批日志
        List<OrderApprovalLog> allApprovalLogs = orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(orderIds, New.emptyList());
        if (CollectionUtils.isEmpty(allApprovalLogs)) {
            return New.emptyMap();
        }

        // 处理订单文件操作日志
        processBatchOrderFileLogs(allApprovalLogs);

        // 收集所有用户ID
        Set<Integer> allUserIds = allApprovalLogs.stream()
                .map(OrderApprovalLog::getOperatorId)
                .filter(id -> !SYSTEM_OPERATOR_ID.equals(id)) // 过滤掉系统操作
                .collect(Collectors.toSet());

        // 批量查询用户信息
        List<UserBaseInfoDTO> userList = userClient.getUserByIdsAndOrgId(
                New.list(allUserIds),
                orderMasterList.get(0).getFuserid()
        );
        Map<Integer, String> userIdNameMap = DictionaryUtils.toMap(userList, UserBaseInfoDTO::getId, UserBaseInfoDTO::getName);

        // 按订单ID分组
        Map<Integer, List<OrderApprovalLog>> orderLogMap = allApprovalLogs.stream()
                .collect(Collectors.groupingBy(OrderApprovalLog::getOrderId));

        // 构造结果
        Map<Integer, List<OrderLogApproveBO>> result = New.map();

        // 按订单处理日志
        Map<Integer, OrderMasterDO> orderIdMasterMap = DictionaryUtils.toMap(orderMasterList, OrderMasterDO::getId, Function.identity());

        for (Map.Entry<Integer, List<OrderApprovalLog>> entry : orderLogMap.entrySet()) {
            Integer orderId = entry.getKey();
            List<OrderApprovalLog> approvalLogList = entry.getValue();
            OrderMasterDO orderMaster = orderIdMasterMap.get(orderId);

            if (orderMaster == null) {
                continue;
            }

            List<OrderLogApproveBO> orderLogApproveList = New.listWithCapacity(approvalLogList.size());

            for (OrderApprovalLog approvalLog : approvalLogList) {
                OrderLogApproveBO orderLogApprove = new OrderLogApproveBO();
                orderLogApprove.setId(approvalLog.getId());
                orderLogApprove.setCreationTime(approvalLog.getCreationTime().getTime());
                orderLogApprove.setOrderApprovalStatus(approvalLog.getApproveStatus());

                OrderApprovalEnum orderApprovalEnum = OrderApprovalEnum.getByValue(approvalLog.getApproveStatus());
                if (orderApprovalEnum != null) {
                    String statusDesc = orderApprovalEnum.getName();
                    // 甘肃妇幼定制 文案
                    if (Objects.equals(OrgEnum.GAN_SU_SHENG_FU_YOU_BAO_JIAN_YUAN_GAN_SU_SHENG_ZHONG_XIN_YI_YUAN.getValue(), orgId)) {
                        if (Objects.equals(orderApprovalEnum.getValue(), OrderApprovalEnum.PASS.getValue())) {
                            statusDesc = "验收确认通过";
                        } else if (Objects.equals(orderApprovalEnum.getValue(), OrderApprovalEnum.REJECT.getValue())) {
                            statusDesc = "验收确认驳回";
                        } else if (Objects.equals(orderApprovalEnum.getValue(), OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL.getValue())){
                            statusDesc = "无需验收确认";
                        }
                    }
                    orderLogApprove.setStatusDesc(statusDesc);
                }

                orderLogApprove.setReason(approvalLog.getReason());
                orderLogApprove.setPhoto(approvalLog.getPhoto());

                if (approvalLog.getApproveLevel() != 0) {
                    orderLogApprove.setApproveLevel(approvalLog.getApproveLevel());
                }

                Integer userId = approvalLog.getOperatorId();

                if (approvalLog.getOperatorName() != null){
                    // 有设置操作人姓名的，优先取操作人姓名
                    orderLogApprove.setUserName(approvalLog.getOperatorName());
                }else if (SYSTEM_OPERATOR_ID.equals(userId)) {
                    orderLogApprove.setUserName(SYSTEM_OPERATOR_NAME);
                } else {
                    // 如果操作人是1540 而且是驳回状态 的话 既是（财务驳回了）供应商也操作取消 订单表获取供应商的名字 ----【特殊需求切记】
                    if (PROMISE_SUPPLIER_FLAG.equals(userId) && OrderApprovalEnum.REJECT_FOR_STATEMENT.getValue().equals(approvalLog.getApproveStatus())) {
                        orderLogApprove.setUserName(orderMaster.getFsuppname());
                        orderLogApprove.setStatusDesc("供应商取消结算");
                    } else if (PROMISE_SUPPLIER_FLAG.equals(userId) && OrderApprovalEnum.CANCEL.getValue().equals(approvalLog.getApproveStatus())) {
                        orderLogApprove.setUserName(orderMaster.getFsuppname());
                        orderLogApprove.setStatusDesc(OrderOperateLogConstant.SUPPLIER_CANCEL_ORDER);
                        if (AUTO_CANCEL.equals(approvalLog.getReason())) {
                            String userName = userIdNameMap.get(userId);
                            orderLogApprove.setUserName(userName);
                            orderLogApprove.setStatusDesc("取消订单");
                        }
                        if (AGREE_CANCEL.equals(approvalLog.getReason())) {
                            orderLogApprove.setStatusDesc(approvalLog.getReason());
                            orderLogApprove.setReason("");
                        }
                    } else if (OrderApprovalEnum.REFUSE_CANCEL.getValue().equals(approvalLog.getApproveStatus())) {
                        if (PROMISE_SUPPLIER_FLAG.equals(userId)) {
                            orderLogApprove.setUserName(orderMaster.getFsuppname());
                        } else {
                            orderLogApprove.setUserName(userIdNameMap.get(userId));
                        }
                        orderLogApprove.setStatusDesc(OrderApprovalEnum.REFUSE_CANCEL.getName());
                        orderLogApprove.setReason(approvalLog.getReason());
                    } else if (SHOW_SUPP_NAME_AS_USER_ENUMS.contains(approvalLog.getApproveStatus())) {
                        // 需要以供应商名展示的 展示供应商名
                        orderLogApprove.setUserName(orderMaster.getFsuppname());
                    } else {
                        orderLogApprove.setUserName(userIdNameMap.get(userId));
                        if (AGREE_CANCEL.equals(approvalLog.getReason())) {
                            orderLogApprove.setStatusDesc(approvalLog.getReason());
                            orderLogApprove.setReason("");
                        }
                    }
                }

                // 国际化处理
                translate(orderLogApprove);
                orderLogApproveList.add(orderLogApprove);
            }

            result.put(orderId, orderLogApproveList);
        }

        return result;
    }

    /**
     * 批量处理订单文件操作日志
     *
     * @param allApprovalLogs 所有订单的审批日志列表
     */
    private void processBatchOrderFileLogs(List<OrderApprovalLog> allApprovalLogs) {
        if (CollectionUtils.isEmpty(allApprovalLogs)) {
            return;
        }

        // 获取所有订单的订单ID
        List<Integer> allOrderIds = allApprovalLogs.stream()
                .map(OrderApprovalLog::getOrderId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询所有订单的文件操作日志
        OrderFileOperationLogBatchQueryRequestDTO requestDTO = new OrderFileOperationLogBatchQueryRequestDTO();
        requestDTO.setOrderIds(allOrderIds);
        List<OrderFileOperationLogDTO> orderFileOperationLogDTOS = orderOtherLogClient.batchQueryFileLog(requestDTO);
        if (CollectionUtils.isEmpty(orderFileOperationLogDTOS)) {
            return;
        }

        // 根据订单日志ID分组
        Map<Integer, List<OrderFileOperationLogDTO>> logId2DTOMap = orderFileOperationLogDTOS.stream()
                .collect(Collectors.groupingBy(OrderFileOperationLogDTO::getLogId));

        // 处理每个审批日志
        for (OrderApprovalLog approvalLog : allApprovalLogs) {
            // 处理删除验收图片备注
            if (Objects.equals(OrderApprovalEnum.DELETE_ACCEPTANCE_PHOTO.getValue(), approvalLog.getApproveStatus())) {
                processDeletePhotoRemark(approvalLog, logId2DTOMap);
            }

            // 处理删除验收附件备注
            if (Objects.equals(OrderApprovalEnum.DELETE_ACCEPTANCE_ATTACHMENT.getValue(), approvalLog.getApproveStatus())) {
                processDeleteAttachmentRemark(approvalLog, logId2DTOMap);
            }
        }
    }

    /**
     * 处理删除验收图片备注
     */
    private void processDeletePhotoRemark(OrderApprovalLog approvalLog, Map<Integer, List<OrderFileOperationLogDTO>> logId2DTOMap) {
        List<OrderFileOperationLogDTO> fileOperationLogDTOS = logId2DTOMap.get(approvalLog.getId());
        if (CollectionUtils.isEmpty(fileOperationLogDTOS)) {
            return;
        }
        AtomicInteger counter = new AtomicInteger(1);
        List<String> htmlLinks = fileOperationLogDTOS.stream()
                .map(dto -> StrUtil.format(OrderOperateLogConstant.HTML_LINK, dto.getUrl(), "图片" + counter.getAndIncrement()))
                .collect(Collectors.toList());
        String remark = StrUtil.format(OrderOperateLogConstant.DELETE_IMAGE_REMARK_PREFIX, fileOperationLogDTOS.size(), StrUtil.join(" ", htmlLinks));
        approvalLog.setReason(remark);
    }

    /**
     * 处理删除验收附件备注
     */
    private void processDeleteAttachmentRemark(OrderApprovalLog approvalLog, Map<Integer, List<OrderFileOperationLogDTO>> logId2DTOMap) {
        List<OrderFileOperationLogDTO> fileOperationLogDTOS = logId2DTOMap.get(approvalLog.getId());
        if (CollectionUtils.isEmpty(fileOperationLogDTOS)) {
            return;
        }
        List<String> htmlLinks = fileOperationLogDTOS.stream()
                .map(fileDTO -> StrUtil.format(OrderOperateLogConstant.HTML_LINK, fileDTO.getUrl(), fileDTO.getFileName()))
                .collect(Collectors.toList());
        String remark = StrUtil.format(OrderOperateLogConstant.DELETE_ATTACHMENT_REMARK_PREFIX, fileOperationLogDTOS.size(), StrUtil.join(" ", htmlLinks));
        approvalLog.setReason(remark);
    }

    /**
     * 日志国际化处理
     */
    private void translate(OrderLogApproveBO orderLogApprove) {
        if (Objects.isNull(orderLogApprove)) {
            return;
        }
        if (Objects.equals(orderLogApprove.getOrderApprovalStatus(), OrderApprovalEnum.CANCEL.getValue())) {
            String reason = orderLogApprove.getReason();
            String cnKey1 = "供应商申请取消原因";
            String cnKey2 = "商家申请取消原因";
            if (StringUtils.startsWith(reason, cnKey1)) {
                reason = StringUtils.replace(reason, cnKey1, LocalI18nUtils.translate(cnKey1));
            } else if (StringUtils.startsWith(reason, cnKey2)) {
                reason = StringUtils.replace(reason, cnKey2, LocalI18nUtils.translate(cnKey2));
            }
            orderLogApprove.setReason(reason);
        }
    }

    /**
     * 批量查询订单验收审批电子签名
     *
     * @param orderIds 订单ID列表
     * @return 电子签名记录列表
     */
    private List<ElectronicSignOperationRecordDTO> batchGetAcceptApproveElectronicSign(List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyList();
        }

        OperationListDTO operationListDTO = new OperationListDTO();
        operationListDTO.setBusinessType(BusinessTypeEnum.ORDER);
        operationListDTO.setOperation(New.list(ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL));
        operationListDTO.setBusinessIdList(orderIds);
        return electronicSignServiceClient.getElectronicSignData(operationListDTO);
    }

    /**
     * 批量查询订单结算日志
     *
     * @param orderIds 订单ID列表
     * @return 按订单ID分组的结算日志列表
     */
    private Map<Integer, List<StatementLogResultDTO>> batchGetStatementLogs(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyMap();
        }

        // 使用批量接口查询，返回值是List<OrderRefStatementLogDTO>
        List<OrderRefStatementLogDTO> orderRefStatementLogDTOList = statementPlatformClient.listStatementLogByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderRefStatementLogDTOList)) {
            return New.emptyMap();
        }

        // 将结果转换成Map<Integer, List<StatementLogResultDTO>>格式
        Map<Integer, List<StatementLogResultDTO>> result = New.map();
        for (OrderRefStatementLogDTO orderRefStatementLogDTO : orderRefStatementLogDTOList) {
            if (orderRefStatementLogDTO != null && orderRefStatementLogDTO.getOrderId() != null) {
                result.put(orderRefStatementLogDTO.getOrderId(), orderRefStatementLogDTO.getStatementLogList());
            }
        }

        return result;
    }

    /**
     * 批量查询订单退货信息
     *
     * @param orderIds 订单ID列表
     * @return 订单ID到退货信息列表的映射
     */
    private Map<Integer, List<GoodsReturn>> batchGetGoodsReturns(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyMap();
        }

        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(goodsReturnList)) {
            return New.emptyMap();
        }

        return goodsReturnList.stream()
                .collect(Collectors.groupingBy(GoodsReturn::getOrderId));
    }

    /**
     * 批量查询退货日志
     *
     * @param returnIds 退货单ID列表
     * @return 退货单ID到退货日志列表的映射
     */
    private Map<Integer, List<GoodsReturnLogDO>> batchGetGoodsReturnLogs(List<Integer> returnIds) {
        if (CollectionUtils.isEmpty(returnIds)) {
            return New.emptyMap();
        }

        List<GoodsReturnLogDO> goodsReturnLogDOList = goodsReturnLogDOMapper.findByReturnIdIn(returnIds);
        if (CollectionUtils.isEmpty(goodsReturnLogDOList)) {
            return New.emptyMap();
        }

        return goodsReturnLogDOList.stream()
                .collect(Collectors.groupingBy(GoodsReturnLogDO::getReturnId));
    }

    /**
     * 批量查询订单订单抽检数据
     *
     * @param orderNos 订单号列表
     * @return 订单号到订单抽检数据的映射
     */
    private Map<String, List<OrderAuditSamplingDTO>> batchGetOrderAuditSampling(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return New.emptyMap();
        }

        List<OrderAuditSamplingDTO> orderAuditSamplingDTOList = orderAuditSamplingClient.listByOrderNos(orderNos);
        if (CollectionUtils.isEmpty(orderAuditSamplingDTOList)) {
            return New.emptyMap();
        }

        return orderAuditSamplingDTOList.stream()
                .collect(Collectors.groupingBy(OrderAuditSamplingDTO::getOrderNo));
    }

    /**
     * 批量查询OMS数据操作日志
     *
     * @param orderNos 订单号列表
     * @return 订单号到OMS数据操作日志的映射
     */
    private Map<String, List<DataOperationLogDTO>> batchGetOmsDateOperationLog(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return New.emptyMap();
        }

        // 创建批量查询请求对象
        DataOperationLogQueryRequest request = new DataOperationLogQueryRequest();
        request.setOrderNos(orderNos);
        // 操作类型为空，查询所有类型
        request.setOperationTypes(null);

        // 批量查询所有订单的日志
        List<DataOperationLogDTO> allLogs = dataOperationLogRpcClient.listByOrderNosAndTypes(request);
        if (CollectionUtils.isEmpty(allLogs)) {
            return New.emptyMap();
        }

        // 按订单号分组
        return allLogs.stream().collect(Collectors.groupingBy(DataOperationLogDTO::getOrderNo));
    }

    /**
     * 查询订单关联的所有业务日志
     */
    @Override
    public List<PurchaseOrderAllRelateLogVO> listOrderAllRelateLog(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return New.emptyList();
        }

        List<OrderMasterDO> orderMasterDOList = batchQueryOrderMaster(orderIdList, null);
        if (CollectionUtils.isEmpty(orderMasterDOList)) {
            return New.emptyList();
        }

        orderIdList = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());

        List<PurchaseOrderAllRelateLogVO> result = New.list();
        // 订单日志集合
        List<OrderOperationLogVO> orderOperationLogVOS = batchOrderOperationLog(orderMasterDOList, true);
        Map<Integer, List<OrderOperationLogVO>> orderId2OrderOperationLogVOS = orderOperationLogVOS.stream()
                .collect(Collectors.groupingBy(OrderOperationLogVO::getOrderId));

        // 竞价单号集合， bidOrderId是竞价单号
        List<String> bidOrderNos = orderMasterDOList.stream()
                .filter(order -> OrderTypeEnum.BID_ORDER.getCode().equals(order.getOrderType()))
                .map(OrderMasterDO::getBidOrderId).distinct().collect(toList());
        // 采购id集合
        List<Integer> purchaseOrderIds = orderMasterDOList.stream()
                .filter(order -> OrderTypeEnum.PURCHASE_ORDER.getCode().equals(order.getOrderType()))
                .map(OrderMasterDO::getFtbuyappid).filter(Objects::nonNull).distinct().collect(toList());

        // 获取采购单ID到采购单号的映射
        Map<Integer, String> purchaseIdToNoMap = New.map();
        if (CollectionUtils.isNotEmpty(purchaseOrderIds)) {
            List<ApplicationMasterDTO> applicationMasterList = applicationBaseClient.findByMasterId(purchaseOrderIds);
            if (CollectionUtils.isNotEmpty(applicationMasterList)) {
                applicationMasterList.forEach(app -> {
                    purchaseIdToNoMap.put(app.getId().intValue(), app.getApplyNumber());
                });
            }
        }

        // 采购单日志
        List<PurchaseApprovalLogDTO> purchaseApprovalLogDTOList = purchaseApprovalLogClient.getApprovalLogByIdListBatch(New.set(purchaseOrderIds));
        Map<Integer, List<PurchaseApprovalLogDTO>> purchaseIdLogMap = purchaseApprovalLogDTOList.stream()
                .collect(Collectors.groupingBy(PurchaseApprovalLogDTO::getApplicationId));

        // 竞价单日志
        List<BidApprovalLogDTO> bidMasterDTOList = bidClient.findOperationLogListByBidNos(bidOrderNos);
        Map<String, List<BidApprovalLogDTO>> bidNoLogMap = bidMasterDTOList.stream()
                .collect(Collectors.groupingBy(BidApprovalLogDTO::getBidNo));

        // 结算单日志
        Map<Integer, List<StatementLogResultDTO>> orderIdStatementLogMap = batchGetStatementLogs(orderIdList);

        for (OrderMasterDO orderMasterDO : orderMasterDOList) {
            Integer orderId = orderMasterDO.getId();
            String orderNo = orderMasterDO.getForderno();
            Integer orderType = orderMasterDO.getOrderType();
            List<OrderOperationLogVO> orderLogList = orderId2OrderOperationLogVOS.get(orderId);
            List<PurchaseApprovalLogDTO> purchaseLogList = purchaseIdLogMap.get(orderMasterDO.getFtbuyappid());
            List<BidApprovalLogDTO> bidLogList = bidNoLogMap.get(orderMasterDO.getBidOrderId());
            List<StatementLogResultDTO> statementLogList = orderIdStatementLogMap.get(orderId);

            // 结算单日志ID集合
            Set<Integer> processedStatementLogIds = New.set();

            // 处理订单日志
            if (CollectionUtils.isNotEmpty(orderLogList)) {
                for (OrderOperationLogVO operationLog : orderLogList) {
                    // 如果是结算类型的日志，则记录ID用于后续去重
                    if (OperateLogBussinessTypeEnum.SETTLEMENT.getCode().equals(operationLog.getBusinessType()) && Objects.nonNull(operationLog.getId())) {
                        processedStatementLogIds.add(operationLog.getId());
                    }
                    PurchaseOrderAllRelateLogVO logVO = new PurchaseOrderAllRelateLogVO();
                    logVO.setId(operationLog.getId())
                            .setOrderId(orderId)
                            .setOrderNo(orderNo)
                            .setOperationType(operationLog.getOperationType())
                            .setOperatorName(operationLog.getOperatorName())
                            .setCreateTime(StringUtils.isNotBlank(operationLog.getTimeStamp()) ? new Date(Long.parseLong(operationLog.getTimeStamp())) : null)
                            .setRemark(operationLog.getRemark())
                            .setBusinessType(operationLog.getBusinessType());
                    result.add(logVO);
                }
            }

            // 处理采购单日志
            if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderType) && CollectionUtils.isNotEmpty(purchaseLogList)) {
                Integer purchaseId = orderMasterDO.getFtbuyappid();
                String purchaseNo = purchaseIdToNoMap.get(purchaseId);
                for (PurchaseApprovalLogDTO purchaseLog : purchaseLogList) {
                    PurchaseOrderAllRelateLogVO logVO = new PurchaseOrderAllRelateLogVO();
                    logVO.setId(purchaseLog.getId())
                            .setOrderId(orderId)
                            .setOrderNo(orderNo)
                            .setPurchaseBidNo(purchaseNo)
                            .setOperationType(purchaseLog.getResult())
                            .setOperatorName(purchaseLog.getApprover())
                            .setCreateTime(purchaseLog.getApproveTime())
                            .setRemark(purchaseLog.getComment())
                            .setBusinessType(OperateLogBussinessTypeEnum.PURCHASE.getCode());
                    result.add(logVO);
                }
            }

            // 处理竞价单日志
            if (OrderTypeEnum.BID_ORDER.getCode().equals(orderType) && CollectionUtils.isNotEmpty(bidLogList)) {
                String bidNo = orderMasterDO.getBidOrderId();
                for (BidApprovalLogDTO bidLog : bidLogList) {
                    PurchaseOrderAllRelateLogVO logVO = new PurchaseOrderAllRelateLogVO();
                    logVO.setId(bidLog.getLogId())
                            .setOrderId(orderId)
                            .setOrderNo(orderNo)
                            .setPurchaseBidNo(bidNo)
                            .setOperationType(bidLog.getOperationName())
                            .setOperatorName(bidLog.getOperatorName())
                            .setCreateTime(bidLog.getOperateDate())
                            .setRemark(bidLog.getNote())
                            .setBusinessType(OperateLogBussinessTypeEnum.BID.getCode());
                    result.add(logVO);
                }
            }

            // 处理结算单日志
            if (CollectionUtils.isNotEmpty(statementLogList)) {
                for (StatementLogResultDTO statementLog : statementLogList) {
                    // 已经在订单中处理的结算日志，跳过
                    if (Objects.nonNull(statementLog.getId()) && processedStatementLogIds.contains(statementLog.getId().intValue())) {
                        continue;
                    }
                    PurchaseOrderAllRelateLogVO logVO = new PurchaseOrderAllRelateLogVO();
                    logVO.setId(statementLog.getId().intValue())
                            .setOrderId(orderId)
                            .setOrderNo(orderNo)
                            .setSettlementNo(statementLog.getBalanceNumber())
                            .setOperationType(statementLog.getOperatorTypeName())
                            .setOperatorName(statementLog.getUserName())
                            .setCreateTime(statementLog.getCreationTime())
                            .setRemark(statementLog.getReason())
                            .setBusinessType(OperateLogBussinessTypeEnum.SETTLEMENT.getCode());
                    result.add(logVO);
                }
            }
        }

        // 按订单ID升序、业务类型升序、创建时间升序、日志ID升序排序
        result.sort(Comparator.comparing(PurchaseOrderAllRelateLogVO::getOrderId, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(PurchaseOrderAllRelateLogVO::getBusinessType, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(log -> Objects.isNull(log.getCreateTime()) ? new Date(0) : log.getCreateTime())
                .thenComparing(log -> Objects.isNull(log.getId()) ? 0 : log.getId()));

        return result;
    }

    /**
     * 查询单个订单关联的所有业务日志
     *
     * @param orderId 订单ID
     * @return 订单关联的所有业务日志
     */
    @Override
    public List<PurchaseOrderAllRelateLogVO> getOrderAllRelateLog(Integer orderId) {
        BusinessErrUtil.notNull(orderId, ExecptionMessageEnum.ORDER_LOG_INTERFACE_ERROR);
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);
        return listOrderAllRelateLog(New.list(orderMasterDO.getId()));
    }

    /**
     * 获取订单操作日志记录
     *
     * @param orderId
     * @param orderNo
     */
    @Override
    public List<OrderOperationLogVO> orderOperationLog(Integer orderId, String orderNo) {
        OrderMasterDO orderMaster = null;
        if (orderId != null) {
            orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        } else {
            BusinessErrUtil.isTrue(StringUtils.isNotBlank(orderNo), ExecptionMessageEnum.ORDER_LOG_INTERFACE_ERROR);
            orderMaster = orderMasterMapper.findByForderno(orderNo);
            BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);
        }
        return batchOrderOperationLog(New.list(orderMaster));
    }

    /**
     * 获取代配送日志
     *
     * @param orderId 订单id
     * @return 操作日志
     */
    @Override
    public List<OrderOperationLogVO> getDeliveryProxyOprLog(Integer orderId) {
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);
        Map<Integer, List<OrderLogApproveBO>> orderId2LogApproveList = this.batchGetOrderApproveLog(New.list(orderMaster));
        List<OrderLogApproveBO> orderLogApproveList = orderId2LogApproveList.get(orderMaster.getId());
        if (CollectionUtils.isEmpty(orderLogApproveList)) {
            return New.emptyList();
        }
        final List<Integer> deliveryProxyOprEnums = New.list(
                OrderApprovalEnum.SUPP_APPLY_CANCEL_DELIVERY_PROXY.getValue(),
                OrderApprovalEnum.BUYER_AGREE_CANCEL_DELIVERY_PROXY.getValue(),
                OrderApprovalEnum.BUYER_REJECT_CANCEL_DELIVERY_PROXY.getValue()
        );
        List<OrderOperationLogVO> operationLogList = New.list();
        orderLogApproveList.stream().filter(log -> deliveryProxyOprEnums.contains(log.getOrderApprovalStatus()))
                .forEach(log -> this.setOperation(operationLogList, orderId, log.getId(), new Date(log.getCreationTime()),
                        log.getUserName(), log.getStatusDesc(), log.getReason(),
                        OperateLogBussinessTypeEnum.ORDER.getCode()));
        // 按时间倒序
        return operationLogList.stream()
                .sorted(Comparator.comparing(OrderOperationLogVO::getCreateTime).reversed())
                .collect(toList());
    }

    /**
     * @param orderMaster
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalInfoVO>
     * @description: 根据订单信息获取供应商或采购人取消操作日志
     * @date: 2021/1/14 19:22
     * @author: zengyanru
     */
    @Override
    public List<OrderApprovalInfoVO> getOrderApprovalInfo(OrderMasterDO orderMaster) {
        if (Objects.isNull(orderMaster)) {
            return New.emptyList();
        }
        Map<Integer, List<OrderLogApproveBO>> orderId2LogApproveList = this.batchGetOrderApproveLog(New.list(orderMaster));
        if (MapUtils.isEmpty(orderId2LogApproveList)) {
            return New.emptyList();
        }
        List<OrderLogApproveBO> orderApprovalList = orderId2LogApproveList.get(orderMaster.getId());
        if (CollectionUtils.isEmpty(orderApprovalList)) {
            return New.emptyList();
        }
        List<OrderApprovalInfoVO> orderApprovalInfoVOList = New.listWithCapacity(orderApprovalList.size());

        orderApprovalList.sort(Comparator.comparing(OrderLogApproveBO::getCreationTime));
        for (OrderLogApproveBO orderLogApprove : orderApprovalList) {
            OrderApprovalInfoVO approveInfoVO = new OrderApprovalInfoVO();
            approveInfoVO.setCreationTime(orderLogApprove.getCreationTime());
            approveInfoVO.setId(orderLogApprove.getId());
            approveInfoVO.setOrderApprovalStatus(OrderApprovalEnum.getByValue(orderLogApprove.getOrderApprovalStatus()));
            approveInfoVO.setApproveLevel(orderLogApprove.getApproveLevel());
            approveInfoVO.setPhoto(orderLogApprove.getPhoto());
            approveInfoVO.setReason(orderLogApprove.getReason());
            approveInfoVO.setStatusDesc(orderLogApprove.getStatusDesc());
            approveInfoVO.setUserName(orderLogApprove.getUserName());
            orderApprovalInfoVOList.add(approveInfoVO);
        }
        return orderApprovalInfoVOList;
    }

    /**
     * 增加申领日志
     *
     * @param operationLogList 日志
     * @param orderId          订单id
     * @param claimList        申领列表
     */
    private void addClaimLogs(List<OrderOperationLogVO> operationLogList, Integer orderId, List<BizWarehouseReceiceDTO> claimList) {
        if (CollectionUtils.isNotEmpty(claimList)) {
            claimList.forEach(item -> this.setOperation(operationLogList, orderId, item.getId(), item.getCreateTime(), item.getApplyUserName(), "确认领用", null,
                    OperateLogBussinessTypeEnum.ORDER.getCode()));
        }
    }
}