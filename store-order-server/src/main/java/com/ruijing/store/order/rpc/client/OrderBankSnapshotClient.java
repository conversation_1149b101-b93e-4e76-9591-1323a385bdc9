package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.OrderBankAccountSnapshotDTO;
import com.reagent.order.base.order.service.OrderBankAccountSnapshotRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.api.base.other.dto.OrderBankDataDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-11-30 10:53
 * @description:
 **/
@ServiceClient
public class OrderBankSnapshotClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderBankAccountSnapshotRpcService orderBankAccountSnapshotRpcService;

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void insertList(List<OrderBankDataDTO> orderBankDataDTOList){
        RemoteResponse<Boolean> response = orderBankAccountSnapshotRpcService.insertList(orderBankDataDTOList.stream().map(this::orderDto2galaxyDto).collect(Collectors.toList()));
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void updateList(List<OrderBankDataDTO> orderBankDataDTOList){
        RemoteResponse<Boolean> response = orderBankAccountSnapshotRpcService.updateList(orderBankDataDTOList.stream().map(this::orderDto2galaxyDto).collect(Collectors.toList()));
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void saveList(List<OrderBankDataDTO> orderBankDataDTOList){
        RemoteResponse<Boolean> response = orderBankAccountSnapshotRpcService.saveList(orderBankDataDTOList.stream().map(this::orderDto2galaxyDto).collect(Collectors.toList()));
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(operationType = OperationType.READ, serviceType = ServiceType.RPC_CLIENT)
    public List<OrderBankDataDTO> listByOrderId(List<Integer> orderIdList){
        RemoteResponse<List<OrderBankAccountSnapshotDTO>> response = orderBankAccountSnapshotRpcService.listByOrderId(orderIdList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        List<OrderBankAccountSnapshotDTO> resultList = response.getData();
        if(CollectionUtils.isEmpty(resultList)){
            return New.emptyList();
        }
        return resultList.stream().map(this::galaxyDto2OrderDto).collect(Collectors.toList());
    }

    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public boolean deleteByOrderIdList(List<Integer> orderIdList){
        RemoteResponse<Boolean> response = orderBankAccountSnapshotRpcService.deleteByOrderIdList(orderIdList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    private OrderBankAccountSnapshotDTO orderDto2galaxyDto(OrderBankDataDTO input){
        return new OrderBankAccountSnapshotDTO()
                .setOrderId(input.getOrderId())
                .setOrderNo(input.getOrderNo())
                .setOrgId(input.getOrgId())
                .setBankId(input.getBankId())
                .setBankAccountName(input.getBankAccountName())
                .setBankName(input.getBankName())
                .setBankBranch(input.getBankBranch())
                .setBankCode(input.getBankCode())
                .setProvinceCode(input.getProvinceCode())
                .setCityCode(input.getCityCode())
                .setBankCardNumber(input.getBankCardNumber())
                .setAccountType(input.getAccountType());
    }

    private OrderBankDataDTO galaxyDto2OrderDto(OrderBankAccountSnapshotDTO input){
        return new OrderBankDataDTO()
                .setOrderId(input.getOrderId())
                .setOrderNo(input.getOrderNo())
                .setOrgId(input.getOrgId())
                .setBankId(input.getBankId())
                .setBankAccountName(input.getBankAccountName())
                .setBankName(input.getBankName())
                .setBankBranch(input.getBankBranch())
                .setBankCode(input.getBankCode())
                .setProvinceCode(input.getProvinceCode())
                .setCityCode(input.getCityCode())
                .setBankCardNumber(input.getBankCardNumber())
                .setAccountType(input.getAccountType());
    }
}
