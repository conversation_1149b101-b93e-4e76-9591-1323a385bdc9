package com.ruijing.store.order.gateway.buyercenter.request;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/5/20 9:48
 **/
public class PrivateChangeFundCardRequestDTO implements Serializable {

    private static final long serialVersionUID = -7248046404132362230L;

    /**
     * 换卡入参
     */
    private ChangeFundCardRequestDTO request;

    /**
     * orgId
     */
    private Integer orgId;

    /**
     * 用户guid
     */
    private String guid;

    /**
     * 是否跳过校验两张经费卡是否一致的环节
     */
    private Boolean skipCheckCardEqual;

    /**
     * 最末级的卡id
     */
    private String cardId;

    public ChangeFundCardRequestDTO getRequest() {
        return request;
    }

    public void setRequest(ChangeFundCardRequestDTO request) {
        this.request = request;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Boolean getSkipCheckCardEqual() {
        return skipCheckCardEqual;
    }

    public void setSkipCheckCardEqual(Boolean skipCheckCardEqual) {
        this.skipCheckCardEqual = skipCheckCardEqual;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PrivateChangeFundCardRequestDTO{");
        sb.append("request=").append(request);
        sb.append(", orgId=").append(orgId);
        sb.append(", guid='").append(guid).append('\'');
        sb.append(", skipCheckCardEqual=").append(skipCheckCardEqual);
        sb.append(", cardId='").append(cardId).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
