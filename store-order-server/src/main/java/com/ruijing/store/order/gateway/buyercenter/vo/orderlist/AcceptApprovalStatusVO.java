package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.order.saturn.api.accept.approve.enums.AcceptApprovalStatusEnum;

import java.io.Serializable;

@Model("验收审批状态实体")
public class AcceptApprovalStatusVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty(value = "验收审批状态值", enumClass = AcceptApprovalStatusEnum.class)
    private Integer acceptApprovalStatus;

    @ModelProperty(value = "验收审批流程状态描述", description = "审批通过/审批驳回/待X级审批")
    private String acceptApprovalStatusDesc;

    @ModelProperty("验收审批级别")
    private Integer acceptApprovalLevel;

    public Integer getAcceptApprovalStatus() {
        return acceptApprovalStatus;
    }

    public AcceptApprovalStatusVO setAcceptApprovalStatus(Integer acceptApprovalStatus) {
        this.acceptApprovalStatus = acceptApprovalStatus;
        return this;
    }

    public String getAcceptApprovalStatusDesc() {
        return acceptApprovalStatusDesc;
    }

    public AcceptApprovalStatusVO setAcceptApprovalStatusDesc(String acceptApprovalStatusDesc) {
        this.acceptApprovalStatusDesc = acceptApprovalStatusDesc;
        return this;
    }

    public Integer getAcceptApprovalLevel() {
        return acceptApprovalLevel;
    }

    public AcceptApprovalStatusVO setAcceptApprovalLevel(Integer acceptApprovalLevel) {
        this.acceptApprovalLevel = acceptApprovalLevel;
        return this;
    }

    @Override
    public String toString() {
        return "AcceptApprovalStatusVO{" +
                "acceptApprovalStatus=" + acceptApprovalStatus +
                ", acceptApprovalStatusDesc='" + acceptApprovalStatusDesc + '\'' +
                ", acceptApprovalLevel=" + acceptApprovalLevel +
                '}';
    }
}