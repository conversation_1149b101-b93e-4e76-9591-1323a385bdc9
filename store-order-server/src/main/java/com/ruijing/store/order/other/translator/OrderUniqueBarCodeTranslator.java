package com.ruijing.store.order.other.translator;

import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.product.OrderProductBatchesStatusEnum;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.store.order.api.base.orderdetail.dto.BatchesBarCodeDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderBatchesDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailBathesDTO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BatchesBarCodeVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderBatchesVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderDetailBatchesVO;
import com.ruijing.store.order.other.dto.UniqueBarCodeDTO;

import java.util.stream.Collectors;

/**
 * @author: liwenyu
 * @createTime: 2023-08-08 15:08
 * @description:
 **/
public class OrderUniqueBarCodeTranslator {

    public static UniqueBarCodeDTO galaxyDto2orderDto(OrderUniqueBarCodeDTO item){
        if(item == null){
            return null;
        }
        return new UniqueBarCodeDTO()
                .setBarCode(item.getUniBarCode())
                .setType(item.getType())
                .setOrderNo(item.getOrderNo())
                .setOrderDetailId(item.getOrderDetailId())
                .setProductName(item.getProductName())
                .setProductCode(item.getProductCode())
                .setProductionDate(DateUtils.format("yyyy-MM-dd", item.getProductionDate()))
                .setSpec(item.getSpec())
                .setBrand(item.getBrand())
                .setSupplierId(item.getSupplierId())
                .setSupplierName(item.getSupplierName())
                .setPrice(item.getPrice())
                .setBatches(item.getBatches())
                .setExpiration(item.getExpiration())
                .setManufacturer(item.getManufacturer())
                .setExterior(item.getExterior())
                .setStatus(item.getStatus())
                .setBatchesStatus(item.getBatchesStatus())
                .setInventoryStatus(item.getInventoryStatus())
                .setTransactionStatus(item.getTransactionStatus())
                .setRoomId(item.getRoomId())
                .setEntryNo(item.getEntryNo())
                .setApplyNo(item.getApplyNo())
                .setExitNo(item.getExitNo())
                .setReturnNo(item.getReturnNo())
                .setPrinted(item.getPrinted())
                .setTotal(item.getTotal())
                .setProductPicture(item.getProductPicture())
                .setReturnReason(item.getReturnReason())
                .setReturnDescription(item.getReturnDescription())
                .setUserGuid(item.getUserGuid())
                .setValid(item.getValid())
                .setGasBottleBarcode(item.getGasBottleBarcode());
    }

    public static OrderUniqueBarCodeDTO orderDto2galaxyDto(UniqueBarCodeDTO item){
        if(item == null){
            return null;
        }
        OrderUniqueBarCodeDTO orderUniqueBarCodeDTO = new OrderUniqueBarCodeDTO()
                .setUniBarCode(item.getBarCode())
                .setType(item.getType())
                .setOrderNo(item.getOrderNo())
                .setOrderDetailId(item.getOrderDetailId())
                .setProductName(item.getProductName())
                .setProductCode(item.getProductCode())
                .setSpec(item.getSpec())
                .setBrand(item.getBrand())
                .setSupplierId(item.getSupplierId())
                .setSupplierName(item.getSupplierName())
                .setPrice(item.getPrice())
                .setBatches(item.getBatches())
                .setExpiration(item.getExpiration())
                .setManufacturer(item.getManufacturer())
                .setExterior(item.getExterior())
                .setBatchesStatus(item.getBatchesStatus())
                .setRoomId(item.getRoomId())
                .setEntryNo(item.getEntryNo())
                .setApplyNo(item.getApplyNo())
                .setExitNo(item.getExitNo())
                .setReturnNo(item.getReturnNo())
                .setPrinted(item.getPrinted())
                .setTotal(item.getTotal())
                .setProductPicture(item.getProductPicture())
                .setReturnReason(item.getReturnReason())
                .setReturnDescription(item.getReturnDescription())
                .setUserGuid(item.getUserGuid())
                .setValid(item.getValid())
                .setGasBottleBarcode(item.getGasBottleBarcode());
        orderUniqueBarCodeDTO.setProductionDate(DateUtils.parse("yyyy-MM-dd",item.getProductionDate()));
        return orderUniqueBarCodeDTO;
    }

    public static OrderDetailBatchesVO detailBatchesDTO2VO(OrderDetailBathesDTO dto){
        if(dto == null){
            return null;
        }
        OrderDetailBatchesVO vo = new OrderDetailBatchesVO();
        vo.setDetailId(dto.getDetailId());
        vo.setProductName(dto.getProductName());
        vo.setProductCode(dto.getProductCode());
        vo.setSpec(dto.getSpec());
        vo.setBrand(dto.getBrand());
        vo.setCasNo(dto.getCasNo());
        vo.setSecondCategoryId(dto.getSecondCategoryId());
        vo.setSecondCategoryName(dto.getSecondCategoryName());
        if(dto.getBatches() != null){
            vo.setBatches(dto.getBatches().stream().map(OrderUniqueBarCodeTranslator::batchesBarCodeDTO2VO).collect(Collectors.toList()));
        }
        return vo;
    }

    public static BatchesBarCodeVO batchesBarCodeDTO2VO(BatchesBarCodeDTO dto){
        if(dto == null){
            return null;
        }
        BatchesBarCodeVO vo = new BatchesBarCodeVO();
        vo.setBarCode(dto.getBarCode());
        vo.setBarCodeImg(dto.getBarCodeImg());
        vo.setBarCodeQrImg(dto.getBarCodeQrImg());
        vo.setType(dto.getType());
        vo.setBatches(dto.getBatches());
        vo.setExpiration(dto.getExpiration());
        vo.setManufacturer(dto.getManufacturer());
        vo.setProductionDate(dto.getProductionDate());
        vo.setGasBottleBarcode(dto.getGasBottleBarcode());
        vo.setQuantity(dto.getQuantity());
        vo.setExterior(dto.getExterior());
        vo.setStatus(dto.getStatus());
        vo.setBatchesStatus(dto.getBatchesStatus());
        vo.setTransactionStatus(dto.getTransactionStatus());
        vo.setInventoryStatus(dto.getInventoryStatus());
        vo.setPrinted(dto.getPrinted());
        vo.setReturnReason(dto.getReturnReason());
        vo.setReturnDescription(dto.getReturnDescription());
        return vo;
    }
}
