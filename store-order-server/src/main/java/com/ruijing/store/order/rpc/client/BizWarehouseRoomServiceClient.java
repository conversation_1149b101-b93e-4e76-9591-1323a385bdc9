package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.store.wms.api.dto.*;
import com.ruijing.store.wms.api.query.StockMultiQueryDTO;
import com.ruijing.store.wms.api.service.BizWarehouseRoomService;
import com.ruijing.store.wms.api.service.WmsStockRpcService;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 11/15/2019 10:44 AM
 */
@ServiceClient
public class BizWarehouseRoomServiceClient {

    private static final String CAT_TYPE = "bizWarehouseRoomServiceClient";

    @MSharpReference(remoteAppkey = "store-wms-service")
    private BizWarehouseRoomService bizWarehouseRoomService;
    
    @MSharpReference(remoteAppkey = "store-wms-service")
    private WmsStockRpcService wmsStockRpcService;

    /**
     * 根据库房id列表查询库房存储商品类型；普通商品分类会返回一级分类，危化品会返回二级分类
     *
     * @param roomIds
     * @return 有异常时返回空列表
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<RoomStorageScopeDTO> queryRoomCategoryId(List<Integer> roomIds) {
        Preconditions.notEmpty(roomIds, "入参为空");
        ApiResult<List<RoomStorageScopeDTO>> response = bizWarehouseRoomService.queryRoomCategoryId(roomIds);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryRoomCategoryId", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(roomIds) + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据部门Id列表获取库房列表
     *
     * @param ids
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseRoomDTO> getWarehouseByDepartmentIds(List<Integer> ids) {
        Preconditions.notEmpty(ids, "入参为空");
        ApiResult<List<BizWarehouseRoomDTO>> response = bizWarehouseRoomService.queryRoomByDepts(ids);
        if (response.successful()) {
            return response.getData() == null ? Collections.emptyList() : response.getData();
        }
        Cat.logError(CAT_TYPE, "getWarehouseByDepartmentIds", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(ids) + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    /**
     * 根据库房id集合批量查询库房阀值
     *
     * @param roomIds
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseRoomRefThresholdDTO> queryRoomThreshold(List<Integer> roomIds) {
        Preconditions.notEmpty(roomIds, "入参为空");
        ApiResult<List<BizWarehouseRoomRefThresholdDTO>> response = bizWarehouseRoomService.queryRoomThreshold(roomIds);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryRoomThreshold", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + JsonUtils.toJson(roomIds) + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public WarehouseThresholdFormulaDTO queryThresholdFormula(Integer orgId) {
        Preconditions.notNull(orgId, "入参为空");
        ApiResult<WarehouseThresholdFormulaDTO> response = bizWarehouseRoomService.queryThresholdFormula(orgId);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryThresholdFormula", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + orgId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }

    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<BizWarehouseEntryDetailDTO> queryStockByRoomId(Integer roomId) {
        Preconditions.notNull(roomId, "入参为空");
        ApiResult<List<BizWarehouseEntryDetailDTO>> response = bizWarehouseRoomService.queryStockByRoomId(roomId);
        if (response.successful()) {
            return response.getData();
        }
        Cat.logError(CAT_TYPE, "queryStockByRoomId", "RPC调用返回失败结果：" + JsonUtils.toJson(response) + ";\n入参：" + roomId + "\n", new RuntimeException("RPC调用返回失败结果"));
        throw new RuntimeException(response.getMessage());
    }
    
    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据多个条件查库存")
    public List<StockDTO> queryByMultiKey(List<StockMultiQueryDTO> param){
        RemoteResponse<List<StockDTO>> response = wmsStockRpcService.queryByMultiKey(param);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    @ServiceLog(serviceType = ServiceType.RPC_CLIENT, description = "根据多个条件查询危险品库存")
    public List<StockDTO> queryDangerousByMultiKey(List<StockMultiQueryDTO> query) {
        if (CollectionUtils.isEmpty(query)) {
            return New.emptyList();
        }
        List<StockDTO> result = new ArrayList<>();
        List<List<StockMultiQueryDTO>> partitionList = Lists.partition(query, 200);
        for (List<StockMultiQueryDTO> partition : partitionList) {
            RemoteResponse<List<StockDTO>> response = wmsStockRpcService.queryDangerousByMultiKey(New.list(partition));
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (CollectionUtils.isNotEmpty(response.getData())) {
                result.addAll(response.getData());
            }
        }
        return result;
    }
}
