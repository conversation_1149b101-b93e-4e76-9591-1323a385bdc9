package com.ruijing.store.order.base.freezedeptlog.translator;

import com.ruijing.store.order.api.base.other.dto.DepartmentFreezeLogDTO;
import com.ruijing.store.order.base.freezedeptlog.dto.FreezeDeptLogDTO;
import com.ruijing.store.order.base.freezedeptlog.model.FreezeDeptLogDO;

/**
 * @description: FreezeDeptLog 转换类
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/29 18:05
 **/
public class FreezeDeptLogTranslator {

    /**
     * DO 类转 DTO类
     * @param freezeDeptLogDO
     * @return
     */
    public static FreezeDeptLogDTO do2DTO(FreezeDeptLogDO freezeDeptLogDO) {
        FreezeDeptLogDTO dto = new FreezeDeptLogDTO();
        dto.setId(freezeDeptLogDO.getId());
        dto.setOrgId(freezeDeptLogDO.getOrgId());
        dto.setDepId(freezeDeptLogDO.getDepId());
        dto.setType(freezeDeptLogDO.getType());
        dto.setHasDeleted(freezeDeptLogDO.getHasDeleted());
        dto.setCreationTime(freezeDeptLogDO.getCreationTime());
        dto.setUpdateTime(freezeDeptLogDO.getUpdateTime());

        return dto;
    }

    /**
     * DO 类转 DTO类
     * @param dto
     * @return
     */
    public static FreezeDeptLogDO dto2DO(FreezeDeptLogDTO dto) {
        FreezeDeptLogDO freezeDeptLogDO = new FreezeDeptLogDO();
        freezeDeptLogDO.setId(dto.getId());
        freezeDeptLogDO.setOrgId(dto.getOrgId());
        freezeDeptLogDO.setDepId(dto.getDepId());
        freezeDeptLogDO.setType(dto.getType());
        freezeDeptLogDO.setHasDeleted(dto.getHasDeleted());
        freezeDeptLogDO.setCreationTime(dto.getCreationTime());
        freezeDeptLogDO.setUpdateTime(dto.getUpdateTime());

        return freezeDeptLogDO;
    }

    /**
     * DO 类转 RPC DTO 类
     * @param item
     * @return
     */
    public static DepartmentFreezeLogDTO doToDepartmentFreezeLogDTO(FreezeDeptLogDO item) {
        DepartmentFreezeLogDTO result = new DepartmentFreezeLogDTO();
        result.setId(item.getId().intValue());
        result.setOrgId(item.getOrgId());
        result.setDepartmentId(item.getDepId());
        result.setType(item.getType());

        return result;
    }
}
