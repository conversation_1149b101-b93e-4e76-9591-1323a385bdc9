package com.ruijing.store.order.gateway.print.warehouse.impl;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehouseClaimPrintDataDTO;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehouseProductPrintDataDTO;
import com.ruijing.store.order.gateway.print.util.ApprovalLogTranslator;
import com.ruijing.store.order.gateway.print.warehouse.WarehouseClaimPrintDataService;
import com.ruijing.store.order.rpc.client.ClaimServiceClient;
import com.ruijing.store.order.rpc.client.OrderDetailExtraClient;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.warehouse.utils.PriceUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseReceiceDetailDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @author: Liwenyu
 * @create: 2024-09-27 17:31
 * @description:
 */
@Service
public class WarehouseClaimPrintDataServiceImpl implements WarehouseClaimPrintDataService {

    @Resource
    private ClaimServiceClient claimServiceClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Override
    public List<WarehouseClaimPrintDataDTO> getClaimPrintData(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<BizWarehouseEntryDTO> warehouseApplicationInfoList) {
        List<BizWarehouseReceiceDTO> claimList = claimServiceClient.queryClaimByOrderNos(New.list(orderMasterDO.getForderno()));
        //根据入库单id列表查找入库单商品信息
        if (CollectionUtils.isEmpty(claimList)) {
            return New.emptyList();
        }
        Map<Integer, OrderDetailDO> detailIdIdentityMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
        List<WarehouseClaimPrintDataDTO> printDataList = New.listWithCapacity(claimList.size());
        for(BizWarehouseReceiceDTO claim : claimList){
            WarehouseClaimPrintDataDTO printData = new WarehouseClaimPrintDataDTO();
            printData.setClaimNo(claim.getReceiceNo());
            printData.setApplyUserName(claim.getApplyUserName());
            printData.setCreateTime(claim.getCreateTime());
            printData.setDeptName(claim.getDeptName());
            printData.setRemark(claim.getRemark());
            printData.setEntryNo(claim.getEntryNo());
            List<WarehouseProductPrintDataDTO> products = claim.getBizWarehouseReceiceDetailDTOList().stream().map(item->this.claimDetail2ProductVO(item, detailIdIdentityMap.get(item.getOrderDetailId()))).collect(toList());

            // 填充订单详情扩展信息
            fillOrderDetailExtraInfo(products);

            printData.setWarehouseProductInfoVOList(products);
            String totalPrice = products.stream().map(item->new BigDecimal(item.getTotalPrice())).reduce(BigDecimal.ZERO, BigDecimal::add).toString();
            printData.setTotalPrice(totalPrice);
            printData.setTotalPriceInChinese(PriceUtil.convert(totalPrice));
            printData.setFlatLog(ApprovalLogTranslator.warehouseClaimLog2FlatListDTO(claim.getReceicePassLogDTOList()));
            printDataList.add(printData);
        }
        return printDataList;
    }

    private WarehouseProductPrintDataDTO claimDetail2ProductVO(BizWarehouseReceiceDetailDTO claimDetail, OrderDetailDO matchDetail){
        WarehouseProductPrintDataDTO warehouseProductInfoVO = new WarehouseProductPrintDataDTO();
        warehouseProductInfoVO.setProductName(claimDetail.getProductName());
        warehouseProductInfoVO.setSpecifications(claimDetail.getSpecifications());
        warehouseProductInfoVO.setUnit(claimDetail.getReceivedUnit());
        warehouseProductInfoVO.setQuantity(claimDetail.getReceivedNum());
        warehouseProductInfoVO.setOrderDetailId(claimDetail.getOrderDetailId());
        if(matchDetail != null){
            warehouseProductInfoVO.setSinglePrice(matchDetail.getFbidprice().toString());
            BigDecimal totalPrice = matchDetail.getFbidprice().multiply(BigDecimal.valueOf(claimDetail.getReceivedNum()));
            if(matchDetail.getFquantity().intValue() == claimDetail.getReceivedNum()){
                totalPrice = totalPrice.add(matchDetail.getRemainderPrice());
            }
            warehouseProductInfoVO.setTotalPrice(totalPrice.toString());
        }
        return warehouseProductInfoVO;
    }

    /**
     * 填充订单详情扩展信息
     *
     * @param warehouseProductPrintDataDTOList 商品打印数据列表
     */
    private void fillOrderDetailExtraInfo(List<WarehouseProductPrintDataDTO> warehouseProductPrintDataDTOList) {
        if (CollectionUtils.isEmpty(warehouseProductPrintDataDTOList)) {
            return;
        }

        // 获取所有需要查询扩展信息的订单详情ID
        List<Integer> orderDetailIds = warehouseProductPrintDataDTOList.stream()
                .map(WarehouseProductPrintDataDTO::getOrderDetailId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderDetailIds)) {
            return;
        }

        // 查询订单详情扩展信息
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(null, orderDetailIds);
        if (CollectionUtils.isEmpty(orderDetailExtraDTOList)) {
            return;
        }

        // 按扩展类型分组
        Map<Integer, Map<Integer, String>> orderDetailId2ExtraInfoMap = orderDetailExtraDTOList.stream()
                .collect(Collectors.groupingBy(
                        OrderDetailExtraDTO::getOrderDetailId,
                        Collectors.toMap(
                                OrderDetailExtraDTO::getExtraKeyType,
                                OrderDetailExtraDTO::getExtraValue,
                                (v1, v2) -> v1
                        )
                ));

        // 填充扩展信息到商品数据中
        warehouseProductPrintDataDTOList.forEach(product -> {
            if (Objects.nonNull(product.getOrderDetailId())) {
                Map<Integer, String> extraInfoMap = orderDetailId2ExtraInfoMap.get(product.getOrderDetailId());
                if (Objects.nonNull(extraInfoMap)) {
                    // 填充医疗器械注册证书编号
                    String medicalDeviceRegisCertNumber = extraInfoMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
                    product.setMedicalDeviceRegisCertNumber(medicalDeviceRegisCertNumber);
                }
            }
        });
    }
}
