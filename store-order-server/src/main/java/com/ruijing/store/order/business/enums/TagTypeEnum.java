package com.ruijing.store.order.business.enums;

/**
 * <AUTHOR>
 * @Date 2020/5/9 0009 16:19
 * @Version 1.0
 * @Desc:描述
 */
public enum TagTypeEnum {

    FEE_TYPE(0, "测试分析费，实验耗材费"),

    FIRST_TIER_CATEGORY(1, "服务，耗材，试剂，动物");

    private final Integer value;

    private final String description;

    TagTypeEnum(int value, String description){
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return description;
    }
}
