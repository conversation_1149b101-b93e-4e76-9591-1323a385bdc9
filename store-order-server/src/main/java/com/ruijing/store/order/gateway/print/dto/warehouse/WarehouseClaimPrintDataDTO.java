package com.ruijing.store.order.gateway.print.dto.warehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.store.order.gateway.print.dto.ApprovalLogFlatListDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Liwenyu
 * @create: 2024-09-27 17:00
 * @description: 申领单数据
 */
public class WarehouseClaimPrintDataDTO implements Serializable {

    private static final long serialVersionUID = 5548834002665810517L;

    @ModelProperty("申领单号")
    private String claimNo;

    @ModelProperty("申领人")
    private String applyUserName;

    @ModelProperty("申领时间")
    private Date createTime;

    @ModelProperty(value = "申领单所有商品的总额的合计")
    private String totalPrice;

    @ModelProperty("申领课题组")
    private String deptName;

    @ModelProperty("入库单号")
    private String entryNo;

    @ModelProperty("合计金额的大写")
    private String totalPriceInChinese;

    @ModelProperty("备注")
    private String remark;

    @ModelProperty("申领商品")
    private List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList;

    @RpcModelProperty("申领日志--时间最新的审批日志")
    private ApprovalLogFlatListDTO flatLog;

    public String getClaimNo() {
        return claimNo;
    }

    public WarehouseClaimPrintDataDTO setClaimNo(String claimNo) {
        this.claimNo = claimNo;
        return this;
    }

    public String getApplyUserName() {
        return applyUserName;
    }

    public WarehouseClaimPrintDataDTO setApplyUserName(String applyUserName) {
        this.applyUserName = applyUserName;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public WarehouseClaimPrintDataDTO setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public WarehouseClaimPrintDataDTO setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
        return this;
    }

    public String getDeptName() {
        return deptName;
    }

    public WarehouseClaimPrintDataDTO setDeptName(String deptName) {
        this.deptName = deptName;
        return this;
    }

    public String getEntryNo() {
        return entryNo;
    }

    public WarehouseClaimPrintDataDTO setEntryNo(String entryNo) {
        this.entryNo = entryNo;
        return this;
    }

    public String getTotalPriceInChinese() {
        return totalPriceInChinese;
    }

    public WarehouseClaimPrintDataDTO setTotalPriceInChinese(String totalPriceInChinese) {
        this.totalPriceInChinese = totalPriceInChinese;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public WarehouseClaimPrintDataDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public List<WarehouseProductPrintDataDTO> getWarehouseProductInfoVOList() {
        return warehouseProductInfoVOList;
    }

    public WarehouseClaimPrintDataDTO setWarehouseProductInfoVOList(List<WarehouseProductPrintDataDTO> warehouseProductInfoVOList) {
        this.warehouseProductInfoVOList = warehouseProductInfoVOList;
        return this;
    }

    public ApprovalLogFlatListDTO getFlatLog() {
        return flatLog;
    }

    public WarehouseClaimPrintDataDTO setFlatLog(ApprovalLogFlatListDTO flatLog) {
        this.flatLog = flatLog;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseClaimPrintDataDTO.class.getSimpleName() + "[", "]")
                .add("applyUserName='" + applyUserName + "'")
                .add("createTime=" + createTime)
                .add("totalPrice='" + totalPrice + "'")
                .add("deptName='" + deptName + "'")
                .add("entryNo='" + entryNo + "'")
                .add("totalPriceInChinese='" + totalPriceInChinese + "'")
                .add("remark='" + remark + "'")
                .add("warehouseProductInfoVOList=" + warehouseProductInfoVOList)
                .add("flatLog=" + flatLog)
                .toString();
    }
}
