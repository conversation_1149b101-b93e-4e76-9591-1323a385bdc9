<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.ThirdPartyCallbackInfoMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO">
    <!--@mbg.generated-->
    <!--@Table t_third_party_callback_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="orgCode" jdbcType="VARCHAR" property="orgcode" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="message" jdbcType="VARCHAR" property="message" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, orgCode, num, message
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_third_party_callback_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_third_party_callback_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_third_party_callback_info (create_time, update_time, orgCode, 
      num, message)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orgcode,jdbcType=VARCHAR}, 
      #{num,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_third_party_callback_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orgcode != null">
        orgCode,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="message != null">
        message,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgcode != null">
        #{orgcode,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO">
    <!--@mbg.generated-->
    update t_third_party_callback_info
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgcode != null">
        orgCode = #{orgcode,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.ThirdPartyCallbackInfoDO">
    <!--@mbg.generated-->
    update t_third_party_callback_info
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      orgCode = #{orgcode,jdbcType=VARCHAR},
      num = #{num,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>