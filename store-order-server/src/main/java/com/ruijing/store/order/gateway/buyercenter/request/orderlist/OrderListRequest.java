package com.ruijing.store.order.gateway.buyercenter.request.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.search.dto.OrderExtraInfoParamDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/17 18:00
 * @Description
 **/
@RpcModel("订单管理-我的订单列表请求体")
public class OrderListRequest implements Serializable {

    private static final long serialVersionUID = 8482997832564521349L;

    /**
     * 微信搜索
     */
    @RpcModelProperty("微信搜索")
    private String wxSearch;

    @RpcModelProperty("移动端代配送列表搜索关键字")
    private String wxDeliveryProxyKeyword;

    /**
     * 审批日志--审批状态
     */
    private List<String> approveStatusList;

    /**
     * 审批日志--审批人id
     */
    private String operatorId;

    /**
     * 经费状态
     */
    @RpcModelProperty("经费状态")
    private List<Integer> fundStatus;

    /**
     * 获取 订单数量 访问类型
     * true 是我的订单
     * false 课题组订单
     */
    @RpcModelProperty("获取 订单数量 访问类型,true 我的订单，false课题组订单，订单计数时需要传递")
    private Boolean myOrderCheck;

    /**
     * 状态：20 待验收审批，
     */
    @RpcModelProperty("状态：20 待验收审批，")
    private Integer status;

    /**
     * 订单状态集合
     */
    @RpcModelProperty("订单状态集合")
    private List<Integer> statusList;

    /**
     * 结算状态
     */
    @RpcModelProperty("结算状态集合")
    private List<Integer> statementStatusList;

    /**
     * {@link com.ruijing.store.order.base.core.enums.TimeOutEnums}
     * 超时类型。与HMS的超时订单tab的超时类型一致
     * -1-所有 0-结算 1-验收
     */
    @RpcModelProperty(value = "超时类型,-1-所有 0-结算 1-验收",enumLink = "com.ruijing.store.order.base.core.enums.TimeOutEnums")
    private Integer overTimeType;

    /**
     * 查询条件(可能是订单号，采购人，供应商)
     */
    @RpcModelProperty("查询条件(可能是订单号，采购人，供应商)")
    private String search;

    /**
     * 采购部门
     */
    @RpcModelProperty("采购部门")
    private Integer department;

    /**
     * 采购部门, 多个查询条件用这个
     */
    @RpcModelProperty("采购部门, 多个查询条件用这个")
    private List<Integer> departmentIds;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("订单号列表")
    private List<String> orderNoList;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 开始时间
     */
    @RpcModelProperty("开始时间, 时间戳或者年月日")
    private String startDate;

    /**
     * 结束时间
     */
    @RpcModelProperty("结束时间，时间戳或者年月日")
    private String endDate;

    /**
     * 采购人姓名
     */
    @RpcModelProperty("采购人姓名")
    private String fbuyername;

    /**
     * 采购人id
     */
    @RpcModelProperty("采购人id")
    private Integer fbuyerId;

    /**
     * 供应商名称
     */
    @RpcModelProperty("供应商名称")
    private String suppname;

    /**
     * 商品或货号
     */
    @RpcModelProperty("商品或货号")
    private String productNameOrCode;

    @RpcModelProperty("商品货号，如果设置会覆盖productNameOrCode")
    private String productCode;

    @RpcModelProperty("商品名称，如果设置会覆盖productNameOrCode")
    private String productName;

    @RpcModelProperty("商品搜索字段，商品名称、cas号或货号")
    private String productSearchContent;

    /**
     * 单据状态   0正常  1线下单
     */
    @RpcModelProperty("单据状态   0正常  1线下单")
    private Integer processSpecies;

    /**
     * 需备案危化品 0正常，1需备案
     */
    @RpcModelProperty("需备案危化品 0正常，1需备案")
    private Integer dangerousType = 0;

    /**
     * 是否需要备案，0 全部，1待备案
     */
    @RpcModelProperty("是否需要备案，0 全部，1待备案")
    private Integer confirmForTheRecord = 0;

    /**
     * 单位拼音大写名称组合
     */
    @RpcModelProperty("单位拼音大写名称组合")
    private String orgCode;

    @RpcModelProperty("存在关联关系,1-是，0-否")
    private Integer relateInfo;

    /**
     * 经费卡号
     **/
    @RpcModelProperty("经费卡号")
    private String cardNo;

    @RpcModelProperty("经费卡id列表")
    private List<String> cardIdList;

    /**
     * 出入库状态
     */
    @RpcModelProperty("出入库状态")
    private Integer inventoryStatus;

    /**
     * 页码
     */
    @RpcModelProperty(value = "页码",required = true)
    private Integer pageNo;

    /**
     * 每页条目数
     */
    @RpcModelProperty(value = "每页条目数",required = true)
    private Integer pageSize;

    /**
     * 单位id
     */
    @RpcModelProperty("单位id")
    private Integer orgId;

    /**
     * 具有权限的经费卡列表
     */
    @RpcModelProperty("具有权限的经费卡列表")
    private List<Integer> cardAuthOrderIds;

    /**
     * 部门id列表，在业务逻辑中当中介
     */
    @RpcModelProperty("部门id列表，在业务逻辑中当中介")
    private List<Integer> deptIdList;

    @RpcModelProperty("cas号，精确匹配，如果设置会覆盖productNameOrCode")
    private String casNo;

    @RpcModelProperty("院区编号，全匹配的经费卡院区编号")
    private String campusCode;

    @RpcModelProperty("院区名字，全匹配的经费卡院区名字")
    private String campusName;

    /**
     * 内部使用，需要去除的状态列表, 默认不搜索拆单的母单
     */
    private List<Integer> excludeStatusList;

    @RpcModelProperty("订单额外信息列表")
    private List<OrderExtraInfoParamDTO> orderExtraInfoList;

    /**
     * {@link com.ruijing.shop.category.api.enums.InboundTypeEnum}
     * 1,试剂;2,耗材;3,服务;4,动物;5,工业品
     */
    @RpcModelProperty("分类标签,1,试剂;2,耗材;3,服务;4,动物;5,工业品")
    private Integer categoryTag;

    @RpcModelProperty("部门名称")
    private String departmentName;

    @RpcModelProperty("品牌名称")
    private String brand;

    @RpcModelProperty("单位名称")
    private String orgName;

    @RpcModelProperty("是否代配送开启")
    private Boolean deliveryProxyOn;

    @RpcModelProperty("代配送状态")
    private Integer deliveryStatus;

    @RpcModelProperty("分拣员")
    private String sortedUser;

    @RpcModelProperty("配送员")
    private String deliveryUser;

    @RpcModelProperty("配送类型")
    private List<Integer> deliveryType;

    /**
     * 是否需要线下单信息
     */
    private boolean needOfflineInfo;

    @RpcModelProperty("商品一级分类id")
    private List<Integer> firstCategoryIdList;

    @RpcModelProperty("商品二级分类id")
    private List<Integer> secondCategoryIdList;

    @RpcModelProperty("商品三级分类id")
    private List<Integer> thirdCategoryIdList;

    @RpcModelProperty("是否查询操作过的代配送订单")
    private Boolean deliveryOperatorOn;

    @RpcModelProperty("起始配送时间")
    private Date deliveredTimeStart;

    @RpcModelProperty("结束配送时间")
    private Date deliveredTimeEnd;

    @RpcModelProperty("经费类型")
    private Integer fundType;

    @RpcModelProperty("订单验收审批等级")
    private Integer acceptApproveLevel;

    @RpcModelProperty("收货人--验收人")
    private String receiveMan;

    @RpcModelProperty("收货联系人--下单收货地址时的联系人")
    private String buyerContactMan;

    @RpcModelProperty("对接状态")
    private List<Integer> dockingStatusList;

    @RpcModelProperty(value = "订单日期排序", enumClass = SortOrderEnum.class)
    private Integer orderDateSort;

    /**
     * 展示测试订单数
     */
    private Boolean showTestOrderCount;

    public List<Integer> getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(List<Integer> deliveryType) {
        this.deliveryType = deliveryType;
    }

    public List<String> getApproveStatusList() {
        return approveStatusList;
    }

    public void setApproveStatusList(List<String> approveStatusList) {
        this.approveStatusList = approveStatusList;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Date getDeliveredTimeStart() {
        return deliveredTimeStart;
    }

    public void setDeliveredTimeStart(Date deliveredTimeStart) {
        this.deliveredTimeStart = deliveredTimeStart;
    }

    public Date getDeliveredTimeEnd() {
        return deliveredTimeEnd;
    }

    public void setDeliveredTimeEnd(Date deliveredTimeEnd) {
        this.deliveredTimeEnd = deliveredTimeEnd;
    }

    public Boolean getDeliveryOperatorOn() {
        return deliveryOperatorOn;
    }

    public void setDeliveryOperatorOn(Boolean deliveryOperatorOn) {
        this.deliveryOperatorOn = deliveryOperatorOn;
    }

    public String getWxDeliveryProxyKeyword() {
        return wxDeliveryProxyKeyword;
    }

    public void setWxDeliveryProxyKeyword(String wxDeliveryProxyKeyword) {
        this.wxDeliveryProxyKeyword = wxDeliveryProxyKeyword;
    }

    public Boolean getDeliveryProxyOn() {
        return deliveryProxyOn;
    }

    public void setDeliveryProxyOn(Boolean deliveryProxyOn) {
        this.deliveryProxyOn = deliveryProxyOn;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getSortedUser() {
        return sortedUser;
    }

    public void setSortedUser(String sortedUser) {
        this.sortedUser = sortedUser;
    }

    public String getDeliveryUser() {
        return deliveryUser;
    }

    public void setDeliveryUser(String deliveryUser) {
        this.deliveryUser = deliveryUser;
    }

    public List<Integer> getStatementStatusList() {
        return statementStatusList;
    }

    public void setStatementStatusList(List<Integer> statementStatusList) {
        this.statementStatusList = statementStatusList;
    }

    public String getWxSearch() {
        return wxSearch;
    }

    public void setWxSearch(String wxSearch) {
        this.wxSearch = wxSearch;
    }

    public List<Integer> getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(List<Integer> fundStatus) {
        this.fundStatus = fundStatus;
    }

    public Boolean getMyOrderCheck() {
        return myOrderCheck;
    }

    public void setMyOrderCheck(Boolean myOrderCheck) {
        this.myOrderCheck = myOrderCheck;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public Integer getOverTimeType() {
        return overTimeType;
    }

    public void setOverTimeType(Integer overTimeType) {
        this.overTimeType = overTimeType;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getFbuyername() {
        return fbuyername;
    }

    public void setFbuyername(String fbuyername) {
        this.fbuyername = fbuyername;
    }

    public Integer getFbuyerId() {
        return fbuyerId;
    }

    public void setFbuyerId(Integer fbuyerId) {
        this.fbuyerId = fbuyerId;
    }

    public String getSuppname() {
        return suppname;
    }

    public void setSuppname(String suppname) {
        this.suppname = suppname;
    }

    public String getProductNameOrCode() {
        return productNameOrCode;
    }

    public void setProductNameOrCode(String productNameOrCode) {
        this.productNameOrCode = productNameOrCode;
    }

    public Integer getProcessSpecies() {
        return processSpecies;
    }

    public void setProcessSpecies(Integer processSpecies) {
        this.processSpecies = processSpecies;
    }

    public Integer getDangerousType() {
        return dangerousType;
    }

    public void setDangerousType(Integer dangerousType) {
        this.dangerousType = dangerousType;
    }

    public Integer getConfirmForTheRecord() {
        return confirmForTheRecord;
    }

    public void setConfirmForTheRecord(Integer confirmForTheRecord) {
        this.confirmForTheRecord = confirmForTheRecord;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getRelateInfo() {
        return relateInfo;
    }

    public void setRelateInfo(Integer relateInfo) {
        this.relateInfo = relateInfo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getInventoryStatus() {
        return inventoryStatus;
    }

    public void setInventoryStatus(Integer inventoryStatus) {
        this.inventoryStatus = inventoryStatus;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public List<Integer> getCardAuthOrderIds() {
        return cardAuthOrderIds;
    }

    public void setCardAuthOrderIds(List<Integer> cardAuthOrderIds) {
        this.cardAuthOrderIds = cardAuthOrderIds;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public OrderListRequest setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public List<Integer> getDeptIdList() {
        return deptIdList;
    }

    public void setDeptIdList(List<Integer> deptIdList) {
        this.deptIdList = deptIdList;
    }

    public String getCasNo() {
        return casNo;
    }

    public OrderListRequest setCasNo(String casNo) {
        this.casNo = casNo;
        return this;
    }

    public List<Integer> getExcludeStatusList() {
        return excludeStatusList;
    }

    public OrderListRequest setExcludeStatusList(List<Integer> excludeStatusList) {
        this.excludeStatusList = excludeStatusList;
        return this;
    }

    public List<Integer> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Integer> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<OrderExtraInfoParamDTO> getOrderExtraInfoList() {
        return orderExtraInfoList;
    }

    public void setOrderExtraInfoList(List<OrderExtraInfoParamDTO> orderExtraInfoList) {
        this.orderExtraInfoList = orderExtraInfoList;
    }

    public Integer getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(Integer categoryTag) {
        this.categoryTag = categoryTag;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductSearchContent() {
        return productSearchContent;
    }

    public void setProductSearchContent(String productSearchContent) {
        this.productSearchContent = productSearchContent;
    }

    public String getCampusCode() {
        return campusCode;
    }

    public void setCampusCode(String campusCode) {
        this.campusCode = campusCode;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    public boolean getNeedOfflineInfo() {
        return needOfflineInfo;
    }

    public void setNeedOfflineInfo(boolean needOfflineInfo) {
        this.needOfflineInfo = needOfflineInfo;
    }

    public List<Integer> getFirstCategoryIdList() {
        return firstCategoryIdList;
    }

    public void setFirstCategoryIdList(List<Integer> firstCategoryIdList) {
        this.firstCategoryIdList = firstCategoryIdList;
    }

    public List<Integer> getSecondCategoryIdList() {
        return secondCategoryIdList;
    }

    public void setSecondCategoryIdList(List<Integer> secondCategoryIdList) {
        this.secondCategoryIdList = secondCategoryIdList;
    }

    public List<Integer> getThirdCategoryIdList() {
        return thirdCategoryIdList;
    }

    public void setThirdCategoryIdList(List<Integer> thirdCategoryIdList) {
        this.thirdCategoryIdList = thirdCategoryIdList;
    }

    public List<String> getCardIdList() {
        return cardIdList;
    }

    public void setCardIdList(List<String> cardIdList) {
        this.cardIdList = cardIdList;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public Integer getAcceptApproveLevel() {
        return acceptApproveLevel;
    }

    public void setAcceptApproveLevel(Integer acceptApproveLevel) {
        this.acceptApproveLevel = acceptApproveLevel;
    }

    public String getReceiveMan() {
        return receiveMan;
    }

    public OrderListRequest setReceiveMan(String receiveMan) {
        this.receiveMan = receiveMan;
        return this;
    }

    public String getBuyerContactMan() {
        return buyerContactMan;
    }

    public OrderListRequest setBuyerContactMan(String buyerContactMan) {
        this.buyerContactMan = buyerContactMan;
        return this;
    }

    public List<Integer> getDockingStatusList() {
        return dockingStatusList;
    }

    public OrderListRequest setDockingStatusList(List<Integer> dockingStatusList) {
        this.dockingStatusList = dockingStatusList;
        return this;
    }

    public Boolean getShowTestOrderCount() {
        return showTestOrderCount;
    }

    public OrderListRequest setShowTestOrderCount(Boolean showTestOrderCount) {
        this.showTestOrderCount = showTestOrderCount;
        return this;
    }

    public Integer getOrderDateSort() {
        return orderDateSort;
    }

    public void setOrderDateSort(Integer orderDateSort) {
        this.orderDateSort = orderDateSort;
    }

    @Override
    public String toString() {
        return "OrderListRequest{" +
                "wxSearch='" + wxSearch + '\'' +
                ", wxDeliveryProxyKeyword='" + wxDeliveryProxyKeyword + '\'' +
                ", approveStatusList=" + approveStatusList +
                ", operatorId='" + operatorId + '\'' +
                ", fundStatus=" + fundStatus +
                ", myOrderCheck=" + myOrderCheck +
                ", status=" + status +
                ", statusList=" + statusList +
                ", statementStatusList=" + statementStatusList +
                ", overTimeType=" + overTimeType +
                ", search='" + search + '\'' +
                ", department=" + department +
                ", departmentIds=" + departmentIds +
                ", orderNo='" + orderNo + '\'' +
                ", orderNoList=" + orderNoList +
                ", orderId=" + orderId +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", fbuyername='" + fbuyername + '\'' +
                ", fbuyerId=" + fbuyerId +
                ", suppname='" + suppname + '\'' +
                ", productNameOrCode='" + productNameOrCode + '\'' +
                ", productCode='" + productCode + '\'' +
                ", productName='" + productName + '\'' +
                ", productSearchContent='" + productSearchContent + '\'' +
                ", processSpecies=" + processSpecies +
                ", dangerousType=" + dangerousType +
                ", confirmForTheRecord=" + confirmForTheRecord +
                ", orgCode='" + orgCode + '\'' +
                ", relateInfo=" + relateInfo +
                ", cardNo='" + cardNo + '\'' +
                ", cardIdList=" + cardIdList +
                ", inventoryStatus=" + inventoryStatus +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", orgId=" + orgId +
                ", cardAuthOrderIds=" + cardAuthOrderIds +
                ", deptIdList=" + deptIdList +
                ", casNo='" + casNo + '\'' +
                ", campusCode='" + campusCode + '\'' +
                ", campusName='" + campusName + '\'' +
                ", excludeStatusList=" + excludeStatusList +
                ", orderExtraInfoList=" + orderExtraInfoList +
                ", categoryTag=" + categoryTag +
                ", departmentName='" + departmentName + '\'' +
                ", brand='" + brand + '\'' +
                ", orgName='" + orgName + '\'' +
                ", deliveryProxyOn=" + deliveryProxyOn +
                ", deliveryStatus=" + deliveryStatus +
                ", sortedUser='" + sortedUser + '\'' +
                ", deliveryUser='" + deliveryUser + '\'' +
                ", deliveryType=" + deliveryType +
                ", needOfflineInfo=" + needOfflineInfo +
                ", firstCategoryIdList=" + firstCategoryIdList +
                ", secondCategoryIdList=" + secondCategoryIdList +
                ", thirdCategoryIdList=" + thirdCategoryIdList +
                ", deliveryOperatorOn=" + deliveryOperatorOn +
                ", deliveredTimeStart=" + deliveredTimeStart +
                ", deliveredTimeEnd=" + deliveredTimeEnd +
                ", fundType=" + fundType +
                ", acceptApproveLevel=" + acceptApproveLevel +
                ", receiveMan='" + receiveMan + '\'' +
                ", buyerContactMan='" + buyerContactMan + '\'' +
                ", dockingStatusList=" + dockingStatusList +
                ", orderDateSort=" + orderDateSort +
                ", showTestOrderCount=" + showTestOrderCount +
                '}';
    }
}
