package com.ruijing.store.order.base.orderconfirm;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.orderconfirm.service.OrderConfirmRPCService;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterCommonReqDTO;
import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.base.core.translator.OrderConfirmForTheRecordTranslator;
import com.ruijing.store.order.base.minor.mapper.OrderConfirmForTheRecordDOMapper;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@MSharpService
public class OrderConfirmRPCServiceImpl implements OrderConfirmRPCService {

    @Resource
    private OrderConfirmForTheRecordDOMapper orderConfirmForTheRecordDOMapper;

    @Override
    @ServiceLog(description = "根据订单id数组查询订单备案信息")
    public RemoteResponse<List<OrderConfirmForTheRecordDTO>> findOrderConfirmByOrderIdList(OrderMasterCommonReqDTO request) {
        List<Integer> orderMasterIds = request.getOrderMasterIds();
        Preconditions.notEmpty(orderMasterIds, "orderMasterIds must not be empty!");
        Preconditions.isTrue(orderMasterIds.size() <= 200, "orderMasterIds must be less than 200!");
        List<OrderConfirmForTheRecordDO> orderConfirmForTheRecordDOList = orderConfirmForTheRecordDOMapper.findByOrderIdIn(orderMasterIds);
        List<OrderConfirmForTheRecordDTO> result = orderConfirmForTheRecordDOList.stream().map(OrderConfirmForTheRecordTranslator::doToDTO).collect(Collectors.toList());
        return RemoteResponse.<List<OrderConfirmForTheRecordDTO>>custom().setSuccess().setData(result);
    }

    @Override
    @ServiceLog(description = "保存订单备案信息", operationType = OperationType.WRITE)
    public RemoteResponse<Integer> saveOrderConfirm(OrderConfirmForTheRecordDTO request) {
        Preconditions.notNull(request, "request must not be null!");
        Preconditions.notNull(request.getOrderId(), "orderId must not be null");

        int affect;
        Integer count = orderConfirmForTheRecordDOMapper.countByOrderId(request.getOrderId());
        if (count == 0) {
            affect = orderConfirmForTheRecordDOMapper.insertSelective(OrderConfirmForTheRecordTranslator.dtoToDO(request));
        } else {
            affect = orderConfirmForTheRecordDOMapper.updateByOrderId(OrderConfirmForTheRecordTranslator.dtoToDO(request));
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }
}
