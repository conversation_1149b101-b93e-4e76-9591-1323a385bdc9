package com.ruijing.store.order.gateway.print.util;

import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.store.order.gateway.print.dto.warehouse.WarehouseProductPrintDataDTO;
import com.ruijing.store.warehouse.utils.OrderDetailsUtil;
import com.ruijing.store.wms.api.dto.BizWarehouseEntryDetailDTO;
import com.ruijing.store.wms.api.dto.BizWarehouseExitDetailDTO;

/**
 * <AUTHOR>
 * @date 2023/2/28 18:04
 * @description
 */
public class WarehouseDataTranslator {

    public static WarehouseProductPrintDataDTO inWarehouseProductDto2PrintDTO(BizWarehouseEntryDetailDTO from) {
        if (from == null) {
            return null;
        }
        WarehouseProductPrintDataDTO to = new WarehouseProductPrintDataDTO();
        to.setTotalQuantity(from.getMeasurementNum() == null ? null : from.getMeasurementNum().doubleValue());
        to.setQuantityUnit(from.getMeasurementUnit());
        to.setSupplierName(from.getSuppName());
        to.setSupplierId(from.getSuppId());
        to.setSpecifications(from.getSpecifications());
        to.setUnit(from.getReceivedUnit());
        to.setQuantity(from.getReceivedNum());
        to.setProductName(from.getProductName());
        to.setGoodCode(from.getProductCode());
        to.setCasNo(from.getCasNo());
        to.setBrand(from.getBrandName());
        to.setForm(from.getForm());
        to.setProductPhoto(from.getFpicpath());
        to.setRegulatoryFlag(from.getControlFlag());
        int dangerousType = OrderDetailsUtil.getDangerousType(from.getDangerousType());
        to.setDangerousType(dangerousType);
        to.setDangerousTypeName(DangerousTypeEnum.get(dangerousType).getName());
        //判断是否是危化品
        boolean isDangerousType = DangerousTypeEnum.isDangerousType(from.getDangerousType());
        to.setDangerousFlag(isDangerousType ? 1 : 0);
        to.setPersonalizedCategoryName(from.getSort());
        to.setContainer(from.getContainer());
        return to;
    }

    public static WarehouseProductPrintDataDTO outWarehouseProductDto2PrintDTO(BizWarehouseExitDetailDTO from) {
        if (from == null) {
            return null;
        }
        WarehouseProductPrintDataDTO to = new WarehouseProductPrintDataDTO();
        to.setTotalQuantity(from.getMeasurementNum() == null ? null : from.getMeasurementNum().doubleValue());
        to.setQuantityUnit(from.getMeasurementUnit());
        to.setSupplierName(from.getSuppName());
        to.setSupplierId(from.getSuppId());
        to.setSpecifications(from.getSpecifications());
        to.setUnit(from.getExitedUnit());
        to.setQuantity(from.getExitedNum());
        to.setProductName(from.getProductName());
        to.setGoodCode(from.getProductCode());
        to.setCasNo(from.getCasNo());
        to.setBrand(from.getBrandName());
        to.setForm(from.getForm());
        to.setRegulatoryFlag(from.getControlFlag());
        int dangerousType = OrderDetailsUtil.getDangerousType(from.getDangerousType());
        to.setDangerousType(dangerousType);
        to.setDangerousTypeName(DangerousTypeEnum.get(dangerousType).getName());
        //判断是否是危化品
        boolean isDangerousType = DangerousTypeEnum.isDangerousType(from.getDangerousType());
        to.setDangerousFlag(isDangerousType ? 1 : 0);
        to.setPersonalizedCategoryName(from.getSort());
        return to;
    }
}
