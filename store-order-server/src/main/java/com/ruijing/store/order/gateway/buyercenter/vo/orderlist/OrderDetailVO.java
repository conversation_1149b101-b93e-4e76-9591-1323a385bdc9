package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.base.core.enums.DangerousEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/11/17 15:27
 * @Description
 **/
@RpcModel("订单管理-我的订单列表订单详情表信息")
public class OrderDetailVO implements Serializable {

    private static final long serialVersionUID = -8121526402871010919L;

    /**
     * 订单明细表Id
     */
    @RpcModelProperty("订单明细表Id")
    private Integer id;

    /**
     *商品名称
     */
    @RpcModelProperty("商品名称")
    private String goodsName;

    /**
     *数量
     */
    @RpcModelProperty("数量")
    private Double quantity;

    /**
     *货号
     */
    @RpcModelProperty("货号")
    private String goodsCode;

    /**
     *单位
     */
    @RpcModelProperty("单位")
    private String unit;

    /**
     *规格
     */
    @RpcModelProperty("规格")
    private String specification;

    /**
     *品牌
     */
    @RpcModelProperty("品牌")
    private String brand;

    /**
     *单价
     */
    @RpcModelProperty("单价")
    private Double price;

    /**
     *总价
     */
    @RpcModelProperty("总价")
    private Double totalPrice;

    /**
     *图片位置
     */
    @RpcModelProperty("图片位置")
    private String picturePath;

    /**
     *等级，评价等级
     */
    @RpcModelProperty("等级，评价等级")
    private Integer level;

    /**
     *评价内容
     */
    @RpcModelProperty("评价内容")
    private String comment;

    /**
     *退货状态
     */
    @RpcModelProperty("退货状态")
    private Integer returnStatus;

    /**
     *商品id
     */
    @RpcModelProperty("商品id")
    private Long productSn;

    /**
     *是否显示原价
     */
    @RpcModelProperty("是否显示原价")
    private boolean showOriginalPrice;

    /**
     *原单价
     */
    @RpcModelProperty("原单价")
    private Double originalPrice;

    /**
     *原总价
     */
    @RpcModelProperty("原总价")
    private Double originAmount;

    /**
     *针对商品的限时折扣活动优惠金额
     */
    @RpcModelProperty("针对商品的限时折扣活动优惠金额")
    private Double activityOff;

    /**
     *运费
     */
    @RpcModelProperty("运费")
    private BigDecimal carryFee;

    /**
     *分类的名称
     */
    @RpcModelProperty("分类的名称, 展示的是下推的层级分类名")
    private String categoryName;

    /**
     * 最末级分类id
     */
    @RpcModelProperty("分类id，最末级层级的分类id")
    private Integer categoryId;

    /**
     *危化品标识
     */
    @RpcModelProperty("危化品标识")
    private String dangerousTag = DangerousEnum.UN_DANGEROUS.getName();

    /**
     * 管制品类型id
     */
    @RpcModelProperty("管制品类型ID")
    private Integer regulatoryType;

    /**
     * 管制品类型名称
     */
    @RpcModelProperty("管制品类型名称")
    private String regulatoryTypeName;

    /**
     *政府目录名
     */
    @RpcModelProperty("政采目录名")
    private String categoryDirectoryName;

    @RpcModelProperty("政采目录code")
    private String categoryDirectoryCode;

    /**
     *cas号
     */
    @RpcModelProperty("cas号")
    private String casNo;

    /**
     *商品顶级分类的id
     */
    @RpcModelProperty("商品顶级分类的id")
    private Integer firstCategoryId;

    /**
     *商品顶级分类的标签
     */
    @RpcModelProperty("商品顶级分类的标签")
    private String firstCategoryTag;

    @RpcModelProperty("商品二级分类id")
    private Integer secondCategoryId;

    @RpcModelProperty("商品二级分类名")
    private String secondCategoryTag;

    @RpcModelProperty("商品三级分类id")
    private Integer thirdCategoryId;

    @RpcModelProperty("商品三级分类名")
    private String thirdCategoryTag;
    /**
     * 可退商品数量
     */
    @RpcModelProperty("可退商品数量")
    private Double quantityCanReturn;

    /**
     * 是否已改价
     */
    @RpcModelProperty("是否已改价")
    private Boolean modifyPriceCheck;

    /**
     * 分类标签，试剂，耗材，服务等
     */
    @RpcModelProperty("分类标签")
    private String categoryTag;

    @RpcModelProperty("代配送，true开启，false关闭")
    private Boolean deliveryProxyOn;

    @RpcModelProperty(value = "采购规则枚举， 0 禁止采购，1 只提醒不禁止采购")
    private Integer purchaseRuleType;

    @RpcModelProperty(value = "采购规则校验信息")
    private String ruleRemindMsg;

    @RpcModelProperty("绑定的气瓶--订单详情返回")
    private List<GasBottleVO> gasBottles;

    @RpcModelProperty("包装规格")
    private String packingSpec;

    @RpcModelProperty("型号")
    private String modelNumber;

    @RpcModelProperty(value = "注册编码", description = "医疗器械注册证书编号")
    private String medicalDeviceRegisCertNumber;

    @RpcModelProperty("完成周期")
    private String completionCycle;

    @RpcModelProperty("出版社")
    private String press;

    @RpcModelProperty("纯度/浓度")
    private String purity;

    @RpcModelProperty("产品规格")
    private String productSpec;

    @ModelProperty("订单商品标签")
    private List<String> showTagList = New.list();

    public String getProductSpec() {
        return productSpec;
    }

    public void setProductSpec(String productSpec) {
        this.productSpec = productSpec;
    }

    public Boolean getDeliveryProxyOn() {
        return deliveryProxyOn;
    }

    public Integer getPurchaseRuleType() {
        return purchaseRuleType;
    }

    public void setPurchaseRuleType(Integer purchaseRuleType) {
        this.purchaseRuleType = purchaseRuleType;
    }

    public String getRuleRemindMsg() {
        return ruleRemindMsg;
    }

    public void setRuleRemindMsg(String ruleRemindMsg) {
        this.ruleRemindMsg = ruleRemindMsg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public Long getProductSn() {
        return productSn;
    }

    public void setProductSn(Long productSn) {
        this.productSn = productSn;
    }

    public boolean isShowOriginalPrice() {
        return showOriginalPrice;
    }

    public void setShowOriginalPrice(boolean showOriginalPrice) {
        this.showOriginalPrice = showOriginalPrice;
    }

    public Double getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(Double originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Double getActivityOff() {
        return activityOff;
    }

    public void setActivityOff(Double activityOff) {
        this.activityOff = activityOff;
    }

    public BigDecimal getCarryFee() {
        return carryFee;
    }

    public void setCarryFee(BigDecimal carryFee) {
        this.carryFee = carryFee;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getDangerousTag() {
        return dangerousTag;
    }

    public void setDangerousTag(String dangerousTag) {
        this.dangerousTag = dangerousTag;
    }

    public Integer getRegulatoryType() {
        return regulatoryType;
    }

    public void setRegulatoryType(Integer regulatoryType) {
        this.regulatoryType = regulatoryType;
    }

    public String getRegulatoryTypeName() {
        return regulatoryTypeName;
    }

    public void setRegulatoryTypeName(String regulatoryTypeName) {
        this.regulatoryTypeName = regulatoryTypeName;
    }

    public String getCategoryDirectoryName() {
        return categoryDirectoryName;
    }

    public void setCategoryDirectoryName(String categoryDirectoryName) {
        this.categoryDirectoryName = categoryDirectoryName;
    }

    public String getCategoryDirectoryCode() {
        return categoryDirectoryCode;
    }

    public OrderDetailVO setCategoryDirectoryCode(String categoryDirectoryCode) {
        this.categoryDirectoryCode = categoryDirectoryCode;
        return this;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public void setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
    }

    public String getFirstCategoryTag() {
        return firstCategoryTag;
    }

    public void setFirstCategoryTag(String firstCategoryTag) {
        this.firstCategoryTag = firstCategoryTag;
    }

    public Double getOriginAmount() {
        return originAmount;
    }

    public void setOriginAmount(Double originAmount) {
        this.originAmount = originAmount;
    }

    public Double getQuantityCanReturn() {
        return quantityCanReturn;
    }

    public void setQuantityCanReturn(Double quantityCanReturn) {
        this.quantityCanReturn = quantityCanReturn;
    }

    public Boolean getModifyPriceCheck() {
        return modifyPriceCheck;
    }

    public void setModifyPriceCheck(Boolean modifyPriceCheck) {
        this.modifyPriceCheck = modifyPriceCheck;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public Boolean isDeliveryProxyOn() {
        return Boolean.TRUE.equals(deliveryProxyOn);
    }

    public void setDeliveryProxyOn(Boolean deliveryProxyOn) {
        this.deliveryProxyOn = deliveryProxyOn;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public void setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
    }

    public String getSecondCategoryTag() {
        return secondCategoryTag;
    }

    public void setSecondCategoryTag(String secondCategoryTag) {
        this.secondCategoryTag = secondCategoryTag;
    }

    public List<GasBottleVO> getGasBottles() {
        return gasBottles;
    }

    public OrderDetailVO setGasBottles(List<GasBottleVO> gasBottles) {
        this.gasBottles = gasBottles;
        return this;
    }

    public String getPackingSpec() {
        return packingSpec;
    }

    public void setPackingSpec(String packingSpec) {
        this.packingSpec = packingSpec;
    }

    public String getModelNumber() {
        return modelNumber;
    }

    public void setModelNumber(String modelNumber) {
        this.modelNumber = modelNumber;
    }

    public String getMedicalDeviceRegisCertNumber() {
        return medicalDeviceRegisCertNumber;
    }

    public void setMedicalDeviceRegisCertNumber(String medicalDeviceRegisCertNumber) {
        this.medicalDeviceRegisCertNumber = medicalDeviceRegisCertNumber;
    }

    public String getCompletionCycle() {
        return completionCycle;
    }

    public void setCompletionCycle(String completionCycle) {
        this.completionCycle = completionCycle;
    }

    public String getPress() {
        return press;
    }

    public void setPress(String press) {
        this.press = press;
    }

    public String getPurity() {
        return purity;
    }

    public void setPurity(String purity) {
        this.purity = purity;
    }

    public Integer getThirdCategoryId() {
        return thirdCategoryId;
    }

    public OrderDetailVO setThirdCategoryId(Integer thirdCategoryId) {
        this.thirdCategoryId = thirdCategoryId;
        return this;
    }

    public String getThirdCategoryTag() {
        return thirdCategoryTag;
    }

    public OrderDetailVO setThirdCategoryTag(String thirdCategoryTag) {
        this.thirdCategoryTag = thirdCategoryTag;
        return this;
    }

    public List<String> getShowTagList() {
        return showTagList;
    }

    public OrderDetailVO setShowTagList(List<String> showTagList) {
        this.showTagList = showTagList;
        return this;
    }

    @Override
    public String toString() {
        return "OrderDetailVO{" +
                "id=" + id +
                ", goodsName='" + goodsName + '\'' +
                ", quantity=" + quantity +
                ", goodsCode='" + goodsCode + '\'' +
                ", unit='" + unit + '\'' +
                ", specification='" + specification + '\'' +
                ", brand='" + brand + '\'' +
                ", price=" + price +
                ", totalPrice=" + totalPrice +
                ", picturePath='" + picturePath + '\'' +
                ", level=" + level +
                ", comment='" + comment + '\'' +
                ", returnStatus=" + returnStatus +
                ", productSn=" + productSn +
                ", showOriginalPrice=" + showOriginalPrice +
                ", originalPrice=" + originalPrice +
                ", originAmount=" + originAmount +
                ", activityOff=" + activityOff +
                ", carryFee=" + carryFee +
                ", categoryName='" + categoryName + '\'' +
                ", categoryId=" + categoryId +
                ", dangerousTag='" + dangerousTag + '\'' +
                ", regulatoryType=" + regulatoryType +
                ", regulatoryTypeName='" + regulatoryTypeName + '\'' +
                ", categoryDirectoryName='" + categoryDirectoryName + '\'' +
                ", categoryDirectoryCode='" + categoryDirectoryCode + '\'' +
                ", casNo='" + casNo + '\'' +
                ", firstCategoryId=" + firstCategoryId +
                ", firstCategoryTag='" + firstCategoryTag + '\'' +
                ", secondCategoryId=" + secondCategoryId +
                ", secondCategoryTag='" + secondCategoryTag + '\'' +
                ", thirdCategoryId=" + thirdCategoryId +
                ", thirdCategoryTag='" + thirdCategoryTag + '\'' +
                ", quantityCanReturn=" + quantityCanReturn +
                ", modifyPriceCheck=" + modifyPriceCheck +
                ", categoryTag='" + categoryTag + '\'' +
                ", deliveryProxyOn=" + deliveryProxyOn +
                ", purchaseRuleType=" + purchaseRuleType +
                ", ruleRemindMsg='" + ruleRemindMsg + '\'' +
                ", gasBottles=" + gasBottles +
                ", packingSpec='" + packingSpec + '\'' +
                ", modelNumber='" + modelNumber + '\'' +
                ", medicalDeviceRegisCertNumber='" + medicalDeviceRegisCertNumber + '\'' +
                ", completionCycle='" + completionCycle + '\'' +
                ", press='" + press + '\'' +
                ", purity='" + purity + '\'' +
                ", productSpec='" + productSpec + '\'' +
                ", showTagList=" + showTagList +
                '}';
    }
}
