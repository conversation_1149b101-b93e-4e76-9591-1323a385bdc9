package com.ruijing.store.order.service.impl;

import com.reagent.order.base.order.dto.OrderDetailBatchesRequestDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeStatisticsDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.batchcode.api.batches.dto.OrderProductBatchesDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.UniqueBarCodeTypeEnum;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BatchesBarCodeVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderBatchesVO;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.OrderDetailBatchesVO;
import com.ruijing.store.order.other.dto.UniqueBarCodeDTO;
import com.ruijing.store.order.rpc.client.OrderBatchesRpcClient;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import com.ruijing.store.order.rpc.client.OrderUniqueBarCodeRPCClient;
import com.ruijing.store.order.service.OrderBatchService;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Liwenyu
 * @create: 2025-03-24 14:36
 * @description: 订单批次模式服务（启用批次，不启用一物一码）
 */
@Service
public class OrderBatchServiceImpl implements OrderBatchService {

    /**
     * 使用一物一码的标志位
     */
    private final int USE_EACH_PRODUCT_EACH_CODE = 1;

    /**
     * 使用批次的标志位
     */
    private final int USE_BATCH = 1 << 1;

    /**
     * 默认类型，做兼容
     */
    private final List<Integer> DEFAULT_TYPE = New.list(UniqueBarCodeTypeEnum.ORG.getCode());

    @Resource
    private OrderBatchesRpcClient orderBatchesRpcClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Override
    public void overWriteBatches(List<UniqueBarCodeDTO> requestDataList) {
        List<OrderProductBatchesDTO> orderProductBatchesDTOList = New.listWithCapacity(requestDataList.size());
        for(UniqueBarCodeDTO item : requestDataList){
            Date productionDate, expirationDate;
            try{
                productionDate = item.getProductionDate() == null ? null : DateUtils.parse(item.getProductionDate());
                expirationDate = item.getExpiration() == null ? null :  DateUtils.parse(item.getExpiration());
            }catch (Exception e){
                throw new BusinessInterceptException("日期格式输入有误，请检查后再提交", e);
            }
            orderProductBatchesDTOList.add(new OrderProductBatchesDTO().setOrderNo(item.getOrderNo())
                    .setOrderDetailId(item.getOrderDetailId())
                    .setQuantity(item.getTotal())
                    .setProductionDate(productionDate)
                    .setBatchNumber(item.getBatches())
                    .setExpiration(expirationDate)
                    .setManufacturer(item.getManufacturer()));
        }
        orderBatchesRpcClient.overwriteOrderBatches(orderProductBatchesDTOList);
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public OrderBatchesVO getOrderBatchesVO(OrderMasterDO orderMasterDO) {
        List<GoodsReturn> returnDataList = goodsReturnMapper.findByOrderId(orderMasterDO.getId());
        Map<Integer, Integer> detailIdReturnCountMap = New.map();
        for(GoodsReturn goodsReturn : returnDataList){
            if(!GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturn.getGoodsReturnStatus())){
                continue;
            }
            List<GoodsReturnInfoDetailVO> returnInfoDetailList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
            for(GoodsReturnInfoDetailVO goodsReturnInfoDetailVO : returnInfoDetailList){
                Integer detailId = Integer.parseInt(goodsReturnInfoDetailVO.getDetailId());
                detailIdReturnCountMap.put(detailId, detailIdReturnCountMap.getOrDefault(detailId, 0) + goodsReturnInfoDetailVO.getQuantity().intValue());
            }
        }

        // 如果标记了是一物一码单或者三院临床单，按一物一码输出
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        OrderBatchesVO orderBatchesVO = new OrderBatchesVO();
        List<OrderDetailBatchesVO> orderDetailBatchesVOList = orderDetailDOList.stream().map(detailDO -> new OrderDetailBatchesVO()
                .setDetailId(detailDO.getId())
                .setProductName(detailDO.getFgoodname())
                .setProductCode(detailDO.getFgoodcode())
                .setSpec(detailDO.getFspec())
                .setBrand(detailDO.getFbrand())
                .setCasNo(detailDO.getCasno())
                .setSecondCategoryId(detailDO.getSecondCategoryId())
                .setSecondCategoryName(detailDO.getSecondCategoryName())).collect(Collectors.toList());
        orderBatchesVO.setOrderDetailBathes(orderDetailBatchesVOList);

        // 非一物一码 仅读取批次
        List<OrderProductBatchesDTO> orderProductBatchesDTOList = orderBatchesRpcClient.listBatchesByOrderNos(New.list(orderMasterDO.getForderno()));
        Map<Integer, List<OrderProductBatchesDTO>> detailIdBatchGroupMap = orderProductBatchesDTOList.stream().collect(Collectors.groupingBy(OrderProductBatchesDTO::getOrderDetailId));
        for (OrderDetailBatchesVO orderDetailBatchesVO : orderDetailBatchesVOList) {
            Integer detailId = orderDetailBatchesVO.getDetailId();
            List<OrderProductBatchesDTO> batchGroup = detailIdBatchGroupMap.get(detailId);
            if (CollectionUtils.isEmpty(batchGroup)) {
                continue;
            }
            Integer returnCount = detailIdReturnCountMap.getOrDefault(detailId, 0);
            List<BatchesBarCodeVO> batchResList = New.listWithCapacity(batchGroup.size());
            for(OrderProductBatchesDTO item : batchGroup){
                Integer quantity = item.getQuantity();
                if(quantity == 0){
                    continue;
                }
                if(returnCount > 0){
                    // 去掉退货的
                    Integer minusCount = Math.min(returnCount, quantity);
                    quantity = quantity - minusCount;
                    returnCount = returnCount - minusCount;
                }
                if(quantity <= 0){
                    continue;
                }
                BatchesBarCodeVO batchRes = new BatchesBarCodeVO()
                        .setQuantity(quantity)
                        .setProductionDate(parseDate2Str(item.getProductionDate()))
                        .setBatches(item.getBatchNumber())
                        .setExpiration(parseDate2Str(item.getExpiration()))
                        .setManufacturer(item.getManufacturer())
                        .setType(UniqueBarCodeTypeEnum.ORG.getCode());
                batchResList.add(batchRes);
            }
            orderDetailBatchesVO.setBatches(batchResList);
        }

        return orderBatchesVO;
    }

    @Override
    public List<OrderUniqueBarCodeDTO> getBatchesByOrders(List<OrderMasterDO> orderMasterList) {
        List<Integer> orderIds = orderMasterList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        Map<Integer, Integer> orderIdModeMap = this.batchGetOrderMode(orderIds);
        List<OrderMasterDO> eachProductEachCodeList = New.listWithCapacity(orderMasterList.size());
        List<OrderMasterDO> noramlBatchList = New.listWithCapacity(orderMasterList.size());
        for(OrderMasterDO order : orderMasterList){
            Integer mode = orderIdModeMap.getOrDefault(order.getId(), 0);
            boolean useBatch = this.enableMode(mode, USE_BATCH);
            boolean useEachProductEachCode = this.enableMode(mode, USE_EACH_PRODUCT_EACH_CODE);
            if(!useBatch){
                continue;
            }
            if(useEachProductEachCode){
                eachProductEachCodeList.add(order);
            }else {
                noramlBatchList.add(order);
            }
        }

        List<OrderUniqueBarCodeDTO> resultList = New.list();
        // 一物一码部分
        if(CollectionUtils.isNotEmpty(eachProductEachCodeList)){
            resultList.addAll(this.getEachProductEachCodeBatches(eachProductEachCodeList));
        }
        if(CollectionUtils.isNotEmpty(noramlBatchList)){
            resultList.addAll(this.getNormalBatches(noramlBatchList));
        }
        return resultList;
    }

    @Override
    public List<OrderUniqueBarCodeStatisticsDTO> getBarCodeBatchesByOrderNo(String orderNo, List<Integer> typeList) {
        BusinessErrUtil.notNull(orderNo, "获取条形码批次信息, 订单号为空");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, "无效的订单");
        int mode = this.getOrderMode(orderMasterDO.getId());
        if(enableMode(mode, USE_EACH_PRODUCT_EACH_CODE)){
            return orderUniqueBarCodeRPCClient.getBarCodeBatchesByOrderNo(orderNo, typeList);
        }
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        List<OrderProductBatchesDTO> orderProductBatchesDTOList = orderBatchesRpcClient.listBatchesByOrderNos(New.list(orderNo));
        Map<Integer, Integer> detailIdInputCountMap = New.mapWithCapacity(orderDetailDOList.size());
        if(CollectionUtils.isNotEmpty(orderProductBatchesDTOList)){
            orderProductBatchesDTOList.forEach(item->detailIdInputCountMap.put(item.getOrderDetailId(), detailIdInputCountMap.getOrDefault(item.getOrderDetailId(), 0) + item.getQuantity()));
        }
        List<OrderUniqueBarCodeStatisticsDTO> orderUniqueBarCodeStatisticsDTOList = New.listWithCapacity(orderDetailDOList.size());
        for(OrderDetailDO orderDetailDO : orderDetailDOList){
            OrderUniqueBarCodeStatisticsDTO orderUniqueBarCodeStatisticsDTO = new OrderUniqueBarCodeStatisticsDTO();
            orderUniqueBarCodeStatisticsDTO.setDetailId(orderDetailDO.getId());
            orderUniqueBarCodeStatisticsDTO.setBrand(orderDetailDO.getFbrand());
            orderUniqueBarCodeStatisticsDTO.setSpec(orderDetailDO.getFspec());
            orderUniqueBarCodeStatisticsDTO.setProductName(orderDetailDO.getFgoodname());
            orderUniqueBarCodeStatisticsDTO.setHasInputTotal(detailIdInputCountMap.getOrDefault(orderDetailDO.getId(), 0));
            orderUniqueBarCodeStatisticsDTO.setPrintedTotal(orderUniqueBarCodeStatisticsDTO.getHasInputTotal());
            orderUniqueBarCodeStatisticsDTO.setTotal(orderDetailDO.getFquantity().intValue());
            orderUniqueBarCodeStatisticsDTOList.add(orderUniqueBarCodeStatisticsDTO);
        }
        return orderUniqueBarCodeStatisticsDTOList;
    }

    private List<OrderUniqueBarCodeDTO> getEachProductEachCodeBatches(List<OrderMasterDO> orderMasterDOList){
        List<Integer> orderIds = orderMasterDOList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIds);
        List<Integer> orderDetailIds = orderDetailList.stream().map(OrderDetailDO::getId).collect(Collectors.toList());
        // 用一物一码的，一物一码做聚合（旧模式，后续替换）
        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByDetailIdList(orderDetailIds);
        if(CollectionUtils.isEmpty(orderUniqueBarCodeDTOList)){
            return New.emptyList();
        }
        List<Function<OrderUniqueBarCodeDTO, ?>> functions = orderUniqueBarCodeRPCClient.getBatchModeNeedAggFieldGetter();
        return orderUniqueBarCodeRPCClient.groupBatchesByField(orderUniqueBarCodeDTOList, functions);
    }

    private List<OrderUniqueBarCodeDTO> getNormalBatches(List<OrderMasterDO> orderMasterDOList){
        List<String> orderNos = orderMasterDOList.stream().map(OrderMasterDO::getForderno).collect(Collectors.toList());
        List<OrderProductBatchesDTO> batchList = orderBatchesRpcClient.listBatchesByOrderNos(orderNos);
        if(CollectionUtils.isEmpty(batchList)){
            return New.emptyList();
        }
        List<OrderUniqueBarCodeDTO> resultList = New.listWithCapacity(batchList.size());
        for(OrderProductBatchesDTO item : batchList){
            OrderUniqueBarCodeDTO result = new OrderUniqueBarCodeDTO();
            result.setOrderNo(item.getOrderNo());
            result.setOrderDetailId(item.getOrderDetailId());
            result.setBatches(item.getBatchNumber());
            result.setExpiration(parseDate2Str(item.getExpiration()));
            result.setManufacturer(item.getManufacturer());
            result.setProductionDate(item.getProductionDate());
            result.setTotal(item.getQuantity());
            resultList.add(result);
        }
        return resultList;
    }

    private String parseDate2Str(Date date){
        return date == null ? null : DateUtils.format("yyyy-MM-dd", date);
    }

    private Map<Integer, Integer> batchGetOrderMode(List<Integer> orderIds){
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderIds), New.list(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue(), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue()));
        Map<Integer, Integer> orderIdModeMap = New.mapWithCapacity(orderIds.size());
        int mode = 0;
        for (OrderExtraDTO orderExtraDTO : orderExtraDTOList) {
            if (OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue().equals(orderExtraDTO.getExtraKey()) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTO.getExtraValue())) {
                mode = mode ^ USE_EACH_PRODUCT_EACH_CODE;
            } else if (OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue().equals(orderExtraDTO.getExtraKey()) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTO.getExtraValue())) {
                mode = mode ^ USE_BATCH;
            }
            orderIdModeMap.put(orderExtraDTO.getOrderId(), mode);
        }
        return orderIdModeMap;
    }

    private int getOrderMode(Integer orderId){
        return this.batchGetOrderMode(New.list(orderId)).getOrDefault(orderId, 0);
    }

    /**
     * 通过按位与判断是否启用了该模式
     * @param mode getOrderMode获取到的结果
     * @param modeFlag 需要判断的标志位
     * @return 是否启用对应的模式
     */
    private boolean enableMode(Integer mode, int modeFlag){
        if(mode == null){
            return false;
        }
        return (mode & modeFlag) == modeFlag;
    }
}
