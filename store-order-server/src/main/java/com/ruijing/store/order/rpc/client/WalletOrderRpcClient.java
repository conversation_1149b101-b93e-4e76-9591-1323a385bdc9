package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.supp.api.wallet.dto.OrderForWalletDTO;
import com.reagent.supp.api.wallet.service.TOrderForWalletService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.shop.shopcommon.base.ApiResult;
import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.shop.wallet.api.dto.WaitingChargingOrderDTO;
import com.ruijing.shop.wallet.api.dto.WalletOrderReturnDTO;
import com.ruijing.shop.wallet.api.enmus.DistributionFlagEnum;
import com.ruijing.shop.wallet.api.service.WalletOrderService;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 供应商订单金额 rpc 接口
 */
@ServiceClient
public class WalletOrderRpcClient {

    @MSharpReference(remoteAppkey = "shop-wallet-service", timeout = "10000")
    private WalletOrderService walletOrderService;

    @MSharpReference(remoteAppkey = "supp", timeout = "10000")
    private TOrderForWalletService tOrderForWalletService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @ServiceLog(description = "加入待扣费钱包订单队列接口", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public Integer noticeWaitingChargingOrder(WaitingChargingOrderDTO request) {
        ApiResult<Integer> response = walletOrderService.noticeWaitingChargingOrder(request);
        Assert.isTrue(response.successful(), "加入待扣费钱包订单队列异常" + JsonUtils.toJson(response.getMsg()));

        return response.getData();
    }

    @ServiceLog(description = "查询待扣费钱包订单队列接口", serviceType = ServiceType.RPC_CLIENT)
    public List<WaitingChargingOrderDTO> queryWaitingChargingOrderPage(String orderNo){
        WaitingChargingOrderDTO waitingChargingOrderDTO = new WaitingChargingOrderDTO();
        waitingChargingOrderDTO.setOrderNo(orderNo);
        waitingChargingOrderDTO.setPage(1);
        waitingChargingOrderDTO.setSize(10);
        PageApiResult<List<WaitingChargingOrderDTO>> pageApiResult = walletOrderService.queryWaitingChargingOrderPage(waitingChargingOrderDTO);
        Preconditions.isTrue(pageApiResult.successful(), pageApiResult.getMsg());
        return pageApiResult.getData();
    }

    @ServiceLog(description = "生成钱包退款记录", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void saveOrderReturn(List<WalletOrderReturnDTO> walletOrderReturnDTOS){
        ApiResult apiResult = walletOrderService.saveOrderReturn(walletOrderReturnDTOS);
        Preconditions.isTrue(apiResult.successful(), apiResult.getMessage());
    }

    public List<OrderForWalletDTO> findByOrderNo(String orderNo) {
        return this.findByOrderNoList(Arrays.asList(orderNo));
    }

    @ServiceLog(description = "根据订单号查询扣费记录, 走老表", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderForWalletDTO> findByOrderNoList(List<String> orderNoList) {
        RemoteResponse<List<OrderForWalletDTO>> response = tOrderForWalletService.findByOrderNo(orderNoList);
        Preconditions.isTrue(response.isSuccess(), "通过订单号查询扣费记录异常" + JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }

    @ServiceLog(description = "根据订单号查询扣费记录", serviceType = ServiceType.RPC_CLIENT)
    public List<WalletOrderReturnDTO> findRefundByReturnNo(String returnNo) {
        List<WalletOrderReturnDTO> result = this.findRefundByReturnNo(returnNo, 1, 100);
        return result;
    }

    public List<WalletOrderReturnDTO> findRefundByReturnNo(String returnNo, Integer page, Integer size) {
        WalletOrderReturnDTO request = new WalletOrderReturnDTO();
        request.setReturnNo(returnNo);
        request.setPage(page);
        request.setSize(size);
        PageApiResult<List<WalletOrderReturnDTO>> response = walletOrderService.queryOrderReturnPage(request);
        Preconditions.isTrue(response.successful(), "根据退货单查询退款单异常" + JsonUtils.toJsonIgnoreNull(response));
        List<WalletOrderReturnDTO> result = response.getData();
        long total = response.getTotal();
        // 如果总记录数大于查询记录数则再次分页查询剩余结果
        if (size.longValue() != total && total > size) {
            long count = total/size;
            for (int i = 0; i < count; i++) {
                result.addAll(this.findRefundByReturnNo(returnNo, ++page, size));
            }
        }
        return result;
    }

    /**
     * 加入待扣费钱包订单队列
     */
    public void addOrderWalletQueue(OrderMasterDO order, Integer deliveryType) {
        String orderNo = order.getForderno();

        WaitingChargingOrderDTO request = new WaitingChargingOrderDTO();
        request.setOrderAmount(order.getForderamounttotal().subtract(BigDecimal.valueOf(order.getReturnAmount())));
        request.setOrderDate(order.getForderdate());
        request.setOrderNo(orderNo);
        request.setOrgId(order.getFuserid().longValue());
        request.setOrgCode(order.getFusercode());
        request.setBuyerId(order.getFbuyerid());
        request.setSuppId(order.getFsuppid());
        request.setCreationTime(new Date());
        request.setDeptId(order.getFbuydepartmentid());
        request.setDeptName(order.getFbuydepartment());
        request.setPlatformDistributionFlag(deliveryType);

        this.noticeWaitingChargingOrder(request);
    }

    /**
     * 加入待扣费钱包订单队列
     */
    public void addOrderWalletQueue(OrderMasterDO order) {
        // 查询配送方式，选择不同的扣费方式
        OrderAddressDTO addressDTO = orderAddressRPCClient.findByOrderId(order.getId());
        if (addressDTO != null && DeliveryTypeEnum.PROXY.getCode().equals(addressDTO.getDeliveryType())) {
            this.addOrderWalletQueue(order, addressDTO.getDeliveryType());
        } else {
            this.addOrderWalletQueue(order, DistributionFlagEnum.SHOPDISTRIBUTION.getValue());
        }
    }
}
