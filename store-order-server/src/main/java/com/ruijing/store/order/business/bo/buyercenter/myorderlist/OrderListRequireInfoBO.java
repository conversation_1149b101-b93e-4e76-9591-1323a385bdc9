package com.ruijing.store.order.business.bo.buyercenter.myorderlist;

import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.order.saturn.api.accept.approve.dto.AcceptApproveInfoDTO;
import com.ruijing.order.saturn.api.audit.sampling.dto.OrderAuditSamplingDTO;
import com.ruijing.order.saturn.api.contract.vo.OrderContractInfoVO;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.shop.trade.api.dto.OrderDeliveryInfoDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptAttachmentDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptPictureDTO;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;
import com.ruijing.store.order.base.minor.model.OrderPic;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptWayEnum;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignConfigVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOfflineInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderPushEventStatusVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/11/25 17:14
 * @Description
 **/
public class OrderListRequireInfoBO {

    /**
     * 前端传来并结果处理的请求
     */
    private OrderListRequest request;

    /**
     * 单位id
     */
    private Integer orgId;

    /**
     * 单位code
     */
    private String orgCode;

    /**
     * 登录用户id
     */
    private Integer curUserId;

    /**
     * 登录用户guid
     */
    private String curUserGuid;

    /**
     * 验收配置枚举
     */
    private OrderAcceptWayEnum confAccept;

    /**
     * 超时订单列表
     */
    private OrderOvertimeBO orderOvertimeBO;

    /**
     * 结算单id与详情的对应关系
     */
    private Map<Long, StatementResultDTO> statementIdResultMapping;

    /**
     * 供应商详细信息，主要是联系信息
     */
    private Map<Integer, SuppShopInfoBO> suppContactInfoMap;

    /**
     * 订单id和评价状态的对应关系
     */
    private Map<Integer, Short> orderIdCommentStatusMap;

    /**
     * 订单id和合同上传状态的对应关系
     */
    private Map<Integer, Integer> orderIdContractStatusMap;

    /**
     * 是否使用新库房系统
     */
    private Boolean useNewWarehouseSystem;

    /**
     * 订单号和对接状态对应关系
     */
    private Map<String, DockingExtra> orderNoDockStatusMap;

    /**
     * 订单id和失败原因（仅保存经费释放失败的对应）对应关系
     */
    private Map<Integer, OrderMasterDO> orderIdMasterMap;

    /**
     * 线上单是否使用锐竞的结算系统
     */
    private Boolean onlineStatementCheck;

    /**
     * 线下单是否使用锐竞的结算系统
     */
    private Boolean offlineStatementCheck;

    /**
     * 订单采购商品（危化品）备案记录对应关系
     */
    private Map<Integer, OrderConfirmForTheRecordDO> orderIdConfirmRecordMap;

    /**
     * 是否为hms
     */
    private boolean hmsCheck;

    /**
     * 订单对应的线下单信息
     */
    private Map<Integer, OrderOfflineInfoVO> orderIdOfflineInfoMap;

    /**
     * 订单验收图片字典
     */
    private Map<String, List<OrderPic>> orderNoPicMap;

    /**
     * 订单代配送设置
     */
    private Map<Integer, OrderAddressDTO> orderIdAddressMap;

    /**
     * 代配送 suppid和代配送院区的map
     */
    private Map<Integer, List<String>> suppIdDeliveryProxyLabel;

    /**
     * 订单号-订单推送状态的map
     */
    private Map<String, List<OrderPushEventStatusVO>> orderNoPushEventStatusMap;

    /**
     * 订单id-当前验收审批信息map
     */
    private Map<Integer, AcceptApproveInfoDTO> orderIdCurrentAcceptApproveInfoMap;

    /**
     * 课题组id-电子签名配置映射
     */
    private Map<Integer, ElectronicSignConfigVO> buyerDepartmentIdSignConfigMap;

    /**
     * 是否具有 显示撤销入库的oms权限
     */
    private boolean showCancelReceiptCtrl;

    /**
     * 订单id-申请取消代配送日志映射
     */
    private Map<Integer, OrderApprovalLog> orderIdCancelCxDelPxyLogMap;

    /**
     * 订单id-额外信息映射
     */
    private Map<Integer, List<OrderExtraDTO>> orderIdExtraMap;

    /**
     * 已经上传发票的订单
     */
    private List<Integer> uploadedInvoiceOrderList;

    /**
     * 订单id-fundCardVo映射
     */
    private Map<Integer, List<OrderFundcardVO>> orderIdFundCardVoMap;

    /**
     * 用户id和名字映射缓存，减少重复查询操作
     */
    private Map<Integer, String> userIdNameMap;

    /**
     * 旧单日期
     */
    private OldDateConfigBO oldDateConfigBO;

    /**
     * 单位对接配置
     */
    private OrgDockingConfigDTO dockingConfig;

    /**
     * 启用了对接配置的订单
     */
    private List<Integer> enableDockingConfigOrderIdList;

    /**
     * 订单id-入库驳回原因映射，广西肿瘤在用
     */
    private Map<Integer, String> orderIdWarehouseRejectReasonMap;

    /**
     * OMS配置 key:配置code value:配置值
     */
    Map<String, List<String>> baseConfigCodeValueMap;

    /**
     * 订单详情扩展信息  key:订单id value:订单详情扩展信息
     */
    Map<Integer, List<OrderDetailExtraDTO>> orderIdDetailExtraMap;

    /**
     * 订单上传文件信息  key:订单id value:文件类型-文件集合
     */
    private Map<Integer, Map<Integer, List<OrderUploadFileDTO>>> orderIdUploadFileMap;

    /**
     * 订单收款账户类型 key:订单id value:收款账户类型
     */
    private Map<Integer, Integer> orderIdAccountTypeMap;

    /**
     * 订单商品验收图片 key: 订单ID value: 商品验收图片列表
     */
    private Map<Integer, List<AcceptPictureDTO>> orderDetailAcceptancePicsMap;

    /**
     * 订单商品验收附件 key: 订单ID value: 商品验收附件列表
     */
    private Map<Integer, List<AcceptAttachmentDTO>> orderDetailAcceptanceFilesMap;

    /**
     * 订单号-合同信息映射 key:订单号 value:合同信息列表
     */
    private Map<String, List<OrderContractInfoVO>> orderNoContractInfoMap;

    /**
     * 权限码-有权限的部门id
     */
    private Map<String, List<Integer>> accessCodeDeptIdMap;

    /**
     * 订单号-抽检记录映射
     */
    private Map<String, OrderAuditSamplingDTO> orderNoAuditSamplingMap;

    /**
     * 订单id-订单详情列表，注：字段有所缩减，需要增加要到mapper层加字段
     */
    private Map<Integer, List<OrderDetailDO>> orderIdOrderDetailMap;

    /**
     * 订单id-发货信息映射
     */
    private Map<Integer, OrderDeliveryInfoDTO> orderIdDeliveryInfoMap;

    public Map<String, List<OrderContractInfoVO>> getOrderNoContractInfoMap() {
        return orderNoContractInfoMap;
    }

    public OrderListRequireInfoBO setOrderNoContractInfoMap(Map<String, List<OrderContractInfoVO>> orderNoContractInfoMap) {
        this.orderNoContractInfoMap = orderNoContractInfoMap;
        return this;
    }

    public Map<Integer, List<AcceptAttachmentDTO>> getOrderDetailAcceptanceFilesMap() {
        return orderDetailAcceptanceFilesMap;
    }

    public OrderListRequireInfoBO setOrderDetailAcceptanceFilesMap(Map<Integer, List<AcceptAttachmentDTO>> orderDetailAcceptanceFilesMap) {
        this.orderDetailAcceptanceFilesMap = orderDetailAcceptanceFilesMap;
        return this;
    }

    public Map<Integer, List<AcceptPictureDTO>> getOrderDetailAcceptancePicsMap() {
        return orderDetailAcceptancePicsMap;
    }

    public OrderListRequireInfoBO setOrderDetailAcceptancePicsMap(Map<Integer, List<AcceptPictureDTO>> orderDetailAcceptancePicsMap) {
        this.orderDetailAcceptancePicsMap = orderDetailAcceptancePicsMap;
        return this;
    }

    public Map<Integer, Integer> getOrderIdAccountTypeMap() {
        return orderIdAccountTypeMap;
    }

    public OrderListRequireInfoBO setOrderIdAccountTypeMap(Map<Integer, Integer> orderIdAccountTypeMap) {
        this.orderIdAccountTypeMap = orderIdAccountTypeMap;
        return this;
    }

    public Map<Integer, Map<Integer, List<OrderUploadFileDTO>>> getOrderIdUploadFileMap() {
        return orderIdUploadFileMap;
    }

    public OrderListRequireInfoBO setOrderIdUploadFileMap(Map<Integer, Map<Integer, List<OrderUploadFileDTO>>> orderIdUploadFileMap) {
        this.orderIdUploadFileMap = orderIdUploadFileMap;
        return this;
    }

    public Map<Integer, List<OrderDetailExtraDTO>> getOrderIdDetailExtraMap() {
        return orderIdDetailExtraMap;
    }

    public OrderListRequireInfoBO setOrderIdDetailExtraMap(Map<Integer, List<OrderDetailExtraDTO>> orderIdDetailExtraMap) {
        this.orderIdDetailExtraMap = orderIdDetailExtraMap;
        return this;
    }

    public Map<String, List<String>> getBaseConfigCodeValueMap() {
        return baseConfigCodeValueMap;
    }

    public OrderListRequireInfoBO setBaseConfigCodeValueMap(Map<String, List<String>> baseConfigCodeValueMap) {
        this.baseConfigCodeValueMap = baseConfigCodeValueMap;
        return this;
    }

    public OrderListRequest getRequest() {
        return request;
    }

    public OrderListRequireInfoBO setRequest(OrderListRequest request) {
        this.request = request;
        return this;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public OrderListRequireInfoBO setOrgId(Integer orgId) {
        this.orgId = orgId;
        return this;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public OrderListRequireInfoBO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public Integer getCurUserId() {
        return curUserId;
    }

    public OrderListRequireInfoBO setCurUserId(Integer curUserId) {
        this.curUserId = curUserId;
        return this;
    }

    public String getCurUserGuid() {
        return curUserGuid;
    }

    public OrderListRequireInfoBO setCurUserGuid(String curUserGuid) {
        this.curUserGuid = curUserGuid;
        return this;
    }

    public OrderAcceptWayEnum getConfAccept() {
        return confAccept;
    }

    public OrderListRequireInfoBO setConfAccept(OrderAcceptWayEnum confAccept) {
        this.confAccept = confAccept;
        return this;
    }

    public OrderOvertimeBO getOrderOvertimeBO() {
        return orderOvertimeBO;
    }

    public OrderListRequireInfoBO setOrderOvertimeBO(OrderOvertimeBO orderOvertimeBO) {
        this.orderOvertimeBO = orderOvertimeBO;
        return this;
    }

    public Map<Long, StatementResultDTO> getStatementIdResultMapping() {
        return statementIdResultMapping;
    }

    public OrderListRequireInfoBO setStatementIdResultMapping(Map<Long, StatementResultDTO> statementIdResultMapping) {
        this.statementIdResultMapping = statementIdResultMapping;
        return this;
    }

    public Map<Integer, Short> getOrderIdCommentStatusMap() {
        return orderIdCommentStatusMap;
    }

    public OrderListRequireInfoBO setOrderIdCommentStatusMap(Map<Integer, Short> orderIdCommentStatusMap) {
        this.orderIdCommentStatusMap = orderIdCommentStatusMap;
        return this;
    }

    public Map<Integer, Integer> getOrderIdContractStatusMap() {
        return orderIdContractStatusMap;
    }

    public OrderListRequireInfoBO setOrderIdContractStatusMap(Map<Integer, Integer> orderIdContractStatusMap) {
        this.orderIdContractStatusMap = orderIdContractStatusMap;
        return this;
    }

    public Boolean getUseNewWarehouseSystem() {
        return useNewWarehouseSystem;
    }

    public OrderListRequireInfoBO setUseNewWarehouseSystem(Boolean useNewWarehouseSystem) {
        this.useNewWarehouseSystem = useNewWarehouseSystem;
        return this;
    }

    public Map<String, DockingExtra> getOrderNoDockStatusMap() {
        return orderNoDockStatusMap;
    }

    public OrderListRequireInfoBO setOrderNoDockStatusMap(Map<String, DockingExtra> orderNoDockStatusMap) {
        this.orderNoDockStatusMap = orderNoDockStatusMap;
        return this;
    }

    public Map<Integer, SuppShopInfoBO> getSuppContactInfoMap() {
        return suppContactInfoMap;
    }

    public OrderListRequireInfoBO setSuppContactInfoMap(Map<Integer, SuppShopInfoBO> suppContactInfoMap) {
        this.suppContactInfoMap = suppContactInfoMap;
        return this;
    }

    public Map<Integer, OrderMasterDO> getOrderIdMasterMap() {
        return orderIdMasterMap;
    }

    public OrderListRequireInfoBO setOrderIdMasterMap(Map<Integer, OrderMasterDO> orderIdMasterMap) {
        this.orderIdMasterMap = orderIdMasterMap;
        return this;
    }

    public Boolean getOnlineStatementCheck() {
        return onlineStatementCheck;
    }

    public OrderListRequireInfoBO setOnlineStatementCheck(Boolean onlineStatementCheck) {
        this.onlineStatementCheck = onlineStatementCheck;
        return this;
    }

    public Boolean getOfflineStatementCheck() {
        return offlineStatementCheck;
    }

    public OrderListRequireInfoBO setOfflineStatementCheck(Boolean offlineStatementCheck) {
        this.offlineStatementCheck = offlineStatementCheck;
        return this;
    }

    public Map<Integer, OrderConfirmForTheRecordDO> getOrderIdConfirmRecordMap() {
        return orderIdConfirmRecordMap;
    }

    public OrderListRequireInfoBO setOrderIdConfirmRecordMap(Map<Integer, OrderConfirmForTheRecordDO> orderIdConfirmRecordMap) {
        this.orderIdConfirmRecordMap = orderIdConfirmRecordMap;
        return this;
    }

    public boolean isHms() {
        return hmsCheck;
    }

    public OrderListRequireInfoBO setHmsCheck(boolean hmsCheck) {
        this.hmsCheck = hmsCheck;
        return this;
    }

    public Map<Integer, OrderOfflineInfoVO> getOrderIdOfflineInfoMap() {
        return orderIdOfflineInfoMap;
    }

    public OrderListRequireInfoBO setOrderIdOfflineInfoMap(Map<Integer, OrderOfflineInfoVO> orderIdOfflineInfoMap) {
        this.orderIdOfflineInfoMap = orderIdOfflineInfoMap;
        return this;
    }

    public Map<String, List<OrderPic>> getOrderNoPicMap() {
        return orderNoPicMap;
    }

    public OrderListRequireInfoBO setOrderNoPicMap(Map<String, List<OrderPic>> orderNoPicMap) {
        this.orderNoPicMap = orderNoPicMap;
        return this;
    }

    public Map<Integer, OrderAddressDTO> getOrderIdAddressMap() {
        return orderIdAddressMap;
    }

    public OrderListRequireInfoBO setOrderIdAddressMap(Map<Integer, OrderAddressDTO> orderIdAddressMap) {
        this.orderIdAddressMap = orderIdAddressMap;
        return this;
    }

    public Map<Integer, List<String>> getSuppIdDeliveryProxyLabel() {
        return suppIdDeliveryProxyLabel;
    }

    public OrderListRequireInfoBO setSuppIdDeliveryProxyLabel(Map<Integer, List<String>> suppIdDeliveryProxyLabel) {
        this.suppIdDeliveryProxyLabel = suppIdDeliveryProxyLabel;
        return this;
    }

    public Map<String, List<OrderPushEventStatusVO>> getOrderNoPushEventStatusMap() {
        return orderNoPushEventStatusMap;
    }

    public OrderListRequireInfoBO setOrderNoPushEventStatusMap(Map<String, List<OrderPushEventStatusVO>> orderNoPushEventStatusMap) {
        this.orderNoPushEventStatusMap = orderNoPushEventStatusMap;
        return this;
    }

    public Map<Integer, AcceptApproveInfoDTO> getOrderIdCurrentAcceptApproveInfoMap() {
        return orderIdCurrentAcceptApproveInfoMap;
    }

    public OrderListRequireInfoBO setOrderIdCurrentAcceptApproveInfoMap(Map<Integer, AcceptApproveInfoDTO> orderIdCurrentAcceptApproveInfoMap) {
        this.orderIdCurrentAcceptApproveInfoMap = orderIdCurrentAcceptApproveInfoMap;
        return this;
    }

    public Map<Integer, ElectronicSignConfigVO> getBuyerDepartmentIdSignConfigMap() {
        return buyerDepartmentIdSignConfigMap;
    }

    public OrderListRequireInfoBO setBuyerDepartmentIdSignConfigMap(Map<Integer, ElectronicSignConfigVO> buyerDepartmentIdSignConfigMap) {
        this.buyerDepartmentIdSignConfigMap = buyerDepartmentIdSignConfigMap;
        return this;
    }

    public boolean isShowCancelReceiptCtrl() {
        return showCancelReceiptCtrl;
    }

    public OrderListRequireInfoBO setShowCancelReceiptCtrl(boolean showCancelReceiptCtrl) {
        this.showCancelReceiptCtrl = showCancelReceiptCtrl;
        return this;
    }

    public Map<Integer, OrderApprovalLog> getOrderIdCancelCxDelPxyLogMap() {
        return orderIdCancelCxDelPxyLogMap;
    }

    public OrderListRequireInfoBO setOrderIdCancelCxDelPxyLogMap(Map<Integer, OrderApprovalLog> orderIdCancelCxDelPxyLogMap) {
        this.orderIdCancelCxDelPxyLogMap = orderIdCancelCxDelPxyLogMap;
        return this;
    }

    public Map<Integer, List<OrderExtraDTO>> getOrderIdExtraMap() {
        return orderIdExtraMap;
    }

    public OrderListRequireInfoBO setOrderIdExtraMap(Map<Integer, List<OrderExtraDTO>> orderIdExtraMap) {
        this.orderIdExtraMap = orderIdExtraMap;
        return this;
    }

    public List<Integer> getUploadedInvoiceOrderList() {
        return uploadedInvoiceOrderList;
    }

    public OrderListRequireInfoBO setUploadedInvoiceOrderList(List<Integer> uploadedInvoiceOrderList) {
        this.uploadedInvoiceOrderList = uploadedInvoiceOrderList;
        return this;
    }

    public Map<Integer, List<OrderFundcardVO>> getOrderIdFundCardVoMap() {
        return orderIdFundCardVoMap;
    }

    public OrderListRequireInfoBO setOrderIdFundCardVoMap(Map<Integer, List<OrderFundcardVO>> orderIdFundCardVoMap) {
        this.orderIdFundCardVoMap = orderIdFundCardVoMap;
        return this;
    }

    public Map<Integer, String> getUserIdNameMap() {
        return userIdNameMap;
    }

    public OrderListRequireInfoBO setUserIdNameMap(Map<Integer, String> userIdNameMap) {
        this.userIdNameMap = userIdNameMap;
        return this;
    }

    public OldDateConfigBO getOldDateConfigBO() {
        return oldDateConfigBO;
    }

    public OrderListRequireInfoBO setOldDateConfigBO(OldDateConfigBO oldDateConfigBO) {
        this.oldDateConfigBO = oldDateConfigBO;
        return this;
    }

    public OrgDockingConfigDTO getDockingConfig() {
        return dockingConfig;
    }

    public OrderListRequireInfoBO setDockingConfig(OrgDockingConfigDTO dockingConfig) {
        this.dockingConfig = dockingConfig;
        return this;
    }

    public List<Integer> getEnableDockingConfigOrderIdList() {
        return enableDockingConfigOrderIdList;
    }

    public OrderListRequireInfoBO setEnableDockingConfigOrderIdList(List<Integer> enableDockingConfigOrderIdList) {
        this.enableDockingConfigOrderIdList = enableDockingConfigOrderIdList;
        return this;
    }

    public Map<Integer, String> getOrderIdWarehouseRejectReasonMap() {
        return orderIdWarehouseRejectReasonMap;
    }

    public OrderListRequireInfoBO setOrderIdWarehouseRejectReasonMap(Map<Integer, String> orderIdWarehouseRejectReasonMap) {
        this.orderIdWarehouseRejectReasonMap = orderIdWarehouseRejectReasonMap;
        return this;
    }

    public Map<String, List<Integer>> getAccessCodeDeptIdMap() {
        return accessCodeDeptIdMap;
    }

    public OrderListRequireInfoBO setAccessCodeDeptIdMap(Map<String, List<Integer>> accessCodeDeptIdMap) {
        this.accessCodeDeptIdMap = accessCodeDeptIdMap;
        return this;
    }

    public Map<String, OrderAuditSamplingDTO> getOrderNoAuditSamplingMap() {
        return orderNoAuditSamplingMap;
    }

    public OrderListRequireInfoBO setOrderNoAuditSamplingMap(Map<String, OrderAuditSamplingDTO> orderNoAuditSamplingMap) {
        this.orderNoAuditSamplingMap = orderNoAuditSamplingMap;
        return this;
    }

    public Map<Integer, List<OrderDetailDO>> getOrderIdOrderDetailMap() {
        return orderIdOrderDetailMap;
    }

    public OrderListRequireInfoBO setOrderIdOrderDetailMap(Map<Integer, List<OrderDetailDO>> orderIdOrderDetailMap) {
        this.orderIdOrderDetailMap = orderIdOrderDetailMap;
        return this;
    }

    public Map<Integer, OrderDeliveryInfoDTO> getOrderIdDeliveryInfoMap() {
        return orderIdDeliveryInfoMap;
    }

    public OrderListRequireInfoBO setOrderIdDeliveryInfoMap(Map<Integer, OrderDeliveryInfoDTO> orderIdDeliveryInfoMap) {
        this.orderIdDeliveryInfoMap = orderIdDeliveryInfoMap;
        return this;
    }
}
