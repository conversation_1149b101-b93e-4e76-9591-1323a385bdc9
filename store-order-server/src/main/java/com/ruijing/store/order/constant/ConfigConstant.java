package com.ruijing.store.order.constant;

public interface ConfigConstant {

    /**
     * 验收审批OMS配置 code
     */
    String ORG_ACCEPTANCE_APPROVAL_CONFIG = "ORG_ACCEPTANCE_APPROVAL_CONFIG";

    /**
     * 验收审批
     */
    Integer ACCEPTANCE_APPROVAL = 1;

    /**
     * 验收时是否需要上传图片
     */
    String ORG_RECEIPT_PIC_CONFIG = "ORG_RECEIPT_PIC_CONFIG";


    /**
     * 验收时是否需要上传视频附件
     *
     *  1无需上传/2非必须上传/3必须上传
     */
    String ORDER_VIDEO_ATTACHMENT = "ORDER_VIDEO_ATTACHMENT";

    /**
     * 其他验收附件
     *
     *  1无需上传/2非必须上传/3必须上传
     */
    String OTHER_ORG_RECEIPT_ATTACHMENT = "OTHER_ORG_RECEIPT_ATTACHMENT";

    /**
     * 入库方式
     */
    String ORG_RECEIPT_STORE_CONFIG = "ORG_RECEIPT_STORE_CONFIG";

    /**
     * 采购验收方式
     */
    String CONFIG_CODE_PROCUREMENT_ACCEPTANCE = "PROCUREMENT_ACCEPTANCE_WAY";

    /**
     * 验收后自动提交结算,单词因为历史原因是错的
     */
    String AUTO_SUBMIT_STATMENT = "AUTO_SUBMIT_STATMENT";

    /**
     * 验收后自动提交结算
     */
    String STATEMENT_SUBMITTED_MANNER = "STATEMENT_SUBMITTED_MANNER";

    /**
     * 订单上传合同配置
     */
    String ORDER_CONTRACT_UPLOAD = "ORDER_CONTRACT_UPLOAD";

    /**
     * 是否自动验收
     */
    String AUTO_ACCEPTANCE_CHCEK = "AUTO_ACCEPTANCE_CHCEK";

    /**
     * 自动验收天数 配置
     */
    String AUTO_ACCEPTANCE_DAYS = "AUTO_ACCEPTANCE_DAYS";

    /**
     * 合同金额 配置
     */
    String ORDER_CONTRACT_MONETARY_LIMITATION = "ORDER_CONTRACT_MONETARY_LIMITATION";

    /**
     * 科研服务分类ID
     */
    Integer SERVICE_CATEGORY_ID = 113;

    /**
     * 动物实验服务分类ID
     */
    Integer ANIMAL_CATEGORY_ID = 648;

    /**
     * 模式动物定制分类ID
     */
    Integer MODEL_ANIMAL_CATEGORY_ID = 655;

    // -----ORG_RECEIPT_PIC_CONFIG:拍照验收配置
    /**
     * 0标识 拍照验收配置 无需拍照验收
     */
    String NOT_NEED_PIC_VALUE = "0";

    /**
     * 1标识 拍照验收配置 强制拍照验收
     */
    String NEED_PIC_VALUE = "1";


    /**
     * 非强制拍照验收
     */
    String NOT_FORCE_PIC_VALUE = "2";

    /**
     * 除服务类强制拍照验收
     */
    String UNLESS_SERVICE_NEED_PIC_VALUE = "3";

    // -----END

    /**
     * 1标识 验收方式配置 交叉验收
     */
    Integer CROSS_ACCEPTANCE_VALUE = 1;

    /**
     * 1标识 自动提交结算
     */
    Integer AUTO_STATEMENT_VALUE = 1;

    /**
     * 1标识 新结算流程 自动提交结算
     */
    Integer STATEMENT_SUBMITTED_MANNER_VALUE = 0;

    /**
     * 1标识 自动验收
     */
    Integer AUTO_RECEIPT = 1;

    /**
     * 是否启用库房
     */
    String USE_WAREHOUSE_SYSTEM = "USE_WAREHOUSE_SYSTEM";

    /**
     * 启用库房的配置
     * 选择经费卡，0不选择/1必需选择
     */
    String MUST_HAVE_FUNDCARD = "MUST_HAVE_FUNDCARD";

    /**
     * 签署采购合同：0 - 无需签署合同，1 - 提醒签署合同
     */
    String REQUIRE_SIGN_PROCUREMENT_CONTRACT = "REQUIRE_SIGN_PROCUREMENT_CONTRACT";

    /**
     * 订单采购合同金额：string
     */
    String ORDER_CONTRACT_THRESHOLD = "ORDER_CONTRACT_THRESHOLD";

    /**
     * 采购合同模板：（字符串）
     */
    String PROCUREMENT_CONTRACT_TEMPLATE = "PROCUREMENT_CONTRACT_TEMPLATE";

    /**
     * 采购人中心订单查看权限code
     */
    String BUYER_CENTER_ORDER_VIEW = "BUYER_CENTER_ORDER_VIEW";

    /**
     * 订单查看
     */
    String ORDER_VIEW = "ORDER_VIEW";

    /**
     * 中大采购单公示天数
     */
    String PURCHASEAPPLY_FIRST_OFFLINE_PUBLICITY="PURCHASEAPPLY_FIRST_OFFLINE_PUBLICITY";

    /**
     * 采购人中心订单验收
     */
    String ORDER_ACCEPTANCE = "ORDER_ACCEPTANCE";

    /**
     * 快速部署（OMS医院配置），使用库房系统的配置值
     */
    String USE_WAREHOUSE_SYSTEM_VALUE = "1";

    /**
     * 线上单是否使用结算系统
     */
    String ONLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE = "1";

    /**
     * 线下单是否使用结算系统
     */
    String OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE = "1";

    /**
     * 快速部署（OMS医院配置），使用的库房系统的版本的配置code
     */
    String WAREHOUSE_SYSTEM_VERSION_CODE = "WAREHOUSE_SYSTEM_VERSION";

    /**
     * 快速部署（OMS医院配置），使用新库房系统的版本的配置值
     */
    String WAREHOUSE_SYSTEM_VERSION_VALUE = "1";

    /**
     * 验收打印
     */
    String ACCEPTANCE_PRINT = "ACCEPTANCE_PRINT";
    
    /**
     * 查看填写发票单据配置
     */
    String SAVE_INVOICE_INLET = "SAVE_INVOICE_INLET";

    /**
     * 是否对接经费卡
     */
    String RESEARCH_FUNDCARD_ISINTERFACE = "RESEARCH_FUNDCARD_ISINTERFACE";

    /**
     * 经费卡已对接配置值：1
     */
    String FUND_CARD_DOCKING_VAL = "1";

    /**
     * 经费卡层级
     */
    String RESEARCH_FUNDCARD_LEVEL = "RESEARCH_FUNDCARD_LEVEL";

    /**
     * 订单验收审批
     */
    String ORDER_APPROVE = "ORDER_APPROVE";

    /**
     * 订单二级验收审批
     */
    String ORDER_APPROVE_LEVEL_SECOND = "ORDER_APPROVE_LEVEL_SECOND";

    /**
     * 经费卡授权
     */
    String FUNDCARD_AUTH = "FUNDCARD_AUTH";

    /**
     * 财务提交
     */
    String FINANCIAL_SUBMISSION = "FINANCIAL_SUBMISSION";

    /**
     * 危化品采购等级
     */
    String DANGEROUS_PURCHASE_ORDER_LEVEL_3 = "DANGEROUS_PURCHASE_ORDER_LEVEL_3";

    /**
     * 线上单是否使用结算系统
     */
    String ONLINE_ORDER_USE_STATEMENT_SYSTEM = "ONLINE_ORDER_USE_STATEMENT_SYSTEM";

    /**
     * 线下单是否使用结算系统
     */
    String OFFLINE_ORDER_USE_STATEMENT_SYSTEM = "OFFLINE_ORDER_USE_STATEMENT_SYSTEM";

    /**
     * 确认备案
     */
    String ORDER_CONFIRM_FOR_THE_RECORD = "ORDER_CONFIRM_FOR_THE_RECORD";

    /**
     * 是否允许供应商可以拆单
     */
    String SUPPLIER_SPLIT_ORDER_PERMISSION = "SUPPLIER_SPLIT_ORDER_PERRMISSION";

    /**
     * 是否允许供应商可以拆单
     */
    String SUPPLIER_SPLIT_ORDER_PERMISSION_ENABLE = "1";

    /**
     * 批量释放经费
     */
    String BATCH_UNFREEZING_FUNDS = "BATCH_UNFREEZING_FUNDS";

    /**
     * 采购人中心待结算换卡权限
     */
    String BUYER_CENTER_STATEMENT_FUND_CARD_EDIT = "BUYER_CENTER_STATEMENT_FUND_CARD_EDIT";

    /**
     * 采购人中心结算中换卡权限
     */
    String BUYER_CENTER_PAYMENT_FUND_CARD_EDIT = "BUYER_CENTER_PAYMENT_FUND_CARD_EDIT";

    String BUYER_CENTER = "BUYER_CENTER";

    /**
     * 采购人中心撤销订单权限
     */
    String BUYER_CENTER_CANCEL_RECEIPT = "BUYER_CENTER_CANCEL_RECEIPT";

    /**
     * 限制只能整单退货
     */
    String ORDER_RETURN_ONLY_WHOLE = "ORDER_RETURN_ONLY_WHOLE";

    /**
     * 确认备案
     */
    String CONFIG_CODE_CONFIRM_FOR_THE_RECORD = "ORDER_CONFIRM_FOR_THE_RECORD";

    //  ---- 订单详情关联 验收图片配置
    /**
     * 订单详情关联验收图片配置
     */
    String ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION = "ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION";

    /**
     * 订单详情关联验收图片配置值：1 无需关联
     */
    String ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION_NONE = "1";

    /**
     * 订单详情关联验收图片配置值：2 部分关联
     */
    String ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION_PART = "2";

    /**
     * 订单详情关联验收图片配置值：3 必须全部关联
     */
    String ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION_ALL = "3";
    //  ---- 订单详情关联 验收图片配置 end

    // ---- 订单详情关联 验收附件配置
    /**
     * 订单详情关联验收图片配置
     */
    String ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION = "ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION";

    /**
     * 订单详情关联验收附件配置值：1 无需关联
     */
    String ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION_NONE = "1";

    /**
     * 订单详情关联验收附件配置值：2 部分关联
     */
    String ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION_PART = "2";

    /**
     * 订单详情关联验收附件配置值：3 必须全部关联
     */
    String ORDER_ACCEPT_ATTACHMENT_OPEN_PRODUCT_ASSOCIATION_ALL = "3";
    // ---- 订单详情关联 验收附件配置 end

    // 其他验收附件 配置
    /**
     * 验收附件配置-1 无需上传
     */
    String NOT_NEED_ACCEPT_ATTACHMENT_VALUE = "1";


    /**
     * 订单抽检权限
     */
    String ORDER_SAMPLING_INSPECTION_CODE = "ORDER_SAMPLING_INSPECTION";

    /**
     * 采购人中心发票附件查看权限
     */
    String BUYER_CENTER_INVOICE_ATTACHMENT_VIEW = "BUYER_CENTER_INVOICE_ATTACHMENT_VIEW";

    /**
     * HMS发票附件查询权限
     */
    String INVOICE_ATTACHMENT_VIEW = "INVOICE_ATTACHMENT_VIEW";

    /**
     * 管制类危化品订单验收
     */
    String REGULATORY_ORDER_ACCEPTANCE = "REGULATORY_ORDER_ACCEPTANCE";

    /**
     * 非管制类危化品订单验收
     */
    String NON_REGULATORY_ORDER_ACCEPTANCE = "NON_REGULATORY_ORDER_ACCEPTANCE";
}
