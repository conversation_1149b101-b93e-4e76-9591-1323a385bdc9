package com.ruijing.store.order.base.core.translator;

import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.goodsreturn.enums.GoodsReturnOperatorTypeEnum;
import com.ruijing.store.goodsreturn.vo.GoodsReturnLogVO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnLogDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.GoodsReturnLogDO;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;

/**
 * 退货操作日志转换类
 */
public class GoodsReturnLogTranslator {

    /**
     * DO 转 VO 对象
     * @param item
     * @return
     */
    public static GoodsReturnLogVO doToVO(GoodsReturnLogDO item) {
        GoodsReturnLogVO result = new GoodsReturnLogVO();
        result.setId(item.getId());
        result.setReturnId(item.getReturnId());
        result.setOperatorName(item.getOperatorName());
        result.setOperationType(item.getOperationType());
        result.setCreateTime(item.getCreateTime().getTime());
        result.setRemark(item.getRemark());
        String imagesURL = item.getImagesURL();
        if (StringUtils.isNotBlank(imagesURL)) {
            String[] split = imagesURL.split(";");
            result.setImagesURL(Arrays.asList(split));
        } else {
            result.setImagesURL(Collections.emptyList());
        }
        return result;
    }

    /**
     * 保存订单退货操作日志
     *
     * @param operatorId 当前登录用户信息id
     * @param operatorName 当前登录用户信息名
     * @param returnId   退货记录
     * @param operatorType  操作人类型
     * @param operationType 操作内容类型
     * @param remark        备注信息
     * @param imagesURL     凭证信息
     */
    public static GoodsReturnLogDO buildDO(Integer operatorId,
                                           String operatorName,
                                           Integer returnId,
                                           GoodsReturnOperatorTypeEnum operatorType,
                                           GoodsReturnOperationTypeEnum operationType,
                                           String remark,
                                           String imagesURL) {
        GoodsReturnLogDO result = new GoodsReturnLogDO();
        result.setReturnId(returnId);
        result.setOperatorId(operatorId);
        result.setOperatorName(operatorName);
        result.setOperatorType(operatorType.getCode());
        result.setOperationType(operationType.getCode());
        result.setRemark(remark);
        result.setImagesURL(imagesURL);
        return result;
    }

    /**
     * DO 转 DTO 对象
     * @param item
     * @return
     */
    public static GoodsReturnLogDTO doToDTO(GoodsReturnLogDO item) {
        GoodsReturnLogDTO result = new GoodsReturnLogDTO();
        result.setId(item.getId());
        result.setReturnId(item.getReturnId());
        result.setOperatorName(item.getOperatorName());
        result.setOperationType(item.getOperationType());
        result.setRemark(item.getRemark());
        String imagesURL = item.getImagesURL();
        if (StringUtils.isNotBlank(imagesURL)) {
            String[] split = imagesURL.split(";");
            result.setImagesURL(Arrays.asList(split));
        } else {
            result.setImagesURL(Collections.emptyList());
        }
        result.setCreateTime(item.getCreateTime());

        return result;
    }
}
