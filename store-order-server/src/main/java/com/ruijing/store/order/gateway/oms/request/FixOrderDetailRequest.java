package com.ruijing.store.order.gateway.oms.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: chenzhanliang
 * @createTime: 2024-11-28 15:59
 * @description:
 **/
public class FixOrderDetailRequest implements Serializable {

    private static final long serialVersionUID = -8493520089244483866L;


    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("订单详情id")
    private Integer orderDetailId;

    @RpcModelProperty("一级分类id")
    private Integer firstCategoryId;

    @RpcModelProperty("一级分类名称")
    private String firstCategoryName;

    @RpcModelProperty("二级分类id")
    private Integer secondCategoryId;

    @RpcModelProperty("二级分类名称")
    private String secondCategoryName;

    @RpcModelProperty("三级分类id")
    private Integer thirdCategoryId;

    @RpcModelProperty("三级分类名称")
    private String thirdCategoryName;

    public Integer getOrderId() {
        return orderId;
    }

    public FixOrderDetailRequest setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public FixOrderDetailRequest setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
        return this;
    }

    public Integer getSecondCategoryId() {
        return secondCategoryId;
    }

    public FixOrderDetailRequest setSecondCategoryId(Integer secondCategoryId) {
        this.secondCategoryId = secondCategoryId;
        return this;
    }

    public String getSecondCategoryName() {
        return secondCategoryName;
    }

    public FixOrderDetailRequest setSecondCategoryName(String secondCategoryName) {
        this.secondCategoryName = secondCategoryName;
        return this;
    }

    public Integer getThirdCategoryId() {
        return thirdCategoryId;
    }

    public FixOrderDetailRequest setThirdCategoryId(Integer thirdCategoryId) {
        this.thirdCategoryId = thirdCategoryId;
        return this;
    }

    public String getThirdCategoryName() {
        return thirdCategoryName;
    }

    public FixOrderDetailRequest setThirdCategoryName(String thirdCategoryName) {
        this.thirdCategoryName = thirdCategoryName;
        return this;
    }

    public Integer getFirstCategoryId() {
        return firstCategoryId;
    }

    public FixOrderDetailRequest setFirstCategoryId(Integer firstCategoryId) {
        this.firstCategoryId = firstCategoryId;
        return this;
    }

    public String getFirstCategoryName() {
        return firstCategoryName;
    }

    public FixOrderDetailRequest setFirstCategoryName(String firstCategoryName) {
        this.firstCategoryName = firstCategoryName;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", FixOrderDetailRequest.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderDetailId=" + orderDetailId)
                .add("firstCategoryId=" + firstCategoryId)
                .add("firstCategoryName='" + firstCategoryName + "'")
                .add("secondCategoryId=" + secondCategoryId)
                .add("secondCategoryName='" + secondCategoryName + "'")
                .add("thirdCategoryId=" + thirdCategoryId)
                .add("thirdCategoryName='" + thirdCategoryName + "'")
                .toString();
    }
}
