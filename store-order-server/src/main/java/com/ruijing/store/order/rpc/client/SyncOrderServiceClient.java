package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.invoker.manager.ReferenceProxyManager;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.sync.common.api.SyncDataCommonRPCService;
import com.ruijing.sync.common.dto.SyncDataRequestDTO;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/11/12 14:09
 **/
@ServiceClient
public class SyncOrderServiceClient {

    @MSharpReference(remoteAppkey = "store-search-sync-order-service")
    private SyncDataCommonRPCService syncDataCommonRPCService;

    /**
     * 订单手动同步RPC接口
     * @param request
     * @param appKey
     */
    public void sync(SyncDataRequestDTO request, String appKey) {
        RpcContext.getCallContext().setTimeout(30000);
        SyncDataCommonRPCService referenceClient = ReferenceProxyManager.getReferenceClient(appKey, SyncDataCommonRPCService.class);
        RemoteResponse<Boolean> response = referenceClient.syncData(request);
        Preconditions.isTrue(response.isSuccess(), "同步订单数据失败！" + JsonUtils.toJsonIgnoreNull(response));
    }

    /**
     * 调用订单检查，mysql与es的数据差异
     * @param requestDTO
     * @return
     */
    public Boolean checkDBAndESWriteToExcel(SyncDataRequestDTO requestDTO) {
        RpcContext.getCallContext().setTimeout(30000);
        RemoteResponse<Boolean> response = syncDataCommonRPCService.checkDBAndESWriteToExcel(requestDTO);
        Preconditions.isTrue(response.isSuccess(), "检测mysql与es数据差异服务出现异常");
        return response.isSuccess();
    }
}
