package com.ruijing.store.order.gateway.buyercenter.controller;

import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.order.base.order.dto.OrderMaterialCodeDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.order.base.order.enums.ExportFileTypeEnum;
import com.reagent.research.statement.api.enums.StatementAccessCodeEnum;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethodParam;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.annotation.Method;
import com.ruijing.fundamental.api.annotation.MethodParam;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.mhttp.annotation.Mapping;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.delivery.service.DeliveryProxyService;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.DeliveryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderdetail.dto.DetailBatchesDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.DetailBatchesRequestDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailBatchesRespondDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.service.OrderOperateLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoQueryDTO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO;
import com.ruijing.store.order.base.excel.dto.OrderExportRequestDTO;
import com.ruijing.store.order.base.minor.service.OrderRemarkService;
import com.ruijing.store.order.business.service.*;
import com.ruijing.store.order.business.service.file.OrderUploadFileService;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.*;
import com.ruijing.store.order.gateway.buyercenter.request.contract.UploadContractRequest;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnBriefInfoRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.*;
import com.ruijing.store.order.gateway.buyercenter.vo.*;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnBriefInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.*;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.*;
import com.ruijing.store.order.other.service.OrderDeliveryRelatedService;
import com.ruijing.store.order.other.service.OrderInvoiceService;
import com.ruijing.store.order.rpc.client.OrderAcceptCommentClient;
import com.ruijing.store.order.rpc.client.OrderUniqueBarCodeRPCClient;
import com.ruijing.store.order.rpc.client.UserClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/11/16 18:06
 * @Description
 **/
@MSharpService(isGateway = "true")
@RpcMapping("/buyerOrderManage")
@RpcApi(value = "订单-采购人中心")
public class BuyerOrderGWController {

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private OrderDetailRelatedService orderDetailRelatedService;

    @Resource
    private OrderExportService orderExportService;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderAnnotationService orderAnnotationService;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;
    
    @Resource
    private DeliveryProxyService deliveryProxyService;

    @Resource
    private OrderInvoiceService orderInvoiceService;

    @Resource
    private OrderRemarkService orderRemarkService;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private OrderPicRelatedService orderPicRelatedService;

    @Resource
    private OrderApprovalService orderApprovalService;

    @Resource
    private OrderUploadFileService orderUploadFileService;

    @Resource
    private OrderContractService orderContractService;

    @Resource
    private OrderOperateLogService orderOperateLogService;

    @Resource
    private OrderDeliveryRelatedService orderDeliveryRelatedService;


    @RpcMethod("新增订单物资编码")
    @RpcMapping("/materialCode/insert")
    public RemoteResponse<Integer> insertMaterialCode(@RpcMethodParam(hidden = true) RjSessionInfo rjSessionInfo, OrderMaterialCodeParamDTO paramDTO){
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.NO_USER_INFO);
        BusinessErrUtil.notNull(orgId, ExecptionMessageEnum.NO_INSTITUTION_INFO);
        if (CollectionUtils.isEmpty(paramDTO.getOrderMaterialCodeDTOList())){
            return RemoteResponse.<Integer>custom().setSuccess().setData(0).build();
        }
        Integer result = orderManageService.insertMaterialCode(paramDTO.getOrderMaterialCodeDTOList());
        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

    @RpcMethod("更新订单物资编码")
    @RpcMapping("/materialCode/update")
    public RemoteResponse<Integer> updateOrderMaterialCode(@RpcMethodParam(hidden = true) RjSessionInfo rjSessionInfo, OrderMaterialCodeDTO orderMaterialCodeDTO){
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.NO_USER_INFO);
        BusinessErrUtil.notNull(orgId, ExecptionMessageEnum.NO_INSTITUTION_INFO);
        Integer result = orderManageService.updateOrderMaterialCode(orderMaterialCodeDTO);
        return RemoteResponse.<Integer>custom().setSuccess().setData(result).build();
    }

    @RpcMethod("条件批量查询最新一个物资编码")
    @RpcMapping("/materialCode/latestCode")
    public RemoteResponse<List<OrderMaterialCodeDTO>> latestMaterialCode(@RpcMethodParam(hidden = true)RjSessionInfo rjSessionInfo, OrderMaterialCodeRequestDTO requestDTO){
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.NO_USER_INFO);
        BusinessErrUtil.notNull(orgId, ExecptionMessageEnum.NO_INSTITUTION_INFO);
        List<OrderMaterialCodeDTO> result = orderManageService.latestMaterialCode(requestDTO);
        return RemoteResponse.<List<OrderMaterialCodeDTO>>custom().setSuccess().setData(result).build();
    }



    /**
     * store迁移-根据订单id检测商品类型，oms配置除服务类强制拍照验收时，采购人确认验收时调用
     *
     * @param param
     */
    @RpcMapping("/isServiceType")
    @RpcMethod(value = "订单商品是否属于服务类")
    public RemoteResponse<Boolean> isServiceType(@RequestBody OrderBasicParamDTO param) {
        RemoteResponse<Boolean> response = orderManageService.isServiceType(param);
        return response;
    }


    /**
     * 工作台状态订单数量统计
     * @return
     */
    @RpcMethod("工作台-->订单状态数量统计")
    @RpcMapping("/workbench/getOrderCountByStatus")
    public RemoteResponse<OrderStatusCountVO> getOrderCountByStatus(RjSessionInfo rjSessionInfo){
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.NO_USER_INFO);
        BusinessErrUtil.notNull(orgId, ExecptionMessageEnum.NO_INSTITUTION_INFO);
        OrderStatusCountVO orderCountByStatus = orderManageService.getOrderCountByStatus(userId.intValue(), rjSessionInfo.getOrgId());
        return RemoteResponse.<OrderStatusCountVO>custom().setSuccess().setData(orderCountByStatus).build();
    }

    @RpcMethod("工作台-->订单金额统计")
    @RpcMapping("/workbench/getMyOrderAmount")
    public RemoteResponse<WorkbenchOrderAmountVO> getMyOrderAmount(RjSessionInfo rjSessionInfo){
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.NO_USER_INFO);
        BusinessErrUtil.notNull(orgId, ExecptionMessageEnum.NO_INSTITUTION_INFO);
        WorkbenchOrderAmountVO result = orderManageService.getMyOrderAmount(userId.intValue(), rjSessionInfo.getOrgId());
        return RemoteResponse.<WorkbenchOrderAmountVO>custom().setSuccess().setData(result).build();
    }

    @RpcMethod("统计管理-->我的订单金额统计")
    @RpcMapping("/statistics/getMyOrderTransaction")
    public PageableResponse<List<OrderTransactionCountVO>> getMyOrderTransaction(RjSessionInfo rjSessionInfo,TransactionCountRequest transactionCountRequest){
        LoginUserInfoBO user = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true,"BUYER_CENTER_STATISTICS_VIEW");
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(user.getDeptIdList()), ExecptionMessageEnum.NO_STATISTICAL_MANAGEMENT_PERMISSION);
        Integer pageNo = transactionCountRequest.getPageNo();
        Integer pageSize = transactionCountRequest.getPageSize();
        Integer departmentId = transactionCountRequest.getDepartmentId();
        List<Integer> departmentIds = null;
        if (departmentId != null){
            departmentIds = New.list(departmentId);
        }
        List<OrderTransactionCountVO> transactionList = orderManageService.getOrderTransaction(departmentIds,user.getUserId()
                ,user.getOrgId(), transactionCountRequest);
        return this.pageOrderTransactionList(pageNo, pageSize, transactionList);
    }

    @RpcMethod("统计管理-->课题组订单金额统计")
    @RpcMapping("/statistics/getDepartmentOrderTransaction")
    public PageableResponse<List<OrderTransactionCountVO>> getDepartmentOrderTransaction(RjSessionInfo rjSessionInfo, TransactionCountRequest transactionCountRequest){
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true,"BUYER_CENTER_STATISTICS_VIEW");
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(loginUserInfo.getDeptIdList()), ExecptionMessageEnum.NO_STATISTICAL_MANAGEMENT_PERMISSION);
        Integer pageNo = transactionCountRequest.getPageNo();
        Integer pageSize = transactionCountRequest.getPageSize();
        Integer departmentId = transactionCountRequest.getDepartmentId();
        List<Integer> departmentIds;
        if (departmentId == null){
            departmentIds =loginUserInfo.getDeptIdList();
        }else {
            departmentIds = New.list(departmentId);
        }
        List<OrderTransactionCountVO> transactionList = orderManageService.getOrderTransaction(departmentIds,null
                ,loginUserInfo.getOrgId(), transactionCountRequest);
        //分页
        return this.pageOrderTransactionList(pageNo, pageSize, transactionList);
    }

    /**
     * 统计交易 手动分页
     * @param pageNo        当前页
     * @param pageSize      页面大小
     * @param transactionList   结果集
     * @return pageAble
     *
     */
    private PageableResponse<List<OrderTransactionCountVO>> pageOrderTransactionList(Integer pageNo, Integer pageSize, List<OrderTransactionCountVO> transactionList) {
        if (CollectionUtils.isEmpty(transactionList)) {
            return PageableResponse.<List<OrderTransactionCountVO>>custom().setSuccess().setData(New.list())
                    .setPageNo(pageNo).setPageSize(pageSize).setTotal(0);
        }
        List<List<OrderTransactionCountVO>> partition = New.partition(transactionList, pageSize);
        Integer page = partition.size() < pageNo ? partition.size() - 1 : pageNo - 1;
        List<OrderTransactionCountVO> transactionCountVOList = partition.get(page);
        return PageableResponse.<List<OrderTransactionCountVO>>custom().setSuccess().setData(transactionCountVOList)
                .setPageNo(pageNo).setPageSize(pageSize).setTotal(transactionList.size());
    }

    @RpcMethod("统计管理-->导出我的订单金额统计")
    @RpcMapping("/statistics/exportMyOrderTransaction")
    public RemoteResponse<String> exportMyOrderTransaction(RjSessionInfo rjSessionInfo, TransactionCountRequest transactionCountRequest) throws IOException {
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true,"BUYER_CENTER_STATISTICS_VIEW");
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(loginUserInfo.getDeptIdList()), ExecptionMessageEnum.NO_STATISTICAL_MANAGEMENT_PERMISSION);
        RpcContext.getProviderContext().setCallAttachments(New.map("user",loginUserInfo));
        Integer departmentId = transactionCountRequest.getDepartmentId();
        List<Integer> departmentIds;
        if (departmentId == null){
            departmentIds =loginUserInfo.getDeptIdList();
        }else {
            departmentIds = New.list(departmentId);
        }
        String url = orderManageService.exportOrderTransaction(departmentIds, loginUserInfo.getUserId().intValue(),
                loginUserInfo.getOrgId(), transactionCountRequest);
        return RemoteResponse.<String>custom().setSuccess().setData(url);
    }

    @RpcMethod("统计管理-->导出课题组订单金额统计")
    @RpcMapping("/statistics/exportDepartmentOrderTransaction")
    public RemoteResponse<String> exportDepartmentOrderTransaction(RjSessionInfo rjSessionInfo, TransactionCountRequest transactionCountRequest) throws IOException {
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true,"BUYER_CENTER_STATISTICS_VIEW");
        BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(loginUserInfo.getDeptIdList()), ExecptionMessageEnum.NO_STATISTICAL_MANAGEMENT_PERMISSION);
        RpcContext.getProviderContext().setCallAttachments(New.map("user",loginUserInfo));
        Integer departmentId = transactionCountRequest.getDepartmentId();
        List<Integer> departmentIds = null;
        if (departmentId != null){
            departmentIds = New.list(departmentId);
        }
        String url = orderManageService.exportOrderTransaction(departmentIds, null,
                loginUserInfo.getOrgId(), transactionCountRequest);
        return RemoteResponse.<String>custom().setSuccess().setData(url);
    }

    @RpcMethod("评价-->订单信息")
    @RpcMapping("/comment/getOrderInfoForComment")
    public RemoteResponse<OrderForCommentVO> getOrderInfoForComment(OrderBasicParamDTO paramDTO, RjSessionInfo rjSessionInfo){
        String orderNo = paramDTO.getOrderNo();
        BusinessErrUtil.notNull(orderNo,"订单号不能为空~！");
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.UNABLE_TO_OBTAIN_LOGIN_INFO);
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        OrderForCommentVO orderForComment = orderManageService.getOrderForComment(orderNo, userId.intValue());
        return RemoteResponse.<OrderForCommentVO>custom().setSuccess().setData(orderForComment);
    }

    @RpcMethod("订单管理-我的订单")
    @RpcMapping("/getMyOrderListForWWW")
    public PageableResponse<OrderListRespVO> getMyOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        BusinessErrUtil.notNull(request,"请求我的订单入参不可为空");
        RpcContext.getProviderContext().getLocale();
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        // 订单列表的特异性设置，在课题组订单中没有下述语句
        request.setFbuyerId(loginInfo.getUserId());
        request.setMyOrderCheck(true);
        request.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        loginInfo.setDeptList(New.list());
        return buyerOrderService.getOrderListForWWW(request, loginInfo,false);
    }

    @RpcMethod("订单管理-我的待审订单")
    @RpcMapping("/getMyPendingOrderList")
    public PageableResponse<OrderListRespVO> getMyPendingOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        BusinessErrUtil.notNull(request,"请求我的订单入参不可为空");
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        return orderApprovalService.getMyPendingOrderList(request, loginInfo, false);
    }

    @RpcMethod("订单管理-我的已审批订单")
    @RpcMapping("/myApprovedList")
    public PageableResponse<OrderListRespVO> myApprovedList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        BusinessErrUtil.notNull(request,"请求我的订单入参不可为空");
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), orgId, true, ConfigConstant.ORDER_VIEW);
        return orderApprovalService.myApprovedList(request, loginInfo, false);
    }

    @RpcMethod("订单管理-我的订单-获取部门基础信息列表")
    @RpcMapping("/getDeptBriefInfoList")
    public RemoteResponse<List<DeptBriefVO>> getDeptBriefInfoList(RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        List<DeptBriefVO> deptBriefList = buyerOrderService.constructDeptsBriefInfo(loginInfo.getDeptList(), loginInfo.getRootDepartmentId());
        return RemoteResponse.<List<DeptBriefVO>>custom().setData(deptBriefList).setSuccess();
    }

    @RpcMethod("订单管理-课题组订单")
    @RpcMapping("/orderListResearchGroup")
    public PageableResponse<OrderListRespVO> getResearchGroupOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request) throws ParseException {
        BusinessErrUtil.notNull(request,"请求课题组订单入参不可为空");
        BusinessErrUtil.isTrue(request.getPageNo() > 0 && request.getPageSize() > 0, "分页参数有误");
        request.setMyOrderCheck(false);
        request.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        if(userClient.getHaveAccessViewStat(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue())){
            request.setShowTestOrderCount(true);
        }
        return buyerOrderService.getOrderListForWWW(request, loginInfo,false);
    }

    @RpcMethod("订单——返回课题组验收审批流下有审批权限的人名列表")
    @RpcMapping("/orderApproveFlowUserList")
    public RemoteResponse<OrderApprovalManInfoVO> getOrderApproveFlowUserList(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        BusinessErrUtil.notNull(request, "返回课题组有审批权限的人名列表请求不可为空");
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        request.setOrgId(orgId);
        OrderApprovalManInfoVO approveManListVO = orderApprovalService.getApproveFlowUserList(request);
        return RemoteResponse.<OrderApprovalManInfoVO>custom().setData(approveManListVO).setSuccess();
    }

    @RpcMethod("订单详情-图片上传权限")
    @RpcMapping("/orderDetailPicConfig")
    @Deprecated
    public RemoteResponse<BaseConfigDTO> getPicConfig(RjSessionInfo rjSessionInfo) {
        BaseConfigDTO picConfig = orderDetailRelatedService.getPicConfig(rjSessionInfo);
        return RemoteResponse.<BaseConfigDTO>custom().setData(picConfig).setSuccess();
    }

    @RpcMethod("订单详情-获取发票信息（新）")
    @RpcMapping("/invoiceDetailsForOrders")
    public RemoteResponse<OrderInvoiceInfoRespVO> invoiceDetailsForOrders(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        BusinessErrUtil.isTrue(rjSessionInfo != null && rjSessionInfo.getGuid() != null, ExecptionMessageEnum.INVOICE_USER_NOT_LOGGED_IN);
        BusinessErrUtil.notNull(request, "获取发票信息接口入参为空");
        String orderNo = request.getOrderNo();
        Integer orgId = rjSessionInfo.getOrgId();
        Long userId = rjSessionInfo.getUserId();
        Integer orderId = request.getOrderId();
        OrderInvoiceInfoRespVO orderInvoiceInfoList = orderDetailRelatedService.retrieveInvoicesForOrder(orderId, orderNo, orgId, userId, StatementAccessCodeEnum.BUYER_INVOICE_EDIT.getAccessCode());
        // 检查发票附件查看权限
        Boolean canViewInvoiceAttachment = userClient.findUserHasAccess(orgId, userId.intValue(), null, ConfigConstant.BUYER_CENTER_INVOICE_ATTACHMENT_VIEW);
        if (BooleanUtils.isNotTrue(canViewInvoiceAttachment) && Objects.nonNull(orderInvoiceInfoList)) {
            orderInvoiceInfoList.setCanModifyInvoice(false);
            List<OrderInvoiceInfoVO> orderInvoiceInfoVOList = orderInvoiceInfoList.getOrderInvoiceInfoVOList();
            if (CollectionUtils.isNotEmpty(orderInvoiceInfoVOList)) {
                orderInvoiceInfoVOList.forEach(orderInvoiceInfoVO -> {
                    orderInvoiceInfoVO.setInvoiceFileList(New.emptyList());
                    orderInvoiceInfoVO.setPicturePathList(New.emptyList());
                });
            }
        }
        return RemoteResponse.<OrderInvoiceInfoRespVO>custom().setData(orderInvoiceInfoList).setSuccess();
    }

    //todo 后续删除
    @RpcMethod(value = "订单详情-获取发票信息", notes = "广州医科大学附属第一医院-线下单-打印出入库单，仍在调用，待需求迭代后移除")
    @RpcMapping("/orderDetailInvoiceList")
    @Deprecated
    public RemoteResponse<List<OrderInvoiceInfoVO>> listInvoiceInfoByOrder(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        BusinessErrUtil.isTrue(rjSessionInfo != null && rjSessionInfo.getGuid() != null, ExecptionMessageEnum.INVOICE_USER_NOT_LOGGED_IN);
        BusinessErrUtil.notNull(request,"获取发票信息接口入参为空");
        String orderNo = request.getOrderNo();
        Integer orgId = rjSessionInfo.getOrgId();
        Integer orderId = request.getOrderId();
        List<OrderInvoiceInfoVO> orderInvoiceInfoList = orderDetailRelatedService.listInvoiceByOrder(orderId, orderNo, orgId);
        return RemoteResponse.<List<OrderInvoiceInfoVO>>custom().setData(orderInvoiceInfoList).setSuccess();
    }

    @RpcMethod("订单-获取订单合同模板")
    @RpcMapping("/getContractTemplate")
    @Deprecated
    public RemoteResponse<List<OrderContractTemplateVO>> getContractTemplate(RjSessionInfo rjSessionInfo) {
        List<OrderContractTemplateVO> contractTemplateList = orderContractService.getOrderTemplateList(rjSessionInfo);
        String returnMsg = "";
        if (CollectionUtils.isEmpty(contractTemplateList)) {
            returnMsg = "合同模板配置为空";
        }
        return RemoteResponse.<List<OrderContractTemplateVO>>custom().setData(contractTemplateList).setSuccess().setMsg(returnMsg);
    }

    @RpcMethod("订单详情-详情信息")
    @RpcMapping("/orderDetail")
    public RemoteResponse<OrderInfoVO> getOrderDetail(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) throws ParseException {
        BusinessErrUtil.notNull(request,"获取订单详情接口入参为空");
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        OrderInfoVO orderInfo = orderDetailRelatedService.getOrderDetail(loginInfo, request, false);
        return RemoteResponse.<OrderInfoVO>custom().setData(orderInfo).setSuccess();
    }

    @Method("获取订单物流信息")
    @Mapping("/getOrderLogisticsInfo")
    public RemoteResponse<OrderLogisticsInfoVO> getOrderLogisticsInfo(RjSessionInfo rjSessionInfo, @MethodParam("orderId") OrderBasicParamDTO orderBasicParamDTO){
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        BusinessErrUtil.notEmpty(loginInfo.getDeptIdList(), "该用户没有权限获取物流信息");
        return RemoteResponse.success(orderDeliveryRelatedService.getOrderLogisticsInfo(orderBasicParamDTO.getOrderId()));
    }

    @RpcMethod("订单管理-取消订单（线上）")
    @RpcMapping("/cancelOrder")
    @ServiceLog(description = "网关-取消订单", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> cancelOrder(RjSessionInfo rjSessionInfo, ApplyCancelOrderReqDTO cancelOrderReq) {
        BusinessErrUtil.notNull(cancelOrderReq,"取消订单接口入参为空");
        Boolean cancelSuccess = buyerOrderService.cancelOrder(rjSessionInfo, cancelOrderReq);
        if (Boolean.TRUE.equals(cancelSuccess)) {
            return RemoteResponse.<Boolean>custom().setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("取消线上订单出错，请稍后重试或联系客服");
        }
    }

    @RpcMethod("订单管理-取消订单（线下）")
    @RpcMapping("/cancelOrderOffline")
    public RemoteResponse<Boolean> cancelOrderOffline(RjSessionInfo rjSessionInfo, ApplyCancelOrderReqDTO applyCancelOrderReq) {
        // 为了让前端传入的字段保持一致，后端做转换
        CancelOrderReqDTO cancelOrderReq = new CancelOrderReqDTO();
        cancelOrderReq.setCancelManId(rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).toString());
        cancelOrderReq.setOrderMasterId(applyCancelOrderReq.getOrderId());
        cancelOrderReq.setCancelReason(applyCancelOrderReq.getFcancelreason());
        cancelOrderManageService.cancelOfflineOrder(cancelOrderReq);
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    @RpcMethod("订单管理-确认验收")
    @RpcMapping("/acceptOrder")
    @ServiceLog(description = "网关-验收订单", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> acceptOrder(RjSessionInfo rjSessionInfo, OrderReceiptParamDTO acceptOrderReq) {
        // TODO: 同样 中大、华农的改动订单先不做，待定
        ReceiptOrderResponseDO receiptOrderResponseDO = buyerOrderService.acceptOrder(rjSessionInfo, acceptOrderReq);
        
        WarehouseResultDTO warehouseResultDTO = receiptOrderResponseDO.getWarehouseResultDTO();
        if(warehouseResultDTO != null){
            // 如果入库失败，抛出入库失败的信息
            BusinessErrUtil.isTrue(warehouseResultDTO.getWarehouseSuccess(), warehouseResultDTO.getWarehouseErrorMsg());
        }
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    @RpcMethod("订单管理-用户拒绝供应商取消订单")
    @RpcMapping("/refuseCancelOrder")
    public RemoteResponse<Boolean> refuseCancelOrder(RjSessionInfo rjSessionInfo, CancelOrderReqDTO cancelOrderReq) {
        Boolean refuseSuccess = buyerOrderService.refuseCancelOrder(rjSessionInfo, cancelOrderReq);
        if (Boolean.TRUE.equals(refuseSuccess)) {
            return RemoteResponse.<Boolean>custom().setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("拒绝供应商取消订单出错，请稍后重试或联系客服");
        }
    }

    @RpcMethod("订单管理-用户同意供应商取消订单")
    @RpcMapping("/agreeCancelOrder")
    public RemoteResponse<Boolean> agreeCancelOrder(RjSessionInfo rjSessionInfo, CancelOrderReqDTO cancelOrderReqDTO) {
        Boolean agreeSuccess = buyerOrderService.agreeCancelOrder(rjSessionInfo, cancelOrderReqDTO);
        if (Boolean.TRUE.equals(agreeSuccess)) {
            return RemoteResponse.<Boolean>custom().setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("同意供应商取消订单出错，请稍后重试或联系客服");
        }
    }

    @RpcMethod("订单详情-订单操作日志记录日志(麻烦传递orderid)")
    @RpcMapping("/orderOpeartionLog")
    @ServiceLog(description = "订单详情-订单操作日志记录日志", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.READ)
    public RemoteResponse<List<OrderOperationLogVO>> getOrderOperationLog(OrderBasicParamDTO orderBasicParam) {
        List<OrderOperationLogVO> operationLogList = orderOperateLogService.orderOperationLog(orderBasicParam.getOrderId(), orderBasicParam.getOrderNo());
        return RemoteResponse.<List<OrderOperationLogVO>>custom().setData(operationLogList).setSuccess();
    }

    @Method(value = "订单详情-查询订单所有关联业务日志", author = "黄有望")
    @Mapping("/orderAllRelateLog")
    public RemoteResponse<List<PurchaseOrderAllRelateLogVO>> getOrderAllRelateLog(@MethodParam(includePropertyNames = { "orderId" }) OrderBasicParamDTO orderBasicParam) {
        List<PurchaseOrderAllRelateLogVO> operationLogList = orderOperateLogService.getOrderAllRelateLog(orderBasicParam.getOrderId());
        return RemoteResponse.<List<PurchaseOrderAllRelateLogVO>>custom().setData(operationLogList).setSuccess();
    }

    @RpcMethod("订单详情-代配送操作日志记录日志(麻烦传递orderid)")
    @RpcMapping("/getDeliveryProxyOprLog")
    public RemoteResponse<List<OrderOperationLogVO>> getDeliveryProxyOprLog(OrderBasicParamDTO orderBasicParam){
        List<OrderOperationLogVO> operationLogList = orderOperateLogService.getDeliveryProxyOprLog(orderBasicParam.getOrderId());
        return RemoteResponse.<List<OrderOperationLogVO>>custom().setData(operationLogList).setSuccess();
    }

    @RpcMethod("订单导出-我的订单-列出 订单或商品导出结果列表")
    @RpcMapping("/syncDownload/exportedList")
    public RemoteResponse<BasePageResultDTO<OrderExcelInfoResponseDTO>> findOrderExcelList(RjSessionInfo rjSessionInfo, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO) {
        OrderExportQueryDTO orderExportQueryDTO = new OrderExportQueryDTO();
        orderExportQueryDTO.setFileTypeList(New.list(
                ExportFileTypeEnum.BUYER_ORDER_DETAIL.getValue(),
                ExportFileTypeEnum.BUYER_PRODUCT_DETAIL.getValue(),
                ExportFileTypeEnum.DIY_BUYER_ORDER.getValue(),
                ExportFileTypeEnum.BUYER_ORDER_LOG.getValue()
        ));
        BasePageResultDTO<OrderExcelInfoResponseDTO> basePageResultDTO = orderExportService.findOrderExcelList(orderExportQueryDTO, orderExcelInfoQueryDTO, rjSessionInfo);
        if (basePageResultDTO.getTotal() > basePageResultDTO.getPageNo() * basePageResultDTO.getPageSize()) {
            return RemoteResponse.success(basePageResultDTO).setHasNext(true);
        }
        return RemoteResponse.success(basePageResultDTO);
    }

    @RpcMethod("订单导出-课题组订单-列出 订单或商品导出结果列表")
    @RpcMapping("/syncDownload/exportedGroupList")
    public RemoteResponse<BasePageResultDTO<OrderExcelInfoResponseDTO>> findGroupOrderExcelList(RjSessionInfo rjSessionInfo, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO) {
        OrderExportQueryDTO orderExportQueryDTO = new OrderExportQueryDTO();
        orderExportQueryDTO.setFileTypeList(New.list(
                ExportFileTypeEnum.BUYER_GROUP_ORDER_DETAIL.getValue(),
                ExportFileTypeEnum.BUYER_GROUP_PRODUCT_DETAIL.getValue(),
                ExportFileTypeEnum.DIY_BUYER_GROUP_ORDER.getValue(),
                ExportFileTypeEnum.BUYER_ORDER_LOG_GROUP.getValue())
        );
        BasePageResultDTO<OrderExcelInfoResponseDTO> basePageResultDTO = orderExportService.findOrderExcelList(orderExportQueryDTO, orderExcelInfoQueryDTO, rjSessionInfo);
        if (basePageResultDTO.getTotal() > (long) basePageResultDTO.getPageNo() * basePageResultDTO.getPageSize()) {
            return RemoteResponse.success(basePageResultDTO).setHasNext(true);
        }
        return RemoteResponse.success(basePageResultDTO);
    }

    @RpcMapping("/syncDownload/deleteExportedItem")
    @RpcMethod("订单导出-删除导出的订单excel记录 ")
    public RemoteResponse<Boolean> deleteOrderExcel(RjSessionInfo rjSessionInfo, OrderExportRequestDTO orderExportRequestDTO) {
        orderExportService.deleteExportedFile(rjSessionInfo, orderExportRequestDTO, false);
        return RemoteResponse.success();
    }

    @RpcMapping("/snapshot/getProductSnapshot")
    @RpcMethod("订单快照（只需要订单id即可）")
    public RemoteResponse<OrderSnapshotVO> getProductSnapshot(OrderBasicParamDTO request) {
        OrderSnapshotVO orderSnapshotVO = orderDetailRelatedService.getOrderSnapshot(request.getOrderDetailId());
        return RemoteResponse.<OrderSnapshotVO>custom().setData(orderSnapshotVO).setSuccess();
    }

    @RpcMapping("/contract/info")
    @RpcMethod("订单管理-合同信息")
    public RemoteResponse<List<OrderInfoVO>> getContractInfo(OrderBasicParamDTO request) {
        // TODO: 参考OrderContractInfoVO的返回值，直接走搜索联表查询。前端可缓存但维护难度大且冗杂
        List<OrderInfoVO> orderInfoList = orderContractService.getContractInfo(request);
        return RemoteResponse.<List<OrderInfoVO>>custom().setData(orderInfoList).setSuccess();
    }

    @RpcMapping("/contract/save")
    @RpcMethod("订单管理-上传合同")
    public RemoteResponse<Boolean> saveContractInfo(RjSessionInfo rjSessionInfo, UploadContractRequest request) {
        Boolean uploadSuccess = orderContractService.uploadContractList(rjSessionInfo, request);
        if (Boolean.TRUE.equals(uploadSuccess)) {
            return RemoteResponse.<Boolean>custom().setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setFailure("采购人中心上传合同出错，请稍后重试或联系客服");
        }
    }

    @RpcMapping("/orderCount")
    @RpcMethod("订单管理-订单计数")
    public RemoteResponse<OrderListCountVO> countOrderByStatus(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        OrderListCountVO orderCount = buyerOrderService.countOrderList(rjSessionInfo, request);
        return RemoteResponse.<OrderListCountVO>custom().setData(orderCount).setSuccess();
    }
    
    @RpcMethod("查找中爆存储仓库的接口")
    @RpcMapping("/getStorageHouse")
    public RemoteResponse<List<OrderStoreHouseVO>> getCooperationStorage(OrderBasicParamDTO request) {
        List<OrderStoreHouseVO> result = orderManageService.findStorageByOrderInfo(request);
        return RemoteResponse.<List<OrderStoreHouseVO>>custom().setSuccess().setData(result);
    }

    @RpcMethod("我的结算--我的待结算换卡")
    @RpcMapping("/saveFundCards")
    @ServiceLog(operationType = OperationType.WRITE, description = "待结算换卡", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> saveFundCards(ChangeFundCardRequestDTO request) {
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);

        refFundcardOrderService.saveFundCardForWaitStatement(request, rjSessionInfo);
        return RemoteResponse.success(true);
    }

    @RpcMethod("我的结算--我的结算单换卡")
    @RpcMapping("/saveFundCardsForStatement")
    @ServiceLog(operationType = OperationType.WRITE, description = "结算换卡", serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> saveFundCardsForStatement(ChangeFundCardRequestDTO request, RjSessionInfo rjSessionInfo){
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        refFundcardOrderService.saveFundCardForStatement(request, rjSessionInfo);
        return RemoteResponse.success(true);
    }

    @RpcMethod("结算/待结算重新冻结")
    @RpcMapping("/refreezeFundCard")
    public RemoteResponse<Boolean> refreezeFundCard(OrderBasicParamDTO orderBasicParamDTO){
        BusinessErrUtil.notNull(orderBasicParamDTO.getOrderId(), "参数不可空");
        refFundcardOrderService.reFreezeFundCard(orderBasicParamDTO.getOrderId());
        return RemoteResponse.success();
    }

    @RpcMethod("我的结算--我的待结算选卡计算报账类型, 入参只需要orderIds")
    @RpcMapping("/calculateFeeType")
    public RemoteResponse<List<OrderDetailFeeTypeVO>> calculateFeeType(ChangeFundCardRequestDTO request) {
        List<Integer> orderIds = request.getOrderIds();
        BusinessErrUtil.notEmpty(orderIds, "计算报销费用类型失败! 订单id不可为空！");
        List<OrderDetailFeeTypeVO> orderDetailFeeType = refFundcardOrderService.getOrderDetailFeeType(orderIds);
        return RemoteResponse.<List<OrderDetailFeeTypeVO>>custom().setSuccess().setData(orderDetailFeeType);
    }
    
    @RpcMapping("/batchAcceptOrder")
    @RpcMethod("订单管理-批量验收订单")
    public RemoteResponse<Boolean> batchAcceptOrder(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(),false, null);
        Boolean batchAcceptSuccess = buyerOrderService.batchAcceptOrder(request, loginInfo);
        return RemoteResponse.<Boolean>custom().setSuccess().setData(batchAcceptSuccess);
    }

    @RpcMapping("/receiveAppendImages")
    @RpcMethod("订单管理-追加验收图片")
    public RemoteResponse<Boolean> saveAppendImages(RjSessionInfo rjSessionInfo, OrderAcceptPicRequest orderAcceptPicRequest) {
        orderPicRelatedService.checkAndSaveAppendAcceptanceImages(rjSessionInfo, orderAcceptPicRequest);
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    @RpcMapping("/receiveAppendDetailImages")
    @RpcMethod("订单管理-追加验收详情关联图片")
    public RemoteResponse<Boolean> saveAppendDetailImages(RjSessionInfo rjSessionInfo, OrderDetailAcceptPicRequest orderDetailAcceptPicRequest) {
        orderPicRelatedService.appendDetailAcceptanceImages(rjSessionInfo, orderDetailAcceptPicRequest);
        return RemoteResponse.<Boolean>custom().setSuccess();
    }

    @RpcMapping("/replaceImages")
    @RpcMethod("订单管理-替换验收图片")
    public RemoteResponse<Boolean> replaceImage(RjSessionInfo rjSessionInfo, OrderAcceptPicRequest orderAcceptPicRequest) {
        orderPicRelatedService.overwriteAcceptanceImages(rjSessionInfo, orderAcceptPicRequest.getOrderId(), orderAcceptPicRequest.getFilePaths());
        return RemoteResponse.<Boolean>custom().setSuccess();
    }


    @RpcMapping("/getBriefGoodsReturnInfo")
    @RpcMethod("订单管理-获取退货单概览信息")
    public RemoteResponse<List<GoodsReturnBriefInfoVO>> getBriefGoodsReturnInfo(GoodsReturnBriefInfoRequest goodsReturnBriefInfoRequest) {
        List<GoodsReturnBriefInfoVO> briefGoodsReturnInfo = buyerOrderService.getBriefGoodsReturnInfo(goodsReturnBriefInfoRequest);
        return RemoteResponse.<List<GoodsReturnBriefInfoVO>>custom().setData(briefGoodsReturnInfo).setSuccess();
    }

    @RpcMapping("/acceptComment/getOrgCommentTag")
    @RpcMethod("订单管理-订单验收评价-获取单位需评价条目")
    // TODO: 前端暂时写死，若2021-05-01未增加新单位的话可以删除此接口
    public RemoteResponse<Map<Integer, String>> getOrgCommentTag(RjSessionInfo rjSessionInfo) {
        Map<Integer, String> orgRelatedCommentMap = orderAcceptCommentClient.getOrgRelatedCommentMap(rjSessionInfo.getOrgId());
        return RemoteResponse.<Map<Integer, String>>custom().setData(orgRelatedCommentMap).setSuccess();
    }

    @RpcMapping("/limitDaysAfterFinish")
    @RpcMethod("订单管理-订单完成后是否在某个天数内，目前仅用于不使用结算单位")
    public RemoteResponse<Boolean> limitDaysAfterFinish(RjSessionInfo rjSessionInfo, OrderStatusLimitDaysRequest limitDaysRequest) {
        Boolean result = buyerOrderService.limitDaysAfterFinish(rjSessionInfo, limitDaysRequest);
        return RemoteResponse.<Boolean>custom().setData(result).setSuccess();
    }

    @RpcMethod("输出订单明细商品的批次, 点击保存&修改批次时调用（一物一码用）")
    @RpcMapping("/outputOrderDetailBatches")
    public RemoteResponse<OrderDetailBatchesRespondDTO> outputOrderDetailBatches(RjSessionInfo rjSessionInfo, DetailBatchesRequestDTO request) {
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.QUERY_BATCH_FAILED_NO_PERMISSION);
        BusinessErrUtil.notNull(request, "入参为空!");
        List<OrderUniqueBarCodeDTO> queryResult = orderUniqueBarCodeRPCClient.findByDetailIdList(request.getOrderDetailBatchesList().stream().map(DetailBatchesDTO::getDetailId).collect(Collectors.toList()));
        OrderDetailBatchesRespondDTO result = new OrderDetailBatchesRespondDTO();
        result.setOrderDetailBatchesList(queryResult.stream().map(OrderUniqueBarCodeRPCClient::barCodeToBatchesDTO).collect(Collectors.toList()));
        return RemoteResponse.<OrderDetailBatchesRespondDTO>custom().setSuccess().setData(result);
    }

    @RpcMapping("/deliveryRemind")
    @RpcMethod("订单管理-订单管理待确认订单/待发货增加发货提醒")
    public RemoteResponse<Boolean> deliveryRemind(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        return buyerOrderService.deliveryRemind(rjSessionInfo, request);
    }

    @RpcMapping("/confirmRemind")
    @RpcMethod("订单管理-待确认订单 发送确认提醒")
    public RemoteResponse<Boolean> confirmRemind(RjSessionInfo rjSessionInfo,
                                                 @RpcMethodParam(includePropertyNames = { "orderId" }) OrderBasicParamDTO request) {
        return buyerOrderService.confirmRemind(rjSessionInfo, request);
    }

    @RpcMapping("/searchOperationConfig")
    @RpcMethod("获取电子签名配置")
    public RemoteResponse<ElectronicSignInfoVO> searchOperationConfig(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request) {
        ElectronicSignInfoVO electronicSignInfoVO = buyerOrderService.searchOperationConfig(rjSessionInfo, request);
        return RemoteResponse.<ElectronicSignInfoVO>custom().setSuccess().setData(electronicSignInfoVO);
    }

    @RpcMapping("/getOvertimeSettleHints")
    @RpcMethod("获取超时结算订单列表提示")
    public RemoteResponse<List<OrderTipsVO>> getOvertimeSettleHints(RjSessionInfo rjSessionInfo, OrderTipsRequest tipsReq) {
        BusinessErrUtil.notNull(tipsReq, "获取超时验收订单列表,入参不可为空");
        Integer userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER) == null ? null : rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue();
        tipsReq.setUserId(tipsReq.getUserId() == null ? userId : tipsReq.getUserId());
        return orderAnnotationService.overtimeSettleHint(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), tipsReq);
    }

    @RpcMapping("/clickHints")
    @RpcMethod("点击超时提示")
    public RemoteResponse<Boolean> clickHints(RjSessionInfo rjSessionInfo, OrderTipsRequest tipsReq) {
        BusinessErrUtil.notNull(tipsReq, "获取超时验收订单列表,入参不可为空");
        Integer userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER) == null ? null : rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER).intValue();
        tipsReq.setUserId(tipsReq.getUserId() == null ? userId : tipsReq.getUserId());
        return orderAnnotationService.clickHints(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), tipsReq);
    }

    @RpcMapping("/removeClickHistory")
    @RpcMethod("删除用户点击超时结算提示记录")
    public RemoteResponse<Boolean> removeClickHistory(RjSessionInfo rjSessionInfo, OrderTipsRequest tipsReq) {
        BusinessErrUtil.notNull(tipsReq, "获取超时验收订单列表,入参不可为空");
        return orderAnnotationService.removeClickHistory(rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), tipsReq);
    }

    @RpcMapping("/modifyDeliveryAddr")
    @RpcMethod("订单详情修改送货地址")
    public RemoteResponse<Boolean> modifyDeliveryAddr(RjSessionInfo rjSessionInfo, ModifyAddrRequestDTO modifyAddrRequestDTO) {
        Boolean modifySuccess = buyerOrderService.modifyOrderAddr(rjSessionInfo, modifyAddrRequestDTO);
        if (Boolean.TRUE.equals(modifySuccess)) {
            return RemoteResponse.<Boolean>custom().setData(modifySuccess).setSuccess();
        } else {
            return RemoteResponse.<Boolean>custom().setData(modifySuccess).setFailure("订单详情修改送货地址失败");
        }
    }

    @RpcMapping("/hasModifiedAddress")
    @RpcMethod("订单id查询是否修改过地址.true:已修改； false：未修改")
    public RemoteResponse<OrderAddressInfoVO> hasModifiedAddress(RjSessionInfo rjSessionInfo, ModifyAddrRequestDTO modifyAddrRequestDTO) {
        OrderAddressInfoVO hasModified = buyerOrderService.checkHasModifiedAddr(modifyAddrRequestDTO.getOrderId());
        return RemoteResponse.<OrderAddressInfoVO>custom().setData(hasModified).setSuccess();
    }
    
    @RpcMapping("/agreeCancelDeliveryProxy")
    @RpcMethod("同意取消代配送")
    public RemoteResponse<Boolean> agreeCancelDeliveryProxy(RjSessionInfo rjSessionInfo, CancelOrderReqDTO request){
        BusinessErrUtil.notNull(request.getOrderMasterId(), "订单id不能为空");
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.WRONG_USER_TYPE);
        deliveryProxyService.buyerAgreeCancel(request.getOrderMasterId(), request.getRefuseReason(), userId.intValue());
        return RemoteResponse.success();
    }

    @RpcMapping("/rejectCancelDeliveryProxy")
    @RpcMethod("拒绝取消代配送")
    public RemoteResponse<Boolean> rejectCancelDeliveryProxy(RjSessionInfo rjSessionInfo, CancelOrderReqDTO request){
        BusinessErrUtil.notNull(request.getOrderMasterId(), "订单id不能为空");
        BusinessErrUtil.notNull(request.getRefuseReason(), "取消理由不能为空");
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        BusinessErrUtil.notNull(userId, ExecptionMessageEnum.WRONG_USER_TYPE);
        deliveryProxyService.buyerRejectCancel(request.getOrderMasterId(), request.getRefuseReason(), userId.intValue());
        return RemoteResponse.success();
    }

    @RpcMethod("我的订单-供应商申请取消代配送的数量")
    @RpcMapping("/myOrderList/deliveryProxy/suppApplyCancel/count")
    public RemoteResponse<Long> countSuppApplyCancelDeliveryProxyMyOrder(RjSessionInfo rjSessionInfo) throws ParseException {
        OrderListRequest request = this.getCommonApplyCancelDeliveryCountRequestParam();
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        // 订单列表的特异性设置，在课题组订单中没有下述语句
        request.setFbuyerId(loginInfo.getUserId());
        request.setMyOrderCheck(true);
        loginInfo.setDeptList(New.list());
        return RemoteResponse.<Long>custom().setSuccess().setData(buyerOrderService.getOrderListForWWW(request, loginInfo,false).getTotal());
    }

    @RpcMethod("课题组订单-供应商申请取消代配送的数量")
    @RpcMapping("/researchGroup/deliveryProxy/suppApplyCancel/count")
    public RemoteResponse<Long> countSuppApplyCancelDeliveryProxyDeptOrder(RjSessionInfo rjSessionInfo) throws ParseException {
        OrderListRequest request = this.getCommonApplyCancelDeliveryCountRequestParam();
        request.setMyOrderCheck(false);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER), true, ConfigConstant.BUYER_CENTER_ORDER_VIEW);
        return RemoteResponse.<Long>custom().setSuccess().setData(buyerOrderService.getOrderListForWWW(request, loginInfo,false).getTotal());
    }

    private OrderListRequest getCommonApplyCancelDeliveryCountRequestParam(){
        OrderListRequest request = new OrderListRequest();
        request.setPageNo(0);
        request.setPageSize(0);
        request.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        request.setDeliveryType(New.list(DeliveryTypeEnum.PROXY.getCode()));
        request.setDeliveryStatus(DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue());
        return request;
    }

    /**
     * 线上单：只有随货发票才能修改
     * 线下单：无限制（按原本的逻辑）
     *
     * @param createInvoiceDTOList
     * @param rjSessionInfo
     * @return
     */
    @RpcMethod("保存发票信息")
    @RpcMapping("/saveOrderInvoice")
    public RemoteResponse<Boolean> saveOrderInvoice(List<CreateInvoiceDTO> createInvoiceDTOList, RjSessionInfo rjSessionInfo){
        Integer orgId = rjSessionInfo.getOrgId();
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        // 检查发票查看权限
        Boolean canViewInvoice = userClient.findUserHasAccess(orgId, userId.intValue(), null, ConfigConstant.BUYER_CENTER_INVOICE_ATTACHMENT_VIEW);
        BusinessErrUtil.isTrue(BooleanUtils.isTrue(canViewInvoice), ExecptionMessageEnum.NO_INVOICE_ATTACHMENT_VIEW_PERMISSION);
        orderInvoiceService.saveInvoice(createInvoiceDTOList, userId, orgId, StatementAccessCodeEnum.BUYER_INVOICE_EDIT.getAccessCode());
        return RemoteResponse.success(true);
    }
    /**
     * 分页查询订单推送失败项
     *
     */
    @RpcMapping("/listStatusChangeNotices")
    @RpcMethod("状态变更提醒列表")
    public RemoteResponse<List<OrderMasterSearchDTO>> listStatusChangeNotices(RjSessionInfo rjSessionInfo) {
        return RemoteResponse.success(buyerOrderService.listStatusChangeNotices(rjSessionInfo));
    }

    /**
     * 分页查询订单推送失败项
     *
     * @param pageQuery
     * @return
     */
    @RpcMapping("/pushFailItemPage")
    @RpcMethod("订单推送失败列表")
    public PageableResponse<List<OrderPushFailItemVO>> orderPushFailItemPageQuery(RjSessionInfo rjSessionInfo, OrderPushFailItemDTO pageQuery) {
        return buyerOrderService.orderPushFailItemPageQuery(rjSessionInfo, pageQuery);
    }

    @RpcMapping("/modifyOrderRemark")
    @RpcMethod("修改订单评论")
    public RemoteResponse<Boolean> modifyOrderRemark(ModifyRemarkRequestDTO modifyRemarkRequestDTO){
        orderRemarkService.saveBuyerRemark(modifyRemarkRequestDTO.getOrderId(), modifyRemarkRequestDTO.getRemark());
        return RemoteResponse.success(true);
    }

    @RpcMapping("/getOrderAcceptanceConfig")
    @RpcMethod("获取订单验收配置")
    public RemoteResponse<OrderAcceptanceConfigVO> getOrderAcceptanceConfig(@RpcMethodParam(includePropertyNames = "orderId") OrderBasicParamDTO request){
        BusinessErrUtil.notNull(request.getOrderId(), ExecptionMessageEnum.SELECT_ORDER_FOR_ACCEPTANCE);
        return RemoteResponse.success(orderAcceptService.getOrderAcceptanceConfig(request.getOrderId()));
    }

    @RpcMapping("/getOrderAcceptanceCount")
    @RpcMethod("订单管理-验收审批计数")
    public RemoteResponse<OrderAcceptanceCountVO> getOrderAcceptanceCount(RjSessionInfo rjSessionInfo,
                                                                          @RpcMethodParam(includePropertyNames = "myOrderCheck")OrderListRequest request) {
        return RemoteResponse.success(orderApprovalService.getOrderAcceptanceCount(rjSessionInfo, request));
    }

    @RpcMethod("订单详情-修改订单拓展信息")
    @RpcMapping("/modifyOrderExtInfo")
    public RemoteResponse<Boolean> modifyOrderExtInfo(RjSessionInfo rjSessionInfo, ModifyOrderExtInfoRequestDTO request) {
        buyerOrderService.modifyOrderExtInfo(rjSessionInfo, request);
        return RemoteResponse.success();
    }

    @Method(value = "验收审批-覆盖上传验收图片", author = "黄有望")
    @Mapping("/acceptApproval/overrideAcceptanceImage")
    public RemoteResponse<Boolean> acceptApprovalOverrideImage(RjSessionInfo rjSessionInfo, OverrideUploadImageRequest request) {
        orderPicRelatedService.acceptApprovalOverrideImage(rjSessionInfo, request);
        return RemoteResponse.success();
    }

    @Method(value = "验收审批-覆盖上传验收附件", author = "黄有望")
    @Mapping("/acceptApproval/overrideAcceptanceAttachment")
    public RemoteResponse<Boolean> acceptApprovalOverrideAttachment(RjSessionInfo rjSessionInfo, OverrideUploadAttachmentRequest request) {
        orderUploadFileService.acceptApprovalOverrideAttachment(rjSessionInfo, request);
        return RemoteResponse.success();
    }


}
