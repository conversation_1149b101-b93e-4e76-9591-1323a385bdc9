package com.ruijing.store.order.base.minor.model;

import java.math.BigDecimal;
import java.util.Date;

public class OrderForWallet {
    private String id;

    /**
    * 验收后订单金额
    */
    private BigDecimal orderAmount;

    /**
    * 订单创建日期
    */
    private Date orderDate;

    /**
    * 订单号
    */
    private String orderNo;

    /**
    * 医院id
    */
    private Integer orgId;

    /**
    * 医院code
    */
    private String orgCode;

    /**
    * 采购人id
    */
    private Integer buyerId;

    /**
    * 供应商id
    */
    private Integer suppId;

    /**
    * 失败次数
    */
    private Integer failCount;

    /**
    * 成功标识位，0失败1成功
    */
    private Integer flag;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    /**
    * 逻辑删除标识
    */
    private Boolean isDeleted;

    /**
    * 逻辑删除时间
    */
    private Date deletionTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderAmount=").append(orderAmount);
        sb.append(", orderDate=").append(orderDate);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", orgId=").append(orgId);
        sb.append(", orgCode=").append(orgCode);
        sb.append(", buyerId=").append(buyerId);
        sb.append(", suppId=").append(suppId);
        sb.append(", failCount=").append(failCount);
        sb.append(", flag=").append(flag);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", deletionTime=").append(deletionTime);
        sb.append("]");
        return sb.toString();
    }
}