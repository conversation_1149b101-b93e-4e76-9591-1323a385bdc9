package com.ruijing.store.order.gateway.buyercenter.vo.contract;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: <PERSON>g <PERSON>
 * @Date: 2020/12/29 10:36
 */
@RpcModel("合同-订单基本信息")
public class OrderInfoForContractVO implements Serializable {

    private static final long serialVersionUID = 4412252573598353684L;

    /**
     * 订单日期
     */
    @RpcModelProperty("订单日期")
    private String orderDate;

    /**
     * 采购部门
     */
    @RpcModelProperty("采购部门")
    private String departmentName;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * 供应商名字
     */
    @RpcModelProperty("供应商名字")
    private String supplierName;

    public String getOrderDate() {
        return orderDate;
    }

    public OrderInfoForContractVO setOrderDate(String orderDate) {
        this.orderDate = orderDate;
        return this;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public OrderInfoForContractVO setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public OrderInfoForContractVO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public OrderInfoForContractVO setSupplierName(String supplierName) {
        this.supplierName = supplierName;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderInfoForContractVO{");
        sb.append("orderDate='").append(orderDate).append('\'');
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", supplierName='").append(supplierName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
