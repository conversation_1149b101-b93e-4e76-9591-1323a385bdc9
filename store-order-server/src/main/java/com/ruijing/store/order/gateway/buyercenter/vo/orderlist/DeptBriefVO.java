package com.ruijing.store.order.gateway.buyercenter.vo.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/2/25 10:32
 * @Description
 **/
@RpcModel("订单管理-订单列表返回体-登陆用户部门信息")
public class DeptBriefVO implements Serializable {

    /**
     * 部门id
     */
    @RpcModelProperty("部门id")
    private Integer id;

    /**
     * 部门名称
     */
    @RpcModelProperty("部门名称")
    private String name;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DeptBriefVO{");
        sb.append("id=").append(id);
        sb.append(", name='").append(name).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
