package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.other.dto.TimeoutStatisticsDTO;
import com.ruijing.store.order.api.base.other.service.TimeOutOrderStatisticsRPCService;
import com.ruijing.store.order.base.baseconfig.enums.TimeOutConfigType;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.util.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@MSharpService
public class TimeOutOrderStatisticsRPCServiceImpl implements TimeOutOrderStatisticsRPCService {

    @Resource
    private TimeoutStatisticsService timeoutStatisticsService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Override
    public RemoteResponse<Integer> updateOrderTimeOutStatistics(OrderBasicParamDTO request) {
        List<Integer> orderIdList = request.getOrderIdList();
        String orgCode = request.getOrgCode();
        Preconditions.notEmpty(orderIdList, "update failure, orderIdList should be not empty");
        Preconditions.isTrue(orderIdList.size() <= 100, "update failure, orderIdList should be less than 100");
        Preconditions.notNull(orgCode, "update failure, orgCode should be not null");

        List<OrderMasterDO> orderMasterList = orderMasterMapper.findByIdIn(orderIdList);
        // 过滤非超时的订单并按课题组分组
        if (CollectionUtils.isEmpty(orderMasterList)) {
            return RemoteResponse.<Integer>custom().setSuccess().setData(0);
        }
        String balanceCycleLimitDaysString = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getCode());
        int balanceCycleLimitDays = NumberUtils.toInt(balanceCycleLimitDaysString, TimeOutConfigType.BALANCE_CYCLE_LIMIT_DAYS.getValue());
        Map<Integer, List<OrderMasterDO>> departmentIdOrderListMap = orderMasterList.stream()
                // 超时结算的订单
                .filter(o -> o.getFlastreceivedate().before(TimeUtil.getSettingTime(-balanceCycleLimitDays + 1)))
                .collect(Collectors.groupingBy(OrderMasterDO::getFbuydepartmentid));

        TimeoutStatisticsDTO timeOutInfo = timeoutStatisticsService.findByDepartmentIdAndType(orderMasterList.get(0).getFuserid(), orderMasterList.get(0).getFbuydepartmentid(), TimeOutBusinessType.BALANCE.getValue());
        if (!TimeOutBusinessType.BALANCE.getValue().equals(timeOutInfo.getType())) {
            return RemoteResponse.<Integer>custom().setSuccess().setData(0);
        }
        int affect = 0;
        for (Map.Entry<Integer, List<OrderMasterDO>> entry : departmentIdOrderListMap.entrySet()) {
            timeoutStatisticsService.executeTimeOutStatisticsDecrease(entry.getValue().size(), entry.getValue().get(0).getFuserid(), entry.getValue().get(0).getFusercode(), entry.getKey(), TimeOutBusinessType.BALANCE);
            affect += entry.getValue().size();
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(affect);
    }
}
