package com.ruijing.store.order.gateway.oms.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/12/7 11:36
 * @description
 */
@RpcModel("修正数据通用请求体")
public class FixDataCommonRequest implements Serializable {

    private static final long serialVersionUID = -8493520089244483866L;

    @RpcModelProperty("订单id")
    private List<Integer> ids;

    @RpcModelProperty("目标状态")
    private Integer status;

    @RpcModelProperty("修正原因")
    private String reason;

    @RpcModelProperty("钉钉审批编号")
    private String dingTalkApprovalNumber;

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDingTalkApprovalNumber() {
        return dingTalkApprovalNumber;
    }

    public void setDingTalkApprovalNumber(String dingTalkApprovalNumber) {
        this.dingTalkApprovalNumber = dingTalkApprovalNumber;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", FixDataCommonRequest.class.getSimpleName() + "[", "]")
                .add("ids=" + ids)
                .add("status=" + status)
                .add("reason='" + reason + "'")
                .add("dingTalkApprovalNumber='" + dingTalkApprovalNumber + "'")
                .toString();
    }
}
