package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.research.api.dto.PageInfo;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderRequestDTO;
import com.reagent.research.statement.api.order.dto.WaitingStatementOrderResultDTO;
import com.reagent.research.statement.api.order.service.StatementOrderApi;
import com.reagent.research.statement.api.statement.dto.*;
import com.reagent.research.statement.api.statement.service.StatementLogApi;
import com.reagent.research.statement.api.statement.service.StatementSearchApi;
import com.reagent.research.statement.api.statement.service.StatementUpdateApi;
import com.reagent.research.statement.api.statement.service.StatementWayConfigRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.RpcCallUtils;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 结算平台rpc client
 * <AUTHOR>
 */
@ServiceClient
@CatAnnotation
public class StatementPlatformClient {

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementOrderApi statementOrderApi;

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementSearchApi statementSearchApi;

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementLogApi statementLogApi;

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementUpdateApi statementUpdateApi;

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementWayConfigRpcService statementWayConfigRpcService;

    private static final Logger LOGGER = LoggerFactory.getLogger(StatementPlatformClient.class);

    /**
     * 保存待结算业务记录
     * @param waitingStatementList
     * @return 待结算单集合
     */
    @ServiceLog(description = "保存待结算业务记录", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public List<WaitingStatementOrderResultDTO> saveWaitingStatement(List<WaitingStatementOrderRequestDTO> waitingStatementList) {
        if (CollectionUtils.isEmpty(waitingStatementList)) {
            return Collections.emptyList();
        }

        RemoteResponse<List<WaitingStatementOrderResultDTO>> response = statementOrderApi.saveWaitingStatementOrders(waitingStatementList);
        Preconditions.isTrue(response.isSuccess(), "保存待结算业务记录失败: " + JsonUtils.toJsonIgnoreNull(response));
        String responseLog = JsonUtils.toJson(response);
        LOGGER.info("保存待结算订单, 出参 => {}", responseLog);
        return response.getData();
    }

    /**
     * 删除待结算业务记录
     * @param orderIdList
     * @return
     */
    @ServiceLog(description = "删除待结算业务记录", serviceType = ServiceType.COMMON_SERVICE, operationType = OperationType.WRITE)
    public void deleteWaitingStatementByOrderId(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return;
        }

        RemoteResponse response = statementOrderApi.deleteWaitingStatementOrders(orderIdList);
        Preconditions.isTrue(response.isSuccess(), "删除待结算业务记录异常: " + JsonUtils.toJsonIgnoreNull(response));
    }

    /**
     * 更新待结算记录
     * @param waitingStatementList
     * @return 待结算单集合
     */
    @ServiceLog(description = "更新待结算中间表记录", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public List<WaitingStatementOrderResultDTO> updateWaitingStatement(List<WaitingStatementOrderRequestDTO> waitingStatementList) {
        if (CollectionUtils.isEmpty(waitingStatementList)) {
            return Collections.emptyList();
        }

        List<WaitingStatementOrderResultDTO> result = new ArrayList<>(waitingStatementList.size());
        List<List<WaitingStatementOrderRequestDTO>> partition = Lists.partition(waitingStatementList, 100);
        for (List<WaitingStatementOrderRequestDTO> dtoList : partition) {
            RemoteResponse<List<WaitingStatementOrderResultDTO>> response = statementOrderApi.updateWaitingStatementOrders(new ArrayList<>(dtoList));
            Preconditions.isTrue(response.isSuccess(), "更新待结算记录异常: " + JsonUtils.toJsonIgnoreNull(response));
            String responseLog = JsonUtils.toJson(response);
            LOGGER.info("保存待结算订单, 出参 => {}", responseLog);
            result.addAll(response.getData());
        }

        return result;
    }

    /**
     * 通过订单结算单id批量查询结算单
     * @param statementIdList
     * @param orgCode
     * @return 结算单集合
     */
    @ServiceLog(description = "通过订单结算单id批量查询结算单", serviceType = ServiceType.COMMON_SERVICE)
    public List<StatementResultDTO> findStatementByIds(List<Long> statementIdList, String orgCode) {
        if (CollectionUtils.isEmpty(statementIdList)) {
            return Collections.emptyList();
        }

        List<StatementResultDTO> resList = New.list();
        List<List<Long>> partition = Lists.partition(statementIdList, 500);
        for (List<Long> statementIdPart : partition) {
            StatementQueryDTO params = new StatementQueryDTO();
            params.setStatementIds(New.list(statementIdPart));
            params.setOrgCode(orgCode);
            RemoteResponse<List<StatementResultDTO>> response = statementSearchApi.findStatementByIds(params);
            Preconditions.isTrue(response.isSuccess(),"通过结算单ID集查询结算单信息异常");
            resList.addAll(response.getData());
        }
        return resList;
    }

    /**
     * 搜索分页查询结算单，仅需要结算单id列表
     * @param query
     * @return
     */
    public List<StatementResultDTO> findStatementByPage(StatementQueryDTO query) {
        RemoteResponse<PageInfo<StatementResultDTO>> statementRes = statementSearchApi.findStatementByPage(query);
        Preconditions.isTrue(statementRes != null && statementRes.isSuccess() && statementRes.getData() != null, "");
        return statementRes.getData().getData();
    }

    /**
     * 更新待结算单的超时标签
     * @param orderIdList   订单id集合
     * @param timeOutStatus 超时状态
     * @return              被更新的记录
     */
    @ServiceLog(description = "更新待结算中间表超时标签", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public List<WaitingStatementOrderResultDTO> updateWaitingStatementTimeStatusOrders(List<Integer> orderIdList, Integer timeOutStatus) {
        List<WaitingStatementOrderResultDTO> result = new ArrayList<>(orderIdList.size());
        List<List<Integer>> partition = Lists.partition(orderIdList, 100);
        for (List<Integer> partitionItem : partition) {
            List<WaitingStatementOrderRequestDTO> request = partitionItem.stream().map(id -> {
                WaitingStatementOrderRequestDTO item = new WaitingStatementOrderRequestDTO();
                item.setOrderId(id);
                item.setTimeOutStatus(timeOutStatus);
                return item;
            }).collect(Collectors.toList());
            RemoteResponse<List<WaitingStatementOrderResultDTO>> response = statementOrderApi.updateWaitingStatementTimeStatusOrders(request);
            Preconditions.isTrue(response.isSuccess(), "更新待结算中间表超时标签异常:" + JsonUtils.toJsonIgnoreNull(response));
            result.addAll(response.getData());
        }

        return result;
    }

    /**
     * @param masterSearchList
     * @param orgCode
     * @return java.util.Map<java.lang.Long, com.reagent.research.statement.api.statement.dto.StatementResultDTO>
     * @description: 构建结算单id-结算单详情对应关系
     * @date: 2020/12/3 9:46
     * @author: zengyanru
     */
    public Map<Long, StatementResultDTO> constructStatementIdMap(List<OrderMasterSearchDTO> masterSearchList, String orgCode) {
        List<Long> statementIdList = new ArrayList<>();
        for (OrderMasterSearchDTO masterSearch : masterSearchList) {
            if (masterSearch.getStatementId() != null) {
                statementIdList.add((long) masterSearch.getStatementId());
            }
        }
        List<StatementResultDTO> statementList = this.findStatementByIds(statementIdList, orgCode);
        // id与结算单对应关系
        return statementList.stream()
                .filter(Objects::nonNull)
                .filter(s -> Objects.nonNull(s.getId()))
                .collect(
                        toMap(StatementResultDTO::getId, Function.identity(), (oldValue, newValue) -> oldValue)
                );
    }

    @ServiceLog(description = "写结算换卡日志", serviceType = ServiceType.RPC_CLIENT)
    public void saveChangeCardLogs(List<StatementLogRequestDTO> statementLogRequestDTOList){
        RemoteResponse<Boolean> response = statementLogApi.saveChangeCardLogs(statementLogRequestDTOList);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "关闭结算/汇总单")
    public void cancelStatementOrSummary(Integer statementId, String reason){
        StatementRequestDTO statementRequestDTO = new StatementRequestDTO();
        statementRequestDTO.setId(statementId.longValue());
        statementRequestDTO.setReason(reason);
        RemoteResponse<Boolean> response = statementUpdateApi.cancelStatementOrSummary(statementRequestDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }

    @ServiceLog(description = "根据订单id集合批量查询查询结算单日志", serviceType = ServiceType.RPC_CLIENT)
    public List<OrderRefStatementLogDTO> listStatementLogByOrderIds(List<Integer> orderIds){
        if (CollectionUtils.isEmpty(orderIds)) {
            return New.emptyList();
        }
        
        List<OrderRefStatementLogDTO> resultList = New.list();
        List<List<Integer>> partitionList = Lists.partition(orderIds, 100);
        
        for (List<Integer> partition : partitionList) {
            List<Integer> batchOrderIds = New.list(partition);
            RemoteResponse<List<OrderRefStatementLogDTO>> response = statementLogApi.listStatementLogByOrderIds(batchOrderIds);
            Preconditions.isTrue(response.isSuccess(), "根据订单id集合批量查询结算单日志失败, " + response.getMsg());
            
            if (CollectionUtils.isNotEmpty(response.getData())) {
                resultList.addAll(response.getData());
            }
        }        
        return resultList;
    }

    @ServiceLog(description = "根据结算单ID查询结算单和汇总单信息,结算单id不能超过300", serviceType = ServiceType.RPC_CLIENT)
    public List<SimpleStatementDTO> listSummaryAndStatementByIds(List<Long> statementIds) {
        if (CollectionUtils.isEmpty(statementIds)) {
            return New.emptyList();
        }
        return RpcCallUtils.partitionExec(statementIds, 300, statementSearchApi::listSummaryAndStatementByIds,
                response -> Preconditions.isTrue(response.isSuccess(), "根据结算单ID查询结算单和汇总单信息, " + response.getMsg()));
    }

    @ServiceLog(description = "获取结算方式配置", serviceType = ServiceType.RPC_CLIENT)
    public List<StatementWayConfigDTO> listStatementWayConfigByIds(List<Integer> statementWayIds) {
        RemoteResponse<List<StatementWayConfigDTO>> response = statementWayConfigRpcService.listStatementWayConfigByIds(statementWayIds);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }
}
