package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.contract.ReagentProductBaseService;
import com.ruijing.store.contract.dto.reagentProduct.ReagentProductDTO;

import java.util.List;

/**
 * @description: 锐竞合同商品RPC Client
 * @author: zhongyulei
 * @create: 2022-01-11 17:15
 */
@ServiceClient
public class ReagentProductBaseClient {

    @MSharpReference(remoteAppkey = "store-price-contract-service")
    private ReagentProductBaseService reagentProductBaseService;

    @ServiceLog(description = "根据试剂库商品id查询试剂库商品信息", serviceType = ServiceType.RPC_CLIENT)
    public List<ReagentProductDTO> listReagentProductByIds(List<Long> productIds) {
        RemoteResponse<List<ReagentProductDTO>> response = reagentProductBaseService.listReagentProductByIds(productIds);
        Preconditions.isTrue(response.isSuccess(), JsonUtils.toJsonIgnoreNull(response));
        return response.getData();
    }
}
