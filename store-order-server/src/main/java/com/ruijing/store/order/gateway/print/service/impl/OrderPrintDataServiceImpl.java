package com.ruijing.store.order.gateway.print.service.impl;

import com.reagent.bid.api.rpc.enums.OperationEnum;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.crm.api.pojo.dto.OfflineSupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.crm.api.pojo.dto.UserDTO;
import com.ruijing.shop.crm.api.pojo.dto.account.UserAccountDTO;
import com.ruijing.shop.crm.api.support.enums.UserTagEnum;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.extend.ApplyInfoDTO;
import com.ruijing.store.approval.api.enums.ApproveLevelEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.other.dto.*;
import com.ruijing.store.order.api.gateway.dto.SupplierUserDTO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptCommentVO;
import com.ruijing.store.order.gateway.print.constant.RoleConstant;
import com.ruijing.store.order.gateway.print.dto.PrintDataRequestDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintApprovalLogDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintDataItemDTO;
import com.ruijing.store.order.gateway.print.dto.other.OrderInvoiceDTO;
import com.ruijing.store.order.gateway.print.service.OrderPrintDataService;
import com.ruijing.store.order.gateway.print.util.ApprovalLogTranslator;
import com.ruijing.store.order.gateway.print.vo.apply.ApplyInstructionsVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.service.OrderBatchService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.RoleDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.MiniDepartmentDTO;
import com.ruijing.store.user.api.dto.invoicetitle.InvoiceTitleDTO;
import com.ruijing.store.user.api.dto.role.UserInDepartmentRoleDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023/2/27 10:57
 * @description
 */
@Service
public class OrderPrintDataServiceImpl implements OrderPrintDataService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private final String CAT_TYPE = getClass().getSimpleName();

    @PearlValue(key = "wechat.order.detail.url")
    private String WECHAT_ORDER_DETAIL_URL;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private GetPrintCommonDataService getPrintCommonDataService;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private UserClient userClient;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private OrderAddressRPCClient orderAddressRpcClient;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private RoleRPCClient roleRpcClient;

    @Resource
    private OrderBatchService orderBatchService;

    @Resource
    private UserDepartmentRoleRpcServiceClient userDepartmentRoleRpcServiceClient;


    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    @Override
    public List<OrderPrintDataItemDTO> getOrderPrintData(PrintDataRequestDTO request) {
        // 获取订单id
        List<Integer> orderIdList = request.getIds();
        List<String> orderNoList = request.getSerialNumbers();
        List<OrderMasterDO> orderInfoList;
        if(CollectionUtils.isEmpty(orderIdList)){
            orderInfoList = orderMasterMapper.findByFordernoIn(orderNoList);
            orderIdList = orderInfoList.stream().map(OrderMasterDO::getId).collect(toList());
        }else{
            orderInfoList = orderMasterMapper.findByIdIn(orderIdList);
        }
        if(CollectionUtils.isEmpty(orderInfoList)){
            return New.emptyList();
        }
        //---------以下是数据预批量获取并形成映射关系
        // 判断第一个订单的id即可，一般不跨单位打印。
        int orgId = orderInfoList.get(0).getFuserid();
        String orgCode = orderInfoList.get(0).getFusercode();
        // 订单id-商品详情映射
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        Map<Integer, List<OrderDetailDO>> orderIdDetailListMap = DictionaryUtils.groupBy(orderDetailList, OrderDetailDO::getFmasterid);
        // 订单id-经费卡映射
        List<RefFundcardOrderDTO> refFundCardList = refFundcardOrderService.findByOrderIdList(orderIdList);
        Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap = DictionaryUtils.groupBy(refFundCardList, ref -> Integer.parseInt(ref.getOrderId()));
        // 获取采购日志
        Map<Integer, List<OrderPurchaseApprovalLogDTO>> orderIdPurchaseLogListMap = this.getCustomPurchaseOrBidLog(orgId, orderInfoList);
        // 获取验收/验收审批日志
        Map<Integer, List<OrderApprovalLogDTO>> orderIdApprovalLogMap = this.getOrderIdApprovalLogMap(orgId, orderIdList);
        // 获取采购单id-采购单数据映射
        Map<Long, ApplicationMasterDTO> appIdMasterMap = this.getAppIdMasterMap(orderInfoList);
        // 买家用户信息
        Set<Integer> buyerIdSet = orderInfoList.stream().map(OrderMasterDO::getFbuyerid).collect(Collectors.toSet());
        List<UserBaseInfoDTO> buyerDTOList = userClient.getUserByIdsAndOrgId(buyerIdSet, orgId);
        Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap = DictionaryUtils.toMap(buyerDTOList, UserBaseInfoDTO::getId, Function.identity());
        // 订单对应课题组负责人信息
        List<Long> departmentIdList = orderInfoList.stream().filter(it -> Objects.nonNull(it.getFbuydepartmentid())).map(it -> it.getFbuydepartmentid().longValue()).distinct().collect(Collectors.toList());
        List<DepartmentDTO> departmentDTOList = departmentRpcClient.getDepartmentsByIds(departmentIdList);
        List<Integer> departmentManagerIdList = departmentDTOList.stream().map(DepartmentDTO::getManagerId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, String> departmentManageIdNameMap = New.emptyMap();
        if(CollectionUtils.isNotEmpty(departmentManagerIdList)){
            List<UserBaseInfoDTO> departmentManagerList = userClient.getUserByIdsAndOrgId(departmentManagerIdList, orgId);
            // 负责人id -> 负责人名字
            Map<Integer, String> manageIdNameMap = DictionaryUtils.toMap(departmentManagerList, UserBaseInfoDTO::getId, UserBaseInfoDTO::getName);
            departmentManageIdNameMap = DictionaryUtils.toMap(departmentDTOList, DepartmentDTO::getId, departmentDTO -> manageIdNameMap.get(departmentDTO.getManagerId()));
        }
        // 自定义订单分类（农科院）
        Map<Integer, String> orderIdSelfDefCateMap = New.emptyMap();
        if (Objects.equals(OrgEnum.GUANG_DONG_SHENG_NONG_YE_KE_XUE_YUAN.getValue(), orgId)) {
            List<BaseOrderExtraDTO> selfDefCateList = orderExtraClient.selectByOrderIdInAndExtraKey(orderIdList, OrderExtraEnum.SELF_DEFINED_CATEGORY.getValue());
            DictionaryUtils.toMap(selfDefCateList, BaseOrderExtraDTO::getOrderId, BaseOrderExtraDTO::getExtraValue);
        }
        // 验收评价
        Map<Integer, OrderAcceptCommentVO> orderIdCommentMap = New.emptyMap();
        if (orgId == OrgEnum.NAN_FANG_YI_KE_DA_XUE_SHEN_ZHEN_YI_YUAN.getValue()) {
            List<OrderAcceptCommentVO> orderCommentList = orderAcceptCommentClient.getOrderComment(orgId, orderIdList);
            orderIdCommentMap = orderCommentList.stream().collect(Collectors.toMap(OrderAcceptCommentVO::getOrderId, Function.identity(), (oldValue, newValue) -> newValue));
        }
        // 经费类型（目前仅广东农科院）
        Map<Integer, String> fundTypeMap = researchFundCardServiceClient.getFundTypeMap(orgCode);
        // 获取单位经费卡层级
        int cardLevel = NumberUtils.toInt(sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.RESEARCH_FUNDCARD_LEVEL));

        // 线下供应商信息
        Map<Integer, OfflineSupplierDTO> offLineSuppIdInfoMap = New.emptyMap();
        if (OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue() == orgId) {
            List<Integer> offLineSuppIdList = orderInfoList.stream().filter(orderMasterDO -> OrderSpeciesEnum.OFFLINE.getValue() == orderMasterDO.getSpecies().intValue()).map(OrderMasterDO::getFsuppid).collect(toList());
            if (CollectionUtils.isNotEmpty(offLineSuppIdList)) {
                List<OfflineSupplierDTO> offlineSupplierDTOList = suppClient.getOfflineSuppByIdList(offLineSuppIdList);
                offLineSuppIdInfoMap = DictionaryUtils.toMap(offlineSupplierDTOList, OfflineSupplierDTO::getId, Function.identity());
            }
        }
        // 收货地址及代配送相关
        List<OrderAddressDTO> addressDTOList = orderAddressRpcClient.findByOrderId(orderIdList);
        Map<Integer, OrderAddressDTO> orderIdAddressMap = DictionaryUtils.toMap(addressDTOList, OrderAddressDTO::getId, Function.identity());
        // 订单id-发票映射
        Map<Integer, List<OrderInvoiceDTO>> orderIdInvoiceMap = this.getOrderIdInvoiceMap(orgId, orderInfoList);
        // 供应商id-对应用户映射的一个缓存，防止多个单多次查询
        Map<Integer, List<UserAccountDTO>> suppIdUserListCacheMap = New.map();
        // 供应商id-单位业务员映射的一个缓存，防止多个单多次查询
        Map<Integer, SupplierUserDTO> suppIdTradeClerkCache = New.map();
        List<Integer> suppIdList = orderInfoList.stream().map(OrderMasterDO::getFsuppid).distinct().collect(toList());
        // 获取法人
        Map<Integer, QualificationDTO> suppIdQualMap = suppClient.getSuppContactInfo(suppIdList);

        Set<Integer> logUserIdSet = New.set();
        orderIdPurchaseLogListMap.values().stream().flatMap(List::stream).forEach(log->logUserIdSet.add(log.getApproverUserId()));
        orderIdApprovalLogMap.values().stream().flatMap(List::stream).forEach(log->logUserIdSet.add(log.getOperatorId()));
        Map<Integer, String> userIdRoleNameMap = this.getUserIdRoleNameMap(logUserIdSet, orgId);

        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderBatchService.getBatchesByOrders(orderInfoList);
        Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdBatchesMap = com.ruijing.order.utils.DictionaryUtils.groupBy(orderUniqueBarCodeDTOList, OrderUniqueBarCodeDTO::getOrderDetailId);

        // 获取发票抬头信息
        List<Integer> invoiceTitleIdList = orderInfoList.stream().map(OrderMasterDO::getInvoiceTitleId).distinct().collect(toList());
        Map<Integer, InvoiceTitleDTO> invoiceTitleId2DTOMap = this.getInvoiceTitleMap(invoiceTitleIdList);

        // 以下是数据组装
        List<OrderPrintDataItemDTO> orderPrintList = new ArrayList<>(orderInfoList.size());
        StringBuilder sb = new StringBuilder();
        for (OrderMasterDO orderItem : orderInfoList) {
            OrderPrintDataItemDTO orderPrint = new OrderPrintDataItemDTO();
            // 订单主表数据
            this.constructOrderMasterData(orderPrint, orderItem, orderIdDetailListMap.get(orderItem.getId()));
            QualificationDTO qualificationDTO = suppIdQualMap.get(orderItem.getFsuppid());
            if(qualificationDTO != null){
                // 供应商数据
                orderPrint.setSuppCorporation(qualificationDTO.getLegalPerson());
                String suppAddress = StringUtils.defaultString(qualificationDTO.getProvince())
                        + StringUtils.defaultString(qualificationDTO.getCity())
                        + StringUtils.defaultString(qualificationDTO.getDistrict())
                        + StringUtils.defaultString(qualificationDTO.getAddress());
                orderPrint.setSuppAddress(suppAddress);
            }
            UserBaseInfoDTO buyerInfo = buyerIdIdentityMap.get(orderItem.getFbuyerid());
            if(buyerInfo != null){
                // 采购人数据
                orderPrint.setBuyerTelephone(buyerInfo.getMobile());
                orderPrint.setBuyerJobNumber(buyerInfo.getJobnumber());
            }
            // 增加订单对应的分类
            orderPrint.setSelfDefCategory(orderIdSelfDefCateMap.get(orderItem.getId()));
            // 课题组负责人
            orderPrint.setDepartmentManagerName(departmentManageIdNameMap.get(orderItem.getFbuydepartmentid()));
            // 订单详情
            this.packageOrderDetailInfo(orderPrint, orderIdDetailListMap.get(orderItem.getId()), detailIdBatchesMap);
            // 经费卡
            orderPrint.setOrderFundCardPrintList(this.newPackageOrderFundCardInfo(orderIdFundCardMap.get(orderItem.getId()), cardLevel, orgId, orgCode, fundTypeMap, buyerIdIdentityMap));
            // 线下单的供应商统一信用码信息
            OfflineSupplierDTO offlineSupplierDTO = offLineSuppIdInfoMap.get(orderItem.getFsuppid());
            if(OrderSpeciesEnum.OFFLINE.getValue() == orderItem.getSpecies().intValue() && offlineSupplierDTO != null){
                orderPrint.setUnifyCode(offlineSupplierDTO.getUnifyCode());
            }
            // 二维码/条形码相关
            this.constructBarCode(orderPrint, orderItem);
            // 构建采购/验收审批日志
            List<OrderPurchaseApprovalLogDTO> bidOrPurchaseLogList = orderIdPurchaseLogListMap.getOrDefault(orderItem.getId(), New.emptyList());
            List<OrderApprovalLogDTO> acceptApproveLogList = orderIdApprovalLogMap.getOrDefault(orderItem.getId(), New.emptyList());
            List<OrderPrintApprovalLogDTO> purchaseApprovalLogPrintList = new ArrayList<>(bidOrPurchaseLogList.size() + acceptApproveLogList.size());
            this.constructBidOrPurchaseApprovalLog(orgCode, orderPrint, bidOrPurchaseLogList, purchaseApprovalLogPrintList, orderItem.getSpecies().intValue(), userIdRoleNameMap);
            this.constructAcceptApprovalLog(orderPrint, acceptApproveLogList, purchaseApprovalLogPrintList);
            if(OrgEnum.CHNEG_DU_YI_XUE_YUAN.getValue() == orgId){
                // 成都医，过滤带“系统”的日志
                purchaseApprovalLogPrintList = purchaseApprovalLogPrintList.stream().filter(orderPrintApprovalLogDTO -> !DockingConstant.SYSTEM_OPERATOR_NAME.equals(orderPrintApprovalLogDTO.getApprover())).collect(toList());
            }

            orderPrint.setOrderPurchaseApprovalLogPrintList(purchaseApprovalLogPrintList);
            // 验收评价
            List<Integer> orderAcceptCommentVO = orderIdCommentMap.get(orderItem.getId()) == null ? null : orderIdCommentMap.get(orderItem.getId()).getAcceptCommentTagList();
            orderPrint.setAcceptCommentIdList(orderAcceptCommentVO);
            // 采购申请说明
            if (orderItem.getFtbuyappid() != null) {
                this.constructAppData(orderPrint, appIdMasterMap.get(Long.valueOf(orderItem.getFtbuyappid())));
            }
            // 第一级验收审批人
            Integer operatorId = orderIdApprovalLogMap.getOrDefault(orderItem.getId(), New.emptyList()).stream().filter(log -> OrderApprovalEnum.PASS.getValue().equals(log.getApproveStatus())).findFirst().map(OrderApprovalLogDTO::getOperatorId).orElse(-1);
            String acceptApprovalName = Optional.ofNullable(buyerIdIdentityMap.get(operatorId)).map(UserBaseInfoDTO::getName).orElse(StringUtils.EMPTY);
            orderPrint.setAcceptApprovalName(acceptApprovalName);
            // 供应商确认订单的人的信息
            orderPrint.setConfirmManData(this.cacheAndGetSuppUser(orderItem, suppIdUserListCacheMap));
            // 获取供应商单位负责人信息
            orderPrint.setSuppTradeClerk(this.cacheAndGetTradeClerk(orderItem, suppIdTradeClerkCache));
            // 是代配送，返回*
            boolean isDeliveryProxy = orderIdAddressMap.get(orderItem.getId()) != null && DeliveryTypeEnum.PROXY.getCode().equals(orderIdAddressMap.get(orderItem.getId()).getDeliveryType());
            orderPrint.setDeliveryProxyMark(isDeliveryProxy ? "*" : null);
            this.setInvoiceData(orderPrint, orderIdInvoiceMap.get(orderItem.getId()), sb);
            //验收人联系方式(确认收货)
            String flastreceivemanid = orderItem.getFlastreceivemanid();
            if(StringUtils.isNotBlank(flastreceivemanid)){
                UserBaseInfoDTO userInfo = userClient.getUserInfo(Integer.valueOf(flastreceivemanid), orgId);
                orderPrint.setAccpectorPhone(userInfo.getMobile());
            }
            // 填充发票抬头信息
            if (Objects.nonNull(orderItem.getInvoiceTitleId())) {
                InvoiceTitleDTO invoiceTitleDTO = invoiceTitleId2DTOMap.get(orderItem.getInvoiceTitleId());
                if (Objects.nonNull(invoiceTitleDTO)) {
                    orderPrint.setInvoiceTaxNo(invoiceTitleDTO.getTaxNo());
                    orderPrint.setInvoiceTitle(invoiceTitleDTO.getTitle());
                }
            }
            orderPrintList.add(orderPrint);
        }
        return orderPrintList;
    }

    /**
     * 获取发票抬头信息
     *
     * @param invoiceTitleIdList 发票抬头ID列表
     * @return 发票抬头ID与发票抬头信息的映射
     */
    private Map<Integer, InvoiceTitleDTO> getInvoiceTitleMap(List<Integer> invoiceTitleIdList) {
        if (CollectionUtils.isEmpty(invoiceTitleIdList)) {
            return New.emptyMap();
        }
        List<InvoiceTitleDTO> invoiceTitleByIdList = invoiceClient.findInvoiceTitleByIdList(invoiceTitleIdList);
        if (CollectionUtils.isEmpty(invoiceTitleByIdList)) {
            return New.emptyMap();
        }
        Map<Integer, InvoiceTitleDTO> titleIdTitleMap = New.map();
        for (InvoiceTitleDTO title : invoiceTitleByIdList) {
            titleIdTitleMap.put(title.getId(), title);
        }
        return titleIdTitleMap;
    }

    /**
     * 获取采购单id-采购单数据映射
     * @param orderInfoList 订单数据
     * @return 映射
     */
    private Map<Long, ApplicationMasterDTO> getAppIdMasterMap(List<OrderMasterDO> orderInfoList){
        int orgId = orderInfoList.get(0).getFuserid();
        // 目前只有江苏省中西医需要返回详情数据
        boolean withDetail = OrgEnum.JIANG_SHU_SHENG_ZHONG_XI_YI_JIE_HE_YI_YUAN.getValue() == orgId;
        List<Integer> appIdList = orderInfoList.stream()
                .filter(orderMasterDO -> OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType())
                || OrderTypeEnum.CLINICAL_PURCHASE_ORDER.getCode().equals(orderMasterDO.getOrderType()))
                .map(OrderMasterDO::getFtbuyappid)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, ApplicationMasterDTO> appIdMasterMap = New.emptyMap();
        if (CollectionUtils.isNotEmpty(appIdList)) {
            ApplicationQueryDTO applicationQueryDTO = new ApplicationQueryDTO();
            applicationQueryDTO.setApplyIds(appIdList.stream().map(Integer::longValue).collect(toList()));
            applicationQueryDTO.setWithDetail(withDetail);
            List<ApplicationMasterDTO> applicationMasterDTOList = applicationBaseClient.findByMasterId(applicationQueryDTO);
            appIdMasterMap = DictionaryUtils.toMap(applicationMasterDTOList, ApplicationMasterDTO::getId, Function.identity());
        }
        return appIdMasterMap;
    }

    /**
     * 构建竞价或采购日志
     * @param orgCode 机构代码
     * @param orderPrintDataItemDTO 打印数据
     * @param orderPurchaseApproveLogList 采购/竞价日志
     * @param purchaseApprovalLogPrintList 最终打印的日志列表
     */
    private void constructBidOrPurchaseApprovalLog(String orgCode, OrderPrintDataItemDTO orderPrintDataItemDTO, List<OrderPurchaseApprovalLogDTO> orderPurchaseApproveLogList, List<OrderPrintApprovalLogDTO> purchaseApprovalLogPrintList, Integer orderType, Map<Integer, String> userIdRoleNameMap) {
        // 采购审批日志，默认一级，特殊单位特殊处理
        int approvalPrintLevel = ApproveLevelEnum.LEVEL1.getValue();
        // 福州皮肤病医院需要5级审批
        if (OrgEnum.FU_ZHOU_PI_FU_BING_FANG_ZHI_YUAN.getCode().equals(orgCode)) {
            approvalPrintLevel = 5;
        }else if(OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE.getCode().equals(orgCode)){
            approvalPrintLevel = 3;
        }
        orderPrintDataItemDTO.setOrderApprovalName(StringUtils.EMPTY);
        orderPrintDataItemDTO.setOrderSecondApprovalName(StringUtils.EMPTY);
        orderPrintDataItemDTO.setOrderThirdApprovalName(StringUtils.EMPTY);
        final int finalApprovalPrintLevel = approvalPrintLevel;
        List<OrderPrintApprovalLogDTO> logDTOList = new ArrayList<>(orderPurchaseApproveLogList.size());
        // 提交审批日志
        OrderPurchaseApprovalLogDTO submitPurchaseLog = null;
        for(OrderPurchaseApprovalLogDTO log : orderPurchaseApproveLogList){
            OrderPrintApprovalLogDTO printDTO = new OrderPrintApprovalLogDTO();
            printDTO.setDate(log.getDateTimeStamp());
            printDTO.setApproverUserId(log.getApproverUserId());
            printDTO.setApprover(log.getApprover());
            printDTO.setOperate(log.getOperate());
            printDTO.setOperateComment(log.getOperateComment());
            printDTO.setApproveLevel(log.getApproveLevel());
            printDTO.setResult(log.getResult());
            printDTO.setApprovePhotoUrl(log.getElectronicSignUrl());
            printDTO.setApproverUserRole(userIdRoleNameMap.get(log.getApproverUserId()));
            purchaseApprovalLogPrintList.add(printDTO);
            logDTOList.add(printDTO);
            // 历史遗留原因，用硬编码"审批通过"判断是否审核通过
            if("审批通过".equals(log.getResult())){
                if (log.getApproveLevel() == finalApprovalPrintLevel){
                    orderPrintDataItemDTO.setOrderApprovalName(log.getApprover());
                }
                if (log.getApproveLevel() == 2){
                    orderPrintDataItemDTO.setOrderSecondApprovalName(log.getApprover());
                }
                if (log.getApproveLevel() == 3){
                    orderPrintDataItemDTO.setOrderThirdApprovalName(log.getApprover());
                }
            }else if("提交审批".equals(log.getResult()) && log.getApproveLevel() == 0){
                submitPurchaseLog = log;
                orderPrintDataItemDTO.setApplicationSubmitApprovalTime(log.getDateTimeStamp());
            }
        }

        //柳州市工人医院
        //取值改为操作人在根部门的角色名称，若存在多个角色时，则分号显示
        //【一级审批通过/一级初审通过/一级初审自动通过/一级终审/一级终审自动通过】  默认只取【项目负责人】
        //若角色为【医院管理员】时，则无需返回
        //只取最新的审批日志
        if(OrgEnum.LIU_ZHOU_SHI_GONG_REN_YI_YUAN.getCode().equals(orgCode)){
            Iterator<OrderPrintApprovalLogDTO> iterator = logDTOList.iterator();
            while (iterator.hasNext()) {
                OrderPrintApprovalLogDTO log = iterator.next();
                if(Objects.isNull(log.getApproveLevel()) || log.getApproveLevel() == 0){
                    iterator.remove();
                    continue;
                }
                //先清空避免脏数据
                log.setApproverUserRole(null);
                if (log.getApproveLevel() == 1) {
                    log.setApproverUserRole(RoleConstant.PROJECT_LEADER);
                    continue;
                }
                List<RoleDTO> rootDeptRoles = userDepartmentRoleRpcServiceClient.getRootDeptRoles(OrgEnum.LIU_ZHOU_SHI_GONG_REN_YI_YUAN.getValue(), log.getApproverUserId());
                String approveUserRole = rootDeptRoles.stream()
                        .map(RoleDTO::getName)
                        .filter(roleName -> !RoleConstant.ORG_ADMINISTRATOR.equals(roleName))
                        .collect(Collectors.joining(";"));
                log.setApproverUserRole(approveUserRole);
            }
        }
        orderPrintDataItemDTO.setPurchaseOrBidApprovalLogList(logDTOList);
        if(OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderType)){
            orderPrintDataItemDTO.setSubmitPurchaseLog(ApprovalLogTranslator.orderPurchaseApprovalLog2PrintLog(submitPurchaseLog));
            orderPrintDataItemDTO.setPurchaseFlatLog(ApprovalLogTranslator.orderPurchaseApprovalLog2FlatListDTO(orderPurchaseApproveLogList));
        }
    }

    /**
     * 构建验收审批日志
     * @param orderApprovalLogList 订单审批日志
     * @param purchaseApprovalLogPrintList 最终展示日志
     */
    private void constructAcceptApprovalLog(OrderPrintDataItemDTO orderPrintDataItemDTO, List<OrderApprovalLogDTO> orderApprovalLogList, List<OrderPrintApprovalLogDTO> purchaseApprovalLogPrintList){
        List<OrderPrintApprovalLogDTO> logDTOList = new ArrayList<>(orderApprovalLogList.size());
        orderApprovalLogList.stream().sorted(Comparator.comparingInt(OrderApprovalLogDTO::getId)).forEach(log -> {
            OrderPrintApprovalLogDTO logDTO =  ApprovalLogTranslator.acceptApproveLog2PrintLog(log);
            purchaseApprovalLogPrintList.add(logDTO);
            logDTOList.add(logDTO);
        });
        orderPrintDataItemDTO.setAcceptApproveLogList(logDTOList);
        // 前面可能不是获取最后一次通过的日志，这里过滤一下
        if(CollectionUtils.isNotEmpty(orderApprovalLogList)){
            int lastRejectIndex = -1;
            for (int i = orderApprovalLogList.size() - 1; i > -1; i--) {
                if (OrderApprovalEnum.REJECT.getValue().equals(orderApprovalLogList.get(i).getApproveStatus())) {
                    lastRejectIndex = i;
                    break;
                }
            }
            // 截取成功的日志返回
            List<OrderApprovalLogDTO> lastApprovalLogList = orderApprovalLogList.subList(lastRejectIndex + 1, orderApprovalLogList.size());
            OrderApprovalLogDTO acceptLog = lastApprovalLogList.stream().filter(log->OrderApprovalEnum.RECEIPT.getValue().equals(log.getApproveStatus())).findFirst().orElse(null);
            orderPrintDataItemDTO.setSubmitAcceptLog(ApprovalLogTranslator.acceptApproveLog2PrintLog(acceptLog));
            orderPrintDataItemDTO.setOrderApproveSuccessLog(ApprovalLogTranslator.orderAcceptApproveLog2FlatListDTO(lastApprovalLogList));
        }
    }

    /**
     * 订单主表大部分数据的处理
     * @param orderPrint 最终展示数据
     * @param orderItem 订单数据
     */
    private void constructOrderMasterData(OrderPrintDataItemDTO orderPrint, OrderMasterDO orderItem, List<OrderDetailDO> orderDetailDOList) {
        orderPrint.setOrderId(orderItem.getId());
        orderPrint.setOrderNo(orderItem.getForderno());
        orderPrint.setApplicationId(orderItem.getFtbuyappid());
        orderPrint.setBuyerName(orderItem.getFbuyername());
        orderPrint.setSuppCode(orderItem.getFsuppcode());
        orderPrint.setSuppName(orderItem.getFsuppname());
        orderPrint.setInvoiceTitleId(orderItem.getInvoiceTitleId());
        // 送货单信息
        orderPrint.setBuyerContactMan(orderItem.getFbuyercontactman());
        orderPrint.setReceiverPhone(orderItem.getFbuyertelephone());
        orderPrint.setReceiverAddress(orderItem.getFbiderdeliveryplace());
        // 减去退货后的价格
        BigDecimal amountTotal = orderItem.getForderamounttotal();
        orderPrint.setTotalPrice(amountTotal);
        BigDecimal returnAmount = orderItem.getReturnAmount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderItem.getReturnAmount());
        orderPrint.setTotalPriceAfterReturn(amountTotal.subtract(returnAmount));
        BigDecimal quantityTotalAfterReturn = orderDetailDOList.stream().map(orderDetailDO -> orderDetailDO.getFquantity()
                .subtract(orderDetailDO.getFcancelquantity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderPrint.setTotalQuantityAfterReturn(quantityTotalAfterReturn);
        // 验收时图片
        orderPrint.setReceivePicUrls(New.emptyList());
        if (orderItem.getReceivePicUrls() != null) {
            orderPrint.setReceivePicUrls(New.list(orderItem.getReceivePicUrls().split(";")));
        }
        orderPrint.setOrderSpecies(orderItem.getSpecies().intValue());
        orderPrint.setOrderReceiptDate(orderItem.getFlastreceivedate() != null ? orderItem.getFlastreceivedate().getTime() : null);
        orderPrint.setReceiverName(orderItem.getFlastreceiveman());
        orderPrint.setDepartmentName(orderItem.getFbuydepartment());
        orderPrint.setOrgId(orderItem.getFuserid());
        orderPrint.setOrgName(orderItem.getFusername());
        orderPrint.setOrderDate(orderItem.getForderdate() != null ? orderItem.getForderdate().getTime() : null);
        // 增加课题组父级名称（当前仅陆军军医）
        orderPrint.setDepartmentParentName(this.getDeptParentName(orderItem));
        // 增加课题组向上两级部门名称
        orderPrint.setDepartmentGrandParentName(this.getDeptGrandParentName(orderItem));
    }

    /**
     * 组装订单商品明细
     *
     * @param orderPrint      打印结果
     * @param orderDetailList 订单商品DO数组
     */
    private void packageOrderDetailInfo(OrderPrintDataItemDTO orderPrint, List<OrderDetailDO> orderDetailList, Map<Integer, List<OrderUniqueBarCodeDTO>> detailIdBatchesMap) {
        List<OrderDetailPrintDTO> orderDetailPrintList = orderDetailList.stream().map(detail->{
                    OrderDetailPrintDTO orderDetailPrintDTO = OrderDetailTranslator.doToOrderDetailPrintDTO(detail);
                    List<OrderUniqueBarCodeDTO> matchBatches = detailIdBatchesMap.get(detail.getId());
                    if(matchBatches != null){
                        orderDetailPrintDTO.setOrderBatchesPrintDTOList(matchBatches.stream().map(this::orderUniqueBarcodeDTO2PrintDTO).collect(toList()));
                    }
                    return orderDetailPrintDTO;
                })
                .filter(detail-> detail.getQuantity() != null && BigDecimal.ZERO.compareTo(detail.getQuantity()) != 0)
                .collect(toList());
        orderPrint.setOrderDetailPrintList(orderDetailPrintList);
    }

    /**
     * 组装采购单数据
     * @param orderPrint 打印结果
     * @param applicationMasterDTO 采购单数据
     */
    private void constructAppData(OrderPrintDataItemDTO orderPrint, ApplicationMasterDTO applicationMasterDTO){
        orderPrint.setPurchaseNote(applicationMasterDTO.getApplyInfo());
        orderPrint.setApplicationNo(applicationMasterDTO.getApplyNumber());
        orderPrint.setApplicationCreateTime(applicationMasterDTO.getCreateTime().getTime());
        List<ApplyInfoDTO> applyInfoDTOList = applicationMasterDTO.fetchApplyInfo();
        //构造采购单申请说明
        List<ApplyInstructionsVO> applyInstructionsVOList = this.constructApplyInstruction(applyInfoDTOList);
        orderPrint.setApplyInfoList(applyInstructionsVOList);
        List<ApplicationDetailDTO> appDetailList = applicationMasterDTO.getDetails();
        if(CollectionUtils.isNotEmpty(appDetailList)){
            // 需要获取并获取到采购单详情数据的，才需要设值
            Map<Long, ApplicationDetailDTO> productIdAppDetailMap = DictionaryUtils.toMap(applicationMasterDTO.getDetails(), ApplicationDetailDTO::getProductSn, Function.identity());
            for(OrderDetailPrintDTO printDetail : orderPrint.getOrderDetailPrintList()){
                ApplicationDetailDTO matchAppDetail = productIdAppDetailMap.get(printDetail.getProductId());
                if(matchAppDetail != null){
                    printDetail.setInventoryQuantity(matchAppDetail.getDetailExtend() == null ? null : matchAppDetail.getDetailExtend().getMeasurementNum());
                }
            }
        }
    }

    /**
     * 构造采购申请说明
     *
     * @param applyInfoDTOList
     * @return
     */
    private List<ApplyInstructionsVO> constructApplyInstruction(List<ApplyInfoDTO> applyInfoDTOList){
        if(CollectionUtils.isEmpty(applyInfoDTOList)){
            return null;
        }
        ArrayList<ApplyInstructionsVO> applyInstructionsVOList = new ArrayList<>(applyInfoDTOList.size());
        for(ApplyInfoDTO applyInfoDTO : applyInfoDTOList){
            ApplyInstructionsVO applyInstructionsVO = new ApplyInstructionsVO();
            applyInstructionsVO.setUniSort(applyInfoDTO.getUniSort());
            applyInstructionsVO.setTitle(applyInfoDTO.getTitle());
            applyInstructionsVO.setValue(applyInfoDTO.getValue());
            applyInstructionsVOList.add(applyInstructionsVO);
        }
        return applyInstructionsVOList;
    }

    /**
     * 二维码条形码相关
     * @param orderPrint 最终展示的大隐书局
     * @param orderItem 订单数据
     */
    private void constructBarCode(OrderPrintDataItemDTO orderPrint, OrderMasterDO orderItem) {
        if(OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getValue() == orderPrint.getOrgId()){
            orderPrint.setOrderNoQrCode(getPrintCommonDataService.getQrCode("rj-" + orderItem.getForderno()));
        }else {
            orderPrint.setOrderNoQrCode(getPrintCommonDataService.getQrCode(orderItem.getForderno()));
        }

        orderPrint.setWechatOrderDetailQrCode(getPrintCommonDataService.getQrCode(String.format(WECHAT_ORDER_DETAIL_URL, orderItem.getId())));

        orderPrint.setBarCode(getPrintCommonDataService.getBarCode(orderItem.getForderno()));
    }

    /**
     * 组装订单经费卡信息
     *
     * @param orderFundCardList 订单关联经费卡数组
     * @param orgCode           机构组织code
     * @param cardLevel         经费卡等级
     * @param fundTypeMap       经费类型映射
     */
    private List<OrderFundCardPrintDTO> newPackageOrderFundCardInfo(List<RefFundcardOrderDTO> orderFundCardList, int cardLevel, Integer orgId, String orgCode, Map<Integer, String> fundTypeMap, Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap) {
        if (CollectionUtils.isEmpty(orderFundCardList)) {
            return New.emptyList();
        }
        List<OrderFundCardPrintDTO> fundCardPrintDTOList = new ArrayList<>();
        List<String> cardIdList = orderFundCardList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
        List<FundCardDTO> fundProjectList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
        if (CollectionUtils.isEmpty(fundProjectList)) {
            return New.emptyList();
        }

        //配置1级经费卡的和中山五院的都是只取第一级数据打印
        //单位配置使用的是一级经费卡
        for (FundCardDTO fundProjectDto : fundProjectList) {
            if(cardIdList.isEmpty()){
                break;
            }
            if(cardIdList.contains(fundProjectDto.getId())){
                OrderFundCardPrintDTO fundCardItem = handleFundCardItem(fundProjectDto, orgId, buyerIdIdentityMap);
                // 经费类型
                fundCardItem.setFundType(fundTypeMap.get(fundProjectDto.getFundType()));
                //经费卡号和项目号一致
                fundCardItem.setCardNo(fundProjectDto.getCode());
                fundCardPrintDTOList.add(fundCardItem);
                cardIdList.remove(fundProjectDto.getId());
                continue;
            }
            // 获取二级经费卡
            List<FundCardDTO> secondFundCardList = fundProjectDto.getFundCardDTOs();
            if (CollectionUtils.isNotEmpty(secondFundCardList)) {
                // 二级卡
                for (FundCardDTO secondFundCard : secondFundCardList) {
                    if(cardIdList.contains(secondFundCard.getId())){
                        OrderFundCardPrintDTO fundCardItem = handleFundCardItem(fundProjectDto, orgId, buyerIdIdentityMap);
                        fundCardItem.setCardNo(secondFundCard.getCode());
                        // 经费类型
                        fundCardItem.setFundType(fundTypeMap.get(secondFundCard.getFundType()));
                        fundCardPrintDTOList.add(fundCardItem);
                        cardIdList.remove(secondFundCard.getId());
                        break;
                    }
                    List<FundCardDTO> thirdFundCardList = secondFundCard.getFundCardDTOs();
                    // 三级卡，因为没有三级卡参数，先这样处理
                    for(FundCardDTO thirdFundCard : thirdFundCardList){
                        if(cardIdList.contains(thirdFundCard.getId())){
                            OrderFundCardPrintDTO fundCardItem = handleFundCardItem(fundProjectDto, orgId, buyerIdIdentityMap);
                            fundCardItem.setCardNo(secondFundCard.getCode());
                            // 经费类型
                            fundCardItem.setFundType(fundTypeMap.get(secondFundCard.getFundType()));
                            fundCardPrintDTOList.add(fundCardItem);
                            cardIdList.remove(secondFundCard.getId());
                            break;
                        }
                    }
                }
            }
        }
        return fundCardPrintDTOList;
    }

    /**
     * 处理经费卡详情信息里的项目编号，名称，负责人
     *
     * @param fundProjectDto 经费卡数据
     * @return 经费卡大隐书局
     */
    private OrderFundCardPrintDTO handleFundCardItem(FundCardDTO fundProjectDto, Integer orgId, Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap) {
        OrderFundCardPrintDTO fundCardItem = new OrderFundCardPrintDTO();
        fundCardItem.setProjectCode(fundProjectDto.getCode());
        fundCardItem.setProjectName(fundProjectDto.getName());
        List<FundCardManagerDTO> fundCardManagerDTOList = fundProjectDto.getFundCardManagerDTOs();
        if (CollectionUtils.isNotEmpty(fundCardManagerDTOList)) {
            fundCardItem.setFundManager(fundCardManagerDTOList.stream().map(FundCardManagerDTO::getManagerName).filter(StringUtils::isNotBlank).collect(Collectors.joining("，")));
            List<Integer> notInitFundCardManagerIdList = fundCardManagerDTOList.stream().map(FundCardManagerDTO::getUserId)
                    .filter(userId-> userId != -1 && buyerIdIdentityMap.get(userId) == null).distinct().collect(toList());
            List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(notInitFundCardManagerIdList, orgId);
            userBaseInfoDTOList.forEach(user->buyerIdIdentityMap.put(user.getId(), user));
            String fundManagerMobile = fundCardManagerDTOList.stream().map(fundCardManagerDTO -> buyerIdIdentityMap.get(fundCardManagerDTO.getUserId())).filter(Objects::nonNull)
                    .map(UserBaseInfoDTO::getMobile).collect(Collectors.joining("，"));
            fundCardItem.setFundManagerPhone(fundManagerMobile);
        }
        return fundCardItem;
    }

    /**
     * 设置供应商用户到orderPrint
     *
     * @param orderMasterDO 订单主表数据
     */
    private SupplierUserDTO cacheAndGetSuppUser(OrderMasterDO orderMasterDO, Map<Integer, List<UserAccountDTO>> suppIdUserListMap) {
        List<UserAccountDTO> suppUserList = suppIdUserListMap.get(orderMasterDO.getFsuppid());
        if (suppUserList == null) {
            // 获取供应商暂无批量接口，待优化... 
            suppUserList = suppClient.querySuppAccountList(orderMasterDO.getFsuppid(), null);
            suppIdUserListMap.put(orderMasterDO.getFsuppid(), suppUserList);
        }
        return suppUserList.stream().filter(Objects::nonNull)
                .filter(supp -> supp.getId() != null && supp.getId().toString().equals(orderMasterDO.getFconfirmmanid()))
                .map(supp -> {
                    SupplierUserDTO suppUserDTO = new SupplierUserDTO();
                    suppUserDTO.setName(supp.getName());
                    suppUserDTO.setPhone(supp.getMobile());
                    return suppUserDTO;
                }).findFirst().orElse(null);
    }

    /**
     * 获取供应商的单位负责人信息（带缓存，减少rpc次数
     * @param orderMasterDO 订单信息
     * @param suppIdTradeClerkCache 缓存
     * @return 供应商信息
     */
    private SupplierUserDTO cacheAndGetTradeClerk(OrderMasterDO orderMasterDO, Map<Integer, SupplierUserDTO> suppIdTradeClerkCache){
        Integer suppId = orderMasterDO.getFsuppid();
        if(suppIdTradeClerkCache.containsKey(suppId)){
            return suppIdTradeClerkCache.get(suppId);
        }
        List<UserDTO> tradeClerkList = suppClient.getSuppliersOrgBusinessUser(suppId, orderMasterDO.getFuserid());
        if(CollectionUtils.isEmpty(tradeClerkList)){
            suppIdTradeClerkCache.put(suppId, null);
            return null;
        }
        UserDTO  firstTradeClerk = tradeClerkList.stream().filter(tradeClerk-> UserTagEnum.ORG_ADMIN.name().equals(tradeClerk.getTag())).findFirst().orElseGet(()->tradeClerkList.get(0));
        SupplierUserDTO supplierUserDTO = new SupplierUserDTO();
        supplierUserDTO.setName(firstTradeClerk.getRealName());
        supplierUserDTO.setPhone(firstTradeClerk.getMobile());
        return supplierUserDTO;
    }

    /**
     * 获取订单id-验收审批日志映射
     * @param orgId 单位id
     * @param orderIdList 订单id列表
     * @return 映射
     */
    private Map<Integer, List<OrderApprovalLogDTO>> getOrderIdApprovalLogMap(int orgId, List<Integer> orderIdList) {
        return getPrintCommonDataService.getOrderIdApprovalLogMap(orgId, orderIdList);
    }

    private Map<Integer, List<OrderPurchaseApprovalLogDTO>> getCustomPurchaseOrBidLog(int orgId, List<OrderMasterDO> orderInfoList) {
        if (orgId == OrgEnum.FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue()) {
            // 佛山市妇幼保健院特殊需求，如为竞价仅返回提交竞价初审、初审通过、初选通过、终审通过的竞价日志
            final List<String> filterBidOperationList = New.list(OperationEnum.SUBMIT_BEGIN_APPROVAL.getName(), OperationEnum.BEGIN_APPROVAL_PASS.getName(),
                    OperationEnum.SUBMIT_FINAL_APPROVAL.getName(), OperationEnum.FINAL_APPROVAL_PASS.getName());
            return getPrintCommonDataService.getPurchaseOrBidLog(orderInfoList, filterBidOperationList, true);
        }
        if(OrgEnum.LIU_ZHOU_SHI_GONG_REN_YI_YUAN.getValue() == orgId){
            // 柳州市工人医院取最新的审批日志
            return getPrintCommonDataService.getPurchaseOrBidLog(orderInfoList, null, true);
        }
        return getPrintCommonDataService.getPurchaseOrBidLog(orderInfoList, null, false);
    }

    /**
     * 获取当前部门的父级部门（目前仅陆军军医大，线下送货单需求）
     *
     * @param orderMasterDO 订单主表数据
     * @return 展示的根部门名
     */
    private String getDeptParentName(OrderMasterDO orderMasterDO) {
        boolean isRootDept = orderMasterDO.getDeptParentId() == null || "根部门".equals(orderMasterDO.getDeptParentName());
        // 陆军医,杭州红十字医院个性化
        List<Integer> notShowRootDeptNameOrgIdList = New.list(OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue(), OrgEnum.HANG_ZHOU_SHI_HONG_SHI_ZI_HUI_YI_YUAN.getValue());
        if (isRootDept && notShowRootDeptNameOrgIdList.contains(orderMasterDO.getFuserid())) {
            // 个性化需求：根部门不展示，其他的需要获取父级部门的名称
            return StringUtils.EMPTY;
        }
        return orderMasterDO.getDeptParentName();
    }

    /**
     * 获取当前采购部门向上两级的部门名称
     *
     * @param orderMasterDO 订单主表数据
     * @return 向上两级部门名称，如果不存在则返回空字符串
     */
    private String getDeptGrandParentName(OrderMasterDO orderMasterDO) {
        Integer buyDepartmentId = orderMasterDO.getFbuydepartmentid();
        if (Objects.isNull(buyDepartmentId)) {
            return StringUtils.EMPTY;
        }

        // 调用RPC接口获取当前部门的所有父级部门（包含当前部门）
        List<MiniDepartmentDTO> allDepartments = userClient.findParentsOrderToRoot(buyDepartmentId);

        if (CollectionUtils.isEmpty(allDepartments)) {
            return StringUtils.EMPTY;
        }

        // 将部门列表转换为Map
        Map<Integer, MiniDepartmentDTO> deptId2DTOMap = allDepartments.stream()
                .collect(Collectors.toMap(MiniDepartmentDTO::getId, Function.identity()));

        // 找到当前采购部门
        MiniDepartmentDTO currentDept = deptId2DTOMap.get(buyDepartmentId);
        if (Objects.isNull(currentDept)) {
            return StringUtils.EMPTY;
        }

        // 找到父级部门（管理部门）
        Integer parentId = currentDept.getParentId();
        if (Objects.isNull(parentId)) {
            return StringUtils.EMPTY;
        }

        MiniDepartmentDTO parentDept = deptId2DTOMap.get(parentId);
        if (Objects.isNull(parentDept)) {
            return StringUtils.EMPTY;
        }

        // 找到祖父级部门（管理部门的管理部门，即向上两级）
        Integer grandParentId = parentDept.getParentId();
        if (Objects.isNull(grandParentId)) {
            return StringUtils.EMPTY;
        }

        MiniDepartmentDTO grandParentDept = deptId2DTOMap.get(grandParentId);
        return Objects.nonNull(grandParentDept) ? grandParentDept.getName() : StringUtils.EMPTY;
    }

    /**
     * 获取订单id-发票映射
     * @param orgId 机构id
     * @param orderMasterInfoList 订单数据
     * @return 映射
     */
    private Map<Integer, List<OrderInvoiceDTO>> getOrderIdInvoiceMap(Integer orgId, List<OrderMasterDO> orderMasterInfoList) {
        List<Long> orderIdLongList = orderMasterInfoList.stream().map(OrderMasterDO::getId).map(Long::valueOf).collect(Collectors.toList());
        InvoiceQueryDTO query = new InvoiceQueryDTO();
        query.setOrgId(Long.valueOf(orgId));
        query.setSourceIds(orderIdLongList);
        query.setInvoiceType(InvoiceTypeEnum.ORDER);
        List<OrderInvoiceInfoVO> invoiceVOList = invoiceClient.findInvoiceVOList(query);

        return this.getOrderIdInvoiceMap(invoiceVOList);
    }

    private OrderInvoiceDTO invoiceData2PrintDTO(OrderInvoiceInfoVO invoiceVO){
        if(invoiceVO == null){
            return null;
        }
        OrderInvoiceDTO invoiceDTO = new OrderInvoiceDTO();
        invoiceDTO.setAmount(invoiceVO.getAmount());
        invoiceDTO.setBankName(invoiceVO.getBankName());
        invoiceDTO.setBankNo(invoiceVO.getBankNo());
        invoiceDTO.setDrawer(invoiceVO.getDrawer());
        invoiceDTO.setInvoiceCode(invoiceVO.getInvoiceCode());
        invoiceDTO.setInvoiceNo(invoiceVO.getInvoiceNo());
        invoiceDTO.setIssueDate(invoiceVO.getIssueDateTimeStamp());
        invoiceDTO.setPicturePathList(invoiceVO.getPicturePathList());
        invoiceDTO.setRemark(invoiceVO.getRemark());
        return invoiceDTO;
    }

    private Map<Integer, List<OrderInvoiceDTO>> getOrderIdInvoiceMap(List<OrderInvoiceInfoVO> invoiceVOList) {
        if(CollectionUtils.isEmpty(invoiceVOList)){
            return New.emptyMap();
        }
        Map<Integer, List<OrderInvoiceDTO>> orderIdInvoiceMap = new HashMap<>();
        for (OrderInvoiceInfoVO orderInvoice : invoiceVOList) {
            List<Integer> orderIdList = orderInvoice.getOrderIdList();
            if (CollectionUtils.isEmpty(orderIdList)) {
                continue;
            }
            for (Integer orderId : orderIdList) {
                if (orderIdInvoiceMap.containsKey(orderId)) {
                    orderIdInvoiceMap.get(orderId).add(this.invoiceData2PrintDTO(orderInvoice));
                } else {
                    orderIdInvoiceMap.put(orderId, New.list(this.invoiceData2PrintDTO(orderInvoice)));
                }
            }
        }
        return orderIdInvoiceMap;
    }

    private void setInvoiceData(OrderPrintDataItemDTO orderPrint, List<OrderInvoiceDTO> orderInvoiceDTOList, StringBuilder sb) {
        if (CollectionUtils.isNotEmpty(orderInvoiceDTOList)) {
            orderPrint.setInvoiceList(orderInvoiceDTOList);
            orderInvoiceDTOList.forEach(orderInvoiceDTO -> {
                sb.append(StringUtils.defaultIfBlank(orderInvoiceDTO.getInvoiceCode(), StringUtils.EMPTY));
                if (StringUtils.isNotBlank(orderInvoiceDTO.getInvoiceCode()) && StringUtils.isNotBlank(orderInvoiceDTO.getInvoiceNo())) {
                    // 有code和no，才拼接分隔符
                    sb.append("-");
                }
                sb.append(orderInvoiceDTO.getInvoiceNo()).append(",");
            });
            orderPrint.setAllInvoiceNo(sb.length() > 0 ? sb.substring(0, sb.length() - 1) : StringUtils.EMPTY);
            sb.setLength(0);
        }
    }

    /**
     * 获取用户在单位中的所有角色名，用";"拼接
     * @param userIdList 用户id
     * @param orgId 单位
     * @return 用户id-角色名映射
     */
    private Map<Integer, String> getUserIdRoleNameMap(Set<Integer> userIdList, Integer orgId){
        if(CollectionUtils.isEmpty(userIdList)){
            return New.emptyMap();
        }
        List<UserInDepartmentRoleDTO> userRoleList = New.list();
        for(Integer userId : userIdList){
            List<UserInDepartmentRoleDTO> userInDepartmentRoleDTOList = userClient.getDepartmentRoleByUserIdAndOrgId(userId, orgId);
            userRoleList.addAll(userInDepartmentRoleDTOList);
        }
        if(CollectionUtils.isEmpty(userRoleList)){
            return New.emptyMap();
        }
        List<Integer> allRoleIdList = userRoleList.stream().map(UserInDepartmentRoleDTO::getRoleId).distinct().collect(toList());
        List<RoleDTO> roleDTOList = roleRpcClient.findByIds(allRoleIdList);
        Map<Integer, String> roleIdNameMap = DictionaryUtils.toMap(roleDTOList, RoleDTO::getId, RoleDTO::getName);
        Map<Integer, Set<Integer>> userIdRoleIdSetMap = userRoleList.stream().collect(Collectors.groupingBy(UserInDepartmentRoleDTO::getUserId, Collectors.mapping(UserInDepartmentRoleDTO::getRoleId, Collectors.toSet())));
        Map<Integer, String> userIdRoleNameMap = new HashMap<>(userIdRoleIdSetMap.size());
        for(Map.Entry<Integer, Set<Integer>> entry : userIdRoleIdSetMap.entrySet()){
            userIdRoleNameMap.put(entry.getKey(), entry.getValue().stream().map(roleIdNameMap::get).filter(Objects::nonNull).collect(Collectors.joining(";")));
        }
        return userIdRoleNameMap;
    }

    private OrderBatchesPrintDTO orderUniqueBarcodeDTO2PrintDTO(OrderUniqueBarCodeDTO barCodeDTO){
        return new OrderBatchesPrintDTO()
                .setBatches(barCodeDTO.getBatches())
                .setTotal(barCodeDTO.getTotal())
                .setManufacturer(barCodeDTO.getManufacturer())
                .setProductionDate(DateUtils.format("yyyy-MM-dd", barCodeDTO.getProductionDate()))
                .setExpiration(barCodeDTO.getExpiration())
                .setDetailId(barCodeDTO.getOrderDetailId());
    }
}
