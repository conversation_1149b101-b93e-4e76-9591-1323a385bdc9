package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.order.api.base.other.dto.OrderBankDataDTO;
import com.ruijing.store.order.api.base.other.service.OrderBankSnapshotRpcService;
import com.ruijing.store.order.rpc.client.OrderBankSnapshotClient;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: liwenyu
 * @createTime: 2023-11-30 11:40
 * @description:
 **/
@MSharpService
public class OrderBankSnapshotRpcServiceImpl implements OrderBankSnapshotRpcService {

    @Resource
    private OrderBankSnapshotClient orderBankSnapshotClient;

    @Override
    @ServiceLog
    public RemoteResponse<Boolean> updateOrderBankData(List<OrderBankDataDTO> orderBankDataDTOList) {
        BusinessErrUtil.isTrue(orderBankDataDTOList.stream().allMatch(item-> Objects.nonNull(item.getOrderId())), "订单号id不可空");
        orderBankSnapshotClient.updateList(orderBankDataDTOList);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog
    public RemoteResponse<Boolean> saveOrderBankData(List<OrderBankDataDTO> orderBankDataDTOList) {
        orderBankSnapshotClient.saveList(orderBankDataDTOList);
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog
    public RemoteResponse<List<OrderBankDataDTO>> getOrderBankData(List<Integer> orderIdList) {
        return RemoteResponse.success(orderBankSnapshotClient.listByOrderId(orderIdList));
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> deleteOrderBankData(List<Integer> orderIdList) {
        return RemoteResponse.success(orderBankSnapshotClient.deleteByOrderIdList(orderIdList));
    }
}
