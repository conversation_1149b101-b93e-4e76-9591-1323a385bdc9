package com.ruijing.store.order.gateway.buyercenter.vo.other;

import com.ruijing.fundamental.api.annotation.ModelProperty;
import com.ruijing.shop.srm.api.dto.shopgasbottle.GasDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.StringJoiner;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-04-17 10:16
 * @description:
 */
public class GasBottleVO implements Serializable {

    private static final long serialVersionUID = 8001689350135256546L;

    @ModelProperty("订单商品码，一物一码下才返回")
    private String orderProductBarcode;

    @ModelProperty("气瓶ID")
    private Integer gasBottleId;

    @ModelProperty("供应商id")
    private Integer suppId;
    @ModelProperty("气瓶编号")
    private String gasBottleCode;
    @ModelProperty("气瓶类型")
    private String gasBottleType;
    @ModelProperty("气体id,新增编辑的时候传,接口/gasBottle/queryGasList返回的id")
    private Integer gasId;
    @ModelProperty("气体(气瓶颜色标志)")
    private GasVO gasDTO;
    @ModelProperty("品牌")
    private String brand;
    @ModelProperty("材质")
    private String material;
    @ModelProperty("出厂日期")
    private Date manufactureDate;
    @ModelProperty("上次维护日期")
    private Date lastMaintenanceDate;
    @ModelProperty("下次维护日期")
    private Date nextMaintenanceDate;
    @ModelProperty("报废日期")
    private Date scrapDate;
    @ModelProperty("气瓶容量")
    private BigDecimal gasBottleCapacity;
    @ModelProperty("容量单位")
    private String gasBottleCapacityUnit;
    @ModelProperty("过期状态(1未过期2已过期3已报废)")
    private Integer expirationStatus;
    @ModelProperty("使用状态(1待使用2使用中3待回收4待回收审批5已回收)")
    private Integer usageStatus;
    @ModelProperty(value = "气瓶二维码")
    private String qrCode;

    public String getOrderProductBarcode() {
        return orderProductBarcode;
    }

    public GasBottleVO setOrderProductBarcode(String orderProductBarcode) {
        this.orderProductBarcode = orderProductBarcode;
        return this;
    }

    public Integer getGasBottleId() {
        return gasBottleId;
    }

    public GasBottleVO setGasBottleId(Integer gasBottleId) {
        this.gasBottleId = gasBottleId;
        return this;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public GasBottleVO setSuppId(Integer suppId) {
        this.suppId = suppId;
        return this;
    }

    public String getGasBottleCode() {
        return gasBottleCode;
    }

    public GasBottleVO setGasBottleCode(String gasBottleCode) {
        this.gasBottleCode = gasBottleCode;
        return this;
    }

    public String getGasBottleType() {
        return gasBottleType;
    }

    public GasBottleVO setGasBottleType(String gasBottleType) {
        this.gasBottleType = gasBottleType;
        return this;
    }

    public Integer getGasId() {
        return gasId;
    }

    public GasBottleVO setGasId(Integer gasId) {
        this.gasId = gasId;
        return this;
    }

    public GasVO getGasDTO() {
        return gasDTO;
    }

    public GasBottleVO setGasDTO(GasVO gasDTO) {
        this.gasDTO = gasDTO;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public GasBottleVO setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getMaterial() {
        return material;
    }

    public GasBottleVO setMaterial(String material) {
        this.material = material;
        return this;
    }

    public Date getManufactureDate() {
        return manufactureDate;
    }

    public GasBottleVO setManufactureDate(Date manufactureDate) {
        this.manufactureDate = manufactureDate;
        return this;
    }

    public Date getLastMaintenanceDate() {
        return lastMaintenanceDate;
    }

    public GasBottleVO setLastMaintenanceDate(Date lastMaintenanceDate) {
        this.lastMaintenanceDate = lastMaintenanceDate;
        return this;
    }

    public Date getNextMaintenanceDate() {
        return nextMaintenanceDate;
    }

    public GasBottleVO setNextMaintenanceDate(Date nextMaintenanceDate) {
        this.nextMaintenanceDate = nextMaintenanceDate;
        return this;
    }

    public Date getScrapDate() {
        return scrapDate;
    }

    public GasBottleVO setScrapDate(Date scrapDate) {
        this.scrapDate = scrapDate;
        return this;
    }

    public BigDecimal getGasBottleCapacity() {
        return gasBottleCapacity;
    }

    public GasBottleVO setGasBottleCapacity(BigDecimal gasBottleCapacity) {
        this.gasBottleCapacity = gasBottleCapacity;
        return this;
    }

    public String getGasBottleCapacityUnit() {
        return gasBottleCapacityUnit;
    }

    public GasBottleVO setGasBottleCapacityUnit(String gasBottleCapacityUnit) {
        this.gasBottleCapacityUnit = gasBottleCapacityUnit;
        return this;
    }

    public Integer getExpirationStatus() {
        return expirationStatus;
    }

    public GasBottleVO setExpirationStatus(Integer expirationStatus) {
        this.expirationStatus = expirationStatus;
        return this;
    }

    public Integer getUsageStatus() {
        return usageStatus;
    }

    public GasBottleVO setUsageStatus(Integer usageStatus) {
        this.usageStatus = usageStatus;
        return this;
    }

    public String getQrCode() {
        return qrCode;
    }

    public GasBottleVO setQrCode(String qrCode) {
        this.qrCode = qrCode;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GasBottleVO.class.getSimpleName() + "[", "]")
                .add("orderProductBarcode='" + orderProductBarcode + "'")
                .add("gasBottleId=" + gasBottleId)
                .add("suppId=" + suppId)
                .add("gasBottleCode='" + gasBottleCode + "'")
                .add("gasBottleType='" + gasBottleType + "'")
                .add("gasId=" + gasId)
                .add("gasDTO=" + gasDTO)
                .add("brand='" + brand + "'")
                .add("material='" + material + "'")
                .add("manufactureDate=" + manufactureDate)
                .add("lastMaintenanceDate=" + lastMaintenanceDate)
                .add("nextMaintenanceDate=" + nextMaintenanceDate)
                .add("scrapDate=" + scrapDate)
                .add("gasBottleCapacity=" + gasBottleCapacity)
                .add("gasBottleCapacityUnit='" + gasBottleCapacityUnit + "'")
                .add("expirationStatus=" + expirationStatus)
                .add("usageStatus=" + usageStatus)
                .add("qrCode='" + qrCode + "'")
                .toString();
    }
}
