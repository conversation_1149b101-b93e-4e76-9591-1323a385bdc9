package com.ruijing.store.order.base.core.service.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationRecordDTO;
import com.ruijing.store.electronicsign.api.dto.OperationListDTO;
import com.ruijing.store.electronicsign.api.enums.BusinessTypeEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.OrderApprovalLogTranslator;
import com.ruijing.store.order.business.enums.OmsFixDataEnum;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOperationLogVO;
import com.ruijing.store.order.rpc.client.ElectronicSignServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @description: 订单日志
 * @author: zhuk
 * @create: 2019-07-15 13:32
 **/
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
@Service
public class OrderApprovalLogServiceImpl implements OrderApprovalLogService {

    /**
     * 代表系统的操作id
     */
    private final static int SYSTEM_OPERATOR_ID = -1;

    /**
     * 代表系统的操作名
     */
    private final static String SYSTEM_OPERATOR_NAME = "系统";

    @Resource(name = "defaultIoExecutor")
    private Executor defaultIoExecutor;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;


    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    @Override
    public RemoteResponse insertOrderApprovalLog(OrderApprovalLogDTO orderApprovalLogDTO) {
        OrderApprovalLog orderApprovalLog = OrderApprovalLogTranslator.dtoToOrderApprovalLog(orderApprovalLogDTO);
        orderApprovalLogMapper.insert(orderApprovalLog);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void insertOrderApprovalLogList(List<OrderApprovalLogDTO> logs) {
        List<OrderApprovalLog> orderApprovalLog = OrderApprovalLogTranslator.dtoToOrderApprovalLog(logs);
        orderApprovalLogMapper.insertList(orderApprovalLog);
    }

    /**
     * 通过订单id列表找订单审批日志，包含详细名字和审批原因
     * @param request
     * @return
     */
    @Override
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
    public List<OrderApprovalLogDTO> findByOrderIdListAndStatus(OrderApprovalRequestDTO request) {
        Assert.notEmpty(request.getOrderIdList(), "订单id不能为空！");

        Set<Integer> queryTypeSet = request.getTypeList() == null ? New.set() : New.set(request.getTypeList());
        List<OrderApprovalEnum> orderApproveStatusList = request.getOrderApproveStatusList();
        if(CollectionUtils.isNotEmpty(orderApproveStatusList)){
            // 兼容旧配置
            queryTypeSet.addAll(orderApproveStatusList.stream().map(OrderApprovalEnum::getValue).collect(Collectors.toSet()));
        }

        final Set<Integer> valStatus = queryTypeSet;
        CompletableFuture<List<OrderApprovalLogDTO>> future = CompletableFuture.supplyAsync(() -> {
            // 查询审批日志
            return orderApprovalLogMapper.findByOrderIdInAndApproveStatusIn(request.getOrderIdList(), valStatus);
        }, defaultIoExecutor).thenCombineAsync(CompletableFuture.supplyAsync(() -> {
            // 查询订单信息
            return orderMasterMapper.findByIdIn(request.getOrderIdList());
        }, defaultIoExecutor), (approvalList, orderList) -> {
            // 审批人id信息
            List<Integer> userIdList = approvalList.stream().distinct().map(OrderApprovalLog::getOperatorId).collect(Collectors.toList());
            BusinessErrUtil.notEmpty(orderList, ExecptionMessageEnum.QUERY_FAILED_NO_ORDER_IN_ID);
            List<UserBaseInfoDTO> userList = userClient.getUserByIdsAndOrgId(userIdList, orderList.get(0).getFuserid());

            Map<Integer, String> userIdNameMap = DictionaryUtils.toMap(userList, UserBaseInfoDTO::getId, UserBaseInfoDTO::getName);
            List<OrderApprovalLogDTO> result = approvalList.stream()
                    .map(OrderApprovalLogTranslator::orderApprovalLogToDto)
                    .peek(o -> {
                        String operatorName = userIdNameMap.get(o.getOperatorId());
                        o.setOperatorName(operatorName);
                        if(SYSTEM_OPERATOR_ID == o.getOperatorId()){
                            o.setOperatorName(SYSTEM_OPERATOR_NAME);
                        }else {
                            o.setOperatorName(operatorName);
                        }
                    }).collect(Collectors.toList());

            return result;
        }, defaultIoExecutor);

        try {
            return future.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e);
        } catch (ExecutionException e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    /**
     * 条件查询审批日志,获取最后一次验收审批通过的记录
     *
     * @param request 请求数据
     * @return 日志
     */
    @Override
    public List<OrderApprovalLogDTO> getLastPassAcceptApproveLog(OrderApprovalRequestDTO request) {
        final List<Integer> acceptApprovalRelateEnumList = New.list(OrderApprovalEnum.REJECT.getValue(), OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.RECEIPT.getValue(), OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL.getValue());
        List<OrderApprovalLogDTO> wholeLogList = this.findByOrderIdListAndStatus(request);
        List<OrderApprovalLogDTO> acceptApproveLogList = wholeLogList.stream().filter(log -> acceptApprovalRelateEnumList.contains(log.getApproveStatus())).collect(Collectors.toList());
        List<OrderApprovalLogDTO> lastAcceptApprovalLog = this.getLastPassLog(acceptApproveLogList);
        // 其他非重复部分，累计起来
        wholeLogList = wholeLogList.stream().filter(log -> !acceptApprovalRelateEnumList.contains(log.getApproveStatus())).collect(toList());
        wholeLogList.addAll(lastAcceptApprovalLog);
        return wholeLogList;
    }

    @Override
    public void fillLogWithElectronicSign(List<OrderApprovalLogDTO> orderApprovalLogDTOList, List<ElectronicSignatureOperationEnum> electronicSignatureOperationEnumList) {
        if (CollectionUtils.isEmpty(orderApprovalLogDTOList)) {
            return;
        }
        List<String> orderIdList = orderApprovalLogDTOList.stream().map(OrderApprovalLogDTO::getOrderId).distinct().map(Object::toString).collect(toList());
        // 获取验收审批电子签名
        OperationListDTO operationListDTO = new OperationListDTO();
        operationListDTO.setBusinessType(BusinessTypeEnum.ORDER);
        operationListDTO.setOperation(electronicSignatureOperationEnumList);
        operationListDTO.setBusinessIdList(orderIdList);
        List<ElectronicSignOperationRecordDTO> electronicSignOperationRecordDTOList = electronicSignServiceClient.getElectronicSignData(operationListDTO);
        // 收货，通过收货电子签名合并（多次验收时如果改变了验收人会有风险，日后需改造验收时电子签名的interactionId）
        this.mergeSignIntoLog(electronicSignOperationRecordDTOList, ElectronicSignatureOperationEnum.ORDER_RECEIVE,
                New.list(OrderApprovalEnum.RECEIPT.getValue()), orderApprovalLogDTOList);
        // 验收审批通过/不通过，通过验收审批电子签名合并
        this.mergeSignIntoLog(electronicSignOperationRecordDTOList, ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL,
                New.list(OrderApprovalEnum.REJECT.getValue(), OrderApprovalEnum.PASS.getValue()), orderApprovalLogDTOList);
    }

    @Override
    public int updateByIds(List<OrderApprovalLogDTO> logs) {
        if (CollectionUtils.isEmpty(logs)) {
            return 0;
        }
        return orderApprovalLogMapper.loopUpdateByIds(logs.stream().map(OrderApprovalLogTranslator::dtoToOrderApprovalLog).collect(Collectors.toList()));
    }

    @Override
    public void saveApprovalLog(Integer orderId, Integer status, Integer operatorId, String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(status);
        orderApprovalLog.setOperatorId(operatorId);
        orderApprovalLog.setReason(reason);
        Preconditions.notNull(orderId, "orderId must not be null");
        Preconditions.notNull(status, "approveStatus must not be null");
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
    }

    /**
     * 获取最后一次审批不通过后的的日志。 从最后一条日志向前遍历找到最后一次审批不通过，从它开始向下的即都为最后一次审批通过的日志了
     *
     * @param logList   日志
     * @return 拒绝后的日志
     */
    private List<OrderApprovalLogDTO> getLastPassLog(List<OrderApprovalLogDTO> logList) {
        Map<Integer, List<OrderApprovalLogDTO>> orderIdLogMap = DictionaryUtils.groupBy(logList, OrderApprovalLogDTO::getOrderId);
        List<OrderApprovalLogDTO> returnLogList = New.list();
        for(Map.Entry<Integer, List<OrderApprovalLogDTO>> entry : orderIdLogMap.entrySet()){
            // 分组后，对每个订单的日志进行筛选最后一次审批不通过后的日志
            List<OrderApprovalLogDTO> itemLogList = entry.getValue();
            int lastRejectIndex = -1;
            for (int i = itemLogList.size() - 1; i > -1; i--) {
                if (OrderApprovalEnum.REJECT.getValue().equals(itemLogList.get(i).getApproveStatus())) {
                    lastRejectIndex = i;
                    break;
                }
            }
            // 截取成功的日志返回
            returnLogList.addAll(itemLogList.subList(lastRejectIndex + 1, itemLogList.size()));
        }
        return returnLogList;
    }

    /**
     * 将电子签名合并到日志中
     *
     * @param signRecordList          签名记录
     * @param signOperationEnum       签名枚举
     * @param orderApprovalEnumList   订单日志类型
     * @param orderApprovalLogDTOList 订单日志记录
     */
    private void mergeSignIntoLog(List<ElectronicSignOperationRecordDTO> signRecordList, ElectronicSignatureOperationEnum signOperationEnum, List<Integer> orderApprovalEnumList, List<OrderApprovalLogDTO> orderApprovalLogDTOList) {
        List<ElectronicSignOperationRecordDTO> acceptApproveSignRecordDTO = signRecordList.stream()
                .filter(recordDTO -> signOperationEnum.getOperation().equals(recordDTO.getOperation())).collect(toList());
        // 转为验收审批日志id-电子签名图片的映射
        Map<String, String> acceptApproveLogIdSignUrlMap = DictionaryUtils.toMap(acceptApproveSignRecordDTO, ElectronicSignOperationRecordDTO::getInteractionId, ElectronicSignOperationRecordDTO::getSignPhoto);
        // 将最后的通过记录封装电子签名参数后返回
        if(ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL.equals(signOperationEnum)){
            // 验收审批，interactionId记得是日志id
            orderApprovalLogDTOList.forEach(log -> {
                if (orderApprovalEnumList.contains(log.getApproveStatus())) {
                    log.setSignUrl(acceptApproveLogIdSignUrlMap.get(log.getId().toString()));
                }
            });
        }else{
            // 其他的操作是订单id
            orderApprovalLogDTOList.forEach(log -> {
                if (orderApprovalEnumList.contains(log.getApproveStatus())) {
                    log.setSignUrl(acceptApproveLogIdSignUrlMap.get(log.getOrderId().toString()));
                }
            });
        }
    }

}
