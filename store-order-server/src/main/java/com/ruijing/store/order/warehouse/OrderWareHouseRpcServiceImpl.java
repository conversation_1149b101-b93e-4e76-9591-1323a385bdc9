package com.ruijing.store.order.warehouse;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.component.BeanContainer;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.warehouse.dto.FinishWarehouseDataDTO;
import com.ruijing.store.order.api.warehouse.service.OrderWareHouseRpcService;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.timeoutstatistics.service.impl.TimeoutStatisticsWithBalanceImpl;
import com.ruijing.store.order.business.service.constant.OrderInboundSucceedConstant;
import com.ruijing.store.order.business.service.impl.InboundSucceedCallbackService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/5 10:39
 * @description
 */
@MSharpService
public class OrderWareHouseRpcServiceImpl implements OrderWareHouseRpcService {

    @Resource
    private InboundSucceedCallbackService inboundSucceedCallbackService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private TimeoutStatisticsWithBalanceImpl timeoutStatisticsWithBalance;
    
    /**
     * 完成入库后处理
     *
     * @param finishWarehouseDataDTO 入库数据
     * @return 是否成功
     */
    @Override
    @ServiceLog(description = "完成入库后处理", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> finishWarehouse(FinishWarehouseDataDTO finishWarehouseDataDTO) {
        Integer orderId = finishWarehouseDataDTO.getOrderId();
        Preconditions.notNull(orderId,"orderId不能为空");
        OrderMasterDO orderInfo = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderInfo, ExecptionMessageEnum.UPDATE_ORDER_FAILED_INVALID_QUERY);
        // 是否完成入库
        boolean warehousingSuccess = isInboundWareHouse(orderInfo.getInventoryStatus().intValue());
        Preconditions.isTrue(warehousingSuccess,"非完成入库或没有无须入库状态，请勿调用该接口！");
        timeoutStatisticsWithBalance.executeAcceptApproveOrWareHouseStatisticChange(-1, orderInfo, null);
        // 调用结算
        this.callInboundCallback(orderInfo);
        
        return RemoteResponse.<Boolean>custom().setData(true).setSuccess();
    }

    /**
     * 调用入库回调完成逻辑
     * @param orderInfo 订单数据
     */
    private void callInboundCallback(OrderMasterDO orderInfo){
        // 入库回调处理
        InboundSucceedCallbackService customCallBackService = BeanContainer.getBean(InboundSucceedCallbackService.class, OrderInboundSucceedConstant.getInboundCallbackStrategy().get(orderInfo.getFusercode()));
        if (customCallBackService != null) {
            customCallBackService.inBoundCallBack(orderInfo.getInventoryStatus().intValue(), orderInfo);
        } else {
            inboundSucceedCallbackService.inBoundCallBack(orderInfo.getInventoryStatus().intValue(), orderInfo);
        }
    }

    /**
     * 是否已入库
     *
     * @param inventoryStatus 入库状态
     * @return 是否
     */
    private boolean isInboundWareHouse(Integer inventoryStatus) {
        return InventoryStatusEnum.COMPLETE.getCode().equals(inventoryStatus)
                || InventoryStatusEnum.NOT_INBOUND.getCode().equals(inventoryStatus);
    }
}
