package com.ruijing.store.order.base.core.translator;

import com.reagent.order.base.order.dto.OrderFundCardDTO;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.other.dto.OrderFundCardCacheRequestDTO;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.gateway.fundcard.request.OrderFundCardParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 经费卡关联缓存对象的转换器
 * <AUTHOR>
 */
public class OrderFundCardCacheTranslator {

    /**
     * 新的经费卡关联表的序列初始值
     */
    private static final int INIT_SEQUENCE = 0;

    /**
     * 经费卡曾经财务冻结过
     */
    private static final int FROZE = 1;

    /**
     * store-order 经费卡缓存dto 转 order-galaxy 经费卡缓存 dto
     * @return
     */
    public static OrderFundCardDTO dtoToCacheDto(OrderFundCardCacheRequestDTO request) {
        if (request == null) {
            return null;
        }
        OrderFundCardDTO dto = new OrderFundCardDTO();
        dto.setId(request.getId());
        dto.setOrderId(request.getOrderId());
        dto.setFundCardId(request.getFundCardId());
        dto.setFundCardCode(request.getFundCardCode());
        dto.setFundCardNo(request.getFundCardNo());
        dto.setFreezeAmount(request.getFreezeAmount());
        // 新的经费卡序列是1
        dto.setSequence(request.getSequence() == null ? INIT_SEQUENCE + 1 : request.getSequence());
        dto.setCreateTime(request.getCreateTime());
        dto.setUpdateTime(request.getUpdateTime());
        dto.setFroze(request.getFroze());

        return dto;
    }

    /**
     * ref 经费卡关联对象转 新经费卡缓存对象
     * @param ref
     * @return
     */
    public static OrderFundCardDTO refToCacheDto(RefFundcardOrderDTO ref) {
        if (ref == null) {
            return null;
        }
        OrderFundCardDTO dto = new OrderFundCardDTO();
        dto.setOrderId(Integer.parseInt(ref.getOrderId()));
        // 暨大，项目id即是经费卡id (项目 == 经费卡）
        dto.setFundCardId(ref.getCardId());
        // ref_fund_card没有cardCode，先滞空
        dto.setFundCardCode(null);
        dto.setFundCardNo(ref.getCardNo());
        dto.setFreezeAmount(ref.getFreezeAmount());
        dto.setSequence(INIT_SEQUENCE);
        Date now = new Date();
        dto.setCreateTime(now);
        dto.setUpdateTime(now);
        dto.setFroze(FROZE);

        return dto;
    }

    /**
     * ref 经费卡关联对象转 新经费卡缓存对象, 批量
     * @param refList
     * @return
     */
    public static List<OrderFundCardDTO> refToCacheDto(List<RefFundcardOrderDTO> refList) {
        if (CollectionUtils.isEmpty(refList)) {
            return New.emptyList();
        }
        return refList.stream().map(ref -> refToCacheDto(ref)).collect(Collectors.toList());
    }

    /**
     * orderFundCardParams 验收审批换卡异构转化
     * @param orderFundCardParams
     * @return
     */
    public static List<OrderFundCardCacheRequestDTO> orderFundCardParamsToDTO(List<OrderFundCardParam> orderFundCardParams, String fundCardLevel) {
        if (CollectionUtils.isEmpty(orderFundCardParams)) {
            return New.emptyList();
        }
        int cardLevel = NumberUtils.toInt(fundCardLevel, 0);
        return orderFundCardParams.stream().map(p -> {
            OrderFundCardCacheRequestDTO param = new OrderFundCardCacheRequestDTO();
            param.setOrderId(p.getOrderId());
            if (FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == cardLevel) {
                param.setFundCardId(p.getProjectId());
                param.setFundCardNo(p.getProjectCode());
            } else if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel) {
                param.setFundCardId(p.getFundCardId());
                param.setFundCardNo(p.getFundCardNo());
            }

            param.setFreezeAmount(p.getFreezeAmount());
            return param;
        }).collect(Collectors.toList());
    }

    /**
     * orderFundCardParams 验收审批换卡异构转化
     * @param orderFundCardParams
     * @return
     */
    public static List<RefFundcardOrderDTO> orderFundCardParamsToRefDTO(List<OrderFundCardParam> orderFundCardParams, String fundCardLevel) {
        if (CollectionUtils.isEmpty(orderFundCardParams)) {
            return New.emptyList();
        }
        int cardLevel = NumberUtils.toInt(fundCardLevel, 0);
        return orderFundCardParams.stream().map(p -> {
            RefFundcardOrderDTO param = new RefFundcardOrderDTO();
            param.setOrderId(p.getOrderId().toString());
            if (FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == cardLevel) {
                param.setCardId(p.getProjectId());
                param.setCardNo(p.getProjectCode());
            } else if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel) {
                param.setCardId(p.getFundCardId());
                param.setCardNo(p.getFundCardNo());
            }

            param.setFreezeAmount(p.getFreezeAmount());
            return param;
        }).collect(Collectors.toList());
    }


}
