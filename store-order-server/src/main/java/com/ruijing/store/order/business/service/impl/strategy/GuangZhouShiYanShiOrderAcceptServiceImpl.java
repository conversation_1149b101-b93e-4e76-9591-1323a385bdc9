package com.ruijing.store.order.business.service.impl.strategy;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.research.statement.api.statement.dto.StatementWayConfigDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 广州实验室 验收策略
 */
@Service(OrgConst.GUANG_ZHOU_SHI_YAN_SHI + OrderAcceptConstant.ACCEPT_SUFFIX)
public class GuangZhouShiYanShiOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    /**
     * 结算配置-集中结算名称（广州实验室定制）
     */
    private static final String UNIFIED_SETTLEMENT_NAME = "集中结算";

    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        // 非 集中结算 订单，走验收-完成模式
        Boolean orderUnifiedSettlementConfig = getOrderUnifiedSettlement(orderMasterDO.getId());
        if (BooleanUtils.isNotTrue(orderUnifiedSettlementConfig)) {
            return 1;
        }
        return super.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
    }

    /**
     * 获取订单是否为集中结算
     *
     * @param orderId 订单ID
     * @return true-集中结算，false-自结算
     */
    private Boolean getOrderUnifiedSettlement(Integer orderId) {
        List<OrderExtraDTO> orderExtraList = orderExtraClient.selectByOrderIdAndExtraKey(
                New.list(orderId), New.list(OrderExtraEnum.STATEMENT_WAY_ID.getValue()));

        // 查不到单据配置快照，默认走集中结算
        if (CollectionUtils.isEmpty(orderExtraList)) {
            return true;
        }

        Integer statementWayId = Integer.valueOf(orderExtraList.get(0).getExtraValue());
        List<StatementWayConfigDTO> configList = statementPlatformClient.listStatementWayConfigByIds(New.list(statementWayId));

        // 查不到对应的OMS结算方式配置，默认走集中结算
        if (CollectionUtils.isEmpty(configList)) {
            return true;
        }

        // 根据配置名称判断是否为集中结算
        return UNIFIED_SETTLEMENT_NAME.equals(configList.get(0).getName());
    }

}
