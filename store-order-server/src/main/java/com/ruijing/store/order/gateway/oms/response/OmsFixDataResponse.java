package com.ruijing.store.order.gateway.oms.response;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.gateway.oms.vo.ErrorMsgVO;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2022/12/14 15:51
 * @description
 */
@RpcModel("OMS修改数据结果")
public class OmsFixDataResponse implements Serializable {

    private static final long serialVersionUID = 596149647362061629L;

    @RpcModelProperty("错误信息")
    private List<ErrorMsgVO> errorInfoList;

    public OmsFixDataResponse(List<ErrorMsgVO> errorInfoList) {
        this.errorInfoList = errorInfoList;
    }

    public List<ErrorMsgVO> getErrorInfoList() {
        return errorInfoList;
    }

    public void setErrorInfoList(List<ErrorMsgVO> errorInfoList) {
        this.errorInfoList = errorInfoList;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OmsFixDataResponse.class.getSimpleName() + "[", "]")
                .add("errorInfoList=" + errorInfoList)
                .toString();
    }
}
