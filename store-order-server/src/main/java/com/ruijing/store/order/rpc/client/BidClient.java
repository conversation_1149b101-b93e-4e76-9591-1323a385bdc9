package com.ruijing.store.order.rpc.client;

import com.google.common.collect.Lists;
import com.reagent.bid.api.base.bidmaster.dto.BidMasterDTO;
import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.bid.api.rpc.dto.BidApprovalRequestDTO;
import com.reagent.bid.api.rpc.dto.BidLogRequestDTO;
import com.reagent.bid.api.rpc.service.BidCommonRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/09/14 10:29
 * @Version 1.0
 * @Desc:描述 新竞价rpc客户端
 */
@ServiceClient
public class BidClient {

    @MSharpReference(remoteAppkey = "store-bid-service")
    private BidCommonRpcService bidCommonRpcService;

    /**
     * 根据订单号、竞价单id、组织code查找竞价审批日志
     * @return List<BidApprovalLogDTO>
     */
    @ServiceLog(description = "根据订单号、竞价单id、组织code查找竞价审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public List<BidApprovalLogDTO> findApprovalLogInfo(String orderNo, String bidId, String orgCode) {
        Preconditions.isTrue(StringUtils.isNotBlank(orderNo), "订单号不能为空");
        Preconditions.isTrue(StringUtils.isNotBlank(bidId), "竞价单单id不能为空");
        Preconditions.isTrue(StringUtils.isNotBlank(orgCode), "单位信息不能为空");
        BidLogRequestDTO bidLogRequestDTO = new BidLogRequestDTO();
        bidLogRequestDTO.setOrderNo(orderNo);
        bidLogRequestDTO.setBidId(bidId);
        bidLogRequestDTO.setOrgCode(orgCode);
        RemoteResponse<List<BidApprovalLogDTO>> remoteResponse = bidCommonRpcService.findApprovalLogInfo(bidLogRequestDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    @ServiceLog(description = "根据竞价单号查找竞价审批日志", serviceType = ServiceType.COMMON_SERVICE)
    public Map<String, List<BidApprovalLogDTO>> batchFindApprovalLogInfo(List<String> bidNoList) {
        Preconditions.notEmpty(bidNoList, "竞价单号不能为空");
        BidApprovalRequestDTO requestDTO = new BidApprovalRequestDTO();
        requestDTO.setBidNoList(bidNoList);
        RemoteResponse<Map<String, List<BidApprovalLogDTO>>> remoteResponse = bidCommonRpcService.batchFindApprovalLogInfo(requestDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 根据订单号、竞价单id、组织code查找的对应级别的审批人id
     * @param bidId
     * @param approveLevel
     * @return Integer
     */
    public Integer getBidApprovalForLevel(String orderNo, String bidId, String orgCode, Integer approveLevel) {
        List<BidApprovalLogDTO> bidLogInfo = this.findApprovalLogInfo(orderNo, bidId, orgCode);
        if (CollectionUtils.isNotEmpty(bidLogInfo)) {
            for (BidApprovalLogDTO logInfo : bidLogInfo) {
                if (approveLevel.equals(logInfo.getLevel())) {
                    return logInfo.getOperatorId();
                }
            }
        }
        return 0;
    }

    /**
     * 根据竞价单号集合查询竞价主表信息
     * @return List<BidMasterDTO>
     */
    @ServiceLog(description = "根据竞价单号集合查询竞价主表信息", serviceType = ServiceType.COMMON_SERVICE)
    public List<BidMasterDTO> findBidMasterByBidNoList(List<String> bidNoList) {
        Preconditions.notEmpty(bidNoList, "竞价单号bidNoList不能为空");
        RemoteResponse<List<BidMasterDTO>> bidMasterByBidNoList = bidCommonRpcService.findBidMasterByBidNoList(bidNoList);
        Preconditions.isTrue(bidMasterByBidNoList.isSuccess(), bidMasterByBidNoList.getMsg());
        return bidMasterByBidNoList.getData();
    }

    /**
     * 根据旧竞价单号找竞价单
     * @param oldBidId
     * @return
     */
    public List<BidApprovalLogDTO> findOldApprovalLogInfo(String oldBidId) {
        BidLogRequestDTO req = new BidLogRequestDTO();
        req.setBidId(oldBidId);
        RemoteResponse<List<BidApprovalLogDTO>> response = bidCommonRpcService.findOldApprovalLogInfo(req);
        Preconditions.isTrue(response != null && response.isSuccess(), "根据旧竞价单号找竞价单");
        return response.getData();
    }

    @ServiceLog(description = "查询竞价单操作日志", serviceType = ServiceType.RPC_CLIENT)
    public List<BidApprovalLogDTO> findOperationLogListByBidNos(List<String> bidNoList) {
        if (CollectionUtils.isEmpty(bidNoList)) {
            return New.emptyList();
        }
        // 分批处理，每批200条
        List<BidApprovalLogDTO> result = New.list();
        List<List<String>> partitionList = Lists.partition(bidNoList, 200);
        for (List<String> partition : partitionList) {
            RemoteResponse<List<BidApprovalLogDTO>> response = bidCommonRpcService.findOperationLogListByBidNoList(New.list(partition));
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
            if (CollectionUtils.isNotEmpty(response.getData())) {
                result.addAll(response.getData());
            }
        }
        return result;
    }
}
