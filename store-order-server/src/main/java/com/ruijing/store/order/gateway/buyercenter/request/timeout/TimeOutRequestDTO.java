package com.ruijing.store.order.gateway.buyercenter.request.timeout;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: li<PERSON>yu
 * @createTime: 2023-11-30 15:18
 * @description:
 **/
public class TimeOutRequestDTO implements Serializable {

    private static final long serialVersionUID = -2056575327827530690L;

    @RpcModelProperty("部门id")
    private Integer deptId;

    public Integer getDeptId() {
        return deptId;
    }

    public TimeOutRequestDTO setDeptId(Integer deptId) {
        this.deptId = deptId;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", TimeOutRequestDTO.class.getSimpleName() + "[", "]")
                .add("deptId=" + deptId)
                .toString();
    }
}
