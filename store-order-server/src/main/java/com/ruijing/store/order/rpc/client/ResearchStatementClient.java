package com.ruijing.store.order.rpc.client;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.research.statement.api.enums.OperatorChannelEnum;
import com.reagent.research.statement.api.enums.ServiceTypeEnum;
import com.reagent.research.statement.api.enums.StatementStatusEnum;
import com.reagent.research.statement.api.enums.StatementWayEnum;
import com.reagent.research.statement.api.statement.dto.StatementRequestDTO;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.reagent.research.statement.api.statement.dto.StatementTimeoutDTO;
import com.reagent.research.statement.api.statement.service.StatementTimeoutRpcService;
import com.reagent.research.statement.api.statement.service.StatementUpdateApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2019/11/13 10:43
 **/
@ServiceClient
@ServiceLog(serviceType = ServiceType.COMMON_SERVICE)
public class ResearchStatementClient {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementUpdateApi statementUpdateApi;

    @MSharpReference(remoteAppkey = "research-statement-web")
    private StatementTimeoutRpcService statementTimeoutRpcService;

    @ServiceLog(description = "获取部门是否结算超时")
    public StatementTimeoutDTO listStatementTimeoutData(Integer orgId, Integer departmentId){
        RemoteResponse<StatementTimeoutDTO> response = statementTimeoutRpcService.listStatementTimeoutData(orgId, departmentId);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
        return response.getData();
    }

    /**
     * 生成结算单（支持新、旧结算）--生成结算单
     * @param statementRequestDTO 入参
     * @return StatementResultDTO
     * @throws CallRpcException
     */
    @ServiceLog(description = "生成结算单（支持新、旧结算）", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public StatementResultDTO createStatementSingle(StatementRequestDTO statementRequestDTO) throws CallRpcException {
        RemoteResponse<StatementResultDTO> remoteResponse = statementUpdateApi.createStatementSingle(statementRequestDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), "生成结算单（支持新、旧结算）失败！");

        return remoteResponse.getData();
    }

    /**
     * 发起结算
     * @param operatorId
     * @param userName
     * @param orderMasterDO
     * @return
     * @throws CallRpcException
     */
    @ServiceLog(description = "生成结算单（支持新、旧结算）",serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public StatementResultDTO createStatementSingle(Integer operatorId, String userName, OrderMasterDO orderMasterDO) throws CallRpcException{

        Integer species = orderMasterDO.getSpecies().intValue();
        com.reagent.research.statement.api.enums.SourceTypeEnum sourceType = ProcessSpeciesEnum.NORMAL.getValue().equals(species) ?
                com.reagent.research.statement.api.enums.SourceTypeEnum.ONLINE
                : com.reagent.research.statement.api.enums.SourceTypeEnum.OFFLINE;

        StatementRequestDTO statementRequestDTO = new StatementRequestDTO();
        statementRequestDTO.setOrgId(orderMasterDO.getFuserid().longValue());
        statementRequestDTO.setOrgName(orderMasterDO.getFusername());
        statementRequestDTO.setOrgCode(orderMasterDO.getFusercode());
        statementRequestDTO.setSupplierId(orderMasterDO.getFsuppid().longValue());
        statementRequestDTO.setSupplierName(orderMasterDO.getFsuppname());
        statementRequestDTO.setDepartmentId(orderMasterDO.getFbuydepartmentid().longValue());
        statementRequestDTO.setDepartmentName(orderMasterDO.getFbuydepartment());
        statementRequestDTO.setSourceType(sourceType.getType());
        statementRequestDTO.setServiceType(ServiceTypeEnum.PURCHASE.getType());
        statementRequestDTO.setStatus(StatementStatusEnum.WaitingToDrawBills.getStatus());
        if(OrgEnum.JI_NAN_DA_XUE.getValue() == orderMasterDO.getFuserid()){
            statementRequestDTO.setStatementWay(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus()) ? StatementWayEnum.SELF_STATEMENT.getStatementWay() : StatementWayEnum.UNION_STATEMENT.getStatementWay());
        }
        statementRequestDTO.setOperatorUserId(operatorId.longValue());
        statementRequestDTO.setOperatorUserName(userName);
        statementRequestDTO.setOperatorType(OperatorChannelEnum.PURCHASE.getChannel());
        statementRequestDTO.setOperatorChannel(OperatorChannelEnum.PURCHASE.getChannel());
        statementRequestDTO.setBalanceMoney(orderMasterDO.getForderamounttotal().subtract(BigDecimal.valueOf(orderMasterDO.getReturnAmount())));
        statementRequestDTO.setBalanceDate(new Date());
        statementRequestDTO.setOrderIds(New.list(orderMasterDO.getId().longValue()));
        statementRequestDTO.setInvoiceTitleId(orderMasterDO.getInvoiceTitleId());
        if (operatorId == -1) {
            //-1是自动验收 发起的结算，用采购人id
            statementRequestDTO.setUserId(orderMasterDO.getFbuyerid().longValue());
        } else {
            statementRequestDTO.setUserId(operatorId.longValue());
        }

        logger.info("提交结算,订单id：{},结算单对象：{}", orderMasterDO.getId(), JsonUtils.toJson(statementRequestDTO));
        StatementResultDTO responseStatementDTO = this.createStatementSingle(statementRequestDTO);
        if (responseStatementDTO == null) {
            throw new CallRpcException("提交结算异常！");
        }
        return responseStatementDTO;
    }

    @ServiceLog(description = "撤销结算",serviceType = ServiceType.COMMON_SERVICE,operationType = OperationType.WRITE)
    public void cancelStatement(int statementId, String reason, Long operatorId, String operatorName){
        StatementRequestDTO statementRequestDTO = new StatementRequestDTO();
        statementRequestDTO.setId((long) statementId);
        statementRequestDTO.setReason(reason);
        statementRequestDTO.setOperatorUserId(operatorId);
        statementRequestDTO.setOperatorUserName(operatorName);
        RemoteResponse<StatementResultDTO> remoteResponse = statementUpdateApi.cancelStatement(statementRequestDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
    }
}
