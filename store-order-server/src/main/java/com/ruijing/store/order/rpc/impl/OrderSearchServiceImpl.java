package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.annotation.OneWay;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.AndFilter;
import com.ruijing.search.client.filter.NestedFilter;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.query.*;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.Record;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.score.ItemFunction;
import com.ruijing.search.client.score.ScoreRank;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.search.curd.dto.Document;
import com.ruijing.search.curd.reponse.UpdateResponse;
import com.ruijing.search.curd.request.UpdateByQueryRequest;
import com.ruijing.search.curd.request.UpdateRequest;
import com.ruijing.search.curd.script.UpdateScript;
import com.ruijing.search.curd.service.SearchUpdateService;
import com.ruijing.store.order.api.general.dto.*;
import com.ruijing.store.order.api.general.service.OrderSearchService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import com.ruijing.store.order.search.translator.OrderPojoTranslator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.objenesis.Objenesis;
import org.objenesis.ObjenesisStd;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@MSharpService
@ServiceLog
public class OrderSearchServiceImpl implements OrderSearchService {

    private static Objenesis objenesis = new ObjenesisStd(true);

    private static final String NESTED_TABLE = "order_detail";

    private static final String ORDER_INDEX_KEY = "order";

    private static final String CAT_TYPE = "OrderSearchService";

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private OrderSearchRPCServiceClient orderSearchRPCServiceClient;

    @MSharpReference(remoteAppkey = "msharp-search-service",token = "${updateSearch.token}")
    private SearchUpdateService searchUpdateService;



    /**
     * 修改订单状态 无返回值
     * @param orderId 订单id
     * @param status 订单状态
     */
    @OneWay
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public void updateStatusSearchOneWay(Integer orderId, Integer status){
        updateStatus(orderId, status);
    }

    /**
     * 更新订单状态 有返回值
     * @param orderId 订单id
     * @param status    订单状态
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<String> updateStatusSearch(Integer orderId, Integer status){

        try {
            UpdateResponse updateResponse = updateStatus(orderId, status);
            return RemoteResponse.<String>custom().setSuccess().setData(updateResponse.getResult()).build();
        } catch (Exception e) {
            logger.error("updateStatusSearch异常：", e);
            Cat.logError(CAT_TYPE, "updateOrderSearch", "调用搜索更新接口失败：", e);
            return RemoteResponse.<String>custom().setFailure("调用搜索更新接口失败：").build();
        }
    }

    /**
     * 更新订单状态
     * @param orderId
     * @param status
     * @return
     */
    private UpdateResponse updateStatus(Integer orderId, Integer status) {
        logger.info("进入 updateStatus，参数1:{},{}", orderId,status);
        UpdateRequest request = new UpdateRequest();
        request.setId(String.valueOf(orderId));
        request.setKey(ORDER_INDEX_KEY);
        Document document = new Document();
        document.addValue("status", status);
        request.setDocument(document);
        UpdateResponse update = searchUpdateService.update(request);
        logger.info("结束 updateStatus，结果{}",update.toString());
        return update;
    }

    /**
     * 更新搜索订单索引 订单状态
     * @param updateOrderSearchParamDTO 订单id，订单状态
     * @return RemoteResponse
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public void updateOrderSearch(UpdateOrderSearchParamDTO updateOrderSearchParamDTO){
        logger.info("进入 updateOrderSearch，参数1:{}", updateOrderSearchParamDTO.toString());
        try {
            String orderId = updateOrderSearchParamDTO.getOrderId();
            Integer status = updateOrderSearchParamDTO.getStatus();
            Integer orderDetailId = updateOrderSearchParamDTO.getOrderDetailId();
            String goodName = updateOrderSearchParamDTO.getGoodName();
            StringBuilder script = new StringBuilder();
            Map<String, Object> paramMap = New.linkedHashMap();
            AndFilter andFilter = new AndFilter();
            //设置修改订单状态 脚本 参数
            if (status != null && StringUtils.isNotEmpty(orderId)){
                script.append("ctx._source.status=params.status");
                paramMap.put("status",status);
                TermFilter orderFilter = new TermFilter("id", orderId);
                andFilter.addFilter(orderFilter);
            }
            //修改 商品名称 脚本 参数
            if (StringUtils.isNotEmpty(goodName)&& StringUtils.isNotEmpty(orderId) && orderDetailId != null) {
                if (script.length()!=0){
                    script.append(";");
                }
                script.append("ctx._source.order_detail[0].fgoodname = params.goodName");
                paramMap.put("goodName",goodName);
                NestedFilter nestedFilter = new NestedFilter(NESTED_TABLE, new TermFilter(NESTED_TABLE + ".detail_id", orderDetailId));
                andFilter.addFilter(nestedFilter);
            }
            UpdateScript updateScript = new UpdateScript(script.toString(),paramMap);
            UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(updateScript,andFilter);
            updateByQueryRequest.setKey(ORDER_INDEX_KEY);
            Boolean update = searchUpdateService.updateByQuery(updateByQueryRequest);
            logger.info("结束 updateOrderSearch，结果{}",update.toString());
        } catch (Exception e) {
            logger.error("searchUpdateService.update异常：", e);
            Cat.logError(CAT_TYPE, "updateOrderSearch", "调用搜索更新接口失败：", e);
        }
    }

    @Override
    public RemoteResponse<OrderSearchResultDTO> searchDefaultResult(OrderSearchFieldsDTO fieldsDto) {
        return this.search(fieldsDto, new OrderSearchExtraResultDTO());
    }

    @Override
    public RemoteResponse<OrderSearchResultDTO> searchOrderByStatementIds(OrderStatementReqDTO orderForStaementReqDTO, OrderSearchExtraResultDTO result) {
        final Request request = new Request();
        request.setKey(ORDER_INDEX_KEY);
        //设置搜索返回字段
        request.addOutputFieldSet(result.getFields());
        //设置分页
        request.setStart(orderForStaementReqDTO.getStartHit());
        request.setPageSize(orderForStaementReqDTO.getPageSize());
        //设置查询条件
        List<String> statementIds = orderForStaementReqDTO.getStatementIds();
        request.addFilter(new TermFilter("statement_id", statementIds.toArray()));
        return getOrderSearchResult(request);
    }

    @Override
    public RemoteResponse<OrderSearchResultDTO> search(OrderSearchFieldsDTO fieldsDto, OrderSearchExtraResultDTO result) {
        final Request request = new Request();
        request.setKey(ORDER_INDEX_KEY);
        //设置查询条件
        fieldsFillRequest(request, fieldsDto);
        // 返回的字段
        request.addOutputFieldSet(result.getFields());
        Integer startHit = fieldsDto.getStartHit();
        request.setStart(startHit < 0 ? 0 : startHit);
        Integer pageSize = fieldsDto.getPageSize();
        request.setPageSize(pageSize < 0 ? 20 : pageSize);
        return getOrderSearchResult(request);
    }

    /**
     * @Description: 根据request请求参数，通过搜索records组装订单对象 返回response
     * @Param: Request request
     * @return: RemoteResponse<OrderSearchResultDTO>
     * @Author: zhuk
     * @Date: 2019/5/31
     */
    private RemoteResponse<OrderSearchResultDTO> getOrderSearchResult(Request request) {
        Response response;
        final Transaction transaction = Cat.newTransaction(CAT_TYPE, "getOrderSearchResult");
        try {
            response = orderSearchRPCServiceClient.search(request);
            transaction.setSuccessStatus();
        } catch (Exception e) {
            transaction.setStatus(e);
            logger.error("调用搜索接口异常：{}", e);
            Cat.logError(CAT_TYPE, "searchOrders", "调用搜索接口失败：" + e.getMessage() + "\n request：" + JsonUtils.toJson(request), e);
            return RemoteResponse.<OrderSearchResultDTO>custom().setFailure(e.getMessage()).build();
        } finally {
            transaction.complete();
        }

        if (!response.isSuccess()) {
            String errorMsg = response.getErrorMessage();
            Cat.logWarn(CAT_TYPE, "searchOrders", "调用搜索接口失败：" + errorMsg + "\n request：" + JsonUtils.toJson(request));
            return RemoteResponse.<OrderSearchResultDTO>custom().setFailure(errorMsg).build();
        }

        final OrderSearchResultDTO resultDto = new OrderSearchResultDTO();
        List<Record> records = response.getRecordList();
        if (CollectionUtils.isEmpty(records)) {
            resultDto.setOrderMasterSearchDTOS(New.emptyList());
            resultDto.setPageSize(0);
            resultDto.setTotalHits(0L);
            return RemoteResponse.<OrderSearchResultDTO>custom().setData(resultDto).setSuccess().build();
        }
        try {
            List<OrderMasterSearchDTO> orderMasterDtos = OrderPojoTranslator.recordsToOrderMasterDTOS(records);
            resultDto.setOrderMasterSearchDTOS(orderMasterDtos);
            resultDto.setPageSize(records.size());
            resultDto.setTotalHits(response.getTotalHits());
        } catch (Exception e) {
            logger.error("调用recordsToOrderMasterDTOS异常：", e);
            Cat.logError(CAT_TYPE, "getOrderSearchResult", e.getMessage() + "   response:" + response, e);
            return RemoteResponse.<OrderSearchResultDTO>custom().setFailure(e.getMessage()).build();
        }
        return RemoteResponse.<OrderSearchResultDTO>custom().setData(resultDto).setMsg("获取成功").setSuccess().build();
    }

    /**
     * @Description: 将OrderSearchFieldsDTO填充search服务的request的请求条件
     * @Param: request
     * @return: fieldsDto
     * @Author: zhuk
     * @Date: 2019/5/31
     */
    private void fieldsFillRequest(Request request, OrderSearchFieldsDTO fieldsDto) {

        //采购人id过滤
        Integer fbuyerid = fieldsDto.getFbuyerid();
        if (fbuyerid != null) {
            request.addFilter(new TermFilter("fbuyerid", fbuyerid));
        }

        //订单Id过滤
        Integer id = fieldsDto.getId();
        if (id != null) {
            request.addFilter(new TermFilter("id", id));
        }

        //订单号过滤
        String orderNo = fieldsDto.getForderno();
        if (StringUtils.isNotBlank(orderNo)) {
            request.addQuery(new PhraseQuery("forderno", orderNo));
        }

        //申请单Id过滤
        Integer ftbuyappid = fieldsDto.getFtbuyappid();
        if (ftbuyappid != null) {
            request.addFilter(new TermFilter("ftbuyappid", ftbuyappid));
        }

        //采购部门id过滤
        List<Integer> fbuydepartmentid = fieldsDto.getFbuydepartmentid();
        if (CollectionUtils.isNotEmpty(fbuydepartmentid)) {
            request.addFilter(new TermFilter("fbuydepartmentid", fbuydepartmentid.toArray()));
        }


        List<Integer> fuserids = fieldsDto.getFuserids();
        if (CollectionUtils.isNotEmpty(fuserids)) {
            request.addFilter(new TermFilter("fuserid", fuserids.toArray()));
        }

        //供应商Id 精确查询
        List<Integer> fsuppids = fieldsDto.getFsuppids();
        if (CollectionUtils.isNotEmpty(fsuppids)) {
            request.addFilter(new TermFilter("fsuppid", fsuppids.toArray()));
        }

        //供应商名称 模糊查询
        String fsuppname = fieldsDto.getFsuppname();
        if (StringUtils.isNotBlank(fsuppname)) {
            request.addQuery(new PhraseQuery("fsuppname", fsuppname));
        }

        //采购人姓名 模糊查询
        String fbuyername = fieldsDto.getFbuyername();
        if (StringUtils.isNotBlank(fbuyername)) {
            request.addQuery(new PhraseQuery("fbuyername", fbuyername));
        }

        String fgoodname = fieldsDto.getFgoodname();
        String fgoodcode = fieldsDto.getFgoodcode();

        //货号，货物名查询
        if (StringUtils.isNotBlank(fgoodname) && StringUtils.isNotBlank(fgoodcode) && fgoodcode.equals(fgoodname)) {
            OrQuery orQuery = new OrQuery();
            orQuery.addQuery(new PhraseQuery(NESTED_TABLE + "." + "fgoodname", fgoodname));
            orQuery.addQuery(new PhraseQuery(NESTED_TABLE + "." + "fgoodcode", fgoodcode));
            request.addQuery(new NestedQuery(NESTED_TABLE, orQuery));
        } else {
            if (StringUtils.isNotBlank(fgoodname)) {
                PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "fgoodname", fgoodname);
                request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
            }

            if (StringUtils.isNotBlank(fgoodcode)) {
                PhraseQuery nestedQuery = new PhraseQuery(NESTED_TABLE + "." + "fgoodcode", fgoodcode);
                request.addQuery(new NestedQuery(NESTED_TABLE, nestedQuery));
            }

        }

        //订单状态过滤
        List<Integer> statusList = fieldsDto.getStatusList();
        if (CollectionUtils.isNotEmpty(statusList)) {
            request.addFilter(new TermFilter("status", statusList.toArray()));
        }

        //流程种类
        Integer species = fieldsDto.getSpecies();
        if (species != null) {
            request.addFilter(new TermFilter("species", species));
        }

        //search字段查询
        String searchKey = fieldsDto.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {

            final OrQuery orQueryMaster = new OrQuery();
            Set<String> fullTextMasterFields = fieldsDto.getFullTextMasterFields();
            fullTextMasterFields.forEach(field -> {
                if ("forderno".equals(field)){
                    //订单号精确查询
                    orQueryMaster.addQuery(new TermQuery(field, searchKey));
                }else {
                    orQueryMaster.addQuery(new PhraseQuery(field, searchKey));
                }
            });

            Set<String> fullTextDetailFields = fieldsDto.getFullTextDetailFields();
            if (CollectionUtils.isNotEmpty(fullTextDetailFields)){
                final OrQuery orQueryDetail = new OrQuery();
                fullTextDetailFields.forEach(detailField -> orQueryDetail.addQuery(new PhraseQuery(detailField, searchKey)));
                NestedQuery nestedQuery = new NestedQuery(NESTED_TABLE, orQueryDetail);
                orQueryMaster.addQuery(nestedQuery);
            }
            request.addQuery(orQueryMaster);
        }

        String upperOrderDate = fieldsDto.getUpperOrderdate();
        String lowerOrderDate = fieldsDto.getLowerOrderdate();
        String forderDate = fieldsDto.getForderdate();
        boolean hasRangeDateSearch = StringUtils.isNotBlank(upperOrderDate) || StringUtils.isNotBlank(lowerOrderDate);
        boolean hasOrderDate = StringUtils.isNotBlank(forderDate);

        final String orderDateField = "forderdate";
        // 若进行日期准确搜索，那日期范围搜索不生效
        if (hasOrderDate) {
            request.addQuery(new TermQuery(orderDateField, forderDate));
        }
        if (!hasOrderDate && hasRangeDateSearch) {
            request.addQuery(new RangeQuery(orderDateField, lowerOrderDate, upperOrderDate));
        }

        //按照订单 状态 score 排序
        if (fieldsDto.getScoreByStatus()) {
            ScoreRank scoreRank = createScoreRankByStatus();
            request.setScoreRank(scoreRank);
            request.addOrderSortItem(new FieldSortItem("_score", SortOrder.DESC));
            request.addOrderSortItem(new FieldSortItem(orderDateField, SortOrder.DESC));
        } else {
            //常规字段排序

            //设置升序排序字段
            List<String> ascFields = fieldsDto.getAscFields();
            if (CollectionUtils.isNotEmpty(ascFields)) {
                ascFields.forEach(asc ->
                        request.addOrderSortItem(new FieldSortItem(asc, SortOrder.ASC)));
            }

            //设置降序排序字段
            List<String> descFields = fieldsDto.getDescFields();
            if (CollectionUtils.isNotEmpty(descFields)) {
                descFields.forEach(desc ->
                        request.addOrderSortItem(new FieldSortItem(desc, SortOrder.DESC)));
            }
        }


    }

    /**
     * 8状态的第一位，
     * 4状态的第二位，
     * 9状态的第三位，
     * 13状态的第四位，
     * 其他状态的第五位，
     *
     * @return
     */
    private ScoreRank createScoreRankByStatus() {

        final String statusFiled = "status";

        //按照 订单状态 设置 rank评分
        ScoreRank scoreRank = new ScoreRank();
        scoreRank.setBoostMode("replace");
        //设置订单状态为8 的 权重为5
        TermFilter status8Filter = new TermFilter(statusFiled, 8);
        ItemFunction itemFunction8 = new ItemFunction(status8Filter, 5f);
        scoreRank.addFunction(itemFunction8);

        //设置订单状态为4 的 权重为4
        TermFilter status4Filter = new TermFilter(statusFiled, 4);
        ItemFunction itemFunction4 = new ItemFunction(status4Filter, 4f);
        scoreRank.addFunction(itemFunction4);

        //设置订单状态为9 的 权重为3
        TermFilter status9Filter = new TermFilter(statusFiled, 9);
        ItemFunction itemFunction9 = new ItemFunction(status9Filter, 3f);
        scoreRank.addFunction(itemFunction9);

        //设置订单状态为13的 权重为2
        TermFilter status13Filter = new TermFilter(statusFiled, 13);
        ItemFunction itemFunction13 = new ItemFunction(status13Filter, 2f);
        scoreRank.addFunction(itemFunction13);
        return scoreRank;
    }
}
