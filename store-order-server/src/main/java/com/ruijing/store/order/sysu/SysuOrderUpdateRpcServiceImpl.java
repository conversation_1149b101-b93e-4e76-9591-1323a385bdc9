package com.ruijing.store.order.sysu;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.store.order.api.sysu.dto.ChangeRefFundCardOrderRequest;
import com.ruijing.store.order.api.sysu.dto.OrderDetailPriceDTO;
import com.ruijing.store.order.api.sysu.dto.UpdateOrderPriceRequest;
import com.ruijing.store.order.api.sysu.service.SysuOrderUpdateRpcService;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.OperationType;
import com.ruijing.store.order.log.enums.ServiceType;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 中大订单相关信息更新接口
 * @date 2023/10/31 21
 */
@MSharpService
public class SysuOrderUpdateRpcServiceImpl implements SysuOrderUpdateRpcService {

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Override
    @ServiceLog(description = "更新订单价格", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> updateOrderPrice(UpdateOrderPriceRequest request) {
        List<OrderDetailPriceDTO> orderDetailPriceDTOList = request.getOrderDetailPriceDTOList();
        Preconditions.notEmpty(orderDetailPriceDTOList, "要更新的订单详情信息不可为空");
        Preconditions.isTrue(orderDetailPriceDTOList.size() <= 200, "订单详情不可超过200条");
        // 更新订单价格
        List<OrderDetailDO> originOrderDetailDOList = orderDetailMapper.findByIdIn(orderDetailPriceDTOList.stream().map(OrderDetailPriceDTO::getOrderDetailId).collect(Collectors.toList()));
        List<OrderMasterDO> originOrderMasterDOList = orderMasterMapper.findByIdIn(originOrderDetailDOList.stream().map(OrderDetailDO::getFmasterid).distinct().collect(Collectors.toList()));
        Map<Integer, OrderDetailDO> orderDetailDOMap = originOrderDetailDOList.stream().collect(Collectors.toMap(OrderDetailDO::getId, Function.identity()));
        Map<Integer, OrderMasterDO> orderMasterDOMap = originOrderMasterDOList.stream().collect(Collectors.toMap(OrderMasterDO::getId, Function.identity()));
        for (OrderDetailPriceDTO orderDetailPriceDTO : orderDetailPriceDTOList) {
            OrderDetailDO orderDetailDO = orderDetailDOMap.get(orderDetailPriceDTO.getOrderDetailId());
            Preconditions.notNull(orderDetailDO, "订单详情id：" + orderDetailPriceDTO.getOrderDetailId() + "对应的订单详情为空！");
            OrderMasterDO orderMasterDO = orderMasterDOMap.get(orderDetailDO.getFmasterid());
            Preconditions.notNull(orderDetailDO, "订单数据异常，订单详情" + orderDetailDO.getId() + "对应的订单信息为空");
            orderMasterDO.setForderamounttotal(orderMasterDO.getForderamounttotal().add(orderDetailPriceDTO.getBidAmount().subtract(orderDetailDO.getFbidamount())));
        }
        orderMasterMapper.batchUpdateOrderTotalAmount(orderMasterDOMap.values().stream().map(orderMasterDO ->{
            OrderMasterDO updateDO = new OrderMasterDO();
            updateDO.setForderamounttotal(orderMasterDO.getForderamounttotal());
            updateDO.setId(orderMasterDO.getId());
            return updateDO;
        }).collect(Collectors.toList()));
        // 更新订单详情价格
        orderDetailMapper.batchUpdatePrice(request.getOrderDetailPriceDTOList());
        return RemoteResponse.success();
    }

    @Override
    @ServiceLog(description = "更新经费卡关联信息", operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> changeRefFundCardOrder(ChangeRefFundCardOrderRequest request) {
        Preconditions.notNull(request.getOrderId(), "订单id不可为空");
        Preconditions.notEmpty(request.getRefFundcardOrderDTOList(), "更换的经费卡不可为空");
        refFundcardOrderMapper.deleteByOrderId(request.getOrderId().toString());
        refFundcardOrderMapper.insertList(request.getRefFundcardOrderDTOList().stream().map(refFundcardOrderDTO -> {
            RefFundcardOrderDO entity = RefFundcardOrderTranslator.dto2DO(refFundcardOrderDTO);
            entity.setId(UUID.randomUUID().toString());
            return entity;
        }).collect(Collectors.toList()));
        return RemoteResponse.success();
    }
}
