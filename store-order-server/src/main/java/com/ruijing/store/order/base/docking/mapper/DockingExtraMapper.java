package com.ruijing.store.order.base.docking.mapper;
import com.ruijing.store.order.base.docking.model.DockingExtra;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 外部订单对接
 * <AUTHOR>
 * @create: 2019/09/25
 */
public interface DockingExtraMapper {

    /**
     * 根据主键删除
     * @param id 主键
     * @return 行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入记录
     * @param record 记录
     * @return  行数
     */
    int insert(DockingExtra record);

    /**
     * 选择性插入
     * @param record 记录
     * @return 行数
     */
    int insertSelective(DockingExtra record);

    /**
     * 根据id 查询对应的记录
     * @param id    主键
     * @return 结果
     */
    DockingExtra selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性 更新
     * @param record 记录
     * @return 结果
     */
    int updateByPrimaryKeySelective(DockingExtra record);

    /**
     * 根据主键更新
     * @param record 记录
     * @return 结果
     */
    int updateByPrimaryKey(DockingExtra record);

    /**
     * 查询dockingExtra
     * @param dockingExtra 入参
     * @return 结果 List<DockingExtra>
     */
    List<DockingExtra> findDockingExtra(DockingExtra dockingExtra);

    /**
     * 根据锐竞订单号更新对接单号信息
     * @param updatedExtraInfo
     * @param info
     * @return
     */
    int updateExtraInfoByInfo(@Param("extraInfo")String updatedExtraInfo,@Param("info")String info);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertList(@Param("list")List<DockingExtra> list);

    /**
     * 根据订单号查询第docking三方平台的订单号
     * @param infoCollection
     * @return
     */
    List<DockingExtra> findByInfoIn(@Param("infoCollection")Collection<String> infoCollection);

    /**
     * 根据锐竞订单号更新对接信息
     * @param updated
     * @return
     */
    int updateByInfo(@Param("updated")DockingExtra updated);

    /**
     * 根据订单号查询记录数
     * @param info
     * @return
     */
    Long countByInfo(@Param("info")String info);

    /**
     * 通过对接单号，类型查询对接信息
     * @param extraInfoCollection
     * @param type
     * @return
     */
    List<DockingExtra> findByExtraInfoInAndType(@Param("extraInfoCollection")Collection<String> extraInfoCollection,@Param("type")Integer type);

    /**
     * 通过订单号，类型查询对接信息
     * @param infoCollection
     * @param type
     * @return
     */
	List<DockingExtra> findByInfoInAndType(@Param("infoCollection")Collection<String> infoCollection, @Param("type")Integer type);

    /**
     * 通过订单号，类型查询对接信息，按时间排序
     * @param infoCollection
     * @param type
     * @return
     */
    List<DockingExtra> findByInfoInAndTypeOrderByUpdateTime(@Param("infoCollection")Collection<String> infoCollection, @Param("type")Integer type);
}
