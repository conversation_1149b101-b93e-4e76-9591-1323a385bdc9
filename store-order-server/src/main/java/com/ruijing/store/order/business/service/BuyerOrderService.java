package com.ruijing.store.order.business.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ApplyCancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.CancelOrderReqDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.request.ModifyAddrRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.ModifyOrderExtInfoRequestDTO;
import com.ruijing.store.order.gateway.buyercenter.request.OrderPushFailItemDTO;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnBriefInfoRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderStatusLimitDaysRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.ElectronicSignInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnBriefInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderAddressInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderOperationLogVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.DeptBriefVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderPushFailItemVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.*;
import com.ruijing.store.user.api.dto.DepartmentDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/11/17 18:12
 * @Description
 **/
public interface BuyerOrderService {

    /**
     * @description: 获取采购人中心订单列表
     * @date: 2020/12/21 15:44
     * @author: zengyanru
     * @param request
     * @param loginInfo
     * @return com.ruijing.store.order.base.core.vo.orderlist.OrderListRespVO
     */
    PageableResponse<OrderListRespVO> getOrderListForWWW(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHmsOrDetail);

    /**
     * @description: 组装返回部门id，名称列表
     * @date: 2021/2/25 10:39
     * @author: zengyanru
     * @param deptList 部门列表
     * @param rootDeptId 根部门id
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderlist.DeptBriefVO>
     */
    List<DeptBriefVO> constructDeptsBriefInfo(List<DepartmentDTO> deptList, Integer rootDeptId);

    /**
     * @description: 取消订单
     * @param cancelOrderReq
     * @return 成功与否
     */
    Boolean cancelOrder(RjSessionInfo rjSessionInfo, ApplyCancelOrderReqDTO cancelOrderReq);

    /**
     * @description: 验收订单
     * @param receiptOrderReq
     * @return 成功与否
     */
    ReceiptOrderResponseDO acceptOrder(RjSessionInfo rjSessionInfo, OrderReceiptParamDTO receiptOrderReq);

    /**
     * 批量验收订单
     * @param batchAcceptRequest
     * @return 成功与否
     */
    Boolean batchAcceptOrder(OrderBasicParamDTO batchAcceptRequest, LoginUserInfoBO loginInfo);

    /**
     * @description: 拒绝取消订单
     * @param cancelOrderReq
     * @return 成功与否
     */
    Boolean refuseCancelOrder(RjSessionInfo rjSessionInfo, CancelOrderReqDTO cancelOrderReq);

    /**
     * @description: 同意取消订单
     * @param cancelOrderReqDTO
     * @return 成功与否
     */
    Boolean agreeCancelOrder(RjSessionInfo rjSessionInfo, CancelOrderReqDTO cancelOrderReqDTO);


    /**
     * @description: 计数不同状态的订单
     * @date: 2021/1/11 10:17
     * @author: zengyanru
     * @param request
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO
     */
    OrderListCountVO countOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request);

    /**
     * @description: 计数不同状态的订单
     * @date: 2021/1/11 10:17
     * @author: zengyanru
     * @param request
     * @return com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListCountVO
     */
    OrderListCountVO countOrderList(RjSessionInfo rjSessionInfo, OrderListRequest request, boolean isHms);


    /**
     * @description: 查找当前订单的退货单基本信息，用于判断验收和展示退货列表或详情
     * @date: 2021/1/27 14:58
     * @author: zengyanru
     * @param goodsReturnBriefInfoRequest
     * @return java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnBriefInfoVO>
     */
    List<GoodsReturnBriefInfoVO> getBriefGoodsReturnInfo(GoodsReturnBriefInfoRequest goodsReturnBriefInfoRequest);

    /**
     * @description: 分页搜索查询的专用，如果是聚合查询则只采用没有page的方法
     * @date: 2021/1/13 18:03
     * @author: zengyanru
     * @param searchRequest
     * @param request
     * @return void
     */
    void constructSearchPageParam(OrderSearchParamDTO searchRequest, OrderListRequest request);

    /**
     * @description: 订单查看 部门单查看逻辑
     * @date: 2021/2/25 17:19
     * @author: zengyanru
     * @param deptList
     * @param request
     * @param rootDepartmentId
     * @return java.util.List<java.lang.Integer>
     */
    List<Integer> getDeptIdListForSearch(List<DepartmentDTO> deptList, OrderListRequest request, Integer rootDepartmentId);

    /**
     * @description: 判断订单某个状态后经过的天数是否在传入天数内，目前仅用于不使用结算单位
     * @date: 2021/4/12 16:25
     * @author: zengyanru
     * @param rjSessionInfo
     * @param limitDaysRequest
     * @return java.lang.Boolean
     */
    Boolean limitDaysAfterFinish(RjSessionInfo rjSessionInfo, OrderStatusLimitDaysRequest limitDaysRequest);


    /**
     * 采购人中心-订单管理待确认订单/待发货增加发货提醒
     * @param rjSessionInfo
     * @param orderBasicParamDTO
     */
    RemoteResponse deliveryRemind(RjSessionInfo rjSessionInfo, OrderBasicParamDTO orderBasicParamDTO);


    /**
     *  获取当前采购人及单位 电子签名配置
     * @param rjSessionInfo
     * @param orderBasicParamDTO
     * @return
     */
    ElectronicSignInfoVO searchOperationConfig(RjSessionInfo rjSessionInfo, OrderBasicParamDTO orderBasicParamDTO);

    /**
     * 修改订单收货地址信息（地址和人）
     * @param rjSessionInfo
     * @param modifyAddrRequestDTO
     * @return
     */
    Boolean modifyOrderAddr(RjSessionInfo rjSessionInfo, ModifyAddrRequestDTO modifyAddrRequestDTO);

    /**
     * 根据订单id查询日志，是否修改过地址.true:已修改； false：未修改
     * @param orderId
     * @return
     */
    OrderAddressInfoVO checkHasModifiedAddr(Integer orderId);


    /**
     * 查询订单列表推送失败项
     *
     * @param rjSessionInfo
     * @param pageQuery
     * @return
     */
    PageableResponse<List<OrderPushFailItemVO>> orderPushFailItemPageQuery(RjSessionInfo rjSessionInfo, OrderPushFailItemDTO pageQuery);


    /**
     * 状态变更提醒列表
     * @param rjSessionInfo 用户信息
     * @return 更新时间最新的待收货、待验收、供应商申请取消订单三张
     */
    List<OrderMasterSearchDTO> listStatusChangeNotices(RjSessionInfo rjSessionInfo);

    /**
     * 给商家发送确认收货提醒
     *
     * @param rjSessionInfo 用户信息
     * @param request       订单id
     */
    RemoteResponse<Boolean> confirmRemind(RjSessionInfo rjSessionInfo, OrderBasicParamDTO request);

    /**
     * 修改订单扩展信息
     */
    void modifyOrderExtInfo(RjSessionInfo rjSessionInfo, ModifyOrderExtInfoRequestDTO request);

    /**
     * 设置订单详情拓展字段
     * @param orderDetailDTO
     * @param orderDetailExtraDTOS
     */
    void setOrderDetailDTOExtraInfo(OrderDetailDTO orderDetailDTO, List<OrderDetailExtraDTO> orderDetailExtraDTOS);

    /**
     * 获取订单详情拓展字段
     * @param orderIdList
     * @return
     */
    Map<Integer, List<OrderDetailExtraDTO>> getOrderIdDetailExtraMap(List<Integer> orderIdList);
}
