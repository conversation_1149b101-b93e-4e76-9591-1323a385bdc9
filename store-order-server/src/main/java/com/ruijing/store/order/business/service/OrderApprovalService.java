package com.ruijing.store.order.business.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalManInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptanceCountVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 审批-订单信息相关Service
 * @Date: 2024/12/09 11:51
 **/
public interface OrderApprovalService {

    /**
     * 采购人中心--我的待审订单
     *
     * @param request   搜索入参
     * @param loginInfo 会话信息
     * @param isHms     是否从hms入口进入
     * @return 我的待审分页数据
     */
    PageableResponse<OrderListRespVO> getMyPendingOrderList(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHms);

    /**
     * 我的已审批订单
     *
     * @param request   分页入参
     * @param loginInfo 用户信息
     * @param isHms     是否为hms请求
     * @return 我的已审批订单
     */
    PageableResponse<OrderListRespVO> myApprovedList(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHms);

    /**
     * 根据入参查询当前单据验收审批流下可验收审批的人名列表
     *
     * @param request 传入订单id，可选部门id传入
     * @return 有验收审批权限的人名列表
     */
    OrderApprovalManInfoVO getApproveFlowUserList(OrderListRequest request);

    /**
     * 获取订单审批数量
     * @param rjSessionInfo 会话信息
     * @param request 订单列表请求参数
     */
    OrderAcceptanceCountVO getOrderAcceptanceCount(RjSessionInfo rjSessionInfo, OrderListRequest request);


}
