package com.ruijing.store.order.business.handler;

import com.ruijing.base.letter.msg.BusinessEnum;
import com.ruijing.base.letter.msg.SysSendDTO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.crm.api.pojo.dto.UserDTO;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.rpc.client.SuppClient;
import com.ruijing.store.order.rpc.client.SysLetterRpcServiceClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31 17:34
 * @description
 */
@Service
public class SysLetterHandler {

    /**
     * 采购业务发送站内信专用，禁止其他业务使用！
     */
    private static final String PURCHASE_BIZ_TOKEN = "23234sdfi2dew3";

    /**
     * 采购人中心 订单详情链接
     */
    @PearlValue(key = "order.pm.orderDetail.url")
    private static String purchaseManagerOrderDetailLink;

    @PearlValue(key = "GOODS_RETURN_HTTP")
    public static String purchaseManagerOrderReturnLink;

    /**
     * 供应商 订单详情链接
     */
    @PearlValue(key = "EMAIL_NEW_ORDER_SUPP_HTTP")
    public static String suppOrderDetailLink;

    @Resource
    private SysLetterRpcServiceClient sysLetterRpcServiceClient;

    @Resource
    private UserClient userClient;

    @Resource
    private SuppClient suppClient;

    /**
     * 发送供应商取消订单的信息给采购人
     *
     * @param masterDO 订单信息
     */
    @ServiceLog(description = "发送申请取消订单申请站内信给采购人", operationType = OperationType.WRITE)
    public void sendApplyCancelToBuyer(OrderMasterDO masterDO) {
        String suppName = masterDO.getFsuppname();
        String letterTitle = "供应商申请取消订单";
        String letterContent = String.format("<div>你好！供应商“%s”申请取消订单%s，请前往订单详情页查看取消原因，确认是否同意对方取消订单。" +
                        "<a href='%s' target='_blank' class='tm-m-color'>点击前往订单详情>></a></div>",
                suppName,
                masterDO.getForderno(),
                String.format(purchaseManagerOrderDetailLink, masterDO.getId(), masterDO.getForderno()));
        this.sendSysLetterToBuyer(masterDO, letterTitle, suppName, letterContent);
    }

    /**
     * 发送供应商退货信息给采购人
     *
     * @param masterDO 订单信息
     */
    @ServiceLog(description = "发送退货信息站内信给采购人", operationType = OperationType.WRITE)
    public void sendReturnGoodsMsgToBuyer(OrderMasterDO masterDO, GoodsReturn goodsReturn) {
        if (!GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(goodsReturn.getGoodsReturnStatus())) {
            return;
        }
        String suppName = masterDO.getFsuppname();
        String letterTitle = "拒绝退货申请";
        String letterContent = String.format("<div>你好！供应商“%s”已拒绝退货单%s的退货申请。请前往退货单详情页查看拒绝退货原因。" +
                        "<a href='%s' target='_blank' class='tm-m-color'>点击前往退货单详情>></a></div>",
                suppName,
                goodsReturn.getReturnNo(),
                String.format(purchaseManagerOrderReturnLink, goodsReturn.getId()));
        this.sendSysLetterToBuyer(masterDO, letterTitle, suppName, letterContent);
    }

    /**
     * 发送拒绝取消代配送站内信给供应商
     * @param orderMasterDO 订单数据
     * @param reason 原因
     */
    @ServiceLog(description = "发送拒绝代配送站内信给供应商", operationType = OperationType.WRITE)
    public void sendRejectCancelDeliveryProxy2Supp(OrderMasterDO orderMasterDO, String reason){
        String letterTitle = "拒绝取消代配送服务通知";
        String letterContent = String.format("<div>您好，您的订单%s，采购人拒绝取消了代配送服务，拒绝原因：%s。" +
                        "<a href='%s' target='_blank' class='tm-m-color'>查看详情>></a>。</div>",
                orderMasterDO.getForderno(),
                reason,
                String.format(suppOrderDetailLink, orderMasterDO.getId()));
        this.sendSysLetterToSupp(orderMasterDO, letterTitle, null, letterContent);
    }

    /**
     * 发送站内信给采购人
     *
     * @param masterDO      订单信息
     * @param letterTitle   站内信标题
     * @param tag           会输出 #文案# 的效果
     * @param letterContent 站内信内容
     */
    @ServiceLog(description = "发送站内信给采购人", operationType = OperationType.WRITE)
    public void sendSysLetterToBuyer(OrderMasterDO masterDO, String letterTitle, String tag, String letterContent) {
        Integer buyerId = masterDO.getFbuyerid();
        Integer orgId = masterDO.getFuserid();
        List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(Collections.singletonList(buyerId), orgId);
        UserBaseInfoDTO user = userBaseInfoDTOList.get(0);
        SysSendDTO letterRequest = new SysSendDTO();
        letterRequest.setToken(PURCHASE_BIZ_TOKEN);
        letterRequest.setBusinessId(BusinessEnum.STORE.getVal());
        letterRequest.setTitle(letterTitle);
        letterRequest.setTag(tag);
        letterRequest.setContent(letterContent);
        letterRequest.setGuid(user.getGuid());
        letterRequest.setOrgId(orgId);
        sysLetterRpcServiceClient.sendLetterToUser(letterRequest);
    }

    /**
     * 发送站内信给采购人
     *
     * @param masterDO      订单信息
     * @param letterTitle   站内信标题
     * @param tag           会输出 #文案# 的效果
     * @param letterContent 站内信内容
     */
    @ServiceLog(description = "发送站内信给供应商", operationType = OperationType.WRITE)
    public void sendSysLetterToSupp(OrderMasterDO masterDO, String letterTitle, String tag, String letterContent){
        List<UserDTO> suppUserDTOList = suppClient
                .getSuppliersOrgBusinessUser(masterDO.getFsuppid(), masterDO.getFuserid());
        for (UserDTO  supplier : suppUserDTOList) {
            SysSendDTO letterRequest = new SysSendDTO();
            letterRequest.setToken(PURCHASE_BIZ_TOKEN);
            letterRequest.setBusinessId(BusinessEnum.SUPP.getVal());
            letterRequest.setTitle(letterTitle);
            letterRequest.setTag(tag);
            letterRequest.setContent(letterContent);
            letterRequest.setGuid(supplier.getGuid());
            letterRequest.setOrgId(masterDO.getFsuppid());
            sysLetterRpcServiceClient.sendLetterToUser(letterRequest);
        }
    }
}
