package com.ruijing.store.order.base.core.model;

import java.util.Date;

public class GoodsReturn {
    private Integer id;

    /**
    * 创建时间
    */
    private Date creationTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 退货原因
    */
    private String returnReason;

    /**
    * 说明
    */
    private String remark;

    /**
    * 退货数量
    */
    @Deprecated
    private Double quantity;

    /**
    * 退货价格
    */
    @Deprecated
    private Double price;

    /**
    * 关联的订单详情Id
    */
    @Deprecated
    private Integer detailId;

    /**
    * 用户Id
    */
    private Integer userId;

    /**
    * 供应商Id
    */
    private Integer supplierId;

    /**
    * 供应商名称
    */
    private String supplierName;

    /**
    * 状态
    */
    private Integer goodsReturnStatus;

    /**
    * 退货单号
    */
    private String returnNo;

    /**
    * 回复时间（包括同意时间和拒绝时间）
    */
    @Deprecated
    private Date replyTime;

    /**
    * 采购人退货时间
    */
    @Deprecated
    private Date returnTime;

    /**
    * 供应商收货时间
    */
    @Deprecated
    private Date receiveTime;

    /**
    * 取消退货申请时间
    */
    @Deprecated
    private Date cancelTime;

    /**
    * 拒绝原因
    */
    private String refuseReason;

    /**
    * 申请人
    */
    @Deprecated
    private String applyName;

    /**
    * 拒绝退货图片
    */
    private String picth;

    /**
    * 同意理由
    */
    private String agreeReason;

    /**
     * 退货商品详情JSON串
     */
    private String goodsReturnDetailJSON;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 部门/课题组id
     */
    private Integer departmentId;

    /**
     * 商品图片路径
     */
    private String productPath;

    /**
     * 是否作废，0否1是
     */
    private Integer invalid;

    /**
     * 采购部门名称
     */
    private String departmentName;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 采购人
     */
    private String buyerName;

    /**
     * 延迟验收次数
     */
    private int delayAcceptCount;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getGoodsReturnStatus() {
        return goodsReturnStatus;
    }

    public void setGoodsReturnStatus(Integer goodsReturnStatus) {
        this.goodsReturnStatus = goodsReturnStatus;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Date getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(Date returnTime) {
        this.returnTime = returnTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public String getApplyName() {
        return applyName;
    }

    public void setApplyName(String applyName) {
        this.applyName = applyName;
    }

    public String getPicth() {
        return picth;
    }

    public void setPicth(String picth) {
        this.picth = picth;
    }

    public String getAgreeReason() {
        return agreeReason;
    }

    public void setAgreeReason(String agreeReason) {
        this.agreeReason = agreeReason;
    }

    public String getGoodsReturnDetailJSON() {
        return goodsReturnDetailJSON;
    }

    public void setGoodsReturnDetailJSON(String goodsReturnDetailJSON) {
        this.goodsReturnDetailJSON = goodsReturnDetailJSON;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getProductPath() {
        return productPath;
    }

    public void setProductPath(String productPath) {
        this.productPath = productPath;
    }

    public Integer getInvalid() {
        return invalid;
    }

    public void setInvalid(Integer invalid) {
        this.invalid = invalid;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public int getDelayAcceptCount() {
        return delayAcceptCount;
    }

    public GoodsReturn setDelayAcceptCount(int delayAcceptCount) {
        this.delayAcceptCount = delayAcceptCount;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturn{");
        sb.append("id=").append(id);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", returnReason='").append(returnReason).append('\'');
        sb.append(", remark='").append(remark).append('\'');
        sb.append(", quantity=").append(quantity);
        sb.append(", price=").append(price);
        sb.append(", detailId=").append(detailId);
        sb.append(", userId=").append(userId);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", supplierName='").append(supplierName).append('\'');
        sb.append(", goodsReturnStatus=").append(goodsReturnStatus);
        sb.append(", returnNo='").append(returnNo).append('\'');
        sb.append(", replyTime=").append(replyTime);
        sb.append(", returnTime=").append(returnTime);
        sb.append(", receiveTime=").append(receiveTime);
        sb.append(", cancelTime=").append(cancelTime);
        sb.append(", refuseReason='").append(refuseReason).append('\'');
        sb.append(", applyName='").append(applyName).append('\'');
        sb.append(", picth='").append(picth).append('\'');
        sb.append(", agreeReason='").append(agreeReason).append('\'');
        sb.append(", detailReturnArrays='").append(goodsReturnDetailJSON).append('\'');
        sb.append(", orderId=").append(orderId);
        sb.append(", orgId=").append(orgId);
        sb.append(", orderNo='").append(orderNo).append('\'');
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", productPath='").append(productPath).append('\'');
        sb.append(", invalided='").append(invalid).append('\'');
        sb.append(", delayAcceptCount='").append(delayAcceptCount).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
