package com.ruijing.store.order.search.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.search.client.aggregation.DateHistogramItem;
import com.ruijing.search.client.aggregation.SumItem;
import com.ruijing.search.client.aggregation.TermsItem;
import com.ruijing.search.client.enums.SortOrder;
import com.ruijing.search.client.filter.NestedFilter;
import com.ruijing.search.client.filter.PhraseFilter;
import com.ruijing.search.client.filter.RangeFilter;
import com.ruijing.search.client.filter.TermFilter;
import com.ruijing.search.client.request.Request;
import com.ruijing.search.client.response.AggsResultItem;
import com.ruijing.search.client.response.Response;
import com.ruijing.search.client.sort.FieldSortItem;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.search.dto.IntervalDTO;
import com.ruijing.store.order.api.search.dto.OrderDateHistogramResultDTO;
import com.ruijing.store.order.api.search.dto.StatisticsManagerParamDTO;
import com.ruijing.store.order.api.search.enums.DateUnitEnum;
import com.ruijing.store.order.api.search.enums.OrderSearchFieldEnum;
import com.ruijing.store.order.rpc.client.OrderSearchRPCServiceClient;
import com.ruijing.store.order.search.service.OrderAggRelatedService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.ruijing.store.order.search.service.impl.OrderSearchBoostServiceImpl.NESTED_TABLE;
import static com.ruijing.store.order.search.service.impl.OrderSearchBoostServiceImpl.ORDER_SEARCH_INDEX;

/**
 * @author: liwenyu
 * @createTime: 2024-02-18 16:06
 * @description:
 **/
@Service
public class OrderAggRelatedServiceImpl implements OrderAggRelatedService {

    @Resource
    private OrderSearchRPCServiceClient orderSearchRpcServiceClient;

    private static final String AMOUNT_ITEM = "amountItem";

    private static final String DATE_AGG_ITEM = "dateAggItem";

    private static final String BUCKETS = "buckets";

    private static final String KEY = "key";

    @Override
    public List<OrderDateHistogramResultDTO> aggAmountAndQuantityByEntitiesDateHistogram(StatisticsManagerParamDTO paramDTO) {
        Request request = this.fillRequestStatistics(paramDTO);
        // 有传聚合字段，则根据字段先聚合再根据字段聚合
        String aggItem = "aggItem";
        OrderSearchFieldEnum aggField = paramDTO.getAggField();
        TermsItem itemAgg = new TermsItem(aggField.getField(), aggItem);
        itemAgg.setTop(paramDTO.getTopSize());
        request.addAggsItem(itemAgg);
        // 日期直方图聚合作为其子聚合
        itemAgg.addAggsItem(this.getDateHistogramItem(paramDTO));
        Response response = orderSearchRpcServiceClient.search(request);
        // 封装返回对象
        List<AggsResultItem> aggResultItems = response.getAggsResult().getAggsResultItems();
        if (CollectionUtils.isEmpty(aggResultItems)) {
            return New.emptyList();
        }
        JSONObject termsJson = JSON.parseObject(aggResultItems.get(0).getJson());
        JSONObject aggFieldJson = termsJson.getJSONObject(aggItem);
        JSONArray aggFieldBucketArray = aggFieldJson.getJSONArray(BUCKETS);
        List<OrderDateHistogramResultDTO> allResultDTOList = New.list();
        for(int i = 0; i < aggFieldBucketArray.size(); i++){
            JSONObject aggFieldBucketItem = aggFieldBucketArray.getJSONObject(i);
            String aggFieldValue = aggFieldBucketItem.getString(KEY);
            JSONObject dateAggItem = aggFieldBucketItem.getJSONObject(DATE_AGG_ITEM);
            // 获取日期直方图数据
            List<OrderDateHistogramResultDTO> dateHistogramResultDTOList = this.getHistogramResultList(dateAggItem);
            dateHistogramResultDTOList.forEach(item-> item.setAggFieldValue(aggFieldValue));
            allResultDTOList.addAll(dateHistogramResultDTOList);
        }
        return allResultDTOList;
    }

    /**
     * 从聚合结果对象获取直方图结果
     * @param dateAggItem 聚合结果对象
     * @return 直方图结果
     */
    private List<OrderDateHistogramResultDTO> getHistogramResultList(JSONObject dateAggItem){
        JSONArray dateAggBucket = dateAggItem.getJSONArray(BUCKETS);
        List<OrderDateHistogramResultDTO> dateHistogramResultDTOList = New.listWithCapacity(dateAggBucket.size());
        for(int j = 0; j < dateAggBucket.size(); j++){
            JSONObject dateObj = dateAggBucket.getJSONObject(j);
            dateHistogramResultDTOList.add(this.translateJsonToHistogramDto(dateObj));
        }
        return dateHistogramResultDTOList;
    }

    private OrderDateHistogramResultDTO translateJsonToHistogramDto(JSONObject jsonObject){
        Long timestamp =  jsonObject.getLong("key");
        String dateString = jsonObject.getString("key_as_string");
        Double orderCount = jsonObject.getDouble("doc_count");
        JSONObject sumJson = jsonObject.getJSONObject(AMOUNT_ITEM);
        Double orderAmount = sumJson.getDouble("value");
        OrderDateHistogramResultDTO aggregationResultDTO = new OrderDateHistogramResultDTO();
        aggregationResultDTO.setDateString(dateString);
        aggregationResultDTO.setTimestamp(timestamp);
        aggregationResultDTO.setAmount(orderAmount);
        aggregationResultDTO.setCount(orderCount);
        return aggregationResultDTO;
    }

    /**
     * 获取直方图聚合参数
     * @param paramDTO 请求参数
     * @return 直方图聚合参数
     */
    private DateHistogramItem getDateHistogramItem(StatisticsManagerParamDTO paramDTO){

        FieldSortItem fieldSortItem = new FieldSortItem("_key", SortOrder.DESC);
        SortOrderEnum sortOrderEnum = paramDTO.getSortOrderEnum();
        if (SortOrderEnum.ASC == sortOrderEnum) {
            fieldSortItem = new FieldSortItem("_key",SortOrder.ASC);
        }
        IntervalDTO intervalDate = paramDTO.getIntervalDate();
        Assert.isTrue(intervalDate != null ,"intervalDate不能为空！");
        String dateFormat = StringUtils.isNotBlank(intervalDate.getDateFormat()) ? intervalDate.getDateFormat() : "yyyy-MM-dd";
        int value = intervalDate.getValue() != null ? intervalDate.getValue() : 1;
        DateUnitEnum dateUnitEnum = intervalDate.getDateUnit() != null ? intervalDate.getDateUnit() : DateUnitEnum.MONTH;
        // 根据过滤条件的时间进行统计
        DateHistogramItem dateHistogramItem = new DateHistogramItem("forderdate" ,DATE_AGG_ITEM, dateFormat, new DateHistogramItem.Interval(value, dateUnitEnum.getSymbol()));

        dateHistogramItem.addAggsItem(new SumItem("forderamounttotal", AMOUNT_ITEM));
        dateHistogramItem.addSortItem(fieldSortItem);
        if (Objects.nonNull(paramDTO.getMinDocCount())) {
            dateHistogramItem.setMinDocCount(paramDTO.getMinDocCount());
        }
        return dateHistogramItem;
    }

    /**
     *  用统计对象 田中搜索request
     * @param paramDTO 入参
     */
    private Request fillRequestStatistics(StatisticsManagerParamDTO paramDTO) {
        Request request = new Request();
        request.setKey(ORDER_SEARCH_INDEX);
        request.setPageSize(0);

        //订单状态过滤
        List<Integer> statusList = paramDTO.getStatusList();
        if (CollectionUtils.isNotEmpty(statusList)) {
            request.addFilter(new TermFilter("status", statusList.toArray()));
        }

        //流程种类
        Integer species = paramDTO.getSpecies();
        if (species != null) {
            request.addFilter(new TermFilter("species", species));
        }

        // 订单来源筛选
        List<Integer> orderTypeList = paramDTO.getOrderTypeList();
        if (CollectionUtils.isNotEmpty(orderTypeList)) {
            request.addFilter(new TermFilter("order_type", orderTypeList.toArray()));
        }

        List<Integer> departmentParentId = paramDTO.getDepartmentParentId();
        if ( CollectionUtils.isNotEmpty(departmentParentId)) {
            request.addFilter(new TermFilter("department_parent_id", departmentParentId.toArray()));
        }

        //采购部门id过滤
        List<Integer> departmentIds = paramDTO.getDepartmentIds();
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            request.addFilter(new TermFilter("fbuydepartmentid", departmentIds.toArray()));
        }

        // 兼容单个单位与单位列表
        Integer orgId = paramDTO.getOrgId();
        List<Integer> orgIdList = paramDTO.getOrgIdList() == null ? New.list() : paramDTO.getOrgIdList();
        if (orgId != null) {
            orgIdList.add(orgId);
        }
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            request.addFilter(new TermFilter("fuserid", orgIdList));
        }

        String brandName = paramDTO.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            PhraseFilter nestedQuery = new PhraseFilter(NESTED_TABLE + "." + "fbrand", brandName);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestedQuery));
        }

        List<Long> productIdList = paramDTO.getProductIdList();
        if (CollectionUtils.isNotEmpty(productIdList)) {
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "product_id", productIdList);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }

        Integer buyerId = paramDTO.getBuyerId();
        if ( buyerId != null) {
            request.addFilter(new TermFilter("fbuyerid", buyerId));
        }

        Integer firstCategoryId = paramDTO.getFirstCategoryId();
        if(firstCategoryId != null){
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "first_category_id", firstCategoryId);
            request.addFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }

        List<Integer> notStatusList = paramDTO.getNotStatusList();
        //订单状态过滤
        if (CollectionUtils.isNotEmpty(notStatusList)) {
            request.addNotFilter(new TermFilter("status", notStatusList));
        }
        //供应商id
        List<Integer> suppIdList = paramDTO.getSuppIdList();
        if (CollectionUtils.isNotEmpty(suppIdList)) {
            request.addFilter(new TermFilter("fsuppid", suppIdList));
        }
        //  日期范围搜索
        String startTime = paramDTO.getStartTime();
        String endTime = paramDTO.getEndTime();
        final String orderDateField = "forderdate";
        if (!(StringUtils.isEmpty(startTime) && StringUtils.isEmpty(endTime))){
            request.addFilter(new RangeFilter(orderDateField, startTime, endTime));
        }

        // 去除的单位id列表
        List<Integer> excludeOrgIdList = paramDTO.getExcludeOrgIdList();
        if (CollectionUtils.isNotEmpty(excludeOrgIdList)) {
            request.addNotFilter(new TermFilter("fuserid", excludeOrgIdList));
        }

        // 去除的供应商id列表，明细和主表都要控制
        List<Integer> excludeSuppIdList = paramDTO.getExcludeSuppIdList();
        if (CollectionUtils.isNotEmpty(excludeSuppIdList)) {
            request.addNotFilter(new TermFilter("fsuppid", excludeSuppIdList));
            TermFilter nestedFilter = new TermFilter(NESTED_TABLE + "." + "supp_id", excludeSuppIdList);
            request.addNotFilter(new NestedFilter(NESTED_TABLE, nestedFilter));
        }

        // 需要排除的用户id，一般用于排除测试用户的数据
        List<Integer> excludeUserIdList = paramDTO.getExcludeUserIdList();
        if(CollectionUtils.isNotEmpty(excludeUserIdList)){
            request.addNotFilter(new TermFilter("fbuyerid", excludeUserIdList));
        }
        return request;
    }
}
