package com.ruijing.store.order.business.handler;

import com.reagent.auth.api.pojo.dto.UserDTO;
import com.reagent.auth.api.pojo.param.UserParam;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.reagent.supp.api.user.service.SupplierUserService;
import com.ruijing.base.letter.msg.BusinessEnum;
import com.ruijing.base.letter.msg.SysSendDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.dto.WeChatMessageDTO;
import com.ruijing.message.api.dto.WeChatMessageDataDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.message.api.enums.WeChatWordColorConstant;
import com.ruijing.message.api.service.MessageService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.exception.CallRpcException;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.store.cms.api.dto.SendingPersonalAndDefaultDTO;
import com.ruijing.store.cms.api.enums.GroupTypeEnum;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.cms.api.enums.SendingSettingStatusEnum;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import com.ruijing.store.cms.api.request.SendingPersonalAndDefaultParam;
import com.ruijing.store.cms.api.request.SendingPersonalSettingParam;
import com.ruijing.store.cms.api.request.SendingSettingParam;
import com.ruijing.store.delivery.service.util.DeliveryProxyUtil;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.enums.TimeOutBusinessType;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.other.dto.OrderTimeOutDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.FileUtils;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 订单邮件处理
 * @author: zhuk
 * @create: 2019-07-16 17:00
 **/
@ServiceLog
@Service
public class OrderEmailHandler {

    private static Logger logger = LoggerFactory.getLogger(OrderEmailHandler.class);

    @Resource
    private OrderDetailService orderDetailService;

    @Autowired
    private UserSyncClient userSyncClient;

    @Autowired
    private SuppClient suppClient;

    @Autowired
    private UserClient userClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private BidClient bidClient;

    @MSharpReference(remoteAppkey = "supp")
    private SupplierUserService supplierUserService;

    @MSharpReference(remoteAppkey = "msharp-message-service")
    private MessageService messageService;

    @Resource
    private SysLetterRpcServiceClient sysLetterRpcServiceClient;

    private static final String CAT_TYPE = "orderEmailHandler";

    /**
     * 申请取消订单 的邮件通知Title
     */
    public static final String APPLY_CANCEL_ORDER_TITLE = "申请订单取消通知";


    /**
     * 拒绝取消订单 的邮件通知Title
     */
    public static final String REFUSE_CANCEL_ORDER_TITLE = "拒绝订单取消通知";

    /**
     * 超时未验收订单 通知Title
     */
    public static final String TIME_OUT_ORDER_EXAMINE = "超时未验收订单通知";

    /**
     * 超时未结算订单 通知Title
     */
    public static final String TIME_OUT_ORDER_BALANCE = "超时未结算订单通知";

    /**
     * 订单验收 的邮件通知Title
     */
    public static final String ORDER_RECEIVE_ORDER_TITLE = "订单验收通知";

    public static final String SYSTEM_AUTO_CANCEL_ID = "-1" ;

    /**
     * 订单邮件 通用内容
     */
    public static final String EMAIL_CONTEXT_COMMON =
            "<a href='%s'>点击链接查看详情</a><br />"
                    + "订单号：%s<br />"
                    + "订单日期：%s<br />"
                    + "订单总价: %s<br />"
                    + "%s<br/>"
                    + "<br /><br /><br />------------------------------------------------------------------------<br />"
                    + "本邮件为系统邮件，请勿直接回复！【锐竞采购平台】";

    public static final String TABLE_HTML = "<table border='1' cellspacing='0' cellpadding='20'><tr><td>商品</td><td>品牌</td><td>货号</td><td>单位</td><td>单价</td></tr>%s</table>";
    public static final String TR_HTML = "<tr><td>%s</td><td>%s</td><td>%s</td><td>%s</td><td>%s</td></tr>";

    /**
     * 采购订单详情链接
     */
    public static final String WWW_ORDER_LINK = "https://www.rjmart.cn/PM/orderDetail?orderType=my&orderId=%s";

    /**
     * 供应商 订单详情链接
     */
    @PearlValue(key = "EMAIL_NEW_ORDER_SUPP_HTTP")
    public static String orderDetailLink;

    /**
     * wwCat 采购人 订单详情链接
     */
    @PearlValue(key = "weCat.orderDetail.url")
    public static String weCatPurchaserOrderDetailLink;

    /**
     * 采购人 订单详情链接
     */
    @PearlValue(key = "weChat.order.basic.url")
    public static String weChatOrderBasicLink;

    /**
     * 采购人 订单详情链接
     */
    @PearlValue(key = "EMAIL_WWW", defaultValue = WWW_ORDER_LINK)
    public static String purchaserOrderDetailLink;

    @PearlValue(key = "weChat.serviceKey")
    private String serviceKey;

    @PearlValue(key = "weChat.receiptTemplateId")
    private String weChatReceiptTemplateId;

    /**
     * 订单验收审批微信消息通知模板id
     */
    @PearlValue(key = "weChat.weChatApprovalOrderTemplateId")
    private String weChatApprovalOrderTemplateId;

    private static final Integer FIRST_APPROVAL_LEVEL = 1;

    /**
     * 待结算订单链接
     */
    @PearlValue(key = "order.await.balance")
    public static String waitingBalanceOrderLink;

    /**
     * 待验收订单链接
     */
    @PearlValue(key = "order.await.examine")
    private static String waitingExamineOrderLink;

    /**
     * 待验收订单链接
     */
    @PearlValue(key = "return.order.detail.for.supplier")
    public static String returnOrderDetailLink;

    /**
     * 通知管制品发货的微信模版id
     */
    @PearlValue(key = "wechat.regulatory.delivery.template.id")
    private String regulatoryDeliveryTemplateId;

    @PearlValue(key = "SEND_EMAIL.TARGET.EMAIL")
    private String SEND_EMAIL_TARGET_EMAIL;

    @PearlValue(key = "SEND_EMAIL.TARGET")
    private String SEND_EMAIL_TARGET_CONFIG;

    @PearlValue(key = "rj.domain.url")
    private String RJ_DOMAIN_URL;

    /**
     * 采购业务发送站内信专用，禁止其他业务使用！
     */
    private static final String PURCHASE_BIZ_TOKEN = "23234sdfi2dew3";

    /**
     * 通知管制品发货的微信模版id
     */
    @PearlValue(key = "order.developers.email")
    private String developersEmail;

    @Resource
    private CmsServerClient cmsServerClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;
    
    @Resource
    private OrderAddressRPCClient orderAddressRpcClient;

    @Resource
    private WeComMessageHandler weComMessageHandler;

    /**
     * 江西肿瘤医院 试剂、耗材 收货时 发送提醒邮件给采购人
     */
    public void sendEmailToPurchaserForJiangZhong(OrderMasterDO orderMaster){
        String emailContext = "订单"+ orderMaster.getForderno()
                +"已经到货并完成验收，请尽快到器械科领用 （如果货物已经到课题组请忽略）。"
                +"<a href='"+ String.format(purchaserOrderDetailLink, orderMaster.getId()) +"'>点我查看详情（如果无法打开，请登陆系统查看。）</a><br />"
                + "<br /><br /><br />------------------------------------------------------------------------<br />"
                + "本邮件为系统邮件，请勿直接回复！【锐竞采购平台】";
        // 获取订单的一级审批人
        UserBaseInfoDTO approval = null;
        if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMaster.getOrderType())) {
            Integer approvalId = purchaseApprovalLogClient.getApprovalIdForLevel(orderMaster.getFtbuyappid(), FIRST_APPROVAL_LEVEL);
            approval = userClient.getUserInfo(approvalId, orderMaster.getFuserid());
        } else {
            Integer approvalId = bidClient.getBidApprovalForLevel(orderMaster.getForderno(), orderMaster.getBidOrderId(), orderMaster.getFusercode(), FIRST_APPROVAL_LEVEL);
            approval = userClient.getUserInfo(approvalId, orderMaster.getFuserid());
        }
        // 邮件通知增加订单一级审批人
        String[] receiver = null;
        // 查无审批人，则只通知采购人
        if (approval == null) {
            receiver = new String[]{orderMaster.getFbuyeremail()};
        } else {
            // 邮箱去重
            List<String> emailList = New.list(approval.getEmail(), orderMaster.getFbuyeremail());
            receiver = emailList.toArray(new String[]{});
        }

        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setOrgId(orderMaster.getFuserid());
        messageDTO.setReceiver(receiver);
        messageDTO.setSubject("江西省肿瘤医院订单材料领用通知");
        messageDTO.setContent(emailContext);
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        messageService.asyncSend(messageDTO);
    }

    /**
     * 获取openid
     * @param orgId
     * @param userId
     * @return
     */
    private String getOpenId(Integer orgId, Integer userId) throws CallRpcException {
        UserBaseInfoDTO userInfo = userClient.getUserInfo(userId, orgId);
        String guid = userInfo.getGuid();
        UserParam userParam = new UserParam();
        userParam.setGuid(guid);
        UserDTO user = userSyncClient.getUser(userParam);
        if (user == null) {
            return "";
        }
        String openId = user.getOpenId();
        return openId;
    }

    /**
     * 江西肿瘤医院 试剂、耗材 收货时 推送微信提醒给采购人
     */
    public void sendWeChatToPurchaserForJiangZhong(OrderMasterDO orderMaster, String goodNames) throws CallRpcException {
        if (orderMaster == null) {
            logger.warn("江西肿瘤医院推送失败，订单信息无效！");
            return;
        }
        //获取openId
        String openId = this.getOpenId(orderMaster.getFuserid(), orderMaster.getFbuyerid());

        //拼装消息
        MessageDTO<WeChatMessageDTO> messageDTO = new MessageDTO();
        messageDTO.setOrgId(orderMaster.getFuserid());
        messageDTO.setMessageType(MessageTypeEnum.WECHAT_MESSAGE);
        WeChatMessageDTO dto = new WeChatMessageDTO();
        dto.setServiceKey(serviceKey);
        dto.setTemplateId(weChatReceiptTemplateId);
        WeChatMessageDataDTO firstDataDTO = new WeChatMessageDataDTO();
        firstDataDTO.setKeyword("first");
        String messageContent ="订单"+ orderMaster.getForderno()
                +"已经到货并完成验收，请尽快到器械科领用 （如果货物已经到课题组请忽略）。";
        firstDataDTO.setContent(messageContent);
        WeChatMessageDataDTO dataDTO1 = parseWeChatMessageDataDTO("keyword1", orderMaster.getFsuppname());
        WeChatMessageDataDTO dataDTO2 = parseWeChatMessageDataDTO("keyword2", DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT,orderMaster.getFdeliverydate()));
        WeChatMessageDataDTO dataDTO3 = parseWeChatMessageDataDTO("keyword3", goodNames);
        WeChatMessageDataDTO dataDTO4 = parseWeChatMessageDataDTO("keyword4", orderMaster.getFbuyername());
        WeChatMessageDataDTO remarkDataDTO = parseWeChatMessageDataDTO("remark", "");
        dto.setUrl(String.format(weCatPurchaserOrderDetailLink, orderMaster.getId()));
        dto.setList(New.list(firstDataDTO,dataDTO1,dataDTO2,dataDTO3,dataDTO4,remarkDataDTO));

        // 待通知的微信用户列表
        List<String> noticeOpenIdList = New.list();
        if (StringUtils.isNotBlank(openId)) {
            noticeOpenIdList.add(openId);
        }
        // 通知完采购人，开始通知订单的一级审批人
        UserBaseInfoDTO approval = null;
        if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderMaster.getOrderType())) {
            Integer approvalId = purchaseApprovalLogClient.getApprovalIdForLevel(orderMaster.getFtbuyappid(), FIRST_APPROVAL_LEVEL);
            approval = userClient.getUserInfo(approvalId, orderMaster.getFuserid());
        } else {
            Integer approvalId = bidClient.getBidApprovalForLevel(orderMaster.getForderno(), orderMaster.getBidOrderId(), orderMaster.getFusercode(), FIRST_APPROVAL_LEVEL);
            approval = userClient.getUserInfo(approvalId, orderMaster.getFuserid());
        }
        if (approval != null) {
            // 根据guid获取weChat用户
            List<UserDTO> weChatList = userSyncClient.getUsersByGuid(New.list(approval.getGuid()));
            if (CollectionUtils.isNotEmpty(weChatList)) {
                // openid去重
                weChatList.forEach(v -> {
                    if (!v.getOpenId().equals(openId)) {
                        noticeOpenIdList.add(v.getOpenId());
                    }
                });
            }
        }
        // openId 列表
        dto.setUserOpenIds(noticeOpenIdList);
        messageDTO.setT(dto);
        messageService.asyncSend(messageDTO);
    }

    /**
     * 采购人验收订单发送邮件通知供应商
     * @param orderMasterDO 订单信息
     */
    public void sendPurchaserReceiveOrderEmailToSupplier(OrderMasterDO orderMasterDO) {
        Set<String> suppUserEmails = getSuppValidEmails(orderMasterDO,SendingBusinessEnum.RECEIPT_ORDER_TO_SUPP);
        if (CollectionUtils.isNotEmpty(suppUserEmails)) {
            String emailContext = createPurchaserReceiveOrderEmailContextToSuppliers(orderMasterDO);

            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setOrgId(orderMasterDO.getFuserid());
            messageDTO.setReceiver(suppUserEmails.toArray(new String[suppUserEmails.size()]));
            messageDTO.setSubject(ORDER_RECEIVE_ORDER_TITLE);
            messageDTO.setContent(emailContext);
            messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
            messageService.asyncSend(messageDTO);
        }
    }

    /**
     * 业务类型 根据业务类型获取  供应商 可以发送 邮件的邮箱
     * @param orderMasterDO
     * @param sendingBusiness
     * @return
     */
    public Set<String> getSuppValidEmails(OrderMasterDO orderMasterDO,SendingBusinessEnum sendingBusiness) {
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOList = getSuppliersContactByType(orderMasterDO);
        Set<String> suppUserEmails = New.set();
        //要返回的设置列表
        List<SendingSettingParam> sendingSettingParamList = New.list();
        for (com.ruijing.shop.crm.api.pojo.dto.UserDTO suppUser : suppUserDTOList) {
            //参数不完整默认发送
            if (StringUtils.isBlank(suppUser.getGuid())){
                suppUserEmails.add(suppUser.getEmail());
                continue;
            }
            SendingSettingParam sendingSettingParam = new SendingSettingParam();
            sendingSettingParam.setGuid(suppUser.getGuid());
            sendingSettingParam.setGroupId(String.valueOf(orderMasterDO.getFsuppid()));
            sendingSettingParam.setGroupType(GroupTypeEnum.SUPPLIER.getValue());
            sendingSettingParam.setBusiness(sendingBusiness.getValue());
            sendingSettingParam.setWay(SendingWayEnum.EMAIL.getValue().byteValue());
            sendingSettingParamList.add(sendingSettingParam);
        }
        List<SendingPersonalAndDefaultDTO> sendSetting = cmsServerClient.getSendSetting(sendingSettingParamList);
        List<String> validSuppGuidList = sendSetting.stream().filter(setting -> setting.getEnable().equals(SendingSettingStatusEnum.OPEN.getValue().byteValue())).map(SendingPersonalAndDefaultDTO::getGuid).collect(Collectors.toList());
        Set<String> suppValidEmail = suppUserDTOList.stream().filter(supp -> validSuppGuidList.contains(supp.getGuid())).map(com.ruijing.shop.crm.api.pojo.dto.UserDTO::getEmail).collect(Collectors.toSet());
        suppUserEmails.addAll(suppValidEmail);
        return suppUserEmails;
    }

    /**
     * 通过调用rpc接口获取供应商的信息
     * @param orderMasterDO 订单信息
     * @return
     */
    private List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> getSuppliersContactByType(OrderMasterDO orderMasterDO) {
        // 获取供应商业务员
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppliersContactByType = null;
        try {
            suppliersContactByType = suppClient.getSuppliersOrgBusinessUser(orderMasterDO.getFsuppid(), orderMasterDO.getFuserid());
        } catch (Exception e) {
            logger.error("调用异常SupplierUserService服务queryForList: {}", e.getMessage());
            Cat.logError(CAT_TYPE, "SupplierUserService", "查询供应商服务异常", e);
            return New.emptyList();
        }
        return suppliersContactByType;
    }

    /**
     * 采购人验收订单创建邮件发送给供应商
     * @param orderMasterDO
     * @return
     */
    private String createPurchaserReceiveOrderEmailContextToSuppliers(OrderMasterDO orderMasterDO) {
        String emailHead = String.format("您好，价值%s元的订单已经由%s验收", orderMasterDO.getForderamounttotal().doubleValue(), orderMasterDO.getFlastreceiveman());
        String emailCommonContext = creatEmailCommonContext(orderMasterDO, orderDetailLink);
        String context = emailHead + emailCommonContext;
        return context;
    }

    /**
     * 订单相关邮件通用邮件体
     *
     * @param orderMaster
     * @param orderDetailUrl
     * @return
     */
    private String creatEmailCommonContext(OrderMasterDO orderMaster, String orderDetailUrl) {

        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setOrderMasterId(orderMaster.getId());
        RemoteResponse<List<OrderDetailDTO>> resultResponse = orderDetailService.findOrderDetailsByMasterId(orderDetailReq);
        StringBuilder sb = new StringBuilder();
        if (resultResponse.isSuccess()) {
            List<OrderDetailDTO> orderDetailDTOs = resultResponse.getData();
            for (OrderDetailDTO orderDetail : orderDetailDTOs) {
                sb.append(String.format(TR_HTML, orderDetail.getFgoodname(), orderDetail.getFbrand(), orderDetail.getFgoodcode(),
                        orderDetail.getFunit(), orderDetail.getFbidprice().toString()));
            }
        }
        String goodTable = sb.toString();

        String context = String.format(EMAIL_CONTEXT_COMMON, String.format(orderDetailUrl, orderMaster.getId()), orderMaster.getForderno(),
                DateUtils.format("yyyy/MM/dd", orderMaster.getForderdate()), orderMaster.getForderamounttotal().doubleValue(), String.format(TABLE_HTML, goodTable));
        return context;
    }

    /**
     * 创建课题组冻结催单邮件内容
     * @param departmentName 课题组名
     * @param orderNos 订单号数组
     * <div class="content">
     *         尊敬的用户：
     *     </div>
     *     >>>>>>>>>>>>结算内容>>>>>>>>>>>>
     *     <div class="content">
     *         您当前有#orderNum#张订单长时间未提交结算，请尽快操作，以免影响#deptName#采购。
     *     </div>
     *     <br>
     *      <div class="activeEmail">
     *          <a class="content" href="https://www.rjmart.cn/purchaser_Manager/index.html#presettle">待结算订单</a>
     *     </div>
     *     <br>
     *     <div class="content">
     *           超时未结算订单：<br>
     *           #orderNos#
     *     </div>
     *     >>>>>>>>>>>>结算内容>>>>>>>>>>>>
     *
     *     >>>>>>>>>>>>验收内容>>>>>>>>>>>>
     *     <div class="content">
     *         您当前有#num#张订单长时间未完成验收，请尽快操作，以免影响#deptName#采购。
     *     </div>
     *     <br>
     *     <div class="activeEmail">
     *           <a class="content" href="https://www.rjmart.cn/purchaser_Manager/index.html#order">待验收订单</a>
     *     </div>
     *     <br>
     *     <div class="content">
     *             超时未验收订单：<br>
     *             #orderNos#
     *     </div>
     *     >>>>>>>>>>>>验收内容>>>>>>>>>>>>
     *
     *     <br>
     *     <br>
     *     <div class="content">
     *         如您系统使用操作有疑问可查询系统帮助中心了解相关流程操作或咨询平台客服。
     *     </div>
     *     <div class="content">客服QQ：2448328732</div>
     *     <div class="content">电话：4009005786</div>
     *     <div class="cizhi">
     *        ————————————————————————
     *     </div>
     *     <div class="content">
     *         本邮件为系统邮件，请勿直接回复！【锐竞采购平台】
     *     </div>
     * @return
     */
    // todo
    private String createDepartmentFreezeNoticeToPurchaser(TimeOutBusinessType type, String departmentName, List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return StringUtils.EMPTY;
        }

        String splitOrders = orderNos.stream().collect(Collectors.joining("，"));
        StringBuilder builder = new StringBuilder();
        // 验收催单邮件内容
        if (type == TimeOutBusinessType.BALANCE) {
            // 结算催单邮件内容
            builder.append(String.format("您当前有%s张订单长时间未提交结算，请尽快操作，以免影响%s采购。", orderNos.size(), departmentName));
            builder.append("<br>\n");
            builder.append("<div class=\"activeEmail\">\n");
            builder.append("     <a class=\"content\" href=\"" + waitingBalanceOrderLink + "\">待结算订单</a>\n");
            builder.append("</div>\n");
            builder.append("<br>");
            builder.append(String.format("超时未结算订单：<br> %s", splitOrders));
        } else {
            // 验收催单邮件内容
            builder.append(String.format("您当前有%s张订单长时间未完成验收，请尽快操作，以免影响%s采购。", orderNos.size(), departmentName));
            builder.append("<br>\n");
            builder.append("<div class=\"activeEmail\">\n");
            builder.append("     <a class=\"content\" href=\"" + waitingExamineOrderLink + "\">待验收订单</a>\n");
            builder.append("</div>\n");
            builder.append("<br>");
            builder.append(String.format("超时未验收订单：<br> %s", splitOrders));
        }

        String emailContext = builder.toString();
        return emailContext;
    }

    /**
     * 异步发送课题组冻结催单邮件
     * @param departmentDTO
     * @param orders
     */
    public void sendEmailDepartmentFreezeNoticeToPurchaser(TimeOutBusinessType type, DepartmentDTO departmentDTO, List<String> emails, List<OrderTimeOutDTO> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        String emailContext = createDepartmentFreezeNoticeToPurchaser(type, departmentDTO.getName(), ListUtils.toList(orders, OrderTimeOutDTO::getOrderNo));
        if (StringUtils.isBlank(emailContext)) {
            return;
        }
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setOrgId(departmentDTO.getOrganizationId());
        messageDTO.setReceiver(emails.toArray(ArrayUtils.EMPTY_STRING_ARRAY));
        if (type == TimeOutBusinessType.ACCEPTANCE) {
            messageDTO.setSubject(TIME_OUT_ORDER_EXAMINE);
        } else {
            messageDTO.setSubject(TIME_OUT_ORDER_BALANCE);
        }
        messageDTO.setContent(emailContext);
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);

        messageService.asyncSend(messageDTO);
    }

    /**
     * 发送微信催单通知给课题组的用户
     * @param weChatUserList    用户
     * @param departmentDTO        课题组
     */
    public void sendWeChatToPurchaserCommon(List<UserDTO> weChatUserList, DepartmentDTO departmentDTO) {
        if (CollectionUtils.isEmpty(weChatUserList)) {
            logger.info("无微信用户需要通知！");
            return;
        }

        List<String> openIdList = ListUtils.toList(weChatUserList, UserDTO::getOpenId);
        // 给所有的微信用户发送催单通知
        for (String openId : openIdList) {
            MessageDTO<WeChatMessageDTO> messageDTO = new MessageDTO();
            messageDTO.setOrgId(departmentDTO.getOrganizationId());
            messageDTO.setMessageType(MessageTypeEnum.WECHAT_MESSAGE);
            WeChatMessageDTO dto = new WeChatMessageDTO();
            dto.setServiceKey(serviceKey);
            dto.setTemplateId(weChatReceiptTemplateId);
            dto.setUserOpenId(openId);

            String content = String.format("您所在的 %s 已被冻结采购功能，将无法进行采购", departmentDTO.getName());
            WeChatMessageDataDTO firstDataDTO = parseWeChatMessageDataDTO("first", content);
            WeChatMessageDataDTO dataDTO1 = parseWeChatMessageDataDTO("keyword1", "该课题组存在订单长时间未验收或未发起结算", WeChatWordColorConstant.BLUE);
            WeChatMessageDataDTO dataDTO2 = parseWeChatMessageDataDTO("keyword2", DateUtils.format("yyyy年MM月dd日 HH:mm", new Date()), WeChatWordColorConstant.BLUE);
            WeChatMessageDataDTO remarkDto = parseWeChatMessageDataDTO("remark", "订单验收／结算后即可自动解冻，点击可进行订单验收");

            dto.setList(New.list(firstDataDTO,dataDTO1,dataDTO2,remarkDto));
            messageDTO.setT(dto);
            // 异步发送通知消息
            messageService.asyncSend(messageDTO);
        }
    }

    /**
     * 管制品发货微信推送消息
     * @param goodNames         管制品商品名
     * @param userBaseInfoList  用户数组
     */
    public void noticeRegulatoryDelivery(String goodNames, List<UserBaseInfoDTO> userBaseInfoList) {
        if (StringUtils.isBlank(regulatoryDeliveryTemplateId)) {
            logger.error("配置 wechat.regulatory.delivery.template.id 为空, 请补全");
            Cat.logWarn(CAT_TYPE, "noticeRegulatoryDelivery", "配置 wechat.regulatory.delivery.template.id 为空, 请补全");
            return;
        }

        List<String> guidList = ListUtils.toList(userBaseInfoList, UserBaseInfoDTO::getGuid);
        // 根据guid查询微信用户信息
        List<UserDTO> weChatUserList = userSyncClient.getUsersByGuid(guidList);
        if (CollectionUtils.isEmpty(weChatUserList)) {
            logger.error("管制品发货通知失败！查无微信用户！");
            Cat.logWarn(CAT_TYPE, "noticeRegulatoryDelivery", "管制品发货通知失败！查无微信用户！");
            return;
        }

        Map<String, String> guidOpenIdMap = DictionaryUtils.toMap(weChatUserList, UserDTO::getGuid, UserDTO::getOpenId);
        String date = DateUtils.format("yyyy年M月d日 H:mm", new Date());
        for (UserBaseInfoDTO user : userBaseInfoList) {
            if (guidOpenIdMap.containsKey(user.getGuid())) {
                // 获取对应用户的openId
                String openId = guidOpenIdMap.get(user.getGuid());
                WeChatMessageDTO weChatMessageDTO = new WeChatMessageDTO();
                weChatMessageDTO.setUserOpenId(openId);
                weChatMessageDTO.setServiceKey(serviceKey);
                weChatMessageDTO.setTemplateId(regulatoryDeliveryTemplateId);

                // 组装微信模版消息
                List<WeChatMessageDataDTO> weChatMessageDataDTOList = new ArrayList<>(4);
                weChatMessageDataDTOList.add(parseWeChatMessageDataDTO("first", user.getName() + " 您好，以下管制品已发货, 请注意查收。"));
                weChatMessageDataDTOList.add(parseWeChatMessageDataDTO("keyword1", goodNames, WeChatWordColorConstant.BLUE));
                weChatMessageDataDTOList.add(parseWeChatMessageDataDTO("keyword2", date, WeChatWordColorConstant.BLUE));
                weChatMessageDataDTOList.add(parseWeChatMessageDataDTO("remark","备注: 详细情况可以登录系统查看订单详情", WeChatWordColorConstant.BLUE));
                weChatMessageDTO.setList(weChatMessageDataDTOList);
                MessageDTO<WeChatMessageDTO> messageDTO = new MessageDTO();
                messageDTO.setOrgId(user.getOrganizationId());
                messageDTO.setT(weChatMessageDTO);
                messageDTO.setMessageType(MessageTypeEnum.WECHAT_MESSAGE);

                // 异步发送管制品发货的通知消息
                messageService.asyncSend(messageDTO);
            } else {
                Cat.logWarn(CAT_TYPE, "noticeRegulatoryDelivery", "用户没有openId:" + user.getName());
                logger.info("用户:{}, 没有openId", user.getName());
            }
        }
    }

    /**
     * 微信消息模版对象封装, 不使用字体颜色
     * @param keyWord
     * @param content
     * @return
     */
    private WeChatMessageDataDTO parseWeChatMessageDataDTO(String keyWord, String content) {
        return parseWeChatMessageDataDTO(keyWord, content, null);
    }

    /**
     * 微信消息模版对象封装
     * @param keyWord 部门名字
     * @param content  通知内容
     * @return
     */
    private WeChatMessageDataDTO parseWeChatMessageDataDTO(String keyWord, String content, String color) {
        WeChatMessageDataDTO firstDataDTO = new WeChatMessageDataDTO();
        firstDataDTO.setKeyword(keyWord);
        firstDataDTO.setContent(content);
        if (color != null) {
            firstDataDTO.setColor(color);
        }
        return firstDataDTO;
    }

    /**
     * 生成订单发送邮件通知供应商
     */
    public void sendOrderGenerateEmailToSupp(OrderMasterDO orderMasterDO) {
        OrderAddressDTO orderAddressDTO = orderAddressRpcClient.findByOrderId(orderMasterDO.getId());
        this.sendOrderGenerateEmailToSupp(orderMasterDO, orderAddressDTO);
    }

    /**
     * 生成订单发送邮件通知供应商
     * @param orderMasterDO 订单数据
     * @param orderAddressDTO 订单地址数据
     */
    public void sendOrderGenerateEmailToSupp(OrderMasterDO orderMasterDO, OrderAddressDTO orderAddressDTO) {
        boolean deliveryProxyOnWithBuyerOpen = DeliveryProxyUtil.isDeliveryProxyOnWithBuyerOpen(orderAddressDTO);
        
        String emailTitle = orderMasterDO.getFsuppname() + "采购系统订单";
        String emailContext = getOrderGenerateEmailToSuppContext(orderMasterDO, deliveryProxyOnWithBuyerOpen);

        Set<String> suppUserEmails = getSuppValidEmails(orderMasterDO, SendingBusinessEnum.NEW_ORDER_TO_SUPP);

        if (CollectionUtils.isNotEmpty(suppUserEmails)) {
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setOrgId(orderMasterDO.getFuserid());
            messageDTO.setSubject(emailTitle);
            messageDTO.setContent(emailContext);
            messageDTO.setReceiver(suppUserEmails.toArray(new String[suppUserEmails.size()]));
            messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
            messageService.asyncSend(messageDTO);
        }

    }

    private String getOrderGenerateEmailToSuppContext(OrderMasterDO orderMasterDO, boolean showDeliveryProxyText) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("您好!")
                .append(orderMasterDO.getFsuppname())
                .append("，")
                .append(orderMasterDO.getFusername())
                .append(orderMasterDO.getFbuydepartment())
                .append("采购了一批")
                .append(ZhongShanDaXueOrgEnum.ZHONG_SHAN_DA_XUE_BAN_GONG.getOrgId() == orderMasterDO.getFuserid() ? "常购物资" : "实验耗材")
                .append("，总价格")
                .append(orderMasterDO.getForderamounttotal().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .append("元，请帮忙核对库存，尽快发货，采购订单号为：")
                .append(orderMasterDO.getForderno())
                .append("，如有问题，请与")
                .append(orderMasterDO.getFbuydepartment())
                .append("联系，谢谢【")
                .append(orderMasterDO.getFusername())
                .append("】<br /> <a href='")
                .append(String.format(orderDetailLink, orderMasterDO.getId()))
                .append("'>")
                .append(String.format(orderDetailLink, orderMasterDO.getId()))
                .append("</a><br />以上信息请以采购平台系统为准<br />");
        
        if(showDeliveryProxyText){
            // 采购人选择代配送额外内容
            stringBuilder.append("采购用户已选择平台代配送服务，你可以去")
                    .append("<a href='")
                    .append(DeliveryProxyUtil.SUPP_DELIVERY_PROXY_SETTING_PAGE)
                    .append("'>开启代配送服务</a>")
                    .append("。<br />");
        }
        stringBuilder.append("<br /><br />------------------------------------------------------------------------<br />本邮件为系统邮件，请勿直接回复！【锐竞采购平台】");
        return stringBuilder.toString();
    }

    /**
     * 生成订单通知供应商邮件文案
     * @param orderMasterDO
     * @return
     */
    private String getOrderGenerateMessageToSuppContext(OrderMasterDO orderMasterDO) {

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("您有一张新订单，")
                .append(orderMasterDO.getFusername())
                .append(orderMasterDO.getFbuydepartment())
                .append("采购了一批商品，价格为")
                .append(orderMasterDO.getForderamounttotal().setScale(2, RoundingMode.HALF_UP).toPlainString())
                .append("元，请登录锐竞平台查看。温馨提醒: 短信提醒服务将在2022-8-4号后关闭，请尽快关注【锐竞采购平台】公众号进入”商家中心“进行消息绑定。【锐竞信息】");
        return stringBuilder.toString();
    }

    /**
     * 业务类型 根据业务类型获取  供应商 可以发送短信的电话号码
     * @param orderMasterDO
     * @param sendingBusiness
     * @return
     */
    private Set<String> getSuppValidPhones(OrderMasterDO orderMasterDO,SendingBusinessEnum sendingBusiness) {
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOList = getSuppliersContactByType(orderMasterDO);
        Set<String> suppUserPhones = New.set();
        //要返回的设置列表
        List<SendingSettingParam> sendingSettingParamList = New.list();
        for (com.ruijing.shop.crm.api.pojo.dto.UserDTO suppUser : suppUserDTOList) {
            //参数不完整默认发送
            if (StringUtils.isBlank(suppUser.getGuid())){
                suppUserPhones.add(suppUser.getMobile());
                continue;
            }
            SendingSettingParam sendingSettingParam = new SendingSettingParam();
            sendingSettingParam.setGuid(suppUser.getGuid());
            sendingSettingParam.setGroupId(String.valueOf(orderMasterDO.getFsuppid()));
            sendingSettingParam.setGroupType(GroupTypeEnum.SUPPLIER.getValue());
            sendingSettingParam.setBusiness(sendingBusiness.getValue());
            sendingSettingParam.setWay(SendingWayEnum.MESSAGE.getValue().byteValue());
            sendingSettingParamList.add(sendingSettingParam);
        }
        List<SendingPersonalAndDefaultDTO> sendSetting = cmsServerClient.getSendSetting(sendingSettingParamList);
        List<String> validSuppGuidList = sendSetting.stream().filter(setting -> setting.getEnable().equals(SendingSettingStatusEnum.OPEN.getValue().byteValue())).map(SendingPersonalAndDefaultDTO::getGuid).collect(Collectors.toList());
        Set<String> suppValidPhones = suppUserDTOList.stream().filter(supp -> validSuppGuidList.contains(supp.getGuid())).map(com.ruijing.shop.crm.api.pojo.dto.UserDTO::getMobile).collect(Collectors.toSet());
        suppUserPhones.addAll(suppValidPhones);
        return suppUserPhones;
    }


    /**
     * 给供应商 发送采购人申请退货的邮件
     *
     * @param goodsReturn 退货单信息
     * @param operatorName 操作人
     */
    public void sendApplyReturnEmailToSupplier(GoodsReturn goodsReturn, String operatorName) {
        this.sendApplyReturnEmailToSupplier(goodsReturn, operatorName, false);
    }

    /**
     * 给供应商 发送采购人申请退货的邮件
     *
     * @param goodsReturn 退货单信息
     * @param operatorName 操作人
     */
    public void sendApplyReturnEmailToSupplier(GoodsReturn goodsReturn, String operatorName, boolean isDelay) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setOrgId(goodsReturn.getOrgId());
        Set<String> receiverEmailList = new HashSet<>();

        String emailContext = this.createApplyReturnEmailToSupplier(goodsReturn, operatorName, isDelay);
        messageDTO.setContent(emailContext);
        messageDTO.setSubject("退货申请通知");
        if (isDelay) {
            messageDTO.setSubject("退货申请提醒");

        }
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOList = suppClient.getSuppliersOrgBusinessUser(goodsReturn.getSupplierId(), goodsReturn.getOrgId());
        flag:
        for (com.ruijing.shop.crm.api.pojo.dto.UserDTO suppUserDTO : suppUserDTOList) {
            receiverEmailList.add(suppUserDTO.getEmail());
            //如果是线下环境 只发一条测试
            if (!Environment.isOnlineEnv()) {
                break flag;
            }
        }
        String[] receiver = receiverEmailList.toArray(new String[receiverEmailList.size()]);
        messageDTO.setReceiver(receiver);
        messageService.asyncSend(messageDTO);
    }

    /**
     * 给供应商 发送采购人申请退货的邮件文案
     *
     * @return
     */
    // todo
    private String createApplyReturnEmailToSupplier(GoodsReturn goodsReturn, String operatorName, boolean isDelay) {
        String emailHead = "退货申请通知。";
        String goodsReturnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON);
        BigDecimal total = goodsReturnInfoDetailVOS.stream().map(GoodsReturnInfoDetailVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        String emailCommonContext = StringUtils.EMPTY;
        if (isDelay) {
            emailCommonContext = String.format("您好，您有一张退货申请单超过6小时未处理，请尽快处理，详情如下：\n单位：%s，课题组：%s，采购人：%s，退货单号：%s。点击链接查看详情，%s ",
                    goodsReturn.getOrgName(),
                    goodsReturn.getDepartmentName(),
                    goodsReturn.getBuyerName(),
                    goodsReturn.getReturnNo(),
                    String.format(returnOrderDetailLink, goodsReturn.getOrderId(), goodsReturn.getId())
            );
        } else {
            emailCommonContext = String.format("您好，%s医院的%s课题组%s申请退货，退货单号为%s。点击链接查看详情，%s \n货品总价：%s元",
                    goodsReturn.getOrgName(),
                    goodsReturn.getDepartmentName(),
                    operatorName,
                    goodsReturn.getReturnNo(),
                    String.format(returnOrderDetailLink, goodsReturn.getOrderId(), goodsReturn.getId()),
                    total.setScale(2, RoundingMode.HALF_UP).toPlainString()
            );
        }

        String context = emailHead + emailCommonContext;
        return context;
    }

    /**
     * 发送下单的站内信息提醒给供应商
     * @param masterDO      订单信息
     */
    public void sendOrderGenerateMessageTOSystemSupplier(OrderMasterDO masterDO) {
        String letterTitle = "新订单通知";
        String letterContent = String.format("<div>您好！您有一张新订单，%s医院%s采购了一批商品，价格为%s元，请尽快处理，<a href='%s' target='_blank' class='tm-m-color'>点击查看订单详情</a></div>",
                masterDO.getFusername(),
                masterDO.getFbuydepartment(),
                masterDO.getForderamounttotal().setScale(2, RoundingMode.HALF_UP).toPlainString(),
                String.format(orderDetailLink, masterDO.getId()));
        commonMessageTOSystemSupplier(masterDO,letterTitle, letterContent);
    }

    /**
     *
     * 发送发货提醒信息给供应商
     * @param masterDO
     */
    public void sendDeliveryRemindMessageTOSystemSupplier(OrderMasterDO masterDO){
        String letterTitle = "提醒发货订单通知";
        String letterContent = String.format("<div>您好！你有一张提醒发货订单%s，%s医院%s采购了一批商品，价格为%s元，请尽快处理，<a href='%s' target='_blank' class='tm-m-color'>点击查看订单详情</a></div>",
                masterDO.getForderno(),
                masterDO.getFusername(),
                masterDO.getFbuydepartment(),
                masterDO.getForderamounttotal().subtract(new BigDecimal(masterDO.getReturnAmount())).setScale(2, RoundingMode.HALF_UP).toPlainString(),
                String.format(orderDetailLink, masterDO.getId()));
        commonMessageTOSystemSupplier(masterDO,letterTitle, letterContent);
    }

    /**
     * 发拒绝取消代配送信息给供应商
     * @param masterDO 订单数据
     * @param reason 原因
     */
    public void sendRejectCancelDeliveryProxy2Supp(OrderMasterDO masterDO, String reason){
        String emailTitle = "拒绝取消代配送服务通知";
        String emailContext = String.format("您好，您的订单%s，采购人拒绝取消了代配送服务，拒绝原因：%s。<a href='%s' target='_blank' class='tm-m-color'>查看详情</a>。<br/>" +
                        "<br/>以上信息请以采购平台系统为准<br/>" +
                        "------------------------------------------------------------------------<br/>" +
                        "本邮件为系统邮件，请勿直接回复！【锐竞采购平台】",
                masterDO.getForderno(),
                reason,
                String.format(orderDetailLink, masterDO.getId()));

        Set<String> suppUserEmails = getSuppValidEmails(masterDO, SendingBusinessEnum.NEW_ORDER_TO_SUPP);

        if (CollectionUtils.isNotEmpty(suppUserEmails)) {
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setOrgId(masterDO.getFuserid());
            messageDTO.setSubject(emailTitle);
            messageDTO.setContent(emailContext);
            messageDTO.setReceiver(suppUserEmails.toArray(new String[suppUserEmails.size()]));
            messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
            messageService.asyncSend(messageDTO);
        }
    }

    /**
     * 公用信息提醒给供应商
     *      发送人给供应商对应单位业务员
     * @param masterDO 订单信息
     * @param letterTitle 消息主题
     * @param letterContent 消息内容
     */
    public void commonMessageTOSystemSupplier(OrderMasterDO masterDO,String letterTitle, String letterContent) {
        List<com.ruijing.shop.crm.api.pojo.dto.UserDTO> suppUserDTOList = suppClient
                .getSuppliersOrgBusinessUser(masterDO.getFsuppid(), masterDO.getFuserid());
        for (com.ruijing.shop.crm.api.pojo.dto.UserDTO supplier : suppUserDTOList) {
            SysSendDTO letterRequest = new SysSendDTO();
            letterRequest.setToken(PURCHASE_BIZ_TOKEN);
            letterRequest.setBusinessId(BusinessEnum.SUPP.getVal());
            letterRequest.setTitle(letterTitle);
            letterRequest.setTag(StringUtils.EMPTY);
            letterRequest.setContent(letterContent);
            letterRequest.setGuid(supplier.getGuid());
            letterRequest.setOrgId(masterDO.getFsuppid());
            sysLetterRpcServiceClient.sendLetterToUser(letterRequest);
        }
    }

    /**
     * 发送警告给开发者
     * @param title         邮件标题
     * @param context       邮件内容
     */
    public void sendWarningToDeveloper(String title, String context) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setSubject(title);
        messageDTO.setContent(context);
        String[] developers = this.developersEmail.split(",");
        messageDTO.setReceiver(developers);
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        messageService.asyncSend(messageDTO);
    }


    /**
     * 发送拆单邮件提醒采购人
     * @param orderId
     * @return
     */
    public boolean sendSplitOrderDeliveryEmail(Integer orderId) {
        if(Objects.isNull(orderId)){
            return false;
        }

        List<OrderMasterSearchDTO> orderQueryResult = orderSearchBoostService.searchOrderById(orderId);
        if (CollectionUtils.isEmpty(orderQueryResult)) {
            return false;
        }
        OrderMasterSearchDTO orderMasterSearchDTO = orderQueryResult.get(0);
        String orderNo = orderMasterSearchDTO.getForderno();
        char lastElement = orderNo.charAt(orderNo.length() - 1);
        if(Character.isDigit(lastElement)){
            return false;
        }
        String emailAddress = getEmailAddress(orderMasterSearchDTO.getFuserid(), orderMasterSearchDTO.getFbuyerid());
        if(StringUtils.isEmpty(emailAddress)){
            return false;
        }
        String subject = "拆单发货邮件提醒";
        String emailContent = getSplitOrderEmailContent(orderMasterSearchDTO);
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setOrgId(orderMasterSearchDTO.getFuserid());
        messageDTO.setReceiver(new String[]{emailAddress});
        messageDTO.setContent(emailContent);
        messageDTO.setSubject(subject);
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        messageService.asyncSend(messageDTO);
        return true;
    }

    /**
     * 拆单发货邮件文案
     * @param orderMasterSearchDTO
     * @return
     */
    private String getSplitOrderEmailContent(OrderMasterSearchDTO orderMasterSearchDTO) {
        String childOrderNO = orderMasterSearchDTO.getForderno();
        String orderNo = childOrderNO.substring(0, childOrderNO.length() - 1);
        String linkUrl = RJ_DOMAIN_URL + "/PM/orderList?applyNo=" + orderNo;
        String emailContent = FileUtils.translateTemplate("email-templet/SplitOrderDeliveryEmail.txt");
        String goodsRowTemp = "<tr><td>%s</td><td>%s</td><td>%s</td><td>%s</td></tr>";
        StringBuilder tableContent = new StringBuilder();
        for (OrderDetailSearchDTO dto : orderMasterSearchDTO.getOrderDetail()) {
            String goodsRow = String.format(goodsRowTemp, dto.getFgoodname(), dto.getFbrand(), dto.getFgoodcode(), dto.getFquantity());
            tableContent.append(goodsRow);
        }
        emailContent = emailContent
                .replace("#childOrderNo", childOrderNO)
                .replace("#orderNo", orderNo)
                .replace("#linkUrl", linkUrl)
                .replace("#tableContent", tableContent);
        return emailContent;
    }

    /**
     * 获取采购人联系人邮箱
     * @param orgId
     * @param buyerId
     * @return
     */
    private String getEmailAddress(Integer orgId, Integer buyerId){
        if(SEND_EMAIL_TARGET_CONFIG.equals("ourself")){
            return SEND_EMAIL_TARGET_EMAIL;
        }
        List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByUserIds(Collections.singletonList(buyerId));
        if (CollectionUtils.isEmpty(userBaseInfoDTOList)) {
            return StringUtils.EMPTY;
        }

        UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOList.get(0);
        SendingPersonalSettingParam settingParam = new SendingPersonalSettingParam();
        settingParam.setBusiness(SendingBusinessEnum.DELIVERY_REMINDER_TO_ORG.getValue());
        settingParam.setWay(SendingWayEnum.EMAIL.getValue().byteValue());
        settingParam.setGuid(userBaseInfoDTO.getGuid());
        SendingPersonalAndDefaultParam param = new SendingPersonalAndDefaultParam();
        param.setGuid(userBaseInfoDTO.getGuid());
        param.setGroupType(GroupTypeEnum.ORGANIZATION.getValue());
        param.setGroupId(orgId.toString());
        param.setPersonalSettingList(Collections.singletonList(settingParam));
        List<SendingPersonalAndDefaultDTO> settings = cmsServerClient.getSettingListByPersonalAndDefault(param);
        if(CollectionUtils.isEmpty(settings)){
            return StringUtils.EMPTY;
        }
        for (SendingPersonalAndDefaultDTO setting : settings) {
            if (setting.getWay().equals(SendingWayEnum.EMAIL.getValue().byteValue())) {
                if (setting.getEnable() == SendingSettingStatusEnum.OPEN.getValue().byteValue()) {
                    return userBaseInfoDTO.getEmail();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    public <T> void sendEmail(MessageDTO<T> messageDTO){
        try{
            messageService.asyncSend(messageDTO);
        } catch (Exception e){
            logger.error("发送邮件失败", e);
        }

    }
}
