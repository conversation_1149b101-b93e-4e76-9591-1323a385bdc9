package com.ruijing.store.order.business.service.impl.strategy;

import com.reagent.commonbase.constant.org.OrgConst;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.business.service.impl.OrderAcceptServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: <PERSON><PERSON>yu
 * @create: 2025-07-09 14:09
 * @description:
 */
@Service(OrgConst.JI_NAN_DA_XUE + OrderAcceptConstant.ACCEPT_SUFFIX)
public class JiNanDaXueOrderAcceptServiceImpl extends OrderAcceptServiceImpl {

    @Override
    public Integer calculateAcceptModel(OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailList, boolean isAcceptApproval, boolean platformWorkFunds, boolean unRelateOrderData) {
        boolean isDistributeStatement = OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(orderMasterDO.getFundStatus());
        if(isDistributeStatement){
            return 3;
        }
        return super.calculateAcceptModel(orderMasterDO, orderDetailList, isAcceptApproval, platformWorkFunds, unRelateOrderData);
    }
}
