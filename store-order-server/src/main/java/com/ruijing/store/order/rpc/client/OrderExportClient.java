package com.ruijing.store.order.rpc.client;

import com.reagent.order.base.order.dto.BasePageResultDTO;
import com.reagent.order.base.order.dto.OrderExportDTO;
import com.reagent.order.base.order.dto.OrderExportQueryDTO;
import com.reagent.order.base.order.dto.OrderExportResultDTO;
import com.reagent.order.base.order.service.OrderExportRpcService;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

@ServiceClient
public class OrderExportClient {

    @MSharpReference(remoteAppkey = "order-galaxy-service")
    private OrderExportRpcService orderExportRpcService;

    /**
     * 查询订单导出列表
     * @param orderExportQueryDTO
     * @return
     */
    @ServiceLog(description = "查询订单导出列表", serviceType = ServiceType.COMMON_SERVICE)
    public BasePageResultDTO<OrderExportDTO> findOrderExportList(OrderExportQueryDTO orderExportQueryDTO) {
        Preconditions.notNull(orderExportQueryDTO, "入参不能为空");
        Preconditions.notNull(orderExportQueryDTO.getUserId(), "用户id不能为空");
        RemoteResponse<BasePageResultDTO<OrderExportDTO>> remoteResponse = orderExportRpcService.findOrderExportList(orderExportQueryDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();

    }

    /**
     * 根据id删除订单导出信息
     * @param id
     */
    @ServiceLog(description = "根据id删除订单导出信息", serviceType = ServiceType.COMMON_SERVICE)
    public void deleteOrderExportInfoById(Integer id) {
        Preconditions.notNull(id,"id不能为空");
        RemoteResponse<Boolean> remoteResponse = orderExportRpcService.deleteOrderExportInfoById(id);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
    }

    /**
     * 保存订单导出信息
     * @param orderExportDTO
     * @return
     */
    @ServiceLog(description = "保存订单导出信息", serviceType = ServiceType.COMMON_SERVICE)
    public OrderExportResultDTO saveOrderExport(OrderExportDTO orderExportDTO) {
        Preconditions.notNull(orderExportDTO, "入参不能为空");
        RemoteResponse<OrderExportResultDTO> remoteResponse = orderExportRpcService.saveOrderExport(orderExportDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }

    /**
     * 更新订单导出信息
     * @param orderExportDTO
     */
    @ServiceLog(description = "更新订单导出信息", serviceType = ServiceType.COMMON_SERVICE)
    public void updateById(OrderExportDTO orderExportDTO) {
        Preconditions.notNull(orderExportDTO, "入参不能为空");
        Preconditions.notNull(orderExportDTO.getId(), "请传入id");
        RemoteResponse<Boolean> remoteResponse = orderExportRpcService.updateById(orderExportDTO);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
    }

    /**
     * 根据id查订单导出信息
     * @param id
     * @return
     */
    @ServiceLog(description = "根据id查订单导出信息", serviceType = ServiceType.COMMON_SERVICE)
    public OrderExportDTO findById(Integer id) {
        Preconditions.notNull(id, "id不能为空");
        RemoteResponse<OrderExportDTO> remoteResponse = orderExportRpcService.findById(id);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }
}
