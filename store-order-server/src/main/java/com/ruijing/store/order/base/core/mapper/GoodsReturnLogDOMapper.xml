<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.core.model.GoodsReturnLogDO">
    <!--@mbg.generated-->
    <!--@Table goods_return_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="return_id" jdbcType="INTEGER" property="returnId" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_type" jdbcType="INTEGER" property="operatorType" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="images_url" jdbcType="VARCHAR" property="imagesURL" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, return_id, operator_id, operator_name, operator_type, operation_type, remark, images_url,
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from goods_return_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from goods_return_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.GoodsReturnLogDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into goods_return_log (return_id, operator_id, operator_name,
    operator_type, operation_type, remark, images_url,
    create_time, update_time)
    values (#{returnId,jdbcType=INTEGER}, #{operatorId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR},
    #{operatorType,jdbcType=INTEGER}, #{operationType,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
    #{imagesURL,jdbcType=VARCHAR}
    #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.core.model.GoodsReturnLogDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into goods_return_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="returnId != null">
        return_id,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="imagesURL != null">
        images_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="returnId != null">
        #{returnId,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="imagesURL != null">
        #{imagesURL,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.core.model.GoodsReturnLogDO">
    <!--@mbg.generated-->
    update goods_return_log
    <set>
      <if test="returnId != null">
        return_id = #{returnId,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="imagesURL != null">
        images_url = #{imagesURL,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.core.model.GoodsReturnLogDO">
    <!--@mbg.generated-->
    update goods_return_log
    set return_id = #{returnId,jdbcType=INTEGER},
    operator_id = #{operatorId,jdbcType=INTEGER},
    operator_name = #{operatorName,jdbcType=VARCHAR},
    operator_type = #{operatorType,jdbcType=INTEGER},
    operation_type = #{operationType,jdbcType=INTEGER},
    remark = #{remark,jdbcType=VARCHAR},
    images_url = #{imagesURL,jdbcType=VARCHAR},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-01-09-->
  <select id="findByReturnId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods_return_log
        where return_id=#{returnId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-02-03-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO goods_return_log(
        return_id,
        operator_id,
        operator_name,
        operator_type,
        operation_type,
        remark,
        images_url
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.returnId,jdbcType=INTEGER},

          <choose>
            <when test="element.operatorId != null ">
              #{element.operatorId,jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.operatorName != null ">
              #{element.operatorName,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.operatorType != null ">
              #{element.operatorType,jdbcType=INTEGER},
            </when>
            <otherwise>
              2,
            </otherwise>
          </choose>

          <choose>
            <when test="element.operationType != null ">
              #{element.operationType,jdbcType=INTEGER},
            </when>
            <otherwise>
              0,
            </otherwise>
          </choose>

          <choose>
            <when test="element.remark != null ">
              #{element.remark,jdbcType=VARCHAR},
            </when>
            <otherwise>
              '',
            </otherwise>
          </choose>

          <choose>
            <when test="element.imagesURL != null ">
              #{element.imagesURL,jdbcType=VARCHAR}
            </when>
            <otherwise>
              ''
            </otherwise>
          </choose>
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2021-04-19-->
  <select id="findByReturnIdInAndOperationType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods_return_log
        where return_id in
        <foreach item="item" index="index" collection="returnIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and operation_type=#{operationType,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2021-07-06-->
  <select id="findReturnIdByOperationAndUpdateTime" resultType="java.lang.Integer">
        select distinct return_id
        from goods_return_log
        <where>
            <if test="operationCollection != null and operationCollection.size() > 0">
                and operation_type in
                <foreach item="item" index="index" collection="operationCollection "
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
          and update_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2021-10-20-->
  <select id="findByReturnIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods_return_log
        where return_id in
        <foreach item="item" index="index" collection="returnIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

  <!--auto generated by MybatisCodeHelper on 2021-10-20-->
  <select id="findIdByOperationType" resultType="java.lang.Integer">
    select
    return_id
    from goods_return_log
    where operation_type=#{operationType,jdbcType=INTEGER}
  </select>
</mapper>