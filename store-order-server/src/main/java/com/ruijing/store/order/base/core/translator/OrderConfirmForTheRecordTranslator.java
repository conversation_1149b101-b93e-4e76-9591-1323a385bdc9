package com.ruijing.store.order.base.core.translator;

import com.ruijing.store.order.api.base.other.dto.OrderConfirmForTheRecordDTO;
import com.ruijing.store.order.base.minor.model.OrderConfirmForTheRecordDO;

/**
 * 订单备案记录转换类
 */
public class OrderConfirmForTheRecordTranslator {

    /**
     * DO 转 DTO
     * @param confirm
     * @return
     */
    public static OrderConfirmForTheRecordDTO doToDTO(OrderConfirmForTheRecordDO confirm) {
        OrderConfirmForTheRecordDTO result = new OrderConfirmForTheRecordDTO();
        result.setId(confirm.getId());
        result.setOrderId(confirm.getOrderId());
        result.setPics(confirm.getPics());
        result.setAddPics(confirm.getAddPics());
        result.setConfirm(confirm.getConfirm());
        result.setType(confirm.getType());
        result.setCreationTime(confirm.getCreationTime());
        result.setUpdateTime(confirm.getUpdateTime());
        result.setDeleted(confirm.getDeleted());
        result.setDeletionTime(confirm.getDeletionTime());

        return result;
    }

    /**
     * DTO 转 DO
     * @param confirm
     * @return
     */
    public static OrderConfirmForTheRecordDO dtoToDO(OrderConfirmForTheRecordDTO confirm) {
        OrderConfirmForTheRecordDO result = new OrderConfirmForTheRecordDO();
        result.setId(confirm.getId());
        result.setOrderId(confirm.getOrderId());
        result.setPics(confirm.getPics());
        result.setAddPics(confirm.getAddPics());
        result.setConfirm(confirm.getConfirm());
        result.setType(confirm.getType());
        result.setCreationTime(confirm.getCreationTime());
        result.setUpdateTime(confirm.getUpdateTime());
        result.setDeleted(confirm.getDeleted());
        result.setDeletionTime(confirm.getDeletionTime());

        return result;
    }
}
