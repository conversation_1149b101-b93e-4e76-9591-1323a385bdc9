package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.saturn.api.accept.approve.dto.AcceptApproveInfoDTO;
import com.ruijing.order.saturn.api.accept.approve.dto.RefAcceptApproveFlowLevelAuthUserDTO;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.search.dto.OrderExtraInfoParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.OrderApprovalService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalManInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderApprovalManItemVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptanceCountVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderListRespVO;
import com.ruijing.store.order.rpc.client.AcceptApprovalClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.user.api.constant.access.OrderAccessConstant;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

/**
 * <AUTHOR>
 * @Description 审批-订单信息相关Service
 * @Date: 2024/12/09 11:56
 **/
@Service
public class OrderApprovalServiceImpl implements OrderApprovalService {

    @Resource
    private UserClient userClient;

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private AcceptApprovalClient acceptApprovalClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Override
    public PageableResponse<OrderListRespVO> getMyPendingOrderList(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHms) {
        request.setMyOrderCheck(true);
        request.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        if(!OrgEnum.SHEN_ZHEN_YI_XUE_KE_XUE_YUAN.getCode().equals(loginInfo.getOrgCode())){
            // 深圳医科学院的奇葩要求，补充验收审批流程前的订单也要做手动验收审批，且不让回退状态。这里不做状态限制来满足此要求
            request.setStatus(OrderStatusEnum.OrderReceiveApproval.getValue());
        }
        request.setNeedOfflineInfo(true);

        // 陆军医的, 写死只能查看当前登录用户所在的课题组的单 modified by zyl
        if (OrgEnum.LU_JUN_JUN_YI_DA_XUE.getCode().equals(loginInfo.getOrgCode())) {
            request.setFbuyerId(loginInfo.getUserId());
        } else {
            // 不管是否有其他权限，只要有待我审批快照就能看
            List<DepartmentDTO> rootDept = userClient.getDepartmentListByIds(New.list(loginInfo.getRootDepartmentId()));
            loginInfo.setDeptList(rootDept);

            // 设置查询参数为当前登录用户id，到order_extra去找匹配项
            OrderExtraInfoParamDTO orderExtraInfoParamDTO = new OrderExtraInfoParamDTO();
            orderExtraInfoParamDTO.setOrderExtraEnum(OrderExtraEnum.ACCEPT_APPROVE_USERS);
            orderExtraInfoParamDTO.setOrderExtraValue(loginInfo.getUserId().toString());
            request.setOrderExtraInfoList(New.list(orderExtraInfoParamDTO));
        }
        return buyerOrderService.getOrderListForWWW(request, loginInfo, isHms);
    }

    /**
     * 我的已审批订单
     *
     * @param request   分页入参
     * @param loginInfo 用户信息
     * @param isHms     是否为hms请求
     * @return 我的已审批订单
     */
    @Override
    public PageableResponse<OrderListRespVO> myApprovedList(OrderListRequest request, LoginUserInfoBO loginInfo, boolean isHms) {
        request.setApproveStatusList(New.list(String.valueOf(OrderApprovalEnum.PASS.getValue()), String.valueOf(OrderApprovalEnum.REJECT.getValue())));
        request.setOperatorId(String.valueOf(loginInfo.getUserId()));
        // 设置所在课题组为根部门，用于跳过权限校验
        List<DepartmentDTO> rootDept = userClient.getDepartmentListByIds(New.list(loginInfo.getRootDepartmentId()));
        loginInfo.setDeptList(rootDept);
        PageableResponse<OrderListRespVO> pageableResponse = buyerOrderService.getOrderListForWWW(request, loginInfo, isHms);
        OrderListRespVO orderListRespVO = pageableResponse.getData();
        if (Objects.isNull(orderListRespVO) || CollectionUtils.isEmpty(orderListRespVO.getOrderList())) {
            return pageableResponse;
        }
        return pageableResponse;
    }

    /**
     * 根据入参查询当前单据审批流下可验收审批的人名列表
     *
     * @param request 传入订单id，可选部门id传入
     * @return 有验收审批权限的等级-人名列表
     */
    @Override
    public OrderApprovalManInfoVO getApproveFlowUserList(OrderListRequest request) {
        // 不考虑串用户，乱登录的情况，直接用部门id和当前登录单位来判断就好
        Integer orderId = request.getOrderId();
        Integer orgId = request.getOrgId();
        Preconditions.notNull(orderId, "需要传入订单id让后台查询");

        String approveManCache = "approveFlowMan_" + orderId;
        OrderApprovalManInfoVO approvalManListVO = (OrderApprovalManInfoVO) cacheClient.getFromCache(approveManCache);
        // 命中缓存，返回审批列表
        if (Objects.nonNull(approvalManListVO)) {
            return approvalManListVO;
        }
        OrderApprovalManInfoVO resp = new OrderApprovalManInfoVO();
        List<AcceptApproveInfoDTO> acceptApproveInfoDTOList = acceptApprovalClient.getAcceptApproveInfo(New.list(orderId));
        if(CollectionUtils.isEmpty(acceptApproveInfoDTOList)){
            return resp.setOrderApprovalManItemVOList(New.emptyList());
        }
        AcceptApproveInfoDTO approvalInfo = acceptApproveInfoDTOList.get(0);
        // 需要整合日志的情况：验收审批流完成/流转过（>一级的情况），即已办
        boolean isComplete = CommonValueUtils.TRUE_INT == approvalInfo.getIsComplete();
        boolean needMergeOrderLog = isComplete || approvalInfo.getCurrentLevel() > 1;
        // 是否需要整合待审批数据：验收审批流未完成，即代办
        boolean needMergeWaitingApproval = !isComplete;
        List<OrderApprovalManItemVO> orderApprovalManItemVOList = new ArrayList<>(8);
        if(needMergeWaitingApproval){
            List<RefAcceptApproveFlowLevelAuthUserDTO> levelAuthUserIdList = acceptApprovalClient.findOrderAllLevelApprovalAuthUser(request.getOrgId(), orderId);
            Set<Integer> userIdSet = levelAuthUserIdList.stream().map(RefAcceptApproveFlowLevelAuthUserDTO::getAuthUserIdList).flatMap(List::stream).collect(toSet());
            Map<Integer, String> userIdNameMap;
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                List<UserBaseInfoDTO> userBaseInfoDTOList = userClient.getUserByIdsAndOrgId(userIdSet, orgId);
                userIdNameMap = DictionaryUtils.toMap(userBaseInfoDTOList, UserBaseInfoDTO::getId, UserBaseInfoDTO::getName);
            } else {
                userIdNameMap = New.emptyMap();
            }
            for(RefAcceptApproveFlowLevelAuthUserDTO item : levelAuthUserIdList){
                if(item.getLevel() < approvalInfo.getCurrentLevel()){
                    // 只取待审批级别的
                    continue;
                }
                OrderApprovalManItemVO orderApprovalManItemVO = new OrderApprovalManItemVO();
                orderApprovalManItemVO.setLevel(item.getLevel());
                if(Boolean.TRUE.equals(item.getNoNeedApproval())){
                    orderApprovalManItemVO.setManList(New.list("无需审批"));
                }else {
                    orderApprovalManItemVO.setManList(item.getAuthUserIdList().stream().map(userIdNameMap::get).collect(toList()));
                }
                orderApprovalManItemVOList.add(orderApprovalManItemVO);
            }
        }
        if(needMergeOrderLog){
            OrderApprovalRequestDTO requestDTO = new OrderApprovalRequestDTO();
            requestDTO.setOrderIdList(New.list(orderId));
            requestDTO.setTypeList(New.list(OrderApprovalEnum.REJECT.getValue(), OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL.getValue()));
            List<OrderApprovalLogDTO> orderApprovalLogDTOList = orderApprovalLogService.getLastPassAcceptApproveLog(requestDTO);
            for (OrderApprovalLogDTO log : orderApprovalLogDTOList){
                if(isComplete || log.getApproveLevel() < approvalInfo.getCurrentLevel()){
                    String operatorName = OrderApprovalEnum.NO_NEED_ACCEPT_APPROVAL.getValue().equals(log.getApproveStatus()) ? "无需审批" : log.getOperatorName();
                    orderApprovalManItemVOList.add(new OrderApprovalManItemVO()
                            .setLevel(log.getApproveLevel())
                            .setManList(New.list(operatorName))
                            .setOperateTime(log.getCreationTime()));
                }
            }
        }
        orderApprovalManItemVOList.sort(Comparator.comparing(OrderApprovalManItemVO::getLevel));
        resp.setOrderApprovalManItemVOList(orderApprovalManItemVOList);
        // 存入缓存
        cacheClient.putToCache(approveManCache, resp, 30);
        return resp;
    }

    /**
     * 获取订单审批数量
     * @param rjSessionInfo 会话信息
     * @param request 订单列表请求参数
     */
    @Override
    public OrderAcceptanceCountVO getOrderAcceptanceCount(RjSessionInfo rjSessionInfo, OrderListRequest request) {
        OrderAcceptanceCountVO acceptanceCountVO = new OrderAcceptanceCountVO();
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.ORDER_VIEW);
        Boolean myOrderCheck = request.getMyOrderCheck();
        BusinessErrUtil.notNull(myOrderCheck, "myOrderCheck不能为空");
        // 待审批数量
        OrderListRequest pendingRequest = new OrderListRequest();
        pendingRequest.setPageNo(0);
        pendingRequest.setPageSize(0);
        pendingRequest.setMyOrderCheck(myOrderCheck);
        pendingRequest.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        if(!OrgEnum.SHEN_ZHEN_YI_XUE_KE_XUE_YUAN.getCode().equals(loginUserInfo.getOrgCode())){
            // 深圳医科学院的奇葩要求，补充验收审批流程前的订单也要做手动验收审批，且不让回退状态。这里不做状态限制来满足此要求
            pendingRequest.setStatus(OrderStatusEnum.OrderReceiveApproval.getValue());
        }
        pendingRequest.setNeedOfflineInfo(true);
        if (OrgEnum.LU_JUN_JUN_YI_DA_XUE.getCode().equals(loginUserInfo.getOrgCode())) {
            pendingRequest.setFbuyerId(loginUserInfo.getUserId());
        } else {
            // 不管是否有其他权限，只要有待我审批快照就能看
            List<DepartmentDTO> rootDept = userClient.getDepartmentListByIds(New.list(loginUserInfo.getRootDepartmentId()));
            loginUserInfo.setDeptList(rootDept);

            // 设置查询参数为当前登录用户id，到order_extra去找匹配项
            OrderExtraInfoParamDTO orderExtraInfoParamDTO = new OrderExtraInfoParamDTO();
            orderExtraInfoParamDTO.setOrderExtraEnum(OrderExtraEnum.ACCEPT_APPROVE_USERS);
            orderExtraInfoParamDTO.setOrderExtraValue(loginUserInfo.getUserId().toString());
            pendingRequest.setOrderExtraInfoList(New.list(orderExtraInfoParamDTO));
        }
        PageableResponse<OrderListRespVO> pendingPageable = buyerOrderService.getOrderListForWWW(pendingRequest, loginUserInfo, false);
        long pendingApprovalCount = pendingPageable.getTotal();

        // 已审批数量

        OrderListRequest approvedRequest = new OrderListRequest();
        approvedRequest.setPageNo(0);
        approvedRequest.setPageSize(0);
        approvedRequest.setApproveStatusList(New.list(String.valueOf(OrderApprovalEnum.PASS.getValue()), String.valueOf(OrderApprovalEnum.REJECT.getValue())));
        approvedRequest.setOperatorId(String.valueOf(loginUserInfo.getUserId()));
        // 设置所在课题组为根部门，用于跳过权限校验
        List<DepartmentDTO> rootDept = userClient.getDepartmentListByIds(New.list(loginUserInfo.getRootDepartmentId()));
        loginUserInfo.setDeptList(rootDept);
        PageableResponse<OrderListRespVO> approvedPageable = buyerOrderService.getOrderListForWWW(approvedRequest, loginUserInfo, false);
        long approvedCount = approvedPageable.getTotal();
        acceptanceCountVO.setPendingApprovalCount((int) pendingApprovalCount);
        acceptanceCountVO.setApprovedCount((int) approvedCount);
        return acceptanceCountVO;
    }

}
