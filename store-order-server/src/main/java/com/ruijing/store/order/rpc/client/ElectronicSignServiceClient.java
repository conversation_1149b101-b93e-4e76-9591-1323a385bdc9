package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.utils.RpcCallUtils;
import com.ruijing.store.electronicsign.api.dto.*;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignGroupEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.electronicsign.api.service.ElectronicSignUserConfigService;
import com.ruijing.store.electronicsign.api.service.ElectronicSignatureOperationService;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 11/15/2019 10:44 AM
 */
@ServiceClient
public class ElectronicSignServiceClient {

    @MSharpReference(remoteAppkey = "store-electronicsign-service")
    private ElectronicSignUserConfigService electronicSignUserConfigClient;

    @MSharpReference(remoteAppkey = "store-electronicsign-service")
    private ElectronicSignatureOperationService electronicSignOperationService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    /**
     * 获取电子签名数据
     * @param operationListDTO 电子签名数据
     * @return 电子签名记录
     */
    public List<ElectronicSignOperationRecordDTO> getElectronicSignData(OperationListDTO operationListDTO){
        Preconditions.notNull(operationListDTO.getBusinessType(),"单据业务类型为空!");
        Preconditions.notEmpty(operationListDTO.getBusinessIdList(),"单据id列表为空!");

        return RpcCallUtils.partitionExec(operationListDTO.getBusinessIdList(), 200, bussinessIdList->{
            OperationListDTO queryParam = new OperationListDTO();
            queryParam.setOperation(operationListDTO.getOperation());
            queryParam.setBusinessIdList(bussinessIdList);
            queryParam.setBusinessType(operationListDTO.getBusinessType());
            return electronicSignOperationService.operationList(queryParam);
        }, (response)->Preconditions.isTrue(response.isSuccess(),"获取用户电子签名数据失败"));
    }
    
    /**
     * 获取用户电子签名信息
     */
    @ServiceLog(description = "获取用户电子签名信息", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.READ)
    public UserConfigDTO findUserConfigByGuid(String guid) {
        Preconditions.isTrue(StringUtils.isNotEmpty(guid), "用户id不能为空！");
        RemoteResponse<UserConfigDTO> response = electronicSignUserConfigClient.findByGuid(guid);
        Preconditions.isTrue(response.isSuccess(), "获取用户电子签名信息失败: " + response.getMsg());
        return response.getData();
    }

    /**
     * 保存电子签名快照(留痕)
     */
    @ServiceLog(description = "保存电子签名快照(留痕)", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void saveElectronicSign(ElectronicSignDTO electronicSignDTO) {
        Preconditions.notNull(electronicSignDTO,"电子签名入参不能为空！");
        // 有密码就会校验密码，无密码则进行保存等其他操作
        RemoteResponse<ElectronicSignResultDTO> response = electronicSignOperationService.electronicSign(electronicSignDTO);
        Preconditions.isTrue(response.isSuccess(), "保存电子签名快照: " + response.getMsg());
        ElectronicSignResultDTO signResultDTO = response.getData();
        Preconditions.notNull(signResultDTO,"保存电子签名快照失败！");
        Preconditions.isTrue(signResultDTO.getFlag(), "保存电子签名失败: " + signResultDTO.getMessage());
    }

    /**
     * 查询电子签名配置
     * @param userGuid
     * @param orgCode
     * @param departmentId
     * @return
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "查询电子签名配置")
    public ElectronicSignOperationDTO searchOperationConfig(String userGuid, String orgCode, Integer departmentId, ElectronicSignatureOperationEnum electronicSignatureOperationEnum) {
        Preconditions.isTrue(StringUtils.isNotBlank(orgCode),"orgCode入参不能为空！");
        Preconditions.notNull(departmentId,"departmentId入参不能为空！");
        Preconditions.notNull(electronicSignatureOperationEnum,"电子签名操作枚举入参不能为空！");
        SearchOperationConfigDTO searchOperationConfigDTO = new SearchOperationConfigDTO();
        searchOperationConfigDTO.setUserGuid(userGuid);
        searchOperationConfigDTO.setOrgCode(orgCode);
        searchOperationConfigDTO.setOperation(electronicSignatureOperationEnum);
        searchOperationConfigDTO.setGroupCode(String.valueOf(departmentId));
        searchOperationConfigDTO.setGroupEnum(ElectronicSignGroupEnum.PURCHASE);
        RemoteResponse<ElectronicSignOperationDTO> response = electronicSignOperationService.searchOperationConfig(searchOperationConfigDTO);
        Assert.isTrue(response.isSuccess(), response.getMsg());
        Assert.notNull(response.getData(), "查不到对应的电子签名配置");
        return response.getData();
    }

    /**
     * 校验电子签名密码
     * @param validatePasswordDTO
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "校验电子签名密码")
    public void validatePassword(ValidatePasswordDTO validatePasswordDTO) {
        Preconditions.notNull(validatePasswordDTO,"validatePasswordDTO入参不能为空！");
        RemoteResponse response = electronicSignUserConfigClient.validatePassword(validatePasswordDTO);
        Assert.isTrue(response.isSuccess(), "电子签名校验失败:" + response.getMsg());
    }

}
