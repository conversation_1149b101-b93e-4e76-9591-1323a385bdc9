package com.ruijing.store.order.rpc.client;

import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.upload.client.FileUploadClient;

import java.util.Map;
import java.util.Objects;

/**
 * @author: zhukai
 * @date : 2021/1/11 下午4:06
 * @description:
 */
public class UploadFileClient {

    private UploadFileClient(){

    };

    /**
     * HMS 订单导出的oss目录id
     */
    public final static Integer ORDER_LIST_ID  = 34;

    /**
     * 订单交易统计目录id
     */
    public final static Integer ORDER_STATIC_ID = 36;

    /**
     * 采购人中心 订单导出的oss目录id
     */
    public final static Integer BUYER_ORDER_LIST_ID = 38;

    /**
     * 确认验收保存入库单
     */
    public final static Integer WARE_HOUSE_LIST_ID = 86;

    /**
     * 订单合同 pdf
     */
    public final static Integer ORDER_CONTRACT_ID = 89 ;

    /**
     * 出库单 订单列表重新生成按钮
     */
    public static final Integer OUT_WAREHOUSE_ID = 101;



    /**
     * key --》 id + env
     * value --》 secretAccess
     */
    private final static Map<String,String> secretAccessMap = New.map(
            "34test","IeE3Wo*ypkd8#r$ds8Wq",
            "34dev","IeE3Wo*ypkd8#r$ds8Wq",
            "34prod","H&63!c&0jpP4KJgUB7s&",
            "36prod","o#FHvcV3HgJ#OZP4P9wd",
            "36dev","o#FHvcV3HgJ#OZP4P9wd",
            "36test","o#FHvcV3HgJ#OZP4P9wd",
            "38test","M8X2OUCyFYF73skeE91t",
            "38dev","M8X2OUCyFYF73skeE91t",
            "38prod","D8Uw9ASSAG*GTAmsku62",
            "86test","123456",
            "86dev","123456",
            "86prod","123456",
            "89test","123456",
            "89dev","123456",
            "89prod", "123456",
            "101test", "123456",
            "101dev", "123456",
            "101prod", "123456"
    );

    /**
     * 上传client map  key --》keyId  value --》client实例
     */
    private static Map<Integer,FileUploadClient> fileUploadClientMap = New.concurrentMapWithCapacity(2);

    /**
     * 根据
     * @param keyId
     * @return
     */
    public static FileUploadClient getFileUploadClientById(Integer keyId) {
        FileUploadClient fileUploadClient = fileUploadClientMap.get(keyId);
        if (Objects.isNull(fileUploadClient)){
            String secretKey = keyId + Environment.getEnv();
            String secretValue = secretAccessMap.get(secretKey);
            fileUploadClient =  new FileUploadClient(keyId,secretValue);
            fileUploadClientMap.put(keyId,fileUploadClient);
        }
        return fileUploadClient;
    }
}
