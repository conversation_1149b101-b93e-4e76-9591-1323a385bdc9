package com.ruijing.store.order.gateway.oms.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @author: chenzhanliang
 * @createTime: 2024-11-28 15:55
 * @description:
 **/
@RpcModel("修正订单详情通用请求体")
public class FixOrderDetailCommonRequest implements Serializable {

    private static final long serialVersionUID = -8493520089244483866L;


    @RpcModelProperty("钉钉审批编号")
    private String dingTalkApprovalNumber;

    @RpcModelProperty("修正原因")
    private String reason;

    @RpcModelProperty("修改的订单详情")
    private List<FixOrderDetailRequest> fixOrderDetailList;

    public String getDingTalkApprovalNumber() {
        return dingTalkApprovalNumber;
    }

    public FixOrderDetailCommonRequest setDingTalkApprovalNumber(String dingTalkApprovalNumber) {
        this.dingTalkApprovalNumber = dingTalkApprovalNumber;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public FixOrderDetailCommonRequest setReason(String reason) {
        this.reason = reason;
        return this;
    }

    public List<FixOrderDetailRequest> getFixOrderDetailList() {
        return fixOrderDetailList;
    }

    public FixOrderDetailCommonRequest setFixOrderDetailList(List<FixOrderDetailRequest> fixOrderDetailList) {
        this.fixOrderDetailList = fixOrderDetailList;
        return this;
    }
}
