package com.ruijing.store.order.gateway.buyercenter.request.goodsreturn;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;

@Model("扫码退货商品详情")
public class GoodsReturnDetailRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("二维码")
    private String barCode;

    @ModelProperty("订单明细id")
    private Integer orderDetailId;

    @ModelProperty(value = "退货原因",description = "商品粒度")
    private String returnReason;

    @ModelProperty(value = "退货说明",description = "商品粒度")
    private String remark;

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public Integer getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Integer orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "GoodsReturnDetailRequest{" +
                "barCode='" + barCode + '\'' +
                ", orderDetailId=" + orderDetailId +
                ", returnReason='" + returnReason + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}