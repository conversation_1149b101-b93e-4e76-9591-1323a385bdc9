package com.ruijing.store.order.search.service;

import com.ruijing.search.client.request.Request;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.*;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggDTO;
import com.ruijing.store.statistic.api.search.dto.AdvertisementOrderAggRequest;
import com.ruijing.store.statistic.api.search.dto.ValidPurchaseCountDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description: 搜索强化
 * @author: zhuk
 * @create: 2019-08-22 14:49
 **/

public interface OrderSearchBoostService {

    /**
     * 订单搜索通用查询
     * @param orderSearchParamDTO
     * @return
     */
    SearchPageResultDTO<OrderMasterSearchDTO> commonSearch(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * 订单搜索
     * @param request
     * @return
     */
    SearchPageResultDTO<OrderMasterSearchDTO> search(Request request);

    /**
     * 根据结算单id 查询  订单
     * @param statementIdList 结算单id
     * @param orderDateSort 排序
     * @return
     */
    List<OrderMasterSearchDTO> searchOrderByStatementIds(List<Integer> statementIdList, SortOrderEnum orderDateSort);

    /**
     * 根据orderDetailId集合 查询订单 集合
     * @param detailIdList 订单详情id集合
     * @return List<OrderMasterSearchDTO>
     */
    List<OrderMasterSearchDTO> searchOrderByDetailIds(List<Integer> detailIdList);

    /**
     * 根据时间范围 获取推送的订单信息
     * @param orderPullParamDTO
     * @return
     */
    BasePageResponseDTO<OrderMasterSearchDTO> getBaseOrderByRangeDate(OrderPullParamDTO orderPullParamDTO);

    /**
     * 根据订单id 搜索订单
     * @param orderId
     * @return
     */
    List<OrderMasterSearchDTO> searchOrderById(Integer orderId);

    /**
     * 根据订单idList 搜索订单
     * @param orderIdList  订单id
     * @param orderDateSort 是否按照订单时间排序
     * @return  List<OrderMasterSearchDTO>
     */
    List<OrderMasterSearchDTO> searchOrderByIdList(List<Integer> orderIdList, SortOrderEnum orderDateSort);

    /**
     * 根据订单idList 搜索订单.并计算返回 现在实际的金额
     * @param orderIdList  订单id
     * @return  List<OrderMasterSearchDTO>
     */
    List<OrderMasterSearchDTO> getActualOrderByIdList(List<Integer> orderIdList, SortOrderEnum orderDateSort);

    /**
     * 统计某段时间活跃供应商数量（排除演示供应商）
     * @param omsStatisticsParamDTO 时间范围，状态list， getNoSuppIdList
     * @return 供应商数量
     */
    Double countSuppliers(OmsStatisticsParamDTO omsStatisticsParamDTO);

    /**
     * 统计一段时间内总买家量（排除演示单位）
     * @param omsStatisticsParamDTO  fuserid列表（不包含）
     * @return 买家数量
     */
    Double countBuyers(OmsStatisticsParamDTO omsStatisticsParamDTO);

    /**
     * 统计 一段时间 的 产品销量
     * @param  productSalesParamDTO 入参
     * @return 销量
     */
    double countProductSales(ProductSalesParamDTO productSalesParamDTO);


    /**
     * 统计 订单金额，课题组数量，供应商数量
     * @param paramDTO 入参
     * @return 订单金额，课题组数量，供应商数量
     */
    StatisticsManagerResultDTO countStatisticsOrder(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计供应商数量
     * @param paramDTO 入参
     * @return 供应商数量
     */
    StatisticsManagerResultDTO countSupplierQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计课题组数量
     * @param paramDTO 入参
     * @return 课题组数量
     */
    StatisticsManagerResultDTO countDepartmentQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * 订单管理 统计订单金额
     * @return 订单金额
     */
    Double countOrderTotalAmount(StatisticsManagerParamDTO paramDTO);

    /**
     *
     * 根据用户id 统计各状态的订单数量
     * @return k-订单状态 v-订单数量
     */
    Map<Integer,Integer> countOrderByStatus(OrderStatisticsParamDTO orderStatisticsParamDTO);

    /**
     *
     * 根据用户id 统计各代配送订单状态的数量
     * @return k-订单状态 v-订单数量
     */
    Map<Integer,Integer> countOrderByDeliveryStatus(OrderStatisticsParamDTO orderStatisticsParamDTO);

    /**
     * @description: 外部的搜索入参与实际搜索入参的转换
     * @date: 2021/3/31 18:15
     * @author: zengyanru
     * @param orderSearchParamDTO
     * @return com.ruijing.search.client.request.Request
     */
    Request searchRequestTransform(OrderSearchParamDTO orderSearchParamDTO);

    /**
     * @description: 通过搜索请求获取状态与订单数量对应map
     * @date: 2021/3/31 17:46
     * @author: zengyanru
     * @param request 搜索请求
     * @return java.util.Map<java.lang.Integer,java.lang.Integer>
     */
    Map<Integer, Integer> countOrderBySearchRequest(Request request);

    /**
     * 按字段聚合来查订单，获取数量
     * @param request 请求
     * @param aggField 聚合字段
     * @return 聚合结果
     */
    Map<Integer, Integer> aggFieldToCountOrderBySearchRequest(Request request, String aggField);

    /**
     * 查询医院购买量top N的商品
     * @return  list<商品id 销量>
     */
    List<OrderOrgStatResultDTO> countOrgTopProduct(OrderOrgStatParamDTO dto);

    /**
     * 查询统计相关合作商在该医院的销量
     * @return 销量
     */
    BigDecimal sumSuppOrgSale(OrderOrgSuppStatParamDTO dto);

    /**
     * 订单属性 聚合 订单金额和数量
     * @param paramDTO
     * @return
     */
    List<OrderAggregationResultDTO> aggOrderAmountAndCount(StatisticsManagerParamDTO paramDTO);

    /**
     * 订单商品属性 聚合  金额 数量
     * @param paramDTO
     * @return
     */
    List<OrderAggregationResultDTO> aggProductAmountAndCount(StatisticsManagerParamDTO paramDTO);

    /**
     * 订单时间柱状图聚合订单金额
     * @param paramDTO 入参
     * @return
     */
    List<OrderDateHistogramResultDTO>  aggOrderAmountDateHistogram(StatisticsManagerParamDTO paramDTO);

    /**
     * 统计交易商品的金额 和数量
     * @param paramDTO
     */
    OrderAggregationResultDTO sumProductAmountAndQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * @description: 按条件获取符合条件的条目数
     * @date: 2021/4/6 14:44
     * @author: zengyanru
     * @param request
     * @return java.lang.Integer
     */
    long searchCountByRequest(Request request);

    /**
     * 统计订单各个状态的数量，采购人中心用，上游是采购那边的：/store/apply/buyerCenter/getWaitToApproveOutline
     * @param request   入参
     * @return          <status, count>
     */
    Map<OrderStatusEnum, Long> countOrderStatusStatistics(OrderStatisticsParamDTO request);

    /**
     * 据条件返回 成功退货金额总和 的聚合结果
     * @param paramDTO
     * @return
     */
    List<GoodsReturnAggResDTO> aggReturnAmountByEntities(StatisticsManagerParamDTO paramDTO);

    /**
     * 按搜索条件，返回购买的商品数量信息
     * @param paramDTO
     * @return
     */
    List<ValidPurchaseCountDTO> aggPurchaseQuantity(StatisticsManagerParamDTO paramDTO);

    /**
     * 广告投放订单聚合，按照条件聚合下单买家数，下单数，下单金额，按照广告id分组
     * @param request
     * @return
     */
    List<AdvertisementOrderAggDTO> aggAdvertisementOrderGroupByNestedField(AdvertisementOrderAggRequest request);

    /**
     * 广告投放订单聚合，按照条件聚合下单买家数，下单数，下单金额，按照suppId分组、订单分组
     * @param request
     * @return
     */
    List<AdvertisementOrderAggDTO> aggAdvertisementOrderGroupByDocField(AdvertisementOrderAggRequest request);

    /**
     * 广告投放订单聚合，汇总统计
     * @param request
     * @return
     */
    List<AdvertisementOrderAggDTO> aggAdvertisementOrder(AdvertisementOrderAggRequest request);
}
