package com.ruijing.store.order.rpc.impl;

import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.bid.api.rpc.enums.OperationEnum;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.dto.request.ThirdPartOrderMasterDTO;
import com.reagent.order.enums.OrderEventTypeEnum;
import com.reagent.order.enums.OuterBuyerDockingTypeEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.fundcard.dto.v2.FundCardManagerDTO;
import com.reagent.research.fundcard.enums.FundCardEntityStatusEnum;
import com.reagent.research.fundcard.enums.FundCardLevelEnum;
import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.ruijing.base.gateway.api.dto.GatewayConstant;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.cooperation.cbsd.api.msg.CbsdStoreHouseDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.concurrent.ListenableFuture;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.pearl.client.PearlClient;
import com.ruijing.shop.crm.api.pojo.dto.LocationDTO;
import com.ruijing.shop.crm.api.pojo.dto.OfflineSupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.SupplierDTO;
import com.ruijing.shop.crm.api.pojo.dto.account.UserAccountDTO;
import com.ruijing.shop.goods.api.constant.RegulatoryTypeConstant;
import com.ruijing.shop.goods.api.dto.BaseProductDTO;
import com.ruijing.store.apply.dto.ApplicationDetailDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplicationQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionDTO;
import com.ruijing.store.apply.dto.application.ApplyMasterExtensionQueryDTO;
import com.ruijing.store.apply.dto.application.ApplyRefSuppBusinessDTO;
import com.ruijing.store.apply.enums.application.PushStatusEnum;
import com.ruijing.store.apply.enums.offline.OfflineProcurementSourceEnum;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.approval.api.enums.ApproveLevelEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderPrintDataConstant;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderTypeEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.*;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.api.base.other.dto.*;
import com.ruijing.store.order.api.gateway.dto.SupplierUserDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.*;
import com.ruijing.store.order.base.docking.mapper.DockingExtraMapper;
import com.ruijing.store.order.base.freezedeptlog.service.FreezeDeptLogService;
import com.ruijing.store.order.base.timeoutstatistics.constant.TimeOutStatisticsServiceConstant;
import com.ruijing.store.order.base.timeoutstatistics.service.TimeoutStatisticsService;
import com.ruijing.store.order.base.timeoutstatistics.service.impl.TimeoutStatisticsWithBalanceImpl;
import com.ruijing.store.order.base.util.OrderCommonUtils;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;
import com.ruijing.store.order.business.enums.myorderlist.OrderSpeciesEnum;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.service.*;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.constant.OrderOperationConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderAcceptCommentVO;
import com.ruijing.store.order.gateway.print.service.impl.GetPrintCommonDataService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.service.OldDateService;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.*;
import com.ruijing.store.user.api.constant.access.PurchaseAccessConstant;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.warehouse.message.bean.OutWarehouseApplicationBean;
import com.ruijing.store.warehouse.message.bean.ProductBean;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import com.ruijing.store.warehouse.service.OutWarehouseGWService;
import com.ruijing.store.warehouse.utils.translator.ProductBeanTranslator;
import com.ruijing.store.wms.api.dto.*;
import com.ruijing.store.wms.api.enums.InboundStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * @description: 订单管理策略
 * @author: zhongyulei
 * @create: 2019/11/6 10:30
 **/
@MSharpService
@ServiceLog
public class OrderManageRpcServiceImpl implements OrderManageRpcService {
    /**
     * 订单申请退货
     */
    private static final int ORDER_APPLY_RETURN = 0;

    /**
     * 订单撤销退货
     */
    private static final int ORDER_CANCEL_RETURN = 1;

    /**
     * 超时订单退货时, 影响的数据统计的影响因子
     */
    private static final int ORDER_RETURN_AFFECT_COUNT = 1;

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "OrderManageRpcServiceImpl";
    
    @PearlValue(key ="wechat.order.detail.url")
    private String WECHAT_ORDER_DETAIL_URL;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    private CancelOrderManageService cancelOrderManageService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Autowired
    private Map<String, TimeoutStatisticsService> timeoutStatisticsServiceMap;

    @Resource
    private FreezeDeptLogService freezeDeptLogService;

    @Resource
    private WaitingStatementService waitingStatementService;

    @Resource
    private UserClient userClient;

    @Resource
    private CooperationClient cooperationClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private ProductClient productClient;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private WalletOrderRpcClient walletOrderRpcClient;

    @Resource(name = "defaultIoExecutor")
    private Executor defaultIoExecutor;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;
    
    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private BizExitServiceClient bizExitServiceClient;

    @Resource
    private OutWarehouseGWService outWarehouseGWService;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private BidClient bidClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private DockingExtraMapper dockingExtraMapper;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;

    @Resource
    private TPIOrderClient tpiOrderClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private DockingConfigServiceClient dockingConfigServiceClient;

    @Resource
    private GetPrintCommonDataService getPrintCommonDataService;

    @Resource
    private OldDateService oldDateService;

    @Resource
    private OrderPicRelatedService orderPicRelatedService;

    @Resource
    private GenerateOrderService generateOrderService;

    @Resource
    private ClaimServiceClient claimServiceClient;

    /**
     * 使用新重推接口的状态，暂时给推送至供应商用，以后重构完成了，就是都走新接口
     */
    private static final List<Integer> USE_NEW_REDO_PUSH_INTERFACE_LIST = New.list(OrderStatusEnum.DeckingFail.getValue(), OrderStatusEnum.WaitingForConfirm.getValue(), OrderStatusEnum.PurchaseApplyToCancel.getValue());

    /**
     * 释放经费
     * @param orderBasicParamDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse orderFundCardUnFreezeById(OrderBasicParamDTO orderBasicParamDTO){
        Assert.isTrue(orderBasicParamDTO!=null,"订单id不能为空");
        Integer orderId = orderBasicParamDTO.getOrderId();
        orderManageService.orderFundCardUnFreezeById(orderId);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    public RemoteResponse orderFundCardUnFreeze(OrderUnFreezeRequestDTO orderUnFreezeRequestDTO) {
        Assert.isTrue(orderUnFreezeRequestDTO != null && orderUnFreezeRequestDTO.getOrderId() != null,"订单id不能为空");
        orderManageService.orderFundCardUnFreeze(orderUnFreezeRequestDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 用户验收订单
     * @param orderReceiptParamDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse userReceiptOrder(OrderReceiptParamDTO orderReceiptParamDTO){
        orderAcceptService.userAcceptOrder(orderReceiptParamDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 用户提交验收审批
     * @param orderApprovalParamDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse submitOrderApproval(OrderApprovalParamDTO orderApprovalParamDTO){
        orderManageService.submitOrderApproval(orderApprovalParamDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 多级验收审批完成，处理验收审批通过逻辑
     *
     * @param orderApprovalParamDTO 验收审批参数
     * @return 是否成功
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE,description = "多级验收审批完成，处理验收审批通过逻辑",serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse<Boolean> acceptApproveFlowPass(OrderApprovalParamDTO orderApprovalParamDTO) {
        orderManageService.acceptApproveFlowPass(orderApprovalParamDTO);
        return RemoteResponse.<Boolean>custom().setData(true).setSuccess();
    }

    /**
     * 江西肿瘤 验收 发送信息给采购人
     * @param orderId
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse sendReceiptMessageForJiangZhong(Integer orderId){
        orderManageService.sendReceiptMessageForJiangZhong(orderId);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    public RemoteResponse<BasePageResponseDTO<OrderMasterTimeOutDTO>> findTimeOutOrders(TimeOutOrderParamsDTO buyerParams) {
        // 入参处理, 查询时间跨度不可以超过12个月
        Integer limitDays = NumberUtils.toInt(PearlClient.get("timeout.limitday", "366"), 366);

        Date startDate = buyerParams.getStartDate();
        Date endDate = buyerParams.getEndDate();
        if (startDate != null && endDate != null) {
            double interval = TimeUtil.timeApartDay(startDate.getTime(), endDate.getTime());
            BusinessErrUtil.isTrue(limitDays >= interval, ExecptionMessageEnum.TIME_SPAN_LIMIT_EXCEEDED, (limitDays - 1));
        }

        // 获取超时订单
        BasePageResponseDTO<OrderMasterTimeOutDTO> result = orderManageService.findTimeOutOrders(buyerParams);
        return RemoteResponse.<BasePageResponseDTO<OrderMasterTimeOutDTO>>custom().setSuccess().setData(result).build();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse applyGoodsReturnWithTimeOut(GoodsReturnParamDTO params) {
        BusinessErrUtil.isTrue(params.getDepartmentId() != null, ExecptionMessageEnum.RETURN_FAILED_MISSING_DEPARTMENT_ID);
        OrderMasterDO orderMasterDO = getOrder(params);
        // 申请退货, 处理统计信息
        executeApplyGoodsReturn(params, orderMasterDO, ORDER_APPLY_RETURN);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse cancelGoodsReturnWithTimeOut(GoodsReturnParamDTO params) {
        BusinessErrUtil.isTrue(params.getDepartmentId() != null, ExecptionMessageEnum.CANCEL_RETURN_FAILED_MISSING_ID);
        OrderMasterDO orderMasterDO = getOrder(params);
        // 撤销退货, 处理统计信息
        executeApplyGoodsReturn(params, orderMasterDO, ORDER_CANCEL_RETURN);
        return RemoteResponse.custom().setSuccess().build();
    }

    private void executeApplyGoodsReturn(GoodsReturnParamDTO params, OrderMasterDO orderMasterDO, int operationType) {
        // todo 获取医院退货配置信息
//        sysConfigClient.getConfigByOrgCodeAndConfigCode("tuihuo");
        // 获取机构/医院配置字典
        Map<String, Integer> timeOutConfigMap = orderManageService.getTimeOutConfigMap(params.getOrgCode());

        // 退货的订单是否验收超时
        boolean isExamineTimeOut = OrderCommonUtils.isReceiveTimeOut(orderMasterDO, timeOutConfigMap);
        // 退货的订单是否结算超时
        boolean isBalanceTimeOut = OrderCommonUtils.isStatementTimeOut(orderMasterDO, timeOutConfigMap);
        if (!isExamineTimeOut && !isBalanceTimeOut) {
            return;
        }

        TimeoutStatisticsService timeoutStatisticsWithExamine = timeoutStatisticsServiceMap.get(TimeOutStatisticsServiceConstant.TIME_OUT_STATISTICS_WITH_EXAMINE);
        TimeoutStatisticsService timeoutStatisticsWithBalance = timeoutStatisticsServiceMap.get(TimeOutStatisticsServiceConstant.TIME_OUT_STATISTICS_WITH_BALANCE);

        // 退货减少超时统计数据, 并根据统计数据解冻课题组
        if (ORDER_APPLY_RETURN == operationType) {
            boolean isLessThanConfigExamineAmount = false;
            boolean isLessThanConfigBalanceAmount = false;

            if (isExamineTimeOut) {
                isLessThanConfigExamineAmount = timeoutStatisticsWithExamine.executeTimeOutStatisticsDecrease(ORDER_RETURN_AFFECT_COUNT, params, timeOutConfigMap);
                isLessThanConfigBalanceAmount = timeoutStatisticsWithBalance.executeTimeOutStatisticsDecrease(0, params, timeOutConfigMap);
            } else if (isBalanceTimeOut) {
                isLessThanConfigExamineAmount = timeoutStatisticsWithExamine.executeTimeOutStatisticsDecrease(0, params, timeOutConfigMap);
                isLessThanConfigBalanceAmount = ((TimeoutStatisticsWithBalanceImpl)timeoutStatisticsWithBalance).executeTimeOutStatisticsDecrease(ORDER_RETURN_AFFECT_COUNT, params, timeOutConfigMap, true);
            }

            // 同时低于 结算和验收 配置的张数阈值, 解冻课题组
            if (isLessThanConfigExamineAmount && isLessThanConfigBalanceAmount) {
                freezeDeptLogService.updateDeletedByOrgIdAndDepartmentIdAndType(params.getOrganizationId(), params.getDepartmentId());
            }

            // 撤销退货添加超时统计数据, 并根据统计数据冻结课题组
        } else if (ORDER_CANCEL_RETURN == operationType) {
            if (isExamineTimeOut) {
                timeoutStatisticsWithExamine.executeTimeOutStatisticsIncrease(ORDER_RETURN_AFFECT_COUNT, params, timeOutConfigMap);
            } else if (isBalanceTimeOut) {
                timeoutStatisticsWithBalance.executeTimeOutStatisticsIncrease(ORDER_RETURN_AFFECT_COUNT, params, timeOutConfigMap);
            }
        }
    }

    private OrderMasterDO getOrder(GoodsReturnParamDTO params) {
        Assert.isTrue(params.getOrderMasterId() != null, "退货失败, 订单id为空");

        // 查询订单主单
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(params.getOrderMasterId());
        Assert.isTrue(orderMasterDO != null, "退货失败, 退货单为空");
        return orderMasterDO;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse pushWaitingStatement(OrderBasicParamDTO param) {
        // 校验订单信息
        BusinessErrUtil.isTrue(param.getOrderId() != null, ExecptionMessageEnum.PUSH_PENDING_SETTLEMENT_NO_ORDER);
        OrderMasterDO order = orderMasterMapper.selectByPrimaryKey(param.getOrderId());
        BusinessErrUtil.isTrue(order != null, ExecptionMessageEnum.PUSH_PENDING_SETTLEMENT_FAILED);

        if (OrderStatusEnum.WaitingForStatement_1.getValue().equals(order.getStatus())) {
            // 如果退货金额和订单金额不一致，属于部分退货，应该推送待结算订单数据
            if (new BigDecimal(order.getReturnAmount()).compareTo(order.getForderamounttotal()) != 0) {
                waitingStatementService.pushWaitingStatement(order.getFusercode(), New.list(order.getId()));
            }
        }

        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse agreeCancelOrder(OrderMasterDTO param) {
        Assert.isTrue(param != null && param.getId() != null, "同意取消订单失败，订单入参为空！");

        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setStatus(param.getStatus());
        updateOrderParamDTO.setOrderId(param.getId());
        updateOrderParamDTO.setShutDownDate(param.getShutDownDate());
        try{
            cacheClient.controlRepeatOperation(OrderOperationConstant.BUYER_CENTER_ORDER_OP + param.getId().toString(),3 * 60);
            // 更新订单取消相关状态信息
            orderMasterMapper.updateOrderById(updateOrderParamDTO);
            // 删除发票信息
            invoiceClient.deleteInvoiceByOrderIds(New.list(param.getId()));
        } finally {
            cacheClient.removeCache(OrderOperationConstant.BUYER_CENTER_ORDER_OP + param.getId().toString());
        }
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 更新验收图片
     *
     * @param additionalAcceptancePicDTO
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse appendReceiveImages(AdditionalAcceptancePicDTO additionalAcceptancePicDTO) {
        Assert.notNull(additionalAcceptancePicDTO,"追加验证图片DTO为空");
        //追加验收图片
        orderPicRelatedService.appendReceiveImagesSkipCheck(additionalAcceptancePicDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog
    public RemoteResponse<List<OrderStoreHouseDTO>> findStorageByDepartmentId(Integer departmentId) {
        List<CbsdStoreHouseDTO> storeHouseList = cooperationClient.findStoreHouseByDepartmentId(departmentId);
        List<OrderStoreHouseDTO> result = storeHouseList.stream().map(s -> CooperationTranslator.dtoToOrderStorageDto(s)).collect(Collectors.toList());
        return RemoteResponse.<List<OrderStoreHouseDTO>>custom().setSuccess().setData(result).build();
    }

    /**
     * 拒绝取消订单
     * @param cancelOrderReqDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.RPC_SERVICE)
    public RemoteResponse refuseCancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        try {
            Preconditions.notNull(cancelOrderReqDTO, "拒绝取消订单入参为null");
            cancelOrderManageService.refuseCancelOrder(cancelOrderReqDTO);
        }catch (Exception e){
            Cat.logError("RPC调用供应商拒绝取消订单接口异常！",e);
            return RemoteResponse.custom().setFailure(e.getMessage()).build();
        }
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(description = "管制品发货通知微信用户")
    public RemoteResponse noticeRegulatoryByOrderNo(String orderNo) {
        Assert.notNull(orderNo, "管制品发货通知失败！订单空为空！");
        OrderMasterDO order = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(order, ExecptionMessageEnum.REGULATED_PRODUCT_DELIVERY_NO_ORDER);

        List<OrderDetailDO> orderDetailList = orderDetailMapper.findByFmasterid(order.getId());
        List<Long> productIdList = ListUtils.toList(orderDetailList, OrderDetailDO::getProductSn);

        // 查询商品信息
        List<BaseProductDTO> productDTOList = productClient.findByIdList(productIdList);
        // 过滤出管制品的商品
        List<Long> regulatoryIdList = productDTOList.stream()
                .filter(p -> RegulatoryTypeConstant.REGULATORY == p.getProductExt().getRegulatoryType())
                .map(BaseProductDTO::getId)
                .collect(Collectors.toList());
        // 管制品商品名
        String goodsName = orderDetailList.stream()
                .filter(d -> regulatoryIdList.contains(d.getProductSn()))
                .map(OrderDetailDO::getFgoodname)
                .distinct()
                .collect(Collectors.joining(";"));

        // 获取管制品用户组
        List<UserBaseInfoDTO> userBaseInfoList = userClient.getUserByPiAndDepartmentIdAndAccessCode(order.getFbuydepartmentid(), order.getFuserid(), PurchaseAccessConstant.REGULATORY_PURCHASE_MESSAGE);
        BusinessErrUtil.notEmpty(userBaseInfoList, ExecptionMessageEnum.REGULATED_PRODUCT_DELIVERY_FAILED);

        // 微信通知用户管制品发货了
        AsyncExecutor.listenableRunAsync(() -> {
            orderEmailHandler.noticeRegulatoryDelivery(goodsName, userBaseInfoList);
        }).addFailureCallback(throwable -> {
            LOGGER.error("微信通知用户管制品发货失败：" + throwable);
            Cat.logError(CAT_TYPE, "noticeRegulatoryDelivery", "微信通知用户管制品发货失败：", throwable);
            throw new IllegalStateException(throwable);
        });
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 同意取消订单
     * @param cancelOrderReqDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse agreeCancelOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        Assert.notNull(cancelOrderReqDTO, "同意取消订单入参为null");
        cancelOrderManageService.agreeCancelOrder(cancelOrderReqDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 取消订单
     * @param applyCancelOrderReqDTO
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse cancelOrder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        Assert.notNull(applyCancelOrderReqDTO, "取消订单入参为null");
        cancelOrderManageService.cancelOrder(applyCancelOrderReqDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<Integer> cancelOrderByAppId(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        final Integer appId = applyCancelOrderReqDTO.getAppId();
        final List<OrderMasterDO> orders = orderMasterMapper.findByFtbuyappidIn(New.list(appId));
        orders.forEach(it -> {
            applyCancelOrderReqDTO.setOrderId(it.getId());
            applyCancelOrderReqDTO.setFcancelmanid(it.getFbuyerid().toString());
            applyCancelOrderReqDTO.setFcancelman(it.getFbuyername());
            applyCancelOrderReqDTO.setFcancelreason("取消采购单");
            cancelOrder(applyCancelOrderReqDTO);
        });
        return RemoteResponse.<Integer>custom().setSuccess().build().setData(orders.size());
    }

    @Override
    public RemoteResponse cancelOrderByThunder(ApplyCancelOrderReqDTO applyCancelOrderReqDTO) {
        Preconditions.notNull(applyCancelOrderReqDTO, "取消订单入参为null");
        cancelOrderManageService.cancelOrderByThunder(applyCancelOrderReqDTO);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 生成订单
     *
     * @param generateOrderDTOS
     * @return
     */
    @Override
    @ServiceLog(operationType = OperationType.WRITE)
    public RemoteResponse<List<GenerateOrderResultDTO>> generateOrder(List<GenerateOrderDTO> generateOrderDTOS) {
        Assert.notNull(generateOrderDTOS, "生成订单入参信息为空");
        List<GenerateOrderResultDTO> generateOrderResultDTOList = generateOrderService.generateNewOrder(generateOrderDTOS);
        // 事务提交后，校验风险订单
        orderManageService.asyncVerifyOrderRisk(generateOrderResultDTOList.stream().map(GenerateOrderResultDTO::getOrderMasterId).collect(toList()));
        return RemoteResponse.<List<GenerateOrderResultDTO>>custom().setSuccess().setData(generateOrderResultDTOList);
    }


    @Override
    @ServiceLog(description = "打印线下汇总单")
    public RemoteResponse<List<PrintApproveDTO>> getOfflineSummaryPrintData(List<Integer> orderIdList) {
        return getCommonSummaryPrintData(orderIdList);
    }

    @Override
    public RemoteResponse noticeWaitingChargingOrder(OrderBasicParamDTO param) {
        BusinessErrUtil.isTrue(param != null && param.getOrderId() != null, ExecptionMessageEnum.ADD_TO_WALLET_QUEUE_FAILED);
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(param.getOrderId());
        // 加入订单钱包扣费队列
        walletOrderRpcClient.addOrderWalletQueue(orderMasterDO);

        return RemoteResponse.custom().setSuccess().build();
    }

    @Override
    @ServiceLog(description = "通用打印汇总单")
    public RemoteResponse<List<PrintApproveDTO>> getCommonSummaryPrintData(List<Integer> orderIdList) {
        Assert.notEmpty(orderIdList, "获取打印数据失败，订单id为空！");
        Integer orderId = orderIdList.get(0);
        Assert.notNull(orderId, "获取打印数据失败，订单为空！");

        OrderMasterDO orderDto = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderDto, ExecptionMessageEnum.PRINT_DATA_INVALID_ORDER);

        PrintApproveDTO printItem = new PrintApproveDTO();
        printItem.setOrderId(orderDto.getId());
        printItem.setHospitalName(orderDto.getFusername());
        printItem.setDepartmentName(orderDto.getFbuydepartment());
        printItem.setSuppName(orderDto.getFsuppname());
        printItem.setSuppCode(orderDto.getFsuppcode());
        printItem.setSummaryNo(orderDto.getForderno());
        printItem.setTelPhone(orderDto.getFbuyertelephone());
        printItem.setBuyerName(orderDto.getFbuyername());
        printItem.setTotalAmount(orderDto.getForderamounttotal());
        printItem.setExistReceivePhoto(StringUtils.isNotEmpty(orderDto.getReceivePicUrls()));
        // 设置条形码
        try {
            String base64Img = BarCodeUtils.getBase64Img(orderDto.getForderno());
            printItem.setBaseImg(base64Img);
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getCommonSummaryPrintData", "条形码生成异常，入参orderNo:" + orderDto.getForderno(), e);

        }
        // 结算日期
        printItem.setBalanceDate(DateUtils.format("yyyy-MM-dd", new Date()));

        // 打印订单信息
        List<PrintOrderDTO> printOrderDtoList = new ArrayList<>(1);
        PrintOrderDTO orderPrintItem = new PrintOrderDTO();
        orderPrintItem.setOrderNo(orderDto.getForderno());
        orderPrintItem.setBuyerName(orderDto.getFbuyername());
        orderPrintItem.setAcceptor(orderDto.getFlastreceiveman());
        String orgCode = orderDto.getFusercode();

        if(OrgEnum.ZHU_HAI_SHI_REN_MIN_YI_YUAN.getCode().equals(orgCode)){
            orderPrintItem.setOrderTotalAmount(orderDto.getForderamounttotal().subtract(new BigDecimal(String.valueOf(orderDto.getReturnAmount()))));
        }else {
            orderPrintItem.setOrderTotalAmount(orderDto.getForderamounttotal());
        }
        // 如果是有线下结算的单位，验收单验收时间使用结算开始时间，目前是代码硬编码 2020/07/15，等待配置
        if (OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_LIU_YI_YUAN.getCode().equals(orgCode) ||
            OrgEnum.WU_YI_DA_XUE.getCode().equals(orgCode) ||
                    OrgEnum.ZHENG_DA_FU_YI.getCode().equals(orgCode)) {
            orderPrintItem.setAcceptanceDate(orderDto.getInStateTime());
        } else {
            orderPrintItem.setAcceptanceDate(orderDto.getFlastreceivedate());
        }
        orderPrintItem.setDeliveryAddress(orderDto.getFbiderdeliveryplace());
        orderPrintItem.setSpecies(orderDto.getSpecies().intValue());

        // 1.拼装关联采购人t_user的信息
        CompletableFuture<Void> userInfoFuture = CompletableFuture.runAsync(() -> {
            Integer buyerId = orderDto.getFbuyerid();
            UserBaseInfoDTO userInfo = userClient.getUserInfo(buyerId, orderDto.getFuserid());
            orderPrintItem.setBuyerTelPhone(userInfo != null ? userInfo.getMobile() : "");
        }, defaultIoExecutor);

        // 2.拼装获取打印汇总单商品详情
        CompletableFuture<Void> printOrderDetailFuture = CompletableFuture.runAsync(() -> {
            packageOrderProductInfo(orderDto, orderPrintItem);
        }, defaultIoExecutor);

        // 3.拼装经费负责人信息
        CompletableFuture<Void> fundCardFuture = CompletableFuture.runAsync(() -> {
            packageOrderCardInfo(orderDto, orderPrintItem);
        }, defaultIoExecutor);

        // 4.拼装订单审批日志
        CompletableFuture<Void> approveLogFuture = CompletableFuture.runAsync(() -> {
            packageApproveLogInfo(orderDto, orderPrintItem);
        }, defaultIoExecutor);

        // 5.拼装发票信息
        CompletableFuture<Void> invoiceFuture = CompletableFuture.runAsync(() -> {
            packageOrderInvoiceInfo(orderDto, printItem);
        }, defaultIoExecutor);

        // 6.获取经费负责人信息
        CompletableFuture<Void> deptManagerFuture = CompletableFuture.runAsync(() -> {
            getDeptManagerService(orderDto, printItem);
        }, defaultIoExecutor);

        // 并行调用rpc拼装信息
        CompletableFuture.allOf(userInfoFuture, printOrderDetailFuture, fundCardFuture, approveLogFuture, invoiceFuture, deptManagerFuture).join();

        printOrderDtoList.add(orderPrintItem);
        // 设置打印订单数组
        printItem.setOrderDtoList(printOrderDtoList);
        List<PrintApproveDTO> result = new ArrayList<>();
        result.add(printItem);
        return RemoteResponse.<List<PrintApproveDTO>>custom().setData(result).setSuccess().build();
    }

    /**
     * 异步设值课题组负责人的姓名（设置特定单位，以免阻塞课题组要求不高的单位的业务）
     * @param orderMasterDO
     * @param printItem
     */
    private void getDeptManagerService(OrderMasterDO orderMasterDO, PrintApproveDTO printItem) {
        if (Objects.equals(OrgEnum.LING_NAN_NONG_KE_SHI_YAN_SHI.getValue(), orderMasterDO.getFuserid())) {
            Integer deptId = orderMasterDO.getFbuydepartmentid();
            BusinessErrUtil.notNull(deptId, ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND, orderMasterDO.getForderno());
            List<DepartmentDTO> deptInfoList = userClient.getDepartmentListByIds(New.list(deptId));
            BusinessErrUtil.notEmpty(deptInfoList, ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND, orderMasterDO.getForderno());
            DepartmentDTO deptInfo = deptInfoList.get(0);
            Integer managerId = deptInfo.getManagerId();
            UserBaseInfoDTO managerInfo = userClient.getUserDetailByID(managerId);
            BusinessErrUtil.notNull(managerInfo, ExecptionMessageEnum.DEPARTMENT_MANAGER_NOT_ENTERED, deptInfo.getName(), orderMasterDO.getForderno());
            printItem.setDeptManagerName(managerInfo.getName());
        }
    }

    /**
     * 异步设值printItem的 发票相关信息
     * @param orderMasterDO
     * @param printItem
     */
    private void packageOrderInvoiceInfo(OrderMasterDO orderMasterDO, PrintApproveDTO printItem) {
        Integer orderId = orderMasterDO.getId();
        InvoiceQueryDTO query = new InvoiceQueryDTO();
        query.setOrgId(Long.valueOf(orderMasterDO.getFuserid()));
        query.setSourceIds(New.list(orderId.longValue()));
        query.setInvoiceType(InvoiceTypeEnum.ORDER);
        List<OrderInvoiceInfoVO> invoiceVOList = invoiceClient.findInvoiceVOList(query);

        Map<Integer, List<OrderInvoiceInfoVO>> orderIdInvoiceMap = this.getOrderIdInvoiceMap(invoiceVOList);
        List<OrderInvoiceInfoVO> orderInvoiceList = orderIdInvoiceMap.get(orderId);
        if (CollectionUtils.isEmpty(orderInvoiceList)) {
            return;
        }
        Set<String> orderInvoiceNoSet = new HashSet<>();
        for (OrderInvoiceInfoVO invoiceInfo : orderInvoiceList) {
            String invoiceCode = invoiceInfo.getInvoiceCode();
            String invoiceNo = invoiceInfo.getInvoiceNo();
            String invoiceNoView = StringUtils.join(New.list(invoiceCode, invoiceNo), "-");
            orderInvoiceNoSet.add(invoiceNoView);
        }
        printItem.setInvoiceNoSet(orderInvoiceNoSet);
    }

    @Override
    @ServiceLog(description = "线下单取消订单Rpc", operationType = OperationType.WRITE)
    public RemoteResponse cancelOfflineOrder(CancelOrderReqDTO request) {
        Assert.notNull(request.getOrderMasterId(), "取消失败！订单id为空");
        cancelOrderManageService.cancelOfflineOrder(request);
        return RemoteResponse.custom().setSuccess().build();
    }

    /**
     * 组装验收单商品信息
     * @param orderDto 订单
     * @param orderPrintItem 打印验收单数据模型
     */
    private void packageOrderProductInfo(OrderMasterDO orderDto, PrintOrderDTO orderPrintItem) {
        List<OrderDetailDO> detailDtoList = orderDetailMapper.findByFmasterid(orderDto.getId());
        List<PrintOrderDetailDTO> orderDetailPrintList = new ArrayList<>(detailDtoList.size());

        PrintOrderDetailDTO detailItem = null;
        for (OrderDetailDO detailDto : detailDtoList) {
            detailItem = new PrintOrderDetailDTO();
            detailItem.setFgoodname(detailDto.getFgoodname());
            detailItem.setFbrand(detailDto.getFbrand());
            detailItem.setFquantity(detailDto.getFquantity() == null ? 0 : detailDto.getFquantity().intValue());
            detailItem.setOriginalPrice(detailDto.getOriginalPrice());
            detailItem.setPrice(detailDto.getFbidprice());
            detailItem.setGoodTotalPrice(detailDto.getFbidamount());
            if(OrgEnum.ZHU_HAI_SHI_REN_MIN_YI_YUAN.getValue() == orderDto.getFuserid()){
                BigDecimal quantityAfterReturn = detailDto.getFquantity().subtract(detailDto.getFcancelquantity());
                detailItem.setFquantity(quantityAfterReturn.intValue());
                detailItem.setGoodTotalPrice(quantityAfterReturn.multiply(detailDto.getFbidprice()).add(detailDto.getRemainderPrice()));
            }
            detailItem.setFunit(detailDto.getFunit());
            detailItem.setFspec(detailDto.getFspec());
            detailItem.setGoodCode(detailDto.getFgoodcode());

            // 费用类型
            Set<String> CategoryFeeTagSet = new HashSet<>(1);
            CategoryFeeTagSet.add(detailDto.getFeeTypeTag());

            detailItem.setCategoryTagSet(CategoryFeeTagSet);
            if(detailItem.getFquantity() > 0){
                // 数量>0才返回
                orderDetailPrintList.add(detailItem);
            }
        }
        orderPrintItem.setOrderDetailDtoList(orderDetailPrintList);
    }

    /**
     * 拼装验收单经费卡信息
     * @param orderDto  订单对象
     * @param orderPrintItem 打印验收单数据模型
     */
    private void packageOrderCardInfo(OrderMasterDO orderDto, PrintOrderDTO orderPrintItem) {
        // 绑卡信息查询
        List<RefFundcardOrderDTO> refFundCardDtoList = refFundcardOrderService.findByOrderIdList(Arrays.asList(orderDto.getId()));
        if (CollectionUtils.isNotEmpty(refFundCardDtoList)) {
            List<String> cardIdList = refFundCardDtoList.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).collect(Collectors.toList());
            List<FundCardDTO> fundProjectList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orderDto.getFusercode(), cardIdList);

            if (CollectionUtils.isNotEmpty(fundProjectList)) {
                List<PrintFundCardManagerDTO> fundCardManagerList = new ArrayList<>();
                for (FundCardDTO fundProjectDto : fundProjectList) {
                    // 获取二级经费卡
                    List<FundCardDTO> secondFundCardList = fundProjectDto.getFundCardDTOs();
                    if (CollectionUtils.isNotEmpty(secondFundCardList)) {
                        secondFundCardList.stream().forEach(fundCard -> {
                            PrintFundCardManagerDTO fundCardItem = new PrintFundCardManagerDTO();
                            fundCardItem.setFundCardProjectName(fundCard.getName());
                            fundCardItem.setFundCardProjectNo(fundCard.getCode());
                            fundCardManagerList.add(fundCardItem);
                        });
                    } else {
                        // 没有二级经费卡默认使用经费项目名做经费卡名
                        PrintFundCardManagerDTO fundCardItem = new PrintFundCardManagerDTO();
                        fundCardItem.setFundCardProjectName(fundProjectDto.getName());
                        fundCardItem.setFundCardProjectNo(fundProjectDto.getCode());
                        fundCardManagerList.add(fundCardItem);
                    }

                }
                orderPrintItem.setFundCardManagerDtoList(fundCardManagerList);
            }
        }
    }

    /**
     * 拼装验收单订单审批日志
     * @param orderDto
     * @param orderPrintItem
     */
    private void packageApproveLogInfo(OrderMasterDO orderDto, PrintOrderDTO orderPrintItem) {
        Integer buyAppId = orderDto.getFtbuyappid();
        List<OrderPurchaseApprovalLogDTO> result;
        // 组装订单审批日志
        if (buyAppId != null) {
            result = getOrderPurchaseApprovalLogById(buyAppId);
        } else {
            String bid = orderDto.getBidOrderId();
            result = getOrderPurchaseApprovalLogByBidId(orderDto.getForderno(), bid, orderDto.getFusercode(), null, false);
        }
        orderPrintItem.setOrderApprovalLogList(result);
    }

    /**
     * 获取采购审批日志
     * @param order
     * @return
     */
    public List<OrderPurchaseApprovalLogDTO> getApproveLogInfo(OrderMasterDO order) {
        return this.getPurchaseOrBidLog(order, null, false);
    }

    /**
     * 获取采购或竞价日志
     *
     * @param order                  订单数据
     * @param filterBidOperationList 指定的竞价操作
     * @param getAfterTheLastReject  是否仅获取最后一次审批通过和其他共通部分的日志
     * @return 竞价/采购日志
     */
    private List<OrderPurchaseApprovalLogDTO> getPurchaseOrBidLog(OrderMasterDO order, List<String> filterBidOperationList, boolean getAfterTheLastReject) {
        Integer buyAppId = order.getFtbuyappid();
        List<OrderPurchaseApprovalLogDTO> result;
        // 组装订单审批日志
        if (buyAppId != null) {
            result = getOrderPurchaseApprovalLogById(buyAppId);
        } else {
            String bid = order.getBidOrderId();
            result = this.getOrderPurchaseApprovalLogByBidId(order.getForderno(), bid, order.getFusercode(), filterBidOperationList, getAfterTheLastReject);
        }
        return result;
    }

    /**
     * 获取竞价日志
     *
     * @param orderNo                订单id
     * @param bid                    竞价单id
     * @param orgCode                单位code
     * @param filterBidOperationList 指定的竞价操作
     * @param getAfterTheLastReject  是否仅获取初审终审最后一次审批通过和其他共通部分的日志
     * @return 日志记录
     */
    private List<OrderPurchaseApprovalLogDTO> getOrderPurchaseApprovalLogByBidId(String orderNo, String bid, String orgCode, List<String> filterBidOperationList, boolean getAfterTheLastReject) {
        List<BidApprovalLogDTO> bidApprovalLogList = bidClient.findApprovalLogInfo(orderNo, bid, orgCode);
        if (getAfterTheLastReject) {
            // 初审相关的枚举（可重复部分）
            final List<String> firstBidEnumList = New.list(OperationEnum.SUBMIT_BEGIN_APPROVAL.getName(), OperationEnum.BEGIN_APPROVAL_PASS.getName(), OperationEnum.FIRST_AUTO_PASS.getName(), OperationEnum.BEGIN_APPROVAL_REJECT.getName(), OperationEnum.FIRST_SKIP_APPROVAL.getName());
            // 终审相关的枚举（可重复部分）
            final List<String> finalBidEnumList = New.list(OperationEnum.SUBMIT_FINAL_APPROVAL.getName(), OperationEnum.FINAL_APPROVAL_PASS.getName(), OperationEnum.FINAL_AUTO_PASS.getName(), OperationEnum.FINAL_APPROVAL_REJECT.getName(), OperationEnum.FINAL_SKIP_APPROVAL.getName());
            // 获取最后一次通过的初审日志
            List<BidApprovalLogDTO> firstBidApprovalLogList = bidApprovalLogList.stream().filter(log -> firstBidEnumList.contains(log.getOperationName())).sorted(Comparator.comparing(BidApprovalLogDTO::getLogId)).collect(toList());
            List<BidApprovalLogDTO> validFirstBidApprovalLogList = this.getBidLogAfterTheLastReject(firstBidApprovalLogList, OperationEnum.BEGIN_APPROVAL_REJECT.getName());
            // 获取最后一次通过的终审日志
            List<BidApprovalLogDTO> finalBidApprovalLogList = bidApprovalLogList.stream().filter(log -> finalBidEnumList.contains(log.getOperationName())).sorted(Comparator.comparing(BidApprovalLogDTO::getLogId)).collect(toList());
            List<BidApprovalLogDTO> validFinalBidApprovalLogList = this.getBidLogAfterTheLastReject(finalBidApprovalLogList, OperationEnum.FINAL_APPROVAL_REJECT.getName());
            // 其他非重复部分，累计起来
            bidApprovalLogList = bidApprovalLogList.stream().filter(log -> !firstBidEnumList.contains(log.getOperationName()) && !finalBidEnumList.contains(log.getOperationName())).collect(toList());
            bidApprovalLogList.addAll(validFirstBidApprovalLogList);
            bidApprovalLogList.addAll(validFinalBidApprovalLogList);
        }
        // 如果有指定查询的竞价操作，则只返回相应的竞价操作
        Stream<BidApprovalLogDTO> stream = CollectionUtils.isNotEmpty(filterBidOperationList) ? bidApprovalLogList.stream().filter(log -> filterBidOperationList.contains(log.getOperationName())) : bidApprovalLogList.stream();
        return stream.sorted(Comparator.comparing(BidApprovalLogDTO::getLogId))
                .map(PurchaseApprovalLogTranslator::dtoToOrderOrderBidLogDTO).collect(Collectors.toList());
    }

    /**
     * 获取最后一次审批不通过后的的日志。 从最后一条日志向前遍历找到最后一次审批不通过，从它开始向下的即都为最后一次审批通过的日志了
     *
     * @param logList   日志
     * @param rejectKey 拒绝的标记
     * @return 拒绝后的日志
     */
    private List<BidApprovalLogDTO> getBidLogAfterTheLastReject(List<BidApprovalLogDTO> logList, String rejectKey) {
        int lastRejectIndex = -1;
        for (int i = logList.size() - 1; i > -1; i--) {
            if (rejectKey.equals(logList.get(i).getOperationName())) {
                lastRejectIndex = i;
                break;
            }
        }
        // 截取成功的日志返回
        return New.list(logList.subList(lastRejectIndex + 1, logList.size()));
    }

    private List<OrderPurchaseApprovalLogDTO> getOrderPurchaseApprovalLogById(Integer buyAppId) {
        List<PurchaseApprovalLogDTO> purchaseApprovalLogList;
        List<OrderPurchaseApprovalLogDTO> result;
        purchaseApprovalLogList = purchaseApprovalLogClient.getApprovalLogById(buyAppId);
        result = purchaseApprovalLogList.stream().map(PurchaseApprovalLogTranslator::dtoToOrderOrderPurchaseLogDTO).collect(Collectors.toList());
        return result;
    }

    @Override
    @ServiceLog(description = "订单打印单据通用接口")
    public RemoteResponse<OrderCommonPrintDataDTO> getCommonPrintData(OrderCommonPrintParamDTO request) {
        // 获取订单id
        List<Integer> orderIdList = request.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "订单id不能为空！");
        List<OrderMasterDO> orderInfoList = orderMasterMapper.findByIdIn(orderIdList);
        BusinessErrUtil.notEmpty(orderInfoList, ExecptionMessageEnum.NO_ORDER_INFO_FOUND);
        List<OrderDetailDO> orderDetailList = orderDetailMapper.findAllByFmasteridIn(orderIdList);

        OrderCommonPrintDataDTO printResult = new OrderCommonPrintDataDTO();
        // 拼装订单数据
        this.packageOrderInfo(orderInfoList, orderDetailList, printResult);

        List<Integer> orderPrintDataEnumList = request.getPrintDataTypeList();
        if (CollectionUtils.isEmpty(orderPrintDataEnumList)) {
            return RemoteResponse.<OrderCommonPrintDataDTO>custom().setSuccess().setData(printResult);
        }
        // 根据单据类型拼装数据
        for (Integer printDataEnum : orderPrintDataEnumList) {
            switch (printDataEnum) {
                case OrderPrintDataConstant.PURCHASE:
                    // todo 获取采购单数据，异步优化
                    this.packagePurchaseInfo(printResult);
                    break;
                case OrderPrintDataConstant.ENTRY_WAREHOUSE:
                    // 拼装入库单数据
                    this.packageEntryWarehouseInfo(orderInfoList, orderDetailList, printResult);
                    break;
                case OrderPrintDataConstant.EXIT_WAREHOUSE:
                    // todo 获取出库单数据，异步优化
                    String orgCode = orderInfoList.get(0).getFusercode();
                    this.packageOutWarehouseInfo(orgCode, orderDetailList, printResult);
                    break;
                case OrderPrintDataConstant.GOODS_RETURN:
                    this.packageGoodsReturnInfo(orderInfoList, printResult);
                    break;
                case OrderPrintDataConstant.INVOICE_INFO:
                    this.packageInvoiceInfo(orderInfoList.get(0).getFuserid(), orderInfoList, printResult);
                    break;
                case OrderPrintDataConstant.DELIVERY_INFO:
                    this.packageDeliveryInfo(orderInfoList, printResult);
                    break;
            }
        }
        return RemoteResponse.<OrderCommonPrintDataDTO>custom().setSuccess().setData(printResult);
    }

    /**
     * 组装送货单信息
     * @param printResult
     */
    private void packageDeliveryInfo(List<OrderMasterDO> orderInfoList, OrderCommonPrintDataDTO printResult) {
        // 组装经费卡信息已在packageOrderInfo的方法中包含，获取供应商信息也因验收单需要打印移过去了，这里移除相关处理方法
    }

    /**
     * 组装采购单关联信息
     * @param printResult
     */
    private void packagePurchaseInfo(OrderCommonPrintDataDTO printResult) {
        List<Integer> appIdList = printResult.getOrderMasterList().stream().map(OrderPrintDTO::getApplicationId).filter(Objects::nonNull).collect(toList());
        if (CollectionUtils.isEmpty(appIdList)) {
            return;
        }

        // 采购单生成时间，异步
        CompletableFuture<Map<Integer, Date>> appCreateTimeMapFuture = AsyncExecutor.callAsync(() -> {
            List<ApplicationMasterDTO> appInfoList = applicationBaseClient.findByMasterId(appIdList);
            return appInfoList.stream().collect(Collectors.toMap(s -> s.getId().intValue(), s -> s.getCreateTime()));
        });

        // 查采购用途（目前仅农科院）
        CompletableFuture<Map<Integer, String>> purchasePurposeFuture = AsyncExecutor.callAsync(() -> {
            ApplyMasterExtensionQueryDTO applyExtReq = new ApplyMasterExtensionQueryDTO();
            applyExtReq.setApplyIds(appIdList);
            List<ApplyMasterExtensionDTO> applyExtRes = applicationBaseClient.findExtensionByApplicationId(applyExtReq);
            Map<Integer, String> applyIdPurposeMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(applyExtRes)) {
                for (ApplyMasterExtensionDTO applyExt : applyExtRes) {
                    applyIdPurposeMap.put(applyExt.getApplyId().intValue(), applyExt.getExtraInfo() == null ? null : applyExt.getExtraInfo().getPurpose());
                }
            }
            return applyIdPurposeMap;
        });

        // 查商品的采购用途（目前仅农科院）
        CompletableFuture<Map<Long, String>> productIdPurposeMapFuture = AsyncExecutor.callAsync(() -> {
            // 商品id列表
            ApplicationQueryDTO param = new ApplicationQueryDTO();
            List<Long> appIdLongList = appIdList.stream().filter(s -> s != null).map(s -> s.longValue()).collect(toList());
            param.setApplyIds(appIdLongList);
            param.setWithDetail(true);
            List<ApplicationMasterDTO> applyMasterList = applicationBaseClient.findByMasterId(param);
            Map<Long, String> prodIdPurposeMap = new HashMap<>();
            for (ApplicationMasterDTO applyMaster : applyMasterList) {
                List<ApplicationDetailDTO> applyDetailList = applyMaster.getDetails();
                for (ApplicationDetailDTO applyDetail : applyDetailList) {
                    prodIdPurposeMap.put(applyDetail.getProductSn(), applyDetail.getPurpose());
                }
            }
            return prodIdPurposeMap;
        });


        // 线下单采购渠道
        List<ApplyRefSuppBusinessDTO> supplierByAppIdList = applicationBaseClient.getOfflineSupplierByAppIdList(appIdList);
        Map<Integer, ApplyRefSuppBusinessDTO> appIdEntityMap = DictionaryUtils.toMap(supplierByAppIdList, ApplyRefSuppBusinessDTO::getApplicationId, Function.identity());

        // 等待异步操作
        Map<Integer, Date> appIdDateMap;
        Map<Integer, String> appIdPurposeMap;
        Map<Long, String> productIdPurposeMap;
        try {
            appIdDateMap = appCreateTimeMapFuture.get();
            appIdPurposeMap = purchasePurposeFuture.get();
            productIdPurposeMap = productIdPurposeMapFuture.get();
        } catch (Exception e) {
            appIdDateMap = new HashMap<>();
            appIdPurposeMap = new HashMap<>();
            productIdPurposeMap = new HashMap<>();
        }

        Map<Integer, Date> finalAppIdDateMap = appIdDateMap;
        Map<Integer, String> finalAppIdPurposeMap = appIdPurposeMap;
        Map<Long, String> finalProductIdPurposeMap = productIdPurposeMap;
        printResult.getOrderMasterList().forEach(orderPrint -> {
            ApplyRefSuppBusinessDTO appRefSupplier = appIdEntityMap.get(orderPrint.getApplicationId());
            Date appCreateTime = finalAppIdDateMap.get(orderPrint.getApplicationId());
            if (appRefSupplier != null) {
                // 线下单采购渠道
                orderPrint.setOfflineProcurementChannel(OfflineProcurementSourceEnum.get(appRefSupplier.getProcurementChannelId()).getName());
            }
            if (appCreateTime != null) {
                orderPrint.setApplicationCreateTime(appCreateTime.getTime());
            }
            // 采购用途
            orderPrint.setPurchasePurpose(finalAppIdPurposeMap.get(orderPrint.getApplicationId()));
            orderPrint.getOrderDetailPrintList().forEach(detail -> {
                detail.setPurpose(finalProductIdPurposeMap.get(detail.getProductId()));
            });
        });
    }

    /**
     * @description: 通用打印接口获取发票信息
     * @date: 2021/3/17 13:20
     * @author: zengyanru
     * @param orgId
     * @param printResult
     * @return void
     */
    @ServiceLog(serviceType = ServiceType.COMMON_SERVICE, description = "通用打印接口获取发票信息")
    private void packageInvoiceInfo(Integer orgId, List<OrderMasterDO> orderMasterInfoList, OrderCommonPrintDataDTO printResult) {
        Preconditions.notEmpty(printResult.getOrderMasterList(), "packageInvoiceInfo入参订单打印信息为空");
        Preconditions.notEmpty(orderMasterInfoList, "packageInvoiceInfo入参订单id信息为空");

        List<Long> orderIdLongList = orderMasterInfoList.stream().map(OrderMasterDO::getId).map(Long::valueOf).collect(Collectors.toList());
        InvoiceQueryDTO query = new InvoiceQueryDTO();
        query.setOrgId(Long.valueOf(orgId));
        query.setSourceIds(orderIdLongList);
        query.setInvoiceType(InvoiceTypeEnum.ORDER);
        List<OrderInvoiceInfoVO> invoiceVOList = invoiceClient.findInvoiceVOList(query);

        Map<Integer, List<OrderInvoiceInfoVO>> orderIdInvoiceMap = this.getOrderIdInvoiceMap(invoiceVOList);

        int orderCount = printResult.getOrderMasterList().size();
        for (int i = 0; i < orderCount; i++) {
            Integer orderId = printResult.getOrderMasterList().get(i).getOrderId();
            List<OrderInvoiceInfoVO> invoiceForOrderIdList = orderIdInvoiceMap.get(orderId) == null ? Collections.emptyList() : orderIdInvoiceMap.get(orderId);
            List<OrderInvoiceInfoDTO> invoiceDTOList = New.list();
            for (OrderInvoiceInfoVO invoiceVO : invoiceForOrderIdList) {
                OrderInvoiceInfoDTO invoiceDTO = new OrderInvoiceInfoDTO();
                invoiceDTO.setAmount(invoiceVO.getAmount());
                invoiceDTO.setBankName(invoiceVO.getBankName());
                invoiceDTO.setBankNo(invoiceVO.getBankNo());
                invoiceDTO.setDrawer(invoiceVO.getDrawer());
                invoiceDTO.setInvoiceCode(invoiceVO.getInvoiceCode());
                invoiceDTO.setInvoiceId(invoiceVO.getInvoiceId());
                invoiceDTO.setInvoiceNo(invoiceVO.getInvoiceNo());
                invoiceDTO.setIssueDate(invoiceVO.getIssueDate());
                invoiceDTO.setPicturePathList(invoiceVO.getPicturePathList());
                invoiceDTO.setRemark(invoiceVO.getRemark());
                invoiceDTOList.add(invoiceDTO);
            }
            printResult.getOrderMasterList().get(i).setOrderInvoiceInfoList(invoiceDTOList);
        }
    }

    /**
     * @description: 适配发票与订单多对多关系，构造订单id与invoice的对应关系
     * @date: 2021/4/6 17:08
     * @author: zengyanru
     * @param invoiceVOList
     * @return java.util.Map<java.lang.Integer,java.util.List<com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderInvoiceInfoVO>>
     */
    @ServiceLog(description = "适配发票与订单多对多关系，构造订单id与invoice的对应关系", serviceType = ServiceType.COMMON_SERVICE)
    private Map<Integer, List<OrderInvoiceInfoVO>> getOrderIdInvoiceMap(List<OrderInvoiceInfoVO> invoiceVOList) {
        Map<Integer, List<OrderInvoiceInfoVO>> orderIdInvoiceMap = new HashMap<>();
        if (CollectionUtils.isEmpty(invoiceVOList)) {
            return orderIdInvoiceMap;
        }
        for (OrderInvoiceInfoVO orderInvoice : invoiceVOList) {
            List<Integer> orderIdList = orderInvoice.getOrderIdList();
            if (CollectionUtils.isEmpty(orderIdList)) {
                continue;
            }
            for (Integer orderId : orderIdList) {
                if (orderIdInvoiceMap.containsKey(orderId)) {
                    orderIdInvoiceMap.get(orderId).add(orderInvoice);
                } else {
                    orderIdInvoiceMap.put(orderId, New.list(orderInvoice));
                }
            }
        }
        return orderIdInvoiceMap;
    }

    /**
     * @description: 拼装即入即出出库单数据
     * @date: 2021/3/17 15:15
     * @author: zengyanru
     * @param orgCode
     * @param printResult
     * @return void
     */
    @ServiceLog(description = "拼装即入即出出库单数据", serviceType = ServiceType.COMMON_SERVICE)
    private void packageOutWarehouseInfo(String orgCode, List<OrderDetailDO> orderDetailList, OrderCommonPrintDataDTO printResult) {
        List<OrderPrintDTO> orderMasterList = printResult.getOrderMasterList();
        List<String> orderNoList = orderMasterList.stream().map(OrderPrintDTO::getOrderNo).collect(Collectors.toList());
        List<BizWarehouseExitDTO> exitInfoList = bizExitServiceClient.queryExitByOrderNoList(orderNoList);
        // 撤销入库相关信息的去除，需要先通过入库单查询
        List<BizWarehouseEntryDTO> entryDTOList = bizWareHouseClient.findEntryByOrderNoList(orderNoList);
        if (CollectionUtils.isNotEmpty(entryDTOList)) {
            List<Integer> validExitIdList = entryDTOList.stream().filter(s -> (!InboundStatus.HAVE_BEEN_WITHDRAW.getValue().equals(s.getStatus()))
                    && (!InboundStatus.HAVING_WITHDRAW.getValue().equals(s.getStatus()))
                    &&(!InboundStatus.NOTINSTORAGE.getValue().equals(s.getStatus()))).map(BizWarehouseEntryDTO::getExitId).collect(toList());
            exitInfoList = exitInfoList.stream().filter(s -> validExitIdList.contains(s.getId())).collect(toList());
        }

        Map<String, List<BizWarehouseExitDTO>> orderNoExitInfoMap = exitInfoList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(BizWarehouseExitDTO::getOrderNo));
        Map<Integer, List<OrderDetailDO>> orderNoDetailListMap = DictionaryUtils.groupBy(orderDetailList, OrderDetailDO::getFmasterid);
        int size = orderMasterList.size();
        for (int i = 0; i < size; i++) {
            OrderPrintDTO orderMaster = orderMasterList.get(i);
            if (orderMaster == null) {
                continue;
            }
            // 根据订单号查找出库单列表
            String orderNo = orderMaster.getOrderNo();
            List<BizWarehouseExitDTO> outWarehouseInfoList = orderNoExitInfoMap.get(orderNo);
            if (CollectionUtils.isEmpty(outWarehouseInfoList)) {
                return;
            }
            List<OrderOutWarehousePrintDTO> orderOutWarehouseInfoList = New.list();
            String orderNoBarcode = "";
            if (WarehouseConstant.ORG_CODE_NEED_ORDER_NO_BARCODE.contains(orgCode)) {
                try {
                    orderNoBarcode = BarCodeUtils.getBase64Img(orderNo);
                } catch (Exception e) {
                    Cat.logError(CAT_TYPE, "packageOutWarehouseInfo", "条形码生成异常，入参,orgCode:" + orgCode + ";orderNo:" + orderNo, e);
                }
            }
            List<OrderDetailDO> thisOrderDetailList = orderNoDetailListMap.get(orderMaster.getOrderId());
            for (BizWarehouseExitDTO bizWarehouseExitDTO : outWarehouseInfoList) {
                //出库单状态（状态0未出库，1已出库）
                if (bizWarehouseExitDTO.getStatus() == 1) {
                    OutWarehouseApplicationBean outWarehouseApplicationBean = outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(bizWarehouseExitDTO, orgCode);
                    Map<String, OrderDetailDO> goodCodeAndOrderDetailMap = thisOrderDetailList.stream().collect(Collectors.toMap(o -> o.getFgoodcode().trim(), Function.identity(), (o, n) -> n));
                    Map<Integer, OrderDetailDO> detailIdAndOrderDetailMap = thisOrderDetailList.stream().collect(Collectors.toMap(o -> o.getId(), Function.identity(), (o, n) -> n));
                    List<ProductBean> productBeanList = bizWarehouseExitDTO.getExitDetailDTOList().stream()
                            .map(item -> {
                                ProductBean productBean = ProductBeanTranslator.bizWarehouseExitDetailDTO2ProductBean(item);
                                // 找到匹配的商品详情，兼容无orderDetailId的记录
                                OrderDetailDO matchDetail;
                                if (item.getOrderDetailId() != null) {
                                    matchDetail = detailIdAndOrderDetailMap.get(item.getOrderDetailId());
                                } else {
                                    matchDetail = goodCodeAndOrderDetailMap.get(item.getProductCode().trim());
                                }
                                if (matchDetail != null) {
                                    // 设置品类为一级商品分类
                                    productBean.setFirstLevelCategoryId(matchDetail.getFirstCategoryId());
                                    productBean.setFirstLevelCategoryName(matchDetail.getFirstCategoryName());
                                }
                                return productBean;
                            })
                            .collect(toList());
                    outWarehouseApplicationBean.setProductBeans(productBeanList);
                    
                    // 组装出库单信息
                    OrderOutWarehousePrintDTO orderOutWarehouseInfo = this.outWarehouseBeanToPrintConverter(outWarehouseApplicationBean, orderNoBarcode);
                    orderOutWarehouseInfoList.add(orderOutWarehouseInfo);
                }
            }
            printResult.getOrderMasterList().get(i).setOrderOutWarehousePrintList(orderOutWarehouseInfoList);
        }
    }

    /**
     * @description: 出库单打印体转换
     * @date: 2021/3/17 15:16
     * @author: zengyanru
     * @param from
     * @param orderNoBarcode
     * @return com.ruijing.store.order.api.base.other.dto.OrderOutWarehousePrintDTO
     */
    private OrderOutWarehousePrintDTO outWarehouseBeanToPrintConverter(OutWarehouseApplicationBean from, String orderNoBarcode) {
        OrderOutWarehousePrintDTO to = new OrderOutWarehousePrintDTO();
        to.setOrderNoBarcode(orderNoBarcode);
        to.setExitNoBarcode(from.getExitNoBarcode());
        to.setOutWarehouseApplicant(from.getOutWarehouseApplicant());
        to.setOutWarehouseApplicationId(from.getOutWarehouseApplicationId());
        to.setOutWarehouseApplicationNo(from.getOutWarehouseApplicationNo());
        to.setOutWarehouseApplicationTime(from.getOutWarehouseApplicationDate().getTime());
        to.setOutWarehouseTime(from.getOutWarehouseDate().getTime());
        to.setStatus(from.getStatus());
        to.setStatusName(from.getStatusName());
        to.setWarehouseId(from.getWarehouseId());
        to.setWarehouseName(from.getWarehouseName());
        to.setOrderOutWarehouseProductInfoVOList(ProductBeanTranslator.productBeanList2OrderOutWarehouseProductInfoDTOList(from.getProductBeans()));
        return to;
    }


    private void packageGoodsReturnInfo(List<OrderMasterDO> orderInfoList, OrderCommonPrintDataDTO printResult) {
        List<Integer> orderIdList = orderInfoList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIdList);
        if (CollectionUtils.isEmpty(goodsReturnList)) {
            return;
        }
        Map<String, List<GoodsReturn>> orderNoGoodsReturnMap = goodsReturnList.stream().collect(Collectors.groupingBy(GoodsReturn::getOrderNo));
        List<OrderPrintDTO> orderMasterList = printResult.getOrderMasterList();
        for (OrderPrintDTO orderPrintDTO : orderMasterList) {
            List<GoodsReturn> item = orderNoGoodsReturnMap.get(orderPrintDTO.getOrderNo());
            if (item == null) {
                continue;
            }

            List<OrderGoodsReturnPrintDTO> result = GoodsReturnTranslator.doToPrintDTO(item);
            orderPrintDTO.setOrderGoodsReturnPrintList(result);
        }

        printResult.setOrderMasterList(orderMasterList);
    }

    /**
     * 组装订单入库单打印信息
     * @param orderInfoList     订单信息
     * @param orderDetailList   订单商品信息
     * @param printResult       打印结果
     */
    private void packageEntryWarehouseInfo(List<OrderMasterDO> orderInfoList, List<OrderDetailDO> orderDetailList, OrderCommonPrintDataDTO printResult) {
        List<String> orderNoList = orderInfoList.stream().map(OrderMasterDO::getForderno).collect(Collectors.toList());
        List<OrderEntryWarehousePrintDTO> orderEntryWarehousePrintList = packageEntryWarehousePrintDTO(orderDetailList, orderNoList);
        if (orderEntryWarehousePrintList == null) {
            return;
        }

        packageEntryWarehouseLogPrintDTO(orderEntryWarehousePrintList);

        Map<String, List<OrderEntryWarehousePrintDTO>> orderNoEntryWarehouseMap = DictionaryUtils.groupBy(orderEntryWarehousePrintList, OrderEntryWarehousePrintDTO::getOrderNo);
        for (OrderPrintDTO orderPrintDTO : printResult.getOrderMasterList()) {
            orderPrintDTO.setOrderEntryWarehousePrintList(orderNoEntryWarehouseMap.get(orderPrintDTO.getOrderNo()));
        }
    }

    /**
     * 组装入库单审批日志打印信息
     * @param dtoList
     */
    private void packageEntryWarehouseLogPrintDTO(List<OrderEntryWarehousePrintDTO> dtoList) {
        List<String> entryNoList = ListUtils.toList(dtoList, OrderEntryWarehousePrintDTO::getEntryNo);
        List<WmsPersionDTO> approvalLogList = bizWareHouseClient.findApprovalLogByOrderNo(entryNoList);
        if (CollectionUtils.isEmpty(approvalLogList)) {
            return;
        }

        Map<String, WmsPersionDTO> entryNoIdentityMap = DictionaryUtils.toMap(approvalLogList, WmsPersionDTO::getEntryOrExitNo, Function.identity());
        dtoList.stream().forEach(
                dto -> dto.setOrderEntryWarehousePrintLog(bizEntryLogDTOToEntryLogPrintDTO(entryNoIdentityMap.get(dto.getEntryNo())))
        );

    }

    private List<OrderEntryWarehousePrintDTO> packageEntryWarehousePrintDTO(List<OrderDetailDO> orderDetailList, List<String> orderNoList) {
        List<BizWarehouseEntryDTO> entryDTOList = bizWareHouseClient.findEntryByOrderNoList(orderNoList);
        if (CollectionUtils.isEmpty(entryDTOList)) {
            return null;
        }
        entryDTOList = entryDTOList.stream().filter(s -> (!InboundStatus.HAVE_BEEN_WITHDRAW.getValue().equals(s.getStatus()))
                &&(!InboundStatus.HAVING_WITHDRAW.getValue().equals(s.getStatus()))
                &&(!InboundStatus.NOTINSTORAGE.getValue().equals(s.getStatus()))).collect(toList());

        List<Integer> entryIdList = entryDTOList.stream().map(BizWarehouseEntryDTO::getId).collect(Collectors.toList());
        List<BizWarehouseEntryDetailDTO> entryDetailList = bizWareHouseClient.findEntryDetailByIdList(entryIdList);
        Map<Integer, List<BizWarehouseEntryDetailDTO>> entryIdDetailMap = DictionaryUtils.groupBy(entryDetailList, BizWarehouseEntryDetailDTO::getEntryId);

        Map<String, OrderDetailDO> productCodeIdentityMap = DictionaryUtils.toMap(orderDetailList, OrderDetailDO::getFgoodcode, Function.identity());
        List<OrderEntryWarehousePrintDTO> orderEntryWarehousePrintList = entryDTOList.stream().map(item -> bizEntryDTOToEntryPrintDTO(item, entryIdDetailMap, productCodeIdentityMap)).collect(Collectors.toList());
        return orderEntryWarehousePrintList;
    }

    /**
     * 入库单dto 转 入库打印dto
     * @param bizWarehouseEntryDTO      入库单dto
     * @param entryIdDetailMap          入库单id -> 入库明细identity
     * @param productCodeIdentityMap    商品编码 -> 订单商品identity
     * @return
     */
    private OrderEntryWarehousePrintDTO bizEntryDTOToEntryPrintDTO(BizWarehouseEntryDTO bizWarehouseEntryDTO,
                                                                   Map<Integer, List<BizWarehouseEntryDetailDTO>> entryIdDetailMap,
                                                                   Map<String, OrderDetailDO> productCodeIdentityMap) {
        OrderEntryWarehousePrintDTO result = new OrderEntryWarehousePrintDTO();
        result.setOrderNo(bizWarehouseEntryDTO.getOrderNo());
        result.setEntryNo(bizWarehouseEntryDTO.getEntryNo());
        result.setRoomName(bizWarehouseEntryDTO.getRoomName());
        result.setApplyDate(bizWarehouseEntryDTO.getCreateTime());
        // 封装入库日志
        List<BizWarehouseEntryLogDTO> entryLogDTOList = bizWarehouseEntryDTO.getLogDTOList();
        if (CollectionUtils.isNotEmpty(entryLogDTOList)) {
            List<OrderWarehousePrintEntryLogDTO> orderWarehousePrintEntryLogDTOList = entryLogDTOList.stream().map(this::bizEntryLogDTOToOrderWarehousePrintEntryLogDTO).collect(toList());
            result.setOrderWarehousePrintEntryLogDTOList(orderWarehousePrintEntryLogDTOList);
        }

        List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailList = entryIdDetailMap.get(bizWarehouseEntryDTO.getId());
        if (CollectionUtils.isNotEmpty(bizWarehouseEntryDetailList)) {
            List<OrderEntryWarehouseProductPrintDTO> bizWarehouseEntryDetailDTOList = bizWarehouseEntryDetailList.stream().map(item -> bizEntryDetailDTOToEntryDetailPrintDTO(item, productCodeIdentityMap)).collect(Collectors.toList());
            result.setOrderEntryWarehouseProductPrintList(bizWarehouseEntryDetailDTOList);
        }

        return result;
    }

    /**
     * 入库单日志dto 转 入库单日志打印dto(新)
     * @param dto
     * @return
     */
    private OrderWarehousePrintEntryLogDTO bizEntryLogDTOToOrderWarehousePrintEntryLogDTO(BizWarehouseEntryLogDTO dto) {
        if (dto == null) {
            return null;
        }
        OrderWarehousePrintEntryLogDTO result = new OrderWarehousePrintEntryLogDTO();
        result.setId(dto.getId());
        result.setEntryId(dto.getEntryId());
        result.setUserGuid(dto.getUserGuid());
        result.setUserName(dto.getUserName());
        result.setCreateTime(dto.getCreateTime());
        result.setBusinessType(dto.getBusinessType());
        result.setBusinessDesc(dto.getBusinessDesc());
        result.setRemark(dto.getRemark());
        return result;
    }

    /**
     * 入库单日志dto 转 入库单日志打印dto
     * @param dto
     * @return
     */
    @Deprecated
    private OrderEntryWarehousePrintLogDTO bizEntryLogDTOToEntryLogPrintDTO(WmsPersionDTO dto) {
        if (dto == null) {
            return null;
        }
        OrderEntryWarehousePrintLogDTO result = new OrderEntryWarehousePrintLogDTO();
        result.setFirstApprovalOperator(dto.getOperator());
        return result;
    }

    /**
     * 入库明细dto 转 入库打印明细dto
     * @param dto                       入库明细dto
     * @param productCodeIdentityMap    商品编码 -> 订单商品identity
     * @return
     */
    private OrderEntryWarehouseProductPrintDTO bizEntryDetailDTOToEntryDetailPrintDTO(BizWarehouseEntryDetailDTO dto, Map<String, OrderDetailDO> productCodeIdentityMap) {
        OrderEntryWarehouseProductPrintDTO result = new OrderEntryWarehouseProductPrintDTO();
        result.setSpecifications(dto.getSpecifications());
        result.setProductName(dto.getProductName());

        result.setBrandName(dto.getBrandName());
        result.setProductCode(dto.getProductCode());
        result.setCasNo(dto.getCasNo());

        OrderDetailDO orderDetail = productCodeIdentityMap.get(dto.getProductCode());
        result.setCategoryName(orderDetail.getCategoryTag());
        result.setPrice(orderDetail != null ? orderDetail.getFbidprice() : BigDecimal.ZERO);
        result.setReceivableNum(dto.getReceivableNum());
        result.setReceivedUnit(dto.getReceivedUnit());
        result.setReceivedNum(dto.getReceivedNum());
        result.setMeasurementUnit(dto.getMeasurementUnit());
        result.setMeasurementNum(BigDecimal.valueOf(dto.getMeasurementNum().doubleValue()));
        result.setFpicpath(dto.getFpicpath());
        result.setForm(dto.getForm());

        return result;
    }

    /**
     * 组装订单，订单商品打印信息
     * @param orderInfoList     订单DO数组
     * @param orderDetailList   订单商品DO数组
     * @param printResult       打印结果
     */
    private void packageOrderInfo(List<OrderMasterDO> orderInfoList, List<OrderDetailDO> orderDetailList, OrderCommonPrintDataDTO printResult) {
        OrderMasterDO orderMasterDO = orderInfoList.get(0);
        List<Integer> orderIdList = orderInfoList.stream().map(OrderMasterDO::getId).collect(Collectors.toList());
        Map<Integer, List<OrderDetailDO>> orderIdOrderDetailMap = DictionaryUtils.groupBy(orderDetailList, OrderDetailDO::getFmasterid);
        List<RefFundcardOrderDTO> refFundCardList = refFundcardOrderService.findByOrderIdList(orderIdList);

        Map<Integer, List<RefFundcardOrderDTO>> orderIdFundCardMap = DictionaryUtils.groupBy(refFundCardList, ref -> Integer.parseInt(ref.getOrderId()));
        // 获取采购日志
        Map<Integer, List<OrderPurchaseApprovalLogDTO>> orderIdPurchaseLogMap;
        if(orderMasterDO.getFuserid() == OrgEnum.FO_SHAN_SHI_FU_YOU_BAO_JIAN_YUAN.getValue()
                || orderMasterDO.getFuserid() == OrgEnum.DA_LIAN_SHI_FU_NV_ER_TONG_YI_LIAO_ZHONG_XIN.getValue()){
            // 佛山市妇幼保健院特殊需求，如为竞价仅返回提交竞价初审、初审通过、初选通过、终审通过的竞价日志。大连妇女儿童医疗中心，需要初审/终审通过及提交终审电子签名（最后一次通过的）
            final List<String> filterBidOperationList = New.list(OperationEnum.SUBMIT_BEGIN_APPROVAL.getName(),
                    OperationEnum.BEGIN_APPROVAL_PASS.getName(), OperationEnum.FIRST_AUTO_PASS.getName(), OperationEnum.FIRST_SKIP_APPROVAL.getName(),
                    OperationEnum.SUBMIT_FINAL_APPROVAL.getName() ,OperationEnum.FINAL_APPROVAL_PASS.getName(), OperationEnum.FINAL_AUTO_PASS.getName(), OperationEnum.FINAL_SKIP_APPROVAL.getName());
            orderIdPurchaseLogMap = getPrintCommonDataService.getPurchaseOrBidLog(orderInfoList, filterBidOperationList, true);
        } else if(orderMasterDO.getFuserid() == OrgEnum.YANG_JIANG_SHI_REN_MIN_YI_YUAUN.getValue()){
            orderIdPurchaseLogMap = New.emptyMap();
        } else{
            // 先留个坑 以后有需要再改
            orderIdPurchaseLogMap = getPrintCommonDataService.getPurchaseOrBidLog(orderInfoList, null, true);
        }
        // 获取验收/验收审批日志
        Map<Integer, List<OrderApprovalLogDTO>> orderIdApprovalLogMap = getPrintCommonDataService.getOrderIdApprovalLogMap(orderMasterDO.getId(), orderIdList);;

        // 构造采购单申请说明
        Map<Long, ApplicationMasterDTO> applyIdNoteMap = new HashMap<>();
        List<Long> applyIdList = New.list();
        List<String> orderNoList = New.list();
        Set<Integer> buyerIdSet = New.set();
        Map<String, Integer> orderNoIdMap = New.map();
        for (OrderMasterDO master : orderInfoList) {
            if(master == null){
                continue;
            }
            if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(master.getOrderType())){
                applyIdList.add(Long.valueOf(master.getFtbuyappid()));
            }
            orderNoList.add(master.getForderno());
            buyerIdSet.add(master.getFbuyerid());
            orderNoIdMap.put(master.getForderno(), master.getId());
        }
        if (CollectionUtils.isNotEmpty(applyIdList)) {
            ApplicationQueryDTO applyQuery = new ApplicationQueryDTO();
            applyQuery.setApplyIds(applyIdList);
            List<ApplicationMasterDTO> applyMasterList = applicationBaseClient.findByMasterId(applyQuery);
            BusinessErrUtil.notNull(applyMasterList, ExecptionMessageEnum.QUERY_PURCHASE_REQUISITION_FAILED, applyIdList);
            for (ApplicationMasterDTO applyMaster : applyMasterList) {
                if (applyMaster != null) {
                    applyIdNoteMap.put(applyMaster.getId(), applyMaster);
                }
            }
        }

        // 买家用户信息
        List<UserBaseInfoDTO> buyerDTOList = userClient.getUserByIdsAndOrgId(buyerIdSet, orderMasterDO.getFuserid());
        Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap = DictionaryUtils.toMap(buyerDTOList, UserBaseInfoDTO::getId, Function.identity());

        // 订单对应课题组负责人信息
        List<Long> departmentIdList = orderInfoList.stream().filter(it -> Objects.nonNull(it.getFbuydepartmentid())).map(it -> it.getFbuydepartmentid().longValue()).collect(Collectors.toList());
        List<DepartmentDTO> orderDepartmentMapping = departmentRpcClient.getDepartmentsByIds(departmentIdList);
        List<Integer> departmentManagerIdList = orderDepartmentMapping.stream().map(DepartmentDTO::getManagerId).filter(Objects::nonNull).collect(Collectors.toList());
        List<UserBaseInfoDTO> departmentManagerList = userClient.getUserByIdsAndOrgId(departmentManagerIdList, orderMasterDO.getFuserid());

        // 课题组父级部门的信息
        ListenableFuture<String> deptParentNameFuture = AsyncExecutor.listenableCallAsync(() -> this.getDeptParentName(orderMasterDO));
        // 自定义订单分类（农科院）
        CompletableFuture<Map<Integer, String>> selfDefCateMapFuture = AsyncExecutor.callAsync(() -> {
            Map<Integer, String> orderIdSelfDefCateMap = new HashMap<>();
            if (Objects.equals(OrgEnum.GUANG_DONG_SHENG_NONG_YE_KE_XUE_YUAN.getValue(), orderMasterDO.getFuserid())) {
                List<BaseOrderExtraDTO> selfDefCateList = orderExtraClient.selectByOrderIdInAndExtraKey(orderIdList, OrderExtraEnum.SELF_DEFINED_CATEGORY.getValue());
                for (BaseOrderExtraDTO selfDefCate : selfDefCateList) {
                    orderIdSelfDefCateMap.put(selfDefCate.getOrderId(), selfDefCate.getExtraValue());
                }
            }
            return orderIdSelfDefCateMap;
        });

        // 负责人id -> 负责人名字
        Map<Integer, String> manageIdNameMap = DictionaryUtils.toMap(departmentManagerList, UserBaseInfoDTO::getId, UserBaseInfoDTO::getName);
        Map<Integer, String> departmentManageIdNameMap = new HashMap<>(orderDepartmentMapping.size());
        for (DepartmentDTO d : orderDepartmentMapping) {
            departmentManageIdNameMap.put(d.getId(), manageIdNameMap.get(d.getManagerId()));
        }

        AtomicReference<String> orderApprovalName = new AtomicReference<>("");
        AtomicReference<String> orderSecondApprovalName = new AtomicReference<>("");
        AtomicReference<String> orderThirdApprovalName = new AtomicReference<>("");
        // 采购审批日志，默认一级，特殊单位特殊处理
        int approvalPrintLevel = ApproveLevelEnum.LEVEL1.getValue();
        // 福州皮肤病医院需要5级审批
        if (OrgEnum.FU_ZHOU_PI_FU_BING_FANG_ZHI_YUAN.getCode().equals(orderMasterDO.getFusercode())) {
            approvalPrintLevel = 5;
        }
        // 这个医院同上, todo 代码待优化
        if (OrgEnum.JIANG_XI_ZHONG_YI_YAO_DA_XUE.getCode().equals(orderMasterDO.getFusercode())) {
            approvalPrintLevel = 3;
        }

        Map<Integer, List<OrderPurchaseApprovalLogPrintDTO>> orderIdPrintLogMap = New.map();
        for(Map.Entry<Integer, List<OrderPurchaseApprovalLogDTO>> entry : orderIdPurchaseLogMap.entrySet()){
            int finalApprovalPrintLevel = approvalPrintLevel;
            orderIdPrintLogMap.put(entry.getKey(), entry.getValue().stream().map(log->{
                OrderPurchaseApprovalLogPrintDTO printDTO = new OrderPurchaseApprovalLogPrintDTO();
                printDTO.setDate(log.getDate());
                printDTO.setApprover(log.getApprover());
                printDTO.setOperate(log.getOperate());
                printDTO.setOperateComment(log.getOperateComment());
                printDTO.setApproveLevel(log.getApproveLevel());
                printDTO.setResult(log.getResult());
                printDTO.setApprovePhotoUrl(log.getElectronicSignUrl());
                if("审批通过".equals(log.getResult())){
                    if (log.getApproveLevel() == finalApprovalPrintLevel) orderApprovalName.set(log.getApprover());
                    if (log.getApproveLevel() == 2) orderSecondApprovalName.set(log.getApprover());
                    if (log.getApproveLevel() == 3) orderThirdApprovalName.set(log.getApprover());
                }
                return printDTO;
            }).collect(toList()));
        }
        for(Map.Entry<Integer, List<OrderApprovalLogDTO>> entry : orderIdApprovalLogMap.entrySet()){
            orderIdPrintLogMap.computeIfAbsent(entry.getKey(), (k)->New.list()).addAll(entry.getValue().stream().sorted(Comparator.comparingInt(OrderApprovalLogDTO::getId)).map(log -> {
                OrderPurchaseApprovalLogPrintDTO printDTO = new OrderPurchaseApprovalLogPrintDTO();
                printDTO.setDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, log.getCreationTime()));
                printDTO.setApprover(log.getOperatorName());
                printDTO.setApproveLevel(log.getApproveLevel());
                printDTO.setOperate(OrderApprovalEnum.getNameByValue(log.getApproveStatus()));
                printDTO.setResult(OrderApprovalEnum.getNameByValue(log.getApproveStatus()));
                if(orderMasterDO.getFuserid() == OrgEnum.YANG_JIANG_SHI_REN_MIN_YI_YUAUN.getValue()){
                    printDTO.setResult(printDTO.getResult().replaceAll("验收审批", "报销审批"));
                    printDTO.setOperate(printDTO.getResult());
                }
                printDTO.setOperateComment(log.getReason());
                printDTO.setApprovePhotoUrl(log.getSignUrl());
                return printDTO;
            }).collect(toList()));
        }

        if(OrgEnum.ZHONG_QING_YI_KE_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getValue() == orderMasterDO.getFuserid()){
            List<BizWarehouseReceiceDTO> bizWarehouseReceiceDTOList = claimServiceClient.queryClaimByOrderNos(orderNoList);
            for(BizWarehouseReceiceDTO claimItem : bizWarehouseReceiceDTOList){
                Integer orderId = orderNoIdMap.get(claimItem.getOrderNo());
                OrderPurchaseApprovalLogPrintDTO log = new OrderPurchaseApprovalLogPrintDTO();
                log.setDate(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, claimItem.getCreateTime()));
                log.setApprover(claimItem.getApplyUserName());
                log.setOperate("确认领用");
                log.setResult("确认领用");
                log.setOperateComment("-");
                orderIdPrintLogMap.computeIfAbsent(orderId, (k)->New.list()).add(log);
            }
        }

        // 验收评价
        List<OrderAcceptCommentVO> orderCommentList = New.list();
        Map<Integer, OrderAcceptCommentVO> orderIdCommentMap = new HashMap<>();
        if (Integer.valueOf(107).equals(orderMasterDO.getFuserid())) {
            orderCommentList = orderAcceptCommentClient.getOrderComment(orderMasterDO.getFuserid(), orderIdList);
            orderIdCommentMap = orderCommentList.stream().collect(Collectors.toMap(OrderAcceptCommentVO::getOrderId, Function.identity(), (oldValue, newValue) -> newValue));
        }

        // 获取课题组父级名称，自定义订单分类(农科院），不阻塞
        String deptParentName = null;
        Map<Integer, String> orderIdSelfDefCateMap;
        try {
            deptParentName = deptParentNameFuture.get();
            orderIdSelfDefCateMap = selfDefCateMapFuture.get();
        } catch (Exception e) {
            orderIdSelfDefCateMap = new HashMap<>();
        }
        // 查询供应商信息
        Map<Integer, SupplierDTO> supplierDTOMap = New.map();
        List<Integer> suppIds = orderInfoList.stream().map(OrderMasterDO::getFsuppid).collect(toList());
        List<SupplierDTO> supplierDTOS = suppClient.getSupplierListByIds(suppIds);
        if (CollectionUtils.isNotEmpty(supplierDTOS)) {
            supplierDTOS.forEach(suppDTO -> supplierDTOMap.put(suppDTO.getId(), suppDTO));
        }
        List<OrderPrintDTO> orderPrintList = new ArrayList<>(orderInfoList.size());
        for (OrderMasterDO orderItem : orderInfoList) {
            OrderPrintDTO orderPrint = new OrderPrintDTO();
            SupplierDTO supplierDTO = supplierDTOMap.get(orderItem.getFsuppid());
            this.packageOrderDetailInfo(orderPrint, orderIdOrderDetailMap.get(orderItem.getId()));
            this.newPackageOrderFundCardInfo(orderPrint, orderIdFundCardMap.get(orderItem.getId()), orderMasterDO.getFusercode());
            orderPrint.setOrderId(orderItem.getId());
            orderPrint.setOrderNo(orderItem.getForderno());
            orderPrint.setApplicationId(orderItem.getFtbuyappid());
            orderPrint.setBuyerName(orderItem.getFbuyername());
            orderPrint.setSuppCode(orderItem.getFsuppcode());
            orderPrint.setSuppName(orderItem.getFsuppname());
            String suppAddress = StringUtils.EMPTY;
            if (Objects.nonNull(supplierDTO) && Objects.nonNull(supplierDTO.getLocation())) {
                LocationDTO location = supplierDTO.getLocation();
                 suppAddress = Stream.of(location.getProvince(), location.getCity(), location.getDistrict(), location.getAddress())
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining());
            }
            orderPrint.setSuppAddress(suppAddress);
            orderPrint.setInvoiceTitleId(orderItem.getInvoiceTitleId());
            orderPrint.setOrderApprovalName(orderApprovalName.get());
            orderPrint.setOrderSecondApprovalName(orderSecondApprovalName.get());
            orderPrint.setOrderThirdApprovalName(orderThirdApprovalName.get());
            // 送货单信息
            orderPrint.setBuyerContactMan(orderItem.getFbuyercontactman());
            orderPrint.setReceiverPhone(orderItem.getFbuyertelephone());
            orderPrint.setReceiverAddress(orderItem.getFbiderdeliveryplace());

            // 减去退货后的价格
            BigDecimal amountTotal = orderItem.getForderamounttotal();
            BigDecimal returnAmount = orderItem.getReturnAmount() == null ? BigDecimal.ZERO : BigDecimal.valueOf(orderItem.getReturnAmount());
            orderPrint.setTotalPriceAfterReturn(amountTotal.subtract(returnAmount));

            // 线下单的供应商统一信用码信息
            String suppUnifyCode = null;

            // 验收时图片
            orderPrint.setReceivePicUrls(New.list());
            if(orderItem.getReceivePicUrls() != null) {
                orderPrint.setReceivePicUrls(New.list(orderItem.getReceivePicUrls().split(";")));
            }

            if (Objects.equals(OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue(), orderMasterDO.getFuserid()) && OrderSpeciesEnum.OFFLINE.getValue() == orderItem.getSpecies().intValue()){
                OfflineSupplierDTO offlineSuppInfo = suppClient.getOfflineSuppById(orderMasterDO.getFsuppid());
                suppUnifyCode = offlineSuppInfo.getUnifyCode();
            }
            orderPrint.setUnifyCode(suppUnifyCode);

            // 二维码或条形码
            try {
                String base64Img = QrCodeUtil.getBase64(orderItem.getForderno(), 120, 120);
                orderPrint.setOrderNoQrCode(base64Img);
            } catch (Exception e) {
                LOGGER.error("单据打印，生成订单号二维码失败\n入参:"+orderItem.getForderno());
                Cat.logError(CAT_TYPE, "packageOrderInfo", "单据打印，生成订单号二维码失败\n入参:"+orderItem.getForderno(), e);
            }
            try{
                // 生成微信订单详情二维码
                String wechatOrderDetailLink = String.format(WECHAT_ORDER_DETAIL_URL, orderItem.getId());
                String wechatOrderDetailQrCode = QrCodeUtil.getBase64(wechatOrderDetailLink, 120, 120);
                orderPrint.setWechatOrderDetailQrCode(wechatOrderDetailQrCode);
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "packageOrderInfo", "生成微信订单详情二维码失败\n入参:"+orderItem.getId(), e);
            }
            try {
                String base64Img = BarCodeUtils.getBase64Img(orderItem.getForderno());
                orderPrint.setBarCode(base64Img);
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "packageOrderInfo", "生成订单号条形码失败\n入参:"+orderItem.getForderno(), e);
            }
            orderPrint.setOrderSpecies(orderItem.getSpecies().intValue());
            // 采购人联系电话
            orderPrint.setBuyerTelephone(buyerIdIdentityMap.get(orderItem.getFbuyerid()) != null ? buyerIdIdentityMap.get(orderItem.getFbuyerid()).getMobile() : StringUtils.EMPTY);
            orderPrint.setOrderReceiptDate(orderItem.getFlastreceivedate());
            orderPrint.setReceiverName(orderItem.getFlastreceiveman());
            orderPrint.setDepartmentName(orderItem.getFbuydepartment());
            // 增加课题组父级名称（当前仅陆军军医）
            orderPrint.setDepartmentParentName(deptParentName);
            // 增加订单对应的分类
            orderPrint.setSelfDefCategory(orderIdSelfDefCateMap.get(orderItem.getId()));
            // 课题组负责人
            orderPrint.setDepartmentManagerName(departmentManageIdNameMap.get(orderItem.getFbuydepartmentid()));
            orderPrint.setOrgName(orderItem.getFusername());
            orderPrint.setOrderPurchaseApprovalLogPrintList(orderIdPrintLogMap.getOrDefault(orderItem.getId(), New.emptyList()));
            // 验收评价
            if (CollectionUtils.isNotEmpty(orderCommentList)) {
                OrderAcceptCommentVO orderAcceptCommentVO = orderIdCommentMap.get(orderItem.getId());
                orderPrint.setAcceptCommentIdList(orderAcceptCommentVO.getAcceptCommentTagList());
            }

            orderPrint.setOrderDate(orderItem.getForderdate());
            // 采购申请说明
            if (OrderTypeEnum.PURCHASE_ORDER.getCode().equals(orderItem.getOrderType()) && orderItem.getFtbuyappid() != null) {
                ApplicationMasterDTO applicationMasterDTO = applyIdNoteMap.get(Long.valueOf(orderItem.getFtbuyappid()));
                if (applicationMasterDTO != null) {
                    orderPrint.setPurchaseNote(applicationMasterDTO.getApplyInfo());
                    orderPrint.setApplicationNo(applicationMasterDTO.getApplyNumber());
                }
            }
            orderPrint.setAcceptApprovalName(getAcceptApprovalName(orderIdApprovalLogMap.getOrDefault(orderItem.getId(), New.emptyList()), buyerIdIdentityMap));
            // 从送货单组装方法搬运至此
            this.setSuppUsersToOrderPrint(orderItem, orderPrint);
            orderPrint.setOldFlag(oldDateService.isOldDate(orderItem.getFuserid(), orderItem.getForderdate(), orderItem.getFundStatus(), orderItem.getSpecies().intValue()));;
            orderPrintList.add(orderPrint);
        }
        printResult.setOrderMasterList(orderPrintList);
    }

    /**
     * 设置供应商用户到orderPrint
     *
     * @param orderMasterDO 订单主表数据
     * @param orderPrint    打印数据
     */
    private void setSuppUsersToOrderPrint(OrderMasterDO orderMasterDO, OrderPrintDTO orderPrint) {
        // 获取供应商暂无批量接口，待优化... 
        List<UserAccountDTO> userAccountDTOS = suppClient.querySuppAccountList(orderMasterDO.getFsuppid(), null);
        List<SupplierUserDTO> suppUserList = userAccountDTOS.stream().filter(Objects::nonNull)
                .filter(supp -> supp.getId() != null && supp.getId().toString().equals(orderMasterDO.getFconfirmmanid()))
                .map(supp -> {
                    SupplierUserDTO suppUserDTO = new SupplierUserDTO();
                    suppUserDTO.setName(supp.getName());
                    suppUserDTO.setPhone(supp.getMobile());
                    return suppUserDTO;
                }).collect(toList());
        orderPrint.setSuppUsers(suppUserList);
    }

    /**
     *
     * 获取验收审批人姓名
     * @param orderApprovalLogList
     * @param buyerIdIdentityMap
     * @return
     */
    private String getAcceptApprovalName(List<OrderApprovalLogDTO> orderApprovalLogList, Map<Integer, UserBaseInfoDTO> buyerIdIdentityMap) {
        Integer operatorId = orderApprovalLogList.stream().filter(log -> OrderApprovalEnum.PASS.getValue().equals(log.getApproveStatus())).findFirst().map(OrderApprovalLogDTO::getOperatorId).orElse(-1);
        return Optional.ofNullable(buyerIdIdentityMap.get(operatorId)).map(UserBaseInfoDTO::getName).orElse("");
    }

    /**
     * 获取当前部门的父级部门（目前仅陆军军医大，线下送货单需求）
     * @param orderMasterDO
     * @return
     */
    private String getDeptParentName(OrderMasterDO orderMasterDO) {
        String deptParentName = null;
        DepartmentDTO deptParentInfo = departmentRpcClient.getDepartmentParentInfo(orderMasterDO.getFbuydepartmentid());
        // 陆军医个性化
        if (Objects.equals(OrgEnum.LU_JUN_JUN_YI_DA_XUE.getValue(), orderMasterDO.getFuserid())) {
            // 个性化需求：根部门不展示，其他的需要获取父级部门的名称
            boolean isRootDept = deptParentInfo.getParentId() == null || "根部门".equals(deptParentInfo.getName());
            return isRootDept ? null : deptParentInfo.getName();
        }
        // 杭州红十字医院个性化
        if (Objects.equals(OrgEnum.HANG_ZHOU_SHI_HONG_SHI_ZI_HUI_YI_YUAN.getValue(), orderMasterDO.getFuserid())) {
            // 个性化需求：根部门返回空串，其他的需要获取父级部门的名称
            boolean isRootDept = deptParentInfo.getParentId() == null || "根部门".equals(deptParentInfo.getName());
            return isRootDept ? "" : deptParentInfo.getName();
        }
        return deptParentInfo.getName();
    }

    /**
     * 组装订单商品明细
     * @param orderPrint                打印结果
     * @param orderDetailList           订单商品DO数组
     */
    private void packageOrderDetailInfo(OrderPrintDTO orderPrint, List<OrderDetailDO> orderDetailList) {
        List<OrderDetailPrintDTO> orderDetailPrintList = new ArrayList<>(orderDetailList.size());
        for (OrderDetailDO orderDetailItem : orderDetailList) {
            OrderDetailPrintDTO orderDetailPrint = OrderDetailTranslator.doToOrderDetailPrintDTO(orderDetailItem);
            // 需要设置下分类标签，待顶级分类冗余字段推进上线后可以删掉这个逻辑
            orderDetailPrint.setCategoryName(orderDetailItem.getCategoryTag());
            orderDetailPrint.setFeeType(orderDetailItem.getFeeTypeTag());
            orderDetailPrintList.add(orderDetailPrint);
        }
        orderPrint.setOrderDetailPrintList(orderDetailPrintList);
    }

    /**
     * 组装订单经费卡信息
     * @param orderPrint        打印结果
     * @param orderFundcardList 订单关联经费卡数组
     * @param orgCode           机构组织code
     */
    private void newPackageOrderFundCardInfo(OrderPrintDTO orderPrint, List<RefFundcardOrderDTO> orderFundcardList, String orgCode) {
        // 经费类型（目前仅广东农科院）
        CompletableFuture<Map<Integer, String>> fundTypeMapFuture = AsyncExecutor.callAsync(() -> researchFundCardServiceClient.getFundTypeMap(orgCode));
        // 获取单位经费卡层级
        String cardLevelString = sysConfigClient.getConfigByOrgCodeAndConfigCode(orgCode, ConfigConstant.RESEARCH_FUNDCARD_LEVEL);
        int cardLevel = NumberUtils.toInt(cardLevelString);

        if (CollectionUtils.isEmpty(orderFundcardList)) {
            return;
        }
        Map<String, RefFundcardOrderDTO> cardIdRefFundCardMap = orderFundcardList.stream().collect(Collectors.toMap(RefFundcardOrderTranslator::getLastLevelCardId, Function.identity(), (o,n)->n));
        List<String> cardIdList = New.list(cardIdRefFundCardMap.keySet());
        List<FundCardDTO> fundProjectList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
        if (CollectionUtils.isEmpty(fundProjectList)) {
            return;
        }

        // 经费类型map（目前仅广东农科院）
        Map<Integer, String> fundTypeMap;
        try {
            fundTypeMap = fundTypeMapFuture.get();
        } catch (Exception e) {
            fundTypeMap = new HashMap<>();
        }

        List<OrderFundCardPrintDTO> fundCardPrintDTOS = new ArrayList<>();
        //配置1级经费卡的和中山五院的都是只取第一级数据打印
        //单位配置使用的是一级经费卡
        for (FundCardDTO fundProjectDto : fundProjectList) {
            OrderFundCardPrintDTO fundCardItem = handleFundCardItem(fundProjectDto);
            if (FundCardLevelEnum.FUND_CARD.getValue() == cardLevel && !OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_WU_YI_YUAN.getCode().equals(orgCode)) {
                // 获取二级经费卡
                List<FundCardDTO> secondFundCardList = fundProjectDto.getFundCardDTOs();
                if (CollectionUtils.isNotEmpty(secondFundCardList)) {
                    for (FundCardDTO fundCard : secondFundCardList) {
                        fundCardItem.setCardNo(fundCard.getCode());
                        // 经费类型
                        fundCardItem.setFundType(fundTypeMap.get(fundCard.getFundType()));
                        // 二级经费卡项目名
                        fundCardItem.setSubjectName(fundCard.getName());
                        // 二级经费卡code
                        fundCardItem.setSubjectCode(fundCard.getCode());
                        fundCardPrintDTOS.add(fundCardItem);
                    }
                }
                continue;
            }
            // 经费类型
            fundCardItem.setFundType(fundTypeMap.get(fundProjectDto.getFundType()));
            //经费卡号和项目号一致
            fundCardItem.setCardNo(fundProjectDto.getCode());
            fundCardPrintDTOS.add(fundCardItem);
        }
        orderPrint.setOrderFundCardPrintList(fundCardPrintDTOS);

        if(OrgEnum.YANG_JIANG_SHI_REN_MIN_YI_YUAUN.getCode().equals(orgCode)){
            List<String> firstLevelCardIdList = fundProjectList.stream().map(FundCardDTO::getId).collect(toList());
            List<FundCardDTO> allCardList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, firstLevelCardIdList);
            List<FundCardPrintDTO> fundCardPrintDTOList =  allCardList.stream().map(card->this.constructCardTree(card, cardIdRefFundCardMap)).filter(Objects::nonNull).collect(toList());
            fundCardPrintDTOList.forEach(fundCardPrintDTO -> {
                fundCardPrintDTO.setBudgetAmount(Optional.ofNullable(fundCardPrintDTO.getSubCardList()).orElse(New.emptyList()).stream().map(FundCardPrintDTO::getBudgetAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            });
            orderPrint.setFundCardPrintDTOList(fundCardPrintDTOList);
        }
    }

    private FundCardPrintDTO constructCardTree(FundCardDTO card, Map<String, RefFundcardOrderDTO> cardIdRefFundCardMap){
        if(FundCardEntityStatusEnum.DELETE.getValue() == card.getStatus()){
            return null;
        }
        RefFundcardOrderDTO bindCard = cardIdRefFundCardMap.get(card.getId());
        FundCardPrintDTO fundCardPrintDTO = new FundCardPrintDTO()
                .setBudgetAmount(card.getBudgetAmount())
                .setCode(card.getCode())
                .setFreezeAmount(bindCard != null ? bindCard.getFreezeAmount() : null);
        if(CollectionUtils.isNotEmpty(card.getFundCardDTOs())){
            fundCardPrintDTO.setSubCardList(card.getFundCardDTOs().stream().map(subCard->this.constructCardTree(subCard, cardIdRefFundCardMap)).filter(Objects::nonNull).collect(toList()));
        }
        return fundCardPrintDTO;
    }

    /**
     * 处理经费卡详情信息里的项目编号，名称，负责人
     * @param fundProjectDto
     * @return
     */
    private OrderFundCardPrintDTO handleFundCardItem(FundCardDTO fundProjectDto) {
        OrderFundCardPrintDTO fundCardItem = new OrderFundCardPrintDTO();
        fundCardItem.setProjectCode(fundProjectDto.getCode());
        fundCardItem.setProjectName(fundProjectDto.getName());
        List<FundCardManagerDTO> fundCardManagerDTOs = fundProjectDto.getFundCardManagerDTOs();
        if (CollectionUtils.isNotEmpty(fundCardManagerDTOs)) {
            fundCardItem.setFundManager(fundCardManagerDTOs.stream().filter(item -> StringUtils.isNotBlank(item.getManagerName())).map(FundCardManagerDTO::getManagerName).collect(Collectors.joining("，")));
        }
        return fundCardItem;
    }

    @Override
    @ServiceLog(description = "重推订单(旧)", serviceType = ServiceType.RPC_SERVICE, operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> retryPushOrderByOrderNo(String orderNo) {
        Preconditions.notNull(orderNo, "重试失败！订单号不可不能为空");
        OrderMasterDO orderInfo = orderMasterMapper.findByForderno(orderNo);
        Preconditions.notNull(orderInfo, orderNo + "查无订单");
        // 获取登录用户得会话信息
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        LoginUserInfoBO userInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        return retryPushOrderCore(orderInfo, userInfo);
    }

    /**
     * 推送订单核心代码
     * @param orderInfo 订单信息
     * @param userInfo  登录用户
     * @return          是否成功
     */
    public RemoteResponse<Boolean> retryPushOrderCore(OrderMasterDO orderInfo, LoginUserInfoBO userInfo) {
        // 更新推送状态，状态前端需要做展示
        String orderNo = orderInfo.getForderno();
        orderOtherLogClient.createOrderDockingLog(orderNo, orderInfo.getFusercode(), orderNo,
                "订单正在推送至" + orderInfo.getFusername() + "管理平台，请稍后。", userInfo.getUserName(), null);

        Boolean retryResult = pushThirdPlatformOrderStrategy(orderInfo);

        return RemoteResponse.<Boolean>custom().setSuccess().setData(retryResult);
    }

    private Boolean pushThirdPlatformOrderStrategy(OrderMasterDO orderInfo) {
        Integer currentStatus = orderInfo.getStatus();

        // 获取登录用户得会话信息
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderInfo.getId());
        UserBaseInfoDTO userInfo = userClient.getUserInfo(orderInfo.getFbuyerid(), orderInfo.getFuserid());

        if(USE_NEW_REDO_PUSH_INTERFACE_LIST.contains(currentStatus)){
            // 如果是8,9(待供应商确认，采购人申请取消)两个状态，是通过新接口重推的
            thirdPartOrderRPCClient.redoPushOrderInfo(orderInfo.getForderno());
        }else{
            // 否则走老接口
            if (dockingConfigServiceClient.getOuterBuyerSet(New.list(OuterBuyerDockingTypeEnum.ORDER_PUSH, OuterBuyerDockingTypeEnum.ORDER_NO_PUSH)).contains(orderInfo.getFusercode())) {
                ThirdPartOrderMasterDTO thirdPartOrder = OrderMasterTranslator.doToThirdPartOrderDTO(orderInfo);
                List<OrderDetailDO> detailDOList = orderDetailMapper.findByFmasterid(orderInfo.getId());
                if (CollectionUtils.isNotEmpty(detailDOList)) {
                    thirdPartOrder.setOrderDetailList(detailDOList.stream().map(OrderDetailTranslator::doToThirdPartOrderDetailDTO).collect(toList()));
                }
                thirdPartOrderRPCClient.pushSingleOrderInfo(thirdPartOrder, OrderEventTypeEnum.RETRY_PUSH_ORDER, userInfo.getGuid(), userInfo.getName());
            } else if (DockingConstant.GUANG_XI_ZHONG_LIU.equals(orderInfo.getFusercode())) {
                if (DockingConstant.WAITING_FOR_RECEIVE == currentStatus || DockingConstant.WAITING_STATEMENT == currentStatus || DockingConstant.CLOSE == currentStatus) {
                    ThirdPartyPlatformOrderBO orderBO = new ThirdPartyPlatformOrderBO();
                    orderBO.setStatus(currentStatus);
                    orderBO.setReagentOrderNo(orderInfo.getForderno());
                    orderBO.setOrgCode(orderInfo.getFusercode());
                    orderMasterForTPIService.updateOrderStatusAsync(orderBO);
                    return true;
                }else if(OrderStatusEnum.WaitingForDockingConfirm.getValue().equals(currentStatus)){
                    ApplicationMasterDTO applicationMasterDTO = applicationBaseClient.getApplicationMasterByApplyId(orderInfo.getFtbuyappid(), false);
                    if(PushStatusEnum.FAIL.getValue() == applicationMasterDTO.getPushStatus()){
                        orderManageService.pushOrderToThirdPlatform(orderInfo, orderDetailDOList);
                    }
                }
            }
        }
        return true;
    }

    private Map<String, List<FundCardDTO>> fetchOrderNoCardMap(OrderMasterDO orderMasterDO) {
        Map<String, List<FundCardDTO>> orderNoFundCardMap = new HashMap<>(1);
        String orgCode = orderMasterDO.getFusercode();
        // 定制化需求，广州医科在生成订单时要获取订单经费项目信息
        if (OrgEnum.GUANG_ZHOU_YI_KE_DA_XUE.getCode().equals(orgCode)) {
            List<RefFundcardOrderDTO> cardByOrderId = refFundcardOrderService.findByOrderId(New.list(orderMasterDO.getId().toString()));
            if (CollectionUtils.isEmpty(cardByOrderId)) {
                return orderNoFundCardMap;
            }
            List<String> cardIdList = cardByOrderId.stream().map(RefFundcardOrderTranslator::getLastLevelCardId).distinct().collect(Collectors.toList());
            Map<String, String> cardIdOrderNoMap = new HashMap<>();
            for (String cardId : cardIdList) {
                cardIdOrderNoMap.put(cardId, orderMasterDO.getForderno());
            }

            List<FundCardDTO> cardList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
            cardList.stream().forEach(card -> {
                List<FundCardDTO> orderFundCardProjectList = new ArrayList<>();
                cardIdOrderNoMap.keySet().stream().forEach(cardId -> {
                    boolean hit = this.siftDown(cardId, card);
                    // 如果经费卡id命中经费卡树，把经费卡项目（1级）信息筛到orderId -> List<FundCardDTO>字典中
                    if (hit && FundCardLevelEnum.FUND_CARD_PROJECT.getValue() == card.getLevel()) {
                        orderFundCardProjectList.add(card);
                        orderNoFundCardMap.put(cardIdOrderNoMap.get(cardId), orderFundCardProjectList);
                    }
                });
            });
        }
        return orderNoFundCardMap;
    }

    /**
     * 通过cardId递归父节点向下查询选经费卡数据
     * @param cardId    cardId
     * @param card      经费卡父节点
     * @return          是否命中
     */
    private boolean siftDown(String cardId, FundCardDTO card) {
        if (cardId == null) {
            return false;
        }
        if (cardId.equals(card.getId())) {
            return true;
        }
        List<FundCardDTO> fundCardDTOs = card.getFundCardDTOs();
        if (CollectionUtils.isEmpty(fundCardDTOs)) {
            return false;
        }
        for (FundCardDTO cardDTO : fundCardDTOs) {
            boolean b = siftDown(cardId, cardDTO);
            if (b) {
                return b;
            }
        }
        return false;
    }

    @Override
    public RemoteResponse<Integer> retryPushOrderByAppId(Integer appId) {
        Preconditions.notNull(appId, "推送失败！appId为空！");
        List<OrderMasterDO> orderList = orderMasterMapper.findByFtbuyappidIn(Arrays.asList(appId));
        BusinessErrUtil.notEmpty(orderList, ExecptionMessageEnum.PUSH_FAILED_NO_ORDER_INFO);
        // 获取登录用户得会话信息
        RjSessionInfo rjSessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment(GatewayConstant.SESSION_INFO);
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        LoginUserInfoBO userInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        for (OrderMasterDO orderMasterDO : orderList) {
            this.retryPushOrderCore(orderMasterDO, userInfo);
        }
        return RemoteResponse.<Integer>custom().setSuccess().setData(orderList.size());
    }
}
