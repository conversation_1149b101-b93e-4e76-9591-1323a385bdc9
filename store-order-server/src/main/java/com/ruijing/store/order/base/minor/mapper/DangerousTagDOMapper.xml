<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijing.store.order.base.minor.mapper.DangerousTagDOMapper">
  <resultMap id="BaseResultMap" type="com.ruijing.store.order.base.minor.model.DangerousTagDO">
    <!--@mbg.generated-->
    <!--@Table t_dangerous_tag-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="cas_no" jdbcType="VARCHAR" property="casNo" />
    <result column="dangerous_type" jdbcType="INTEGER" property="dangerousType" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="creation_time" jdbcType="TIMESTAMP" property="creationTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="regulatory_type" jdbcType="INTEGER" property="regulatoryType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, cas_no, dangerous_type, business_type, creation_time, update_time, 
    regulatory_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_dangerous_tag
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_dangerous_tag
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.DangerousTagDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dangerous_tag (business_id, cas_no, dangerous_type, 
      business_type, creation_time, update_time, 
      regulatory_type)
    values (#{businessId,jdbcType=VARCHAR}, #{casNo,jdbcType=VARCHAR}, #{dangerousType,jdbcType=INTEGER}, 
      #{businessType,jdbcType=INTEGER}, #{creationTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{regulatoryType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruijing.store.order.base.minor.model.DangerousTagDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_dangerous_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="casNo != null">
        cas_no,
      </if>
      <if test="dangerousType != null">
        dangerous_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="creationTime != null">
        creation_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="regulatoryType != null">
        regulatory_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="casNo != null">
        #{casNo,jdbcType=VARCHAR},
      </if>
      <if test="dangerousType != null">
        #{dangerousType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="regulatoryType != null">
        #{regulatoryType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruijing.store.order.base.minor.model.DangerousTagDO">
    <!--@mbg.generated-->
    update t_dangerous_tag
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="casNo != null">
        cas_no = #{casNo,jdbcType=VARCHAR},
      </if>
      <if test="dangerousType != null">
        dangerous_type = #{dangerousType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="creationTime != null">
        creation_time = #{creationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="regulatoryType != null">
        regulatory_type = #{regulatoryType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruijing.store.order.base.minor.model.DangerousTagDO">
    <!--@mbg.generated-->
    update t_dangerous_tag
    set business_id = #{businessId,jdbcType=VARCHAR},
      cas_no = #{casNo,jdbcType=VARCHAR},
      dangerous_type = #{dangerousType,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      creation_time = #{creationTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      regulatory_type = #{regulatoryType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2020-05-11-->
  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_dangerous_tag(
        id,
        business_id,
        cas_no,
        dangerous_type,
        business_type,
        creation_time,
        update_time,
        regulatory_type
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.id,jdbcType=BIGINT},
            #{element.businessId,jdbcType=VARCHAR},
            #{element.casNo,jdbcType=VARCHAR},
            #{element.dangerousType,jdbcType=INTEGER},
            #{element.businessType,jdbcType=INTEGER},
            #{element.creationTime,jdbcType=TIMESTAMP},
            #{element.updateTime,jdbcType=TIMESTAMP},
            #{element.regulatoryType,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-08-11-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO t_dangerous_tag(
        business_id,
        cas_no,
        dangerous_type,
        business_type,
        creation_time,
        update_time,
        regulatory_type
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.businessId,jdbcType=VARCHAR},
            #{element.casNo,jdbcType=VARCHAR},
            #{element.dangerousType,jdbcType=INTEGER},
            #{element.businessType,jdbcType=INTEGER},
            <choose>
                <when test="element.creationTime">
                    #{element.creationTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            <choose>
                <when test="element.updateTime">
                    #{element.updateTime,jdbcType=TIMESTAMP},
                </when>
                <otherwise>
                    now(),
                </otherwise>
            </choose>
            <choose>
                <when test="element.regulatoryType">
                    #{element.regulatoryType,jdbcType=INTEGER}
                </when>
                <otherwise>
                    2
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>

<!--auto generated by MybatisCodeHelper on 2020-11-04-->
  <select id="selectByBusinessIdInAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_dangerous_tag
        <where>
            <if test="businessIdCollection != null and businessIdCollection.size() > 0">
                and business_id in
                <foreach item="item" index="index" collection="businessIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="businessType != null">
                and business_type=#{businessType,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>