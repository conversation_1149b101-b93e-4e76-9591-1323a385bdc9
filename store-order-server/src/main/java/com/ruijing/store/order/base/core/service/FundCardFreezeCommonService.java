package com.ruijing.store.order.base.core.service;

import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.gateway.buyercenter.request.ChangeFundCardRequestDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/9 9:25
 * @description
 */
public interface FundCardFreezeCommonService {

    /**
     * 获取重新冻结的参数
     * @param orderMasterDO 订单数据
     * @return 冻结参数
     */
    ChangeFundCardRequestDTO getReFreezeRequestParam(OrderMasterDO orderMasterDO);

    /**
     * 订单支付
     * @param unfinishedOrderList 未完成->已完成的订单
     */
    void orderDefray(List<OrderMasterDO> unfinishedOrderList);
}
