package com.ruijing.store.order.gateway.print.vo.apply;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @author: chenzhanliang
 * @createTime: 2024-04-26 16:54
 * @description: 采购说明
 **/
@RpcModel("采购说明")
public class ApplyInstructionsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("序号")
    private Integer uniSort;

    @RpcModelProperty("说明标题")
    private String title;

    @RpcModelProperty("值")
    private String value;

    public ApplyInstructionsVO() {

    }

    public ApplyInstructionsVO(Integer uniSort, String title, String value) {
        this.uniSort = uniSort;
        this.title = title;
        this.value = value;
    }

    public Integer getUniSort() {
        return uniSort;
    }

    public void setUniSort(Integer uniSort) {
        this.uniSort = uniSort;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
