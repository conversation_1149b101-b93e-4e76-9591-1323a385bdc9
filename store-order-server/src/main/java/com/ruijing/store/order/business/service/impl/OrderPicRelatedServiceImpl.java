package com.ruijing.store.order.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.request.OrderDetailAcceptancePicRequestDTO;
import com.reagent.order.base.order.dto.request.OrderFileOperationLogRequestDTO;
import com.reagent.order.enums.OrderPushEventEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.baseconfig.api.enums.ConfigCodeEnum;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.InventoryStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptPictureDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.AdditionalAcceptancePicDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.PictureDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderApprovalLogMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderApprovalLog;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.minor.mapper.OrderPicMapper;
import com.ruijing.store.order.base.minor.model.OrderPic;
import com.ruijing.store.order.business.bo.ThirdPartyPlatformOrderBO;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptByPhotoEnum;
import com.ruijing.store.order.business.enums.myorderlist.OrderAcceptWayEnum;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.business.service.OrderMasterForTPIService;
import com.ruijing.store.order.business.service.OrderPicRelatedService;
import com.ruijing.store.order.business.service.constant.OrderAcceptConstant;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderAcceptPicRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderDetailAcceptPicRequest;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OverrideUploadImageRequest;
import com.ruijing.store.order.rpc.client.OrderAcceptCommentClient;
import com.ruijing.store.order.rpc.client.OrderOtherLogClient;
import com.ruijing.store.order.rpc.client.RePushRPCServiceClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * @author: chenzhanliang
 * @createTime: 2024-09-23 10:15
 * @description:
 **/
@Service
public class OrderPicRelatedServiceImpl implements OrderPicRelatedService {

    @Resource
    private UserClient userClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;


    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrderPicMapper orderPicMapper;

    @Resource
    private OrderApprovalLogMapper orderApprovalLogMapper;

    @Resource
    private OrderMasterForTPIService orderMasterForTPIService;

    @Resource
    private OrderAcceptCommentClient orderAcceptCommentClient;

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderOtherLogClient orderOtherLogClient;


    @Resource
    private RePushRPCServiceClient rePushRpcServiceClient;


    /**
     * 追加上传图片，可以先删除再插入验收照片的单位
     */
    private final Set<Integer> DELETE_AND_UPDATE_ACCEPTANCE_PHOTO_ORG_SET = New.set(OrgEnum.GUANG_XI_ZHONG_LIU.getValue());


    private final Logger logger = LoggerFactory.getLogger(getClass());


    /**
     * @param rjSessionInfo
     * @return java.lang.Boolean
     * @description: 保存追加验收图片到数据库
     * @date: 2021/1/26 11:45
     * @author: zengyanru
     */
    @Override
    @ServiceLog(description = "存追加验收图片到数据库", serviceType = ServiceType.COMMON_SERVICE)
    public Boolean checkAndSaveAppendAcceptanceImages(RjSessionInfo rjSessionInfo, OrderAcceptPicRequest orderAcceptPicRequest) {
        Integer orderId = orderAcceptPicRequest.getOrderId();
        List<String> filePaths = orderAcceptPicRequest.getFilePaths();
        BusinessErrUtil.notNull(orderId, ExecptionMessageEnum.ORDER_ID_REQUIRED_FOR_IMAGES);
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.ORDER_ACCEPTANCE);
        // 找到订单
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
        this.validateAcceptanceImageCommon(orderMaster, loginInfo);

        // 校验图片数量
        String receivedPicUrls = orderMaster.getReceivePicUrls();
        checkPicNum(orderMaster, filePaths.size(), OrderAcceptConstant.UPLOAD_RECEIVE_PIC_THRESHOLD);
        // 开始验收
        String newlyReceivePicUrls = StringUtils.join(filePaths, ";");
        String combinedPicUrls;
        if (StringUtils.isEmpty(receivedPicUrls)) {
            combinedPicUrls = newlyReceivePicUrls;
        } else {
            combinedPicUrls = receivedPicUrls + ";" + newlyReceivePicUrls;
        }
        AdditionalAcceptancePicDTO addtionalAcceptPicDTO = new AdditionalAcceptancePicDTO();
        addtionalAcceptPicDTO.setOrderId(orderId);
        addtionalAcceptPicDTO.setOrderNo(orderMaster.getForderno());
        addtionalAcceptPicDTO.setReceivePicUrls(combinedPicUrls);
        addtionalAcceptPicDTO.setListReceivePicUrl(filePaths);
        addtionalAcceptPicDTO.setUserId(loginInfo.getUserId());
        addtionalAcceptPicDTO.setSkipLog(orderAcceptPicRequest.getSkipLog());
        this.appendReceiveImagesSkipCheck(addtionalAcceptPicDTO);

        // todo 广西肿瘤追加图片时，重新推送入库状态到对方
        this.syncOrderStatus(orderMaster);
        return true;
    }

    /**
     * 校验图片数量
     *
     * @param addPicNum       新增图片数量
     * @param uploadThreshold 上传阈值
     * @ param orderMasterDO 订单信息
     */
    private void checkPicNum(OrderMasterDO orderMasterDO, int addPicNum, int uploadThreshold) {
        String receivedPicUrls = orderMasterDO.getReceivePicUrls();
        if (StringUtils.isNotBlank(receivedPicUrls)) {
            String[] picUrls = receivedPicUrls.split(";");
            BusinessErrUtil.isTrue(picUrls.length + addPicNum <= uploadThreshold, ExecptionMessageEnum.MAX_UPLOAD_ACCEPTANCE_PHOTOS, uploadThreshold);
        }
    }


    @Override
    @ServiceLog(description = "存追加验收图片", serviceType = ServiceType.COMMON_SERVICE)
    public void appendReceiveImagesSkipCheck(AdditionalAcceptancePicDTO additionalAcceptancePicDTO) {
        Assert.notNull(additionalAcceptancePicDTO, "追加验收图片DTO为空");
        //更新order_master验收图片
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(additionalAcceptancePicDTO.getOrderId());
        updateOrderParamDTO.setReceivePicUrls(additionalAcceptancePicDTO.getReceivePicUrls());
        orderMasterMapper.updateOrderById(updateOrderParamDTO);
        List<String> picUrlList = additionalAcceptancePicDTO.getListReceivePicUrl();
        List<OrderPic> orderPicList = picUrlList.stream().map(url -> {
                    OrderPic orderPic = new OrderPic();
                    orderPic.setOrderNo(additionalAcceptancePicDTO.getOrderNo());
                    orderPic.setPic(url);
                    return orderPic;
                }
        ).collect(toList());
        if (CollectionUtils.isNotEmpty(picUrlList)){
            orderPicMapper.batchInsert(orderPicList);
        }

        if (BooleanUtils.isNotTrue(additionalAcceptancePicDTO.getSkipLog())) {
            this.createOrderOperateLog(additionalAcceptancePicDTO.getOrderId()
                    , OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_PICTURE.getValue()
                    , additionalAcceptancePicDTO.getUserId()
                    , "新追加" + additionalAcceptancePicDTO.getListReceivePicUrl().size() + "张照片");
        }
    }

    @ServiceLog(description = "存追加详情关联验收图片", serviceType = ServiceType.COMMON_SERVICE)
    public void appendDetailAcceptanceImages(RjSessionInfo rjSessionInfo, OrderDetailAcceptPicRequest orderDetailAcceptPicRequest) {
        Integer orderId = orderDetailAcceptPicRequest.getOrderId();
        List<AcceptPictureDTO> detailPictureDTOList = orderDetailAcceptPicRequest.getDetailPictureDTOList();
        BusinessErrUtil.notNull(orderId, "订单id不能为空");
        BusinessErrUtil.notEmpty(detailPictureDTOList, ExecptionMessageEnum.PLEASE_UPLOAD_PICTURES_FIRST);
        List<String> newPicUrlList = detailPictureDTOList.stream()
                .map(AcceptPictureDTO::getPictureDTO)
                .filter(Objects::nonNull)
                .map(PictureDTO::getPictureUrl)
                .filter(StringUtils::isNotBlank)
                .collect(toList());
        BusinessErrUtil.notEmpty(newPicUrlList, ExecptionMessageEnum.PLEASE_UPLOAD_PICTURES_FIRST);


        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
        // 校验图片数量
        checkPicNum(orderMaster, detailPictureDTOList.size(), OrderAcceptConstant.UPLOAD_RECEIVE_PIC_THRESHOLD);

        // 先关联订单
        OrderAcceptPicRequest orderAcceptPicRequest = new OrderAcceptPicRequest();
        orderAcceptPicRequest.setOrderId(orderId);
        orderAcceptPicRequest.setFilePaths(newPicUrlList);
        orderAcceptPicRequest.setSkipLog(orderAcceptPicRequest.getSkipLog());
        checkAndSaveAppendAcceptanceImages(rjSessionInfo, orderAcceptPicRequest);

        // 关联订单详情
        List<OrderDetailAcceptancePicRequestDTO> saveList = New.list();
        for (AcceptPictureDTO acceptPictureDTO : detailPictureDTOList) {
            List<Integer> orderDetailIdList = acceptPictureDTO.getOrderDetailIdList();
            if (Objects.isNull(acceptPictureDTO.getPictureDTO())) {
                continue;
            }

            PictureDTO pictureDTO = acceptPictureDTO.getPictureDTO();

            // 允许detailId为空
            if (CollectionUtils.isEmpty(orderDetailIdList)) {
                OrderDetailAcceptancePicRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptancePicRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderId);
                orderDetailAcceptanceFileDTO.setDetailId(null);
                orderDetailAcceptanceFileDTO.setUrl(pictureDTO.getPictureUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(pictureDTO.getPictureName()));
                saveList.add(orderDetailAcceptanceFileDTO);
                continue;
            }

            for (Integer orderDetailId : orderDetailIdList) {
                OrderDetailAcceptancePicRequestDTO orderDetailAcceptanceFileDTO = new OrderDetailAcceptancePicRequestDTO();
                orderDetailAcceptanceFileDTO.setOrderId(orderId);
                orderDetailAcceptanceFileDTO.setDetailId(orderDetailId);
                orderDetailAcceptanceFileDTO.setUrl(pictureDTO.getPictureUrl());
                orderDetailAcceptanceFileDTO.setFileName(StringUtils.defaultString(pictureDTO.getPictureName()));
                saveList.add(orderDetailAcceptanceFileDTO);
            }
        }
        // 插入新的
        if (CollectionUtils.isNotEmpty(saveList)) {
            orderAcceptCommentClient.batchSaveDetailAcceptancePic(saveList);
        }
    }

    @Override
    @ServiceLog(description = "覆盖验收图片", serviceType = ServiceType.COMMON_SERVICE)
    public Boolean overwriteAcceptanceImages(RjSessionInfo rjSessionInfo, Integer orderId, List<String> filePaths) {
        Preconditions.notNull(orderId, "追加验收图片必须先选择订单，请传入订单id");
        LoginUserInfoBO loginInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(), true, ConfigConstant.ORDER_ACCEPTANCE);
        // 找到订单
        OrderMasterDO orderMaster = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMaster, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
        this.validateAcceptanceImageCommon(orderMaster, loginInfo);
        if(!(DELETE_AND_UPDATE_ACCEPTANCE_PHOTO_ORG_SET.contains(loginInfo.getOrgId()) && InventoryStatusEnum.FAILED.getCode().equals(orderMaster.getInventoryStatus().intValue()))){
            BusinessErrUtil.isTrue(false, "该单位/状态不支持替换验收图片");
        }
        this.replaceImage(filePaths, OrderAcceptConstant.UPLOAD_RECEIVE_PIC_THRESHOLD, orderMaster, loginInfo.getUserId());
        this.syncOrderStatus(orderMaster);
        return true;
    }


    private void replaceImage(List<String> filePaths, int uploadThreshold, OrderMasterDO orderMasterDo, Integer userId){
        String orderNo = orderMasterDo.getForderno();
        BusinessErrUtil.isTrue(filePaths.size() <= uploadThreshold, ExecptionMessageEnum.MAX_UPLOAD_ACCEPTANCE_PHOTOS, uploadThreshold);
        BusinessErrUtil.notEmpty(filePaths, "没有上传验收图片，不能提交");

        String receivePicUrls = orderMasterDo.getReceivePicUrls() == null ? StringUtils.EMPTY : orderMasterDo.getReceivePicUrls();
        String[]  split = StringUtils.split(receivePicUrls, ";");
        Set<String> originalPicSet = New.set(split);
        Set<String> newPicSet = New.set(filePaths);
        BusinessErrUtil.isTrue(!CollectionUtils.isEqualCollection(originalPicSet,newPicSet), "验收材料未更新，提交入库失败");

        //新增的图片
        newPicSet.removeAll(originalPicSet);

        //先删除，再插入
        orderPicMapper.deleteByOrderNo(orderNo);
        List<OrderPic> orderPicList = filePaths.stream().map(url -> {
                    OrderPic orderPic = new OrderPic();
                    orderPic.setOrderNo(orderMasterDo.getForderno());
                    orderPic.setPic(url);
                    return orderPic;
                }
        ).collect(toList());
        if (!CollectionUtils.isEmpty(filePaths)){
            orderPicMapper.batchInsert(orderPicList);
        }

        //更新order_master验收图片
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(orderMasterDo.getId());
        String newlyReceivePicUrls = StringUtils.join(filePaths, ";");
        updateOrderParamDTO.setReceivePicUrls(newlyReceivePicUrls);
        orderMasterMapper.updateOrderById(updateOrderParamDTO);

        this.createOrderOperateLog(orderMasterDo.getId()
                , OrderApprovalEnum.DELETE_AND_UPDATE_ACCEPTANCE_PHOTO.getValue()
                , userId
                , "删除后更新"+ newPicSet.size()+"张验收照片");
    }

    /**
     * 验收审批--覆盖上传验收图片
     */
    @Override
    @ServiceLog(description = "验收审批--覆盖上传验收图片", serviceType = ServiceType.COMMON_SERVICE)
    public void acceptApprovalOverrideImage(RjSessionInfo rjSessionInfo, OverrideUploadImageRequest request) {
        overrideAcceptImageCore(rjSessionInfo, request);
    }

    /**
     * 覆盖上传验收图片核心方法
     */
    private void overrideAcceptImageCore(RjSessionInfo rjSessionInfo, OverrideUploadImageRequest request) {
        Integer orderId = request.getOrderId();
        UserBaseInfoDTO userDTO = userClient.getUserInfoByGuidAndOrgid(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        BusinessErrUtil.notNull(userDTO, ExecptionMessageEnum.USER_INFO_NOT_FOUND);
        List<PictureDTO> pictureDTOList = request.getPictureDTOList();
        List<AcceptPictureDTO> detailPictureDTOList = request.getDetailPictureDTOList();
        List<String> picUrlList = New.emptyList();
        // 提取图片URL集合
        if (CollectionUtils.isNotEmpty(pictureDTOList)) {
            picUrlList = pictureDTOList.stream().map(PictureDTO::getPictureUrl)
                    .filter(StringUtils::isNotBlank).
                    collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(detailPictureDTOList)) {
            picUrlList = detailPictureDTOList.stream().map(AcceptPictureDTO::getPictureDTO)
                    .filter(Objects::nonNull)
                    .map(PictureDTO::getPictureUrl)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }

        // 查询订单信息
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_ID_NOT_FOUND);
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
        BusinessErrUtil.notEmpty(orderDetailDOList, ExecptionMessageEnum.NO_PRODUCT_DETAILS_FOR_ORDER);

        // 查询OMS验收配置
        List<String> configCodeList = New.list(ConfigConstant.ORG_RECEIPT_PIC_CONFIG,
                ConfigCodeEnum.ORG_RECEIPT_PIC_AMOUNT_LIMIT.name(),
                ConfigCodeEnum.ORDER_ACCEPT_PHOTO_OPEN_PRODUCT_ASSOCIATION.name());
        Map<String, String> receiptConfigMap = sysConfigClient.getConfigMapByOrgCodeAndConfigCode(orderMasterDO.getFusercode(), configCodeList);

        // 校验图片验收配置
        validateAcceptPicConfig(receiptConfigMap, orderMasterDO, orderDetailDOList, picUrlList, detailPictureDTOList);

        // 旧图片集合
        List<String> oldPicUrlList = StringUtils.isBlank(orderMasterDO.getReceivePicUrls()) ? New.emptyList() :
                StrUtil.split(orderMasterDO.getReceivePicUrls(), ";");

        Set<String> oldPicSet = New.set(oldPicUrlList);
        Set<String> newPicSet = New.set(picUrlList);
        // 被删除的图片
        Set<String> deletedPicSet = New.set(oldPicSet);
        deletedPicSet.removeAll(newPicSet);

        // 新增的图片
        Set<String> addedPicSet = New.set(newPicSet);
        addedPicSet.removeAll(oldPicSet);

        // 删除订单关联图片
        orderPicMapper.deleteByOrderNo(orderMasterDO.getForderno());
        // 删除订单主表验收图片
        UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
        updateOrderParamDTO.setOrderId(orderId);
        updateOrderParamDTO.setReceivePicUrls(StringUtils.EMPTY);
        orderMasterMapper.updateAcceptInfoById(updateOrderParamDTO);
        if (CollectionUtils.isNotEmpty(detailPictureDTOList)) {
            // 删除详情旧图片
            orderAcceptCommentClient.deleteDetailAcceptancePic(orderId);
            // 保存详情关联图片
            OrderDetailAcceptPicRequest orderDetailAcceptPicRequest = new OrderDetailAcceptPicRequest();
            orderDetailAcceptPicRequest.setOrderId(orderId);
            orderDetailAcceptPicRequest.setDetailPictureDTOList(detailPictureDTOList);
            orderDetailAcceptPicRequest.setSkipLog(true);
            appendDetailAcceptanceImages(rjSessionInfo, orderDetailAcceptPicRequest);
        } else if (CollectionUtils.isNotEmpty(pictureDTOList)) {
            // 保存订单关联图片
            OrderAcceptPicRequest orderAcceptPicRequest = new OrderAcceptPicRequest();
            orderAcceptPicRequest.setOrderId(orderId);
            orderAcceptPicRequest.setFilePaths(picUrlList);
            orderAcceptPicRequest.setSkipLog(true);
            checkAndSaveAppendAcceptanceImages(rjSessionInfo, orderAcceptPicRequest);
        }

        // 记录新增操作日志
        if (CollectionUtils.isNotEmpty(addedPicSet)) {
            this.createOrderOperateLog(orderId,
                    OrderApprovalEnum.ADDITIONAL_ACCEPTANCE_PICTURE.getValue(),
                    userDTO.getId(),
                    StrUtil.format("新追加{}张照片", addedPicSet.size()));
        }

        // 记录删除操作日志
        if (CollectionUtils.isNotEmpty(deletedPicSet)) {
            Integer orderOperateLogId = this.createOrderOperateLog(orderId, OrderApprovalEnum.DELETE_ACCEPTANCE_PHOTO.getValue(), userDTO.getId(), StringUtils.EMPTY);
            // 保存订单文件操作日志
            createOrderFileLog(orderId, orderOperateLogId, New.list(deletedPicSet));
        }

    }

    /**
     * 校验图片验收配置
     */
    private void validateAcceptPicConfig(Map<String, String> receiptConfigMap, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, List<String> picUrlList, List<AcceptPictureDTO> detailPictureList) {
        //特殊验收图片校验
        if (orderAcceptService.customValidationPicture(picUrlList, orderMasterDO, orderDetailDOList)) {
            return;
        }

        //拍照验收校验
        orderAcceptService.validateAcceptPicUpload(receiptConfigMap, orderMasterDO, orderDetailDOList, picUrlList);

        // 校验 订单详情图片关联配置
        orderAcceptService.validateDetailPicUpload(receiptConfigMap, orderDetailDOList, detailPictureList);
    }





    /**
     * 验收图片通用校验
     *
     * @param orderMaster
     * @param loginInfo
     */
    private void validateAcceptanceImageCommon(OrderMasterDO orderMaster, LoginUserInfoBO loginInfo){
        // 配置确认
        List<BaseConfigDTO> configList = sysConfigClient.getValueByOrgCodeAndConfigCode(loginInfo.getOrgCode(), New.list(ConfigConstant.ORG_RECEIPT_PIC_CONFIG, ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE));
        BusinessErrUtil.notEmpty(configList, ExecptionMessageEnum.IMAGE_UPLOAD_CONFIG_NOT_FOUND, loginInfo.getOrgName());
        Map<String, String> configCodeValueMap = configList.stream().collect(toMap(BaseConfigDTO::getConfigCode, BaseConfigDTO::getConfigValue, (oldValue, newValue) -> oldValue));
        String picAcceptanceCode = configCodeValueMap.get(ConfigConstant.ORG_RECEIPT_PIC_CONFIG);
        boolean needPictureAccept = OrderAcceptByPhotoEnum.COMPULSORY.getValue().toString().equals(picAcceptanceCode)
                || OrderAcceptByPhotoEnum.NOT_COMPULSORY.getValue().toString().equals(picAcceptanceCode)
                || OrderAcceptByPhotoEnum.COMPULSORY_EXCEPT_SERVICE.getValue().toString().equals(picAcceptanceCode);
        BusinessErrUtil.isTrue(needPictureAccept, ExecptionMessageEnum.NO_PHOTO_ACCEPTANCE_REQUIRED);

        // 权限确认，验收方式确认
        BusinessErrUtil.isTrue(loginInfo.getDeptIdList().contains(orderMaster.getFbuydepartmentid()), ExecptionMessageEnum.NO_IMAGE_ACCEPTANCE_PERMISSION);
        String procurementAcceptWay = configCodeValueMap.get(ConfigConstant.CONFIG_CODE_PROCUREMENT_ACCEPTANCE);
        BusinessErrUtil.notNull(procurementAcceptWay, ExecptionMessageEnum.ORG_NOT_CONFIGURED_ACCEPTANCE_METHOD, loginInfo.getOrgName());
        if (OrderAcceptWayEnum.CROSS_ACCEPTANCE.value.equals(Integer.valueOf(procurementAcceptWay))) {
            BusinessErrUtil.isTrue(!loginInfo.getUserId().equals(orderMaster.getFbuyerid()), ExecptionMessageEnum.ORG_CROSS_ACCEPTANCE_NOT_ALLOWED, loginInfo.getOrgName());
        }

        // 校验订单状态，需要在“待验收审批”、“待结算”、“结算中”
        boolean allowToAddPic = OrderStatusEnum.Statementing_1.value.equals(orderMaster.getStatus())
                || OrderStatusEnum.WaitingForStatement_1.value.equals(orderMaster.getStatus())
                || OrderStatusEnum.OrderReceiveApproval.value.equals(orderMaster.getStatus())
                || OrderStatusEnum.Finish.value.equals(orderMaster.getStatus());
        BusinessErrUtil.isTrue(allowToAddPic, ExecptionMessageEnum.ONLY_SPECIFIC_STATUS_CAN_ADD_ACCEPTANCE_IMAGES);
    }


    /**
     * 同步订单状态（单位要求在某个动作后触发同步）
     *
     * @param orderMaster
     */
    private void syncOrderStatus(OrderMasterDO orderMaster){
        if (DockingConstant.GUANG_XI_ZHONG_LIU.equals(orderMaster.getFusercode()) && !OrderDateConstant.isOldOrderForNormal(orderMaster.getFusercode(), orderMaster.getForderdate())) {
            ThirdPartyPlatformOrderBO thirdPartyPlatformOrderBO = new ThirdPartyPlatformOrderBO();
            thirdPartyPlatformOrderBO.setOrgCode(orderMaster.getFusercode());
            thirdPartyPlatformOrderBO.setReagentOrderNo(orderMaster.getForderno());
            thirdPartyPlatformOrderBO.setStatus(OrderStatusEnum.WaitingForStatement_1.getValue());
            orderMasterForTPIService.updateOrderStatusAsync(thirdPartyPlatformOrderBO);

            UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
            updateOrderParamDTO.setOrderId(orderMaster.getId());
            updateOrderParamDTO.setInventoryStatus(InventoryStatusEnum.WAITING_FOR_REVIEW.getCode());
            orderMasterMapper.updateOrderById(updateOrderParamDTO);
        }

        if(OrgEnum.JIN_FENG_SHI_YAN_SHI.getCode().equals(orderMaster.getFusercode()) && !OrderDateConstant.isOldOrderForView(orderMaster.getFusercode(), orderMaster.getForderdate())){
            BusinessErrUtil.isTrue(OrderStatusEnum.OrderReceiveApproval.getValue().equals(orderMaster.getStatus()), ExecptionMessageEnum.ORDER_NOT_PENDING_ACCEPTANCE_CAN_NOT_SYNC_STATUS);
            rePushRpcServiceClient.rePush(orderMaster.getForderno(), null, OrderPushEventEnum.NOTICE_RECEIVE_TO_OUTER_BUYER, true);
        }
    }

    /**
     * 返回日志自增ID
     */
    private Integer createOrderOperateLog(Integer orderId , Integer approveStatus, Integer userId , String reason) {
        OrderApprovalLog orderApprovalLog = new OrderApprovalLog();
        orderApprovalLog.setOrderId(orderId);
        orderApprovalLog.setApproveStatus(approveStatus);
        orderApprovalLog.setOperatorId(userId);
        orderApprovalLog.setReason(reason);
        orderApprovalLog.setCreationTime(new Date());
        orderApprovalLogMapper.insertSelective(orderApprovalLog);
        return orderApprovalLog.getId();
    }

    /**
     * 保存订单文件操作日志
     *
     * @param orderId 订单ID
     * @param logId 日志ID
     * @param deletedPicSet 被删除的图片集合
     */
    private void createOrderFileLog(Integer orderId, Integer logId, List<String> deletedPicSet) {
        if (CollectionUtils.isEmpty(deletedPicSet)) {
            return;
        }
        List<OrderFileOperationLogRequestDTO> fileOperationLogs = deletedPicSet.stream().map(deletedPic -> {
            OrderFileOperationLogRequestDTO dto = new OrderFileOperationLogRequestDTO();
            dto.setOrderId(orderId);
            dto.setLogId(logId);
            dto.setUrl(deletedPic);
            dto.setFileName(StringUtils.EMPTY);
            return dto;
        }).collect(toList());
        orderOtherLogClient.batchSaveFileLog(fileOperationLogs);
    }
}
