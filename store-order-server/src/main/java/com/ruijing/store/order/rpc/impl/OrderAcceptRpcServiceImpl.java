package com.ruijing.store.order.rpc.impl;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.ReceiptOrderResponseDO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderAcceptRpcService;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.business.service.OrderAcceptService;
import com.ruijing.store.order.rpc.client.AcceptApprovalClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 订单验收模块
 * @author: zhangzhifeng
 * @create: 2021/07/01 10:30
 **/
@MSharpService
@ServiceLog
public class OrderAcceptRpcServiceImpl implements OrderAcceptRpcService {

    @Resource
    private OrderAcceptService orderAcceptService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private AcceptApprovalClient acceptApprovalClient;

    /**
     * 用户手动验收订单
     * @param orderReceiptParamDTO
     * @return
     */
    @Override
    public RemoteResponse<ReceiptOrderResponseDO> userAcceptOrder(OrderReceiptParamDTO orderReceiptParamDTO) {
        Assert.isTrue(orderReceiptParamDTO != null ,"订单验收入参不能为空!");
        ReceiptOrderResponseDO receiptOrderResponseDO = orderAcceptService.userAcceptOrder(orderReceiptParamDTO);
        return RemoteResponse.<ReceiptOrderResponseDO>custom().setSuccess().setData(receiptOrderResponseDO).build();
    }

    @Override
    public RemoteResponse<ReceiptOrderResponseDO> autoAcceptOrder(OrderReceiptParamDTO request) {
        final Integer orderId = request.getOrderId();
        final Integer userId = request.getUserId();
        final String userName = request.getUserName();
        final Integer inventoryStatus = request.getInventoryStatus();
        Preconditions.notNull(userId, "accept failure, userId must be not null: orderId = " + orderId);
        OrderMasterDO order = orderMasterMapper.selectByPrimaryKey(orderId);
        Preconditions.notNull(order, "accept failure, order doesn't existed: orderId = " + orderId);

        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForReceive.getValue().equals(order.getStatus()), ExecptionMessageEnum.RECEIVING_FAILED_WRONG_STATUS, OrderStatusEnum.get(order.getStatus()));

        OrderReceiptParamDTO params = new OrderReceiptParamDTO();
        params.setUserId(userId);
        params.setOrderId(orderId);
        params.setUserName(userName);
        params.setOrderSnapshot(OrderMasterTranslator.orderMasterDO2OrderMasterDTO(order));
        params.setOrgId(order.getFuserid());
        params.setInventoryStatus(inventoryStatus);
        params.setOrgCode(order.getFusercode());
        ReceiptOrderResponseDO response = orderAcceptService.autoAcceptOrder(params, null);
        return RemoteResponse.<ReceiptOrderResponseDO>custom().setSuccess().setData(response);
    }
}
