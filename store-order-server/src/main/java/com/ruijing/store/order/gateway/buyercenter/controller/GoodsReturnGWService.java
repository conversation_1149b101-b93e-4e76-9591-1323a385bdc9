package com.ruijing.store.order.gateway.buyercenter.controller;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.fundamental.remoting.msharp.annotation.RpcMapping;
import com.ruijing.store.order.business.service.GoodsReturnService;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnOrderRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.goodsreturn.GoodsReturnRespVO;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2020/11/6 19:12
 * @Description
 **/
@MSharpService(isGateway = "true")
@RpcMapping("/returnOrders")
@RpcApi(value = "订单-我的退货")
public class GoodsReturnGWService {

    @Resource
    private GoodsReturnService goodsReturnService;

    @ServiceLog(description = "采购人中心获取退货单数据", serviceType = ServiceType.RPC_SERVICE)
    @RpcMapping("/getGoodReturnList")
    public RemoteResponse<GoodsReturnRespVO> getReturnOrderInfo(RjSessionInfo rjSessionInfo, GoodsReturnOrderRequest request) {
        GoodsReturnRespVO response = goodsReturnService.getReturnOrderForProcure(request, rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        return RemoteResponse.<GoodsReturnRespVO>custom().setSuccess().setData(response);
    }
}
