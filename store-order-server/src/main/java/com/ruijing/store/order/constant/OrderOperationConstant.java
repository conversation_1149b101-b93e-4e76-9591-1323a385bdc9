package com.ruijing.store.order.constant;

/**
 * <AUTHOR>
 * @Date 2021/1/27 10:21
 * @Description
 **/
public interface OrderOperationConstant {

    // 采购人中心 订单列表页面对订单进行的 取消、验收 操作（入库不在服务）
    String BUYER_CENTER_ORDER_OP = "BUYER_CENTER_ORDER_OP_";

    // HMS 验收审批换卡操作
    String ORDER_APPROVAL_CHANGE_FUND_CARD = "ORDER_APPROVAL_CHANGE_FUND_CARD_OP_";

    // 导出队列控制
    String EXPORT_QUEUE_CONTROL = "EXPORT_QUEUE_CONTROL";

    // 允许同时存在的全平台的导出任务数
    Long LIMIT_EXPORT_TASK_COUNT = 3L;

    /**
     * 订单验收/取消发货操作锁
     */
    String ACCEPT_REDIS_CACHE_KEY = "ACCEPT_ORDER_";
}
