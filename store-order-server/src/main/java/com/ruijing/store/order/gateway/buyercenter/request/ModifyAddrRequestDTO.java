package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2021/11/29 16:12
 */
@RpcModel("修改订单收货地址请求体")
public class ModifyAddrRequestDTO implements Serializable {

    private static final long serialVersionUID = -8754637577917231227L;

    @RpcModelProperty(value="地址", required = true)
    private String address;

    @RpcModelProperty(value="城市", required = true)
    private String city;

    @RpcModelProperty(value="收货人", required = true)
    private String consignee;

    @RpcModelProperty(value="区", required = true)
    private String district;

    @RpcModelProperty(value="收货电话", required = true)
    private String mobile;

    @RpcModelProperty(value="省份", required = true)
    private String province;

    @RpcModelProperty("地址标签")
    private String label;

    @RpcModelProperty("地址标签备注")
    private String labelRemarks;


    @RpcModelProperty("地址id（找不到就不传）")
    private Integer addressId;

    @RpcModelProperty(value = "订单id", required = true)
    private Integer orderId;

    /**
     * 内部用订单号
     */
    private String orderNo;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getLabelRemarks() {
        return labelRemarks;
    }

    public void setLabelRemarks(String labelRemarks) {
        this.labelRemarks = labelRemarks;
    }

    public String getAddress() {
        return address;
    }

    public ModifyAddrRequestDTO setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getCity() {
        return city;
    }

    public ModifyAddrRequestDTO setCity(String city) {
        this.city = city;
        return this;
    }

    public String getConsignee() {
        return consignee;
    }

    public ModifyAddrRequestDTO setConsignee(String consignee) {
        this.consignee = consignee;
        return this;
    }

    public String getDistrict() {
        return district;
    }

    public ModifyAddrRequestDTO setDistrict(String district) {
        this.district = district;
        return this;
    }

    public String getMobile() {
        return mobile;
    }

    public ModifyAddrRequestDTO setMobile(String mobile) {
        this.mobile = mobile;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public ModifyAddrRequestDTO setProvince(String province) {
        this.province = province;
        return this;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public ModifyAddrRequestDTO setAddressId(Integer addressId) {
        this.addressId = addressId;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public ModifyAddrRequestDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public ModifyAddrRequestDTO setOrderNo(String orderNo) {
        this.orderNo = orderNo;
        return this;
    }

    @Override
    public String toString() {
        return "ModifyAddrRequestDTO{" +
                "address='" + address + '\'' +
                ", city='" + city + '\'' +
                ", consignee='" + consignee + '\'' +
                ", district='" + district + '\'' +
                ", mobile='" + mobile + '\'' +
                ", province='" + province + '\'' +
                ", label='" + label + '\'' +
                ", labelRemarks='" + labelRemarks + '\'' +
                ", addressId=" + addressId +
                ", orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                '}';
    }
}
