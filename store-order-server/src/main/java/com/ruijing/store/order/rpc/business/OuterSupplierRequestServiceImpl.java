package com.ruijing.store.order.rpc.business;

import com.reagent.order.api.outer.supplier.OuterSupplierRequestService;
import com.reagent.order.dto.outer.supplier.OuterSupplierReturnDTO;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.request.GoodsReturnBaseRequestDTO;
import com.ruijing.store.goodsreturn.request.SupplierAuthorityDTO;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.GoodsReturnInfoDetailVO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Liwenyu
 * @create: 2024-03-22 11:59
 * @description:
 */
@MSharpService
public class OuterSupplierRequestServiceImpl implements OuterSupplierRequestService {

    @Resource
    private SupplierGoodsReturnService supplierGoodsReturnService;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "更新退货状态")
    public RemoteResponse<Boolean> updateReturnStatus(OuterSupplierReturnDTO returnDTO) {
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByReturnNoIn(New.list(returnDTO.getReturnNo()));
        BusinessErrUtil.notEmpty(goodsReturns, ExecptionMessageEnum.RETURN_ORDER_NOT_FOUND, returnDTO.getReturnNo());
        GoodsReturn goodsReturn = goodsReturns.get(0);

        GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO = new GoodsReturnBaseRequestDTO();
        goodsReturnBaseRequestDTO.setReturnId(goodsReturn.getId());
        goodsReturnBaseRequestDTO.setReturnStatus(returnDTO.getReturnStatus());
        goodsReturnBaseRequestDTO.setReason(returnDTO.getRemark());
        if(CollectionUtils.isNotEmpty(returnDTO.getDetails())){
            goodsReturnBaseRequestDTO.setGoodsReturnInfoDetailVOList(returnDTO.getDetails().stream().map(detail->{
                GoodsReturnInfoDetailVO vo = new GoodsReturnInfoDetailVO();
                vo.setGoodsCode(detail.getGoodCode());
                vo.setReturnAcceptMan(detail.getReturnAcceptMan());
                vo.setReturnAcceptAddr(detail.getReturnAcceptAddr());
                vo.setReturnAcceptPhone(detail.getReturnAcceptPhone());
                return vo;
            }).collect(Collectors.toList()));
        }
        // 传入一个假的供应商信息
        SupplierAuthorityDTO dummy = new SupplierAuthorityDTO();
        dummy.setSupplierId(goodsReturn.getSupplierId());
        dummy.setUserName("当当网");
        supplierGoodsReturnService.updateGoodsReturnStatus(goodsReturnBaseRequestDTO, goodsReturn, dummy);
        return RemoteResponse.success();
    }
}
