package com.ruijing.store.order.business.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.reagent.order.base.order.dto.*;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.order.base.order.enums.ExportFileTypeEnum;
import com.reagent.order.base.order.enums.OrderExportStatusEnum;
import com.reagent.research.fundcard.enums.SpeciesEnum;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.PageableResponse;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.message.Transaction;
import com.ruijing.fundamental.cat.util.CatUtils;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.upload.client.FileUploadClient;
import com.ruijing.fundamental.upload.client.FileUploadResp;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.whitehole.database.dto.address.data.DeliveryOperationLogDTO;
import com.ruijing.order.whitehole.database.dto.address.request.DeliveryOperationLogRequestDTO;
import com.ruijing.order.whitehole.database.dto.file.OrderUploadFileDTO;
import com.ruijing.order.whitehole.eventbase.enums.file.FileBusinessTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.oms.api.dto.OmsAccessDTO;
import com.ruijing.store.oms.api.enums.UserOrgModuleEnum;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.search.dto.FieldRangeDTO;
import com.ruijing.store.order.api.search.dto.FieldSortDTO;
import com.ruijing.store.order.api.search.dto.OrderSearchParamDTO;
import com.ruijing.store.order.api.search.dto.SearchPageResultDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoQueryDTO;
import com.ruijing.store.order.base.excel.dto.OrderExcelInfoResponseDTO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.SuppShopInfoBO;
import com.ruijing.store.order.business.service.BuyerOrderService;
import com.ruijing.store.order.business.service.OMSOrderService;
import com.ruijing.store.order.business.service.OrderExportService;
import com.ruijing.store.order.business.service.orderexport.ExportContext;
import com.ruijing.store.order.business.service.orderexport.ExportHeaderAndBody;
import com.ruijing.store.order.business.service.orderexport.cellstyle.CellStyle;
import com.ruijing.store.order.gateway.buyercenter.request.orderlist.OrderListRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderDetailVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderInfoVO;
import com.ruijing.store.order.gateway.buyercenter.vo.orderlist.OrderMasterVO;
import com.ruijing.store.order.gateway.oms.request.DeliveryUpdateRequest;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Zeng Yanru
 * @Description:
 * @DateTime: 2022/7/8 16:36
 */
@Service
public class OMSOrderServiceImpl implements OMSOrderService {

    private static final String CAT_TYPE = "OMSOrderServiceImpl";

    private static final Logger LOGGER = LoggerFactory.getLogger(OMSOrderServiceImpl.class);

    /**
     * 不需要展示送货单打印的状态。发货前的状态都不展示
     */
    private final List<Integer> NOT_SHOW_DELIVERY_PRINT_STATUS_LIST = New.list(
            OrderStatusEnum.DeckingFail.getValue(),
            OrderStatusEnum.WaitingForDockingConfirm.getValue(),
            OrderStatusEnum.WaitingForConfirm.getValue(),
            OrderStatusEnum.PurchaseApplyToCancel.getValue(),
            OrderStatusEnum.SupplierApplyToCancel.getValue(),
            OrderStatusEnum.ORDER_SPLIT_UP.getValue()
            );

    @Resource
    private BuyerOrderService buyerOrderService;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private OrderExportClient orderExportClient;

    @Resource
    private OrderExportService orderExportService;

    @Resource
    private UserClient userClient;

    @Resource
    private OrderUploadFileRpcClient orderUploadFileRpcClient;

    @Resource
    private DeliveryOperationLogClient deliveryOperationLogClient;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private SuppClient suppClient;

    /**
     * 代配送订单列表
     *
     * @param request
     * @return
     * @throws ParseException
     */
    @Override
    public PageableResponse<List<OrderInfoVO>> getDeliveryProxyOrderList(OrderListRequest request, Integer beginId, RjSessionInfo rjSessionInfo, UserOrgModuleEnum userOrgModuleEnum) {
        // 构造搜索入参（业务处理增加代配送搜，增加id倒序排序）
        List<OrderMasterSearchDTO> masterSearchList;
        List<OrderAddressDTO> addrList;
        Long totalHit;
        List<Integer> orderIdList;
        List<OrderInfoVO> resList = New.list();
        OrderSearchParamDTO searchParam = new OrderSearchParamDTO();
        buyerOrderService.constructSearchPageParam(searchParam, request);
        if(Boolean.TRUE.equals(request.getDeliveryOperatorOn())){
            searchParam.setDeliveryOperatorGuid(rjSessionInfo.getGuid());
        }
        searchParam.setDeliveryTypeList(New.list(DeliveryTypeEnum.PROXY.getCode()));
        List<FieldSortDTO> fieldSortDTOList = New.list();
        FieldSortDTO fieldSortDTO = new FieldSortDTO("id", SortOrderEnum.DESC);
        fieldSortDTOList.add(fieldSortDTO);
        searchParam.setFieldSortList(fieldSortDTOList);

        // 适配导出翻页需求
        if (beginId != null) {
            FieldRangeDTO idRangeDTO = new FieldRangeDTO();
            idRangeDTO.setField("id");
            idRangeDTO.setUpper(beginId.toString());
            idRangeDTO.setIncludeUpper(false);
            searchParam.getFieldRangeList().add(idRangeDTO);
        }

        // ---用户关联单位特殊处理
        // 1.获取用户关联单位
        List<Integer> linkOrgIdList = userClient.listOrgIdsByGuidAndModule(rjSessionInfo.getGuid(), userOrgModuleEnum);
        // 2.判断是否满足下面的条件，满足则特殊处理。否则取用buyerOrderService.constructSearchPageParam中生成的查询条件
        // 是否需要在搜索中直接过滤
        boolean needPreFilterLinkOrgIdList = false;
        if(request.getOrderId() == null && request.getOrderNo() == null){
            // 是否精确搜索的（主要是为了解决扫码扫不出来的时候提示需要特殊处理），不按订单id或订单号精确搜索的，就是不扫码的，直接作为过滤条件
            if (CollectionUtils.isNotEmpty(linkOrgIdList)) {
                needPreFilterLinkOrgIdList = true;
            }
        }
        if(needPreFilterLinkOrgIdList){
            if(request.getOrgId() != null && !linkOrgIdList.contains(request.getOrgId())){
                // 请求中需要过滤单位，但关联的单位里面没有这个单位，直接返回空
                return PageableResponse.<List<OrderInfoVO>>custom().setData(Collections.emptyList()).setTotal(0).setSuccess().setMsg("你无权限查看该单位数据");
            }
            if(request.getOrgId() == null){
                // 不需要过滤单位的，将关联单位直接作为条件传入
                searchParam.setOrgIdList(linkOrgIdList);
            }
        }

        // 搜索结果组装
        SearchPageResultDTO<OrderMasterSearchDTO> searchRes = orderSearchBoostService.commonSearch(searchParam);

        // 从搜索中获取对应信息
        totalHit = searchRes.getTotalHits();
        masterSearchList = searchRes.getRecordList();
        String emptyListShowMsg = null;
        // 3.如果是按订单id或订单号精确搜索的，肯定只会返回一条，可以不用考虑分页，搜出来之后再根据单位过滤，来获取是否需要提示"你无权限查看该单位数据"
        if(!(request.getOrderId() == null && request.getOrderNo() == null)){
            if (CollectionUtils.isNotEmpty(linkOrgIdList)) {
                masterSearchList = searchRes.getRecordList().stream()
                        .filter(orderMasterSearchDTO -> linkOrgIdList.contains(orderMasterSearchDTO.getFuserid()))
                        .collect(Collectors.toList());
                totalHit = (long) masterSearchList.size();
                if(CollectionUtils.isEmpty(masterSearchList)){
                    emptyListShowMsg = "你无权限查看该单位数据";
                }
            }
        }
        if (CollectionUtils.isEmpty(masterSearchList)) {
            return PageableResponse.<List<OrderInfoVO>>custom().setData(Collections.emptyList()).setTotal(totalHit).setSuccess().setMsg(emptyListShowMsg);
        }

        // 获取订单对应的地址表，构造订单id-地址名称的mapping
        orderIdList = masterSearchList.stream().filter(Objects::nonNull).map(OrderMasterSearchDTO::getId).collect(Collectors.toList());
        addrList = orderAddressRPCClient.findByOrderId(orderIdList);
        Map<Integer, OrderAddressDTO> orderAddressDTOMap = addrList.stream().collect(Collectors.toMap(OrderAddressDTO::getId, Function.identity()));
        // 补充订单详情id信息
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        Map<Integer, List<OrderDetailDO>> orderDetailMap = orderDetailDOList.stream().collect(Collectors.groupingBy(OrderDetailDO::getFmasterid));
        masterSearchList.forEach(masterSearch -> {
            // 主表
            resList.add(this.deliveryProxySearchToInfoTranslator(masterSearch, orderAddressDTOMap.get(masterSearch.getId()), orderDetailMap.get(masterSearch.getId())));
        });
        // 查询所有订单的分拣、配送图片
        List<OrderUploadFileDTO> allOrderUploadFileDTOList = orderUploadFileRpcClient.getOrderUploadFileList(orderIdList, New.list(FileBusinessTypeEnum.DELIVERY.getCode(), FileBusinessTypeEnum.SORTED.getCode()));
        Map<Integer, List<OrderUploadFileDTO>> orderUploadFileMap = allOrderUploadFileDTOList.stream().collect(Collectors.groupingBy(OrderUploadFileDTO::getOrderId));
        // 查询所有订单的分拣备注及配送备注
        DeliveryOperationLogRequestDTO requestDTO = new DeliveryOperationLogRequestDTO();
        // 通过分页查询接口查询日志总数，再查出所有日志获取最新的配送备注和分拣备注，若后续存在性能问题可改为直接在地址表存放最新备注
        requestDTO.setOrderIdList(orderIdList);
        Map<Integer, List<DeliveryOperationLogDTO>> deliveryOperationLogMap = New.map();
        PageableResponse<List<DeliveryOperationLogDTO>> pageableResponse = deliveryOperationLogClient.listOperationLogInOrderId(requestDTO);
        deliveryOperationLogMap = pageableResponse.getData().stream().collect(Collectors.groupingBy(DeliveryOperationLogDTO::getOrderId));

        // 取业务员电话，同一个供应商每个单位业务员可能都不一样，需要按单位+供应商来找到电话
        Map<Integer, Map<Integer, String>> orgIdSuppIdTelMap = New.map();
        try {
            Map<Integer, List<Integer>> orgIdSuppIdListMap = masterSearchList.stream().filter(item->SpeciesEnum.ONLINE.getValue() == item.getSpecies())
                    .collect(Collectors.groupingBy(OrderMasterSearchDTO::getFuserid, Collectors.mapping(OrderMasterSearchDTO::getFsuppid, Collectors.toList())));
            orgIdSuppIdListMap.forEach((orgId,suppIdList)->{
                Map<Integer, SuppShopInfoBO> suppContactInfoMap = suppClient.getSupplierContactInfoMap(suppIdList, orgId);
                suppContactInfoMap.forEach((suppId, suppInfo)->{
                    Map<Integer, String> suppIdTelMap = orgIdSuppIdTelMap.computeIfAbsent(orgId, k -> New.map());
                    suppIdTelMap.put(suppId, suppInfo.getMobile());
                });
            });
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getDeliveryProxyOrderList", e.getMessage(), e);
        }

        for(OrderInfoVO orderInfoVO : resList){
            // 补充分拣图片及配送图片
            List<OrderUploadFileDTO> orderUploadFileDTOList = Optional.ofNullable(orderUploadFileMap.get(orderInfoVO.getOrder().getId())).orElse(Collections.emptyList());
            orderInfoVO.setSortedFileUrlList(orderUploadFileDTOList.stream().filter(orderUploadFileDTO -> FileBusinessTypeEnum.SORTED.getCode().equals(orderUploadFileDTO.getFileBusinessType())).map(OrderUploadFileDTO::getUrl).collect(Collectors.toList()));
            orderInfoVO.setDeliveryFileUrlList(orderUploadFileDTOList.stream().filter(orderUploadFileDTO -> FileBusinessTypeEnum.DELIVERY.getCode().equals(orderUploadFileDTO.getFileBusinessType())).map(OrderUploadFileDTO::getUrl).collect(Collectors.toList()));
            // 补充备注
            List<DeliveryOperationLogDTO> deliveryOperationLogDTOList = Optional.ofNullable(deliveryOperationLogMap.get(orderInfoVO.getOrder().getId())).orElse(Collections.emptyList());
            if(CollectionUtils.isNotEmpty(deliveryOperationLogDTOList)){
                List<DeliveryOperationLogDTO> sortedLogList = deliveryOperationLogDTOList.stream().filter(deliveryOperationLogDTO -> (deliveryOperationLogDTO.getOperateType().equals(DeliveryOperationEnum.SORTED.getValue()) || deliveryOperationLogDTO.getOperateType().equals(DeliveryOperationEnum.SORT_APPEND.getValue()))).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(sortedLogList)){
                    orderInfoVO.setSortedNote(sortedLogList.get(sortedLogList.size() - 1).getNote());
                }
                List<DeliveryOperationLogDTO> deliveryLogList = deliveryOperationLogDTOList.stream().filter(deliveryOperationLogDTO -> deliveryOperationLogDTO.getOperateType().equals(DeliveryOperationEnum.DELIVERED.getValue()) || deliveryOperationLogDTO.getOperateType().equals(DeliveryOperationEnum.DELIVERY_APPEND.getValue())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(deliveryLogList)){
                    orderInfoVO.setDeliveryNote(deliveryLogList.get(deliveryLogList.size() - 1).getNote());
                }
            }
            String suppTel = orgIdSuppIdTelMap.getOrDefault(orderInfoVO.getOrder().getOrgId(), New.emptyMap()).get(orderInfoVO.getOrder().getSupplierId());
            orderInfoVO.getOrder().setSuppTelephone(suppTel);
        }
        return PageableResponse.<List<OrderInfoVO>>custom().setData(resList).setTotal(totalHit).setSuccess();
    }

    @Override
    public OrderInfoVO getDeliveryProxyOrderDetail(OrderListRequest request, RjSessionInfo rjSessionInfo, UserOrgModuleEnum userOrgModuleEnum) {
        // 代码迁移自controller层
        PageableResponse<List<OrderInfoVO>> orderDetailRes = getDeliveryProxyOrderList(request, null, rjSessionInfo, userOrgModuleEnum);
        if (CollectionUtils.isNotEmpty(orderDetailRes.getData())) {
            return orderDetailRes.getData().get(0);
        } else {
            // 根据入参定制化报错
            if (StringUtils.isNotBlank(request.getOrderNo())) {
                throw new BusinessInterceptException(ExecptionMessageEnum.ORDER_NOT_FOUND_CHECK_NUMBER);
            } else if (StringUtils.isNotBlank(request.getProductCode())) {
                throw new BusinessInterceptException(ExecptionMessageEnum.ORDER_NOT_FOUND_CHECK_ITEM_NUMBER);
            }
            throw new BusinessInterceptException(ExecptionMessageEnum.ORDER_NOT_FOUND_CHECK_NUMBER);
        }
    }

    /**
     * 最普通的搜索master对象转为订单列表info对象
     * @param masterSearch 搜索出来的对象
     * @return 不加任何修饰表里存的信息，采购人订单的列表对象体，仅保留代配送列表需要的信息
     */
    private OrderInfoVO deliveryProxySearchToInfoTranslator(OrderMasterSearchDTO masterSearch, OrderAddressDTO orderAddressDTO, List<OrderDetailDO> orderDetailDOList) {
        BusinessErrUtil.notNull(orderAddressDTO, ExecptionMessageEnum.ORDER_ADDRESS_NOT_FOUND);
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        OrderMasterVO orderMasterVO = new OrderMasterVO();
        orderMasterVO.setId(masterSearch.getId());
        orderMasterVO.setOrderNo(masterSearch.getForderno());
        orderMasterVO.setOrderDate(masterSearch.getForderdate());
        orderMasterVO.setTotalPrice(BigDecimal.valueOf(masterSearch.getForderamounttotal()));
        orderMasterVO.setStatus(masterSearch.getStatus());
        orderMasterVO.setOrgName(masterSearch.getFusername());
        orderMasterVO.setOrgId(masterSearch.getFuserid());
        orderMasterVO.setDepartmentName(masterSearch.getFbuydepartment());
        orderMasterVO.setBuyDepartmentId(masterSearch.getFbuydepartmentid());
        orderMasterVO.setBuyerName(masterSearch.getFbuyername());
        orderMasterVO.setBuyUserId(masterSearch.getFbuyerid());
        orderMasterVO.setSupplierId(masterSearch.getFsuppid());
        orderMasterVO.setSupplierName(masterSearch.getFsuppname());
        String addr = StringUtils.defaultIfBlank(orderAddressDTO.getProvince(), StringUtils.EMPTY)
                + StringUtils.defaultIfBlank(orderAddressDTO.getCity(), StringUtils.EMPTY)
                + StringUtils.defaultIfBlank(orderAddressDTO.getRegion(), StringUtils.EMPTY)
                + StringUtils.defaultIfBlank(orderAddressDTO.getAddress(), StringUtils.EMPTY);
        orderMasterVO.setDeliveryAddress(addr);
        orderMasterVO.setDeliveryProxySourceType(orderAddressDTO.getProxySourceType());
        orderMasterVO.setBuyerContactMan(masterSearch.getFbuyercontactman());
        orderMasterVO.setBuyerTelephone(masterSearch.getFbuyertelephone());
        orderMasterVO.setDeliveryDate(masterSearch.getFdeliverydate() == null ? null : DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, masterSearch.getFdeliverydate()));
        orderInfoVO.setOrder(orderMasterVO);
        List<OrderDetailVO> detailList = New.list();
        for (OrderDetailDO orderDetailDO : orderDetailDOList) {
            OrderDetailVO detailRes = new OrderDetailVO();
            detailRes.setId(orderDetailDO.getId());
            detailRes.setGoodsName(orderDetailDO.getFgoodname());
            detailRes.setGoodsCode(orderDetailDO.getFgoodcode());
            detailRes.setCategoryName(orderDetailDO.getFclassification());
            detailRes.setBrand(orderDetailDO.getFbrand());
            detailRes.setPrice(orderDetailDO.getFbidprice().doubleValue());
            detailRes.setQuantity(orderDetailDO.getFquantity().doubleValue());
            detailRes.setTotalPrice(orderDetailDO.getFbidamount().doubleValue());
            detailRes.setPicturePath(orderDetailDO.getFpicpath());
            detailRes.setUnit(orderDetailDO.getFunit());
            detailRes.setSpecification(orderDetailDO.getFspec());
            detailRes.setReturnStatus(orderDetailDO.getReturnStatus());
            detailList.add(detailRes);
        }
        orderInfoVO.setOrderDetails(detailList);
        orderInfoVO.setDeliveryStatus(masterSearch.getDeliveryStatus());
        orderInfoVO.setDeliveryUser(masterSearch.getDeliveryUser());
        orderInfoVO.setSortedUser(masterSearch.getSortedUser());
        orderInfoVO.setDeliveredTime(masterSearch.getDeliveredTime());
        orderInfoVO.setShowPrintDeliveryNote(!NOT_SHOW_DELIVERY_PRINT_STATUS_LIST.contains(masterSearch.getStatus()));
        return orderInfoVO;
    }

    /**
     * （异步）导出 代配送订单列表
     *
     * @param request
     * @param rjSessionInfo
     * @return
     */
    @Override
    @ServiceLog(description = "导出 代配送订单列表", serviceType = ServiceType.RPC_SERVICE)
    public void exportDeliveryProxyOrderList(OrderListRequest request, RjSessionInfo rjSessionInfo) {
        // 更新导出状态为导出中
        Date date = new Date();
        String fileName = "代配送订单-" + DateUtils.format("yyyyMMddHHmmss", date);
        LoginUserInfoBO loginInfo = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId(RjUserTypeEnum.OMS_USER).intValue());
        OrderExportResultDTO orderExportResultDTO = this.preExport(loginInfo, date, fileName);
        AsyncExecutor.listenableRunAsync(() -> {
            this.exportDeliveryProxyOrder(request, fileName, orderExportResultDTO, rjSessionInfo);
        }).addFailureCallback(throwable -> {
            LOGGER.error("异步导出代配送订单明细失败", throwable);
            Cat.logError(CAT_TYPE, "exportDeliveryProxyOrderList", "异步导出代配送订单明细失败", throwable);
        });
    }

    /**
     * 导出代配送订单，用于异步操作
     * @param request
     * @param fileName
     * @param orderExportResultDTO
     */
    private void exportDeliveryProxyOrder(OrderListRequest request, String fileName, OrderExportResultDTO orderExportResultDTO, RjSessionInfo rjSessionInfo) {
        // 实际导出，成功则处理为导出成功，否则为失败
        ExcelWriter writer = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        ByteArrayInputStream byteArrayInputStream = null;
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "exportDeliveryProxyOrderList");

        // default export strategy
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            ExportHeaderAndBody exportStrategy = ExportContext.getExportStrategyByOrg(-1);
            writer= EasyExcel.write(byteArrayOutputStream).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            WriteSheet sheet = EasyExcel
                    .writerSheet("订单明细")
                    .head(exportStrategy.exportDeliveryProxyHeaderOMS())
                    .registerWriteHandler(CellStyle.getCellStyleStrategy())
                    .build();

            request.setPageSize(0);
            PageableResponse<List<OrderInfoVO>> response = this.getDeliveryProxyOrderList(request, null, rjSessionInfo, UserOrgModuleEnum.AGENT_DELIVERY_ORDER);

            // 单个文件分页获取列表
            long totalHits = response.getTotal();
            Integer beginId = null;
            Integer pageSize = 2000;
            request.setPageSize(pageSize);
            long searchCount = totalHits == 0 ? 0 : ((totalHits/pageSize) + 1);
            List<OrderInfoVO> resList = null;
            OrderInfoVO orderInfoVO;
            for (long i = 0; i < searchCount; i++) {
                response = this.getDeliveryProxyOrderList(request, beginId, rjSessionInfo, UserOrgModuleEnum.AGENT_DELIVERY_ORDER);
                resList = response.getData();
                if (CollectionUtils.isNotEmpty(resList)) {
                    orderInfoVO = resList.get(resList.size() - 1);
                    if (orderInfoVO != null) {
                        beginId = orderInfoVO.getOrder().getId();
                    }
                    writer.write(exportStrategy.exportDeliveryProxyDataOMS(resList), sheet);
                }
            }
            resList.clear();
            writer.finish();
            byteArrayOutputStream.flush();
            byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            FileUploadClient fileUploadClient = UploadFileClient.getFileUploadClientById(UploadFileClient.ORDER_LIST_ID);
            FileUploadResp fileUploadResp = fileUploadClient.uploadFile(fileName + ".xlsx", byteArrayInputStream);
            Preconditions.isTrue(fileUploadResp.isSuccess(), fileUploadResp.getMsg());
            String absolutePath = fileUploadResp.getAbsolutePath();

            OrderExportDTO updateDTO = new OrderExportDTO();
            updateDTO.setId(orderExportResultDTO.getId());
            updateDTO.setFileUrl(absolutePath);
            updateDTO.setStatus(OrderExportStatusEnum.EXPORT_SUCCESS.getValue());
            orderExportClient.updateById(updateDTO);
            transaction.setSuccess();
        } catch (IOException e) {
            OrderExportDTO updateDTO = new OrderExportDTO();
            updateDTO.setId(orderExportResultDTO.getId());
            updateDTO.setStatus(OrderExportStatusEnum.EXPORT_FAIL.getValue());
            String message = e.getMessage();
            if (StringUtils.isNotBlank(message) && message.length() > 2000) {
                message = message.substring(0, 2000);
            }
            updateDTO.setFailReason(message);
            orderExportClient.updateById(updateDTO);
            LOGGER.error("代配送订单导出异常！{}", e);
            transaction.addData(CatUtils.buildStackInfo("订单导出异常！", e));
        } finally {
            if (writer != null) {
                writer.finish();
            }
            try {
                if (byteArrayInputStream != null){
                    byteArrayInputStream.close();
                }
                if (byteArrayOutputStream != null ){
                    byteArrayOutputStream.close();
                }
            } catch (IOException e) {
                LOGGER.error("IO异常！{}", e);
                transaction.addData(CatUtils.buildStackInfo("IO异常！", e));
                transaction.setStatus(e);
            }
            transaction.complete();
        }
    }

    /**
     * 导出前，增加导出中的记录
     * @param loginInfo
     * @param date
     * @param fileName
     * @return
     */
    private OrderExportResultDTO preExport(LoginUserInfoBO loginInfo, Date date, String fileName) {
        OrderExportDTO orderExportDTO = new OrderExportDTO();
        orderExportDTO.setFileName(fileName);
        orderExportDTO.setExportDate(date);
        orderExportDTO.setOrgId(-1);
        orderExportDTO.setOrgCode("OMS");
        orderExportDTO.setOrgName("OMS");
        orderExportDTO.setUserId(loginInfo.getUserId());
        orderExportDTO.setUserName(loginInfo.getUserName());
        orderExportDTO.setStatus(OrderExportStatusEnum.EXPORTING.getValue());
        orderExportDTO.setFileType(ExportFileTypeEnum.OMS_DELIVERY_PROXY_ORDER_LIST.getValue());
        orderExportDTO.setFileUrl("");
        return orderExportClient.saveOrderExport(orderExportDTO);
    }

    /**
     * 导出的 代配送订单列表
     *
     * @param rjSessionInfo
     * @param orderExcelInfoQueryDTO
     * @return
     */
    @Override
    public RemoteResponse<BasePageResultDTO<OrderExcelInfoResponseDTO>> exportedDeliveryProxyList(RjSessionInfo rjSessionInfo, OrderExcelInfoQueryDTO orderExcelInfoQueryDTO) {
        // 查询
        OrderExportQueryDTO query = new OrderExportQueryDTO();
        query.setPageNo(orderExcelInfoQueryDTO.getPageNo());
        query.setPageSize(orderExcelInfoQueryDTO.getPageSize());
        query.setExportDateStart(orderExcelInfoQueryDTO.getStartDate());
        query.setExportDateEnd(orderExcelInfoQueryDTO.getEndDate());
        query.setStatus(orderExcelInfoQueryDTO.getStatus());
        query.setUserId(rjSessionInfo.getUserId(RjUserTypeEnum.OMS_USER).intValue());
        query.setOrgId(-1);
        query.setFileTypeList(New.list(ExportFileTypeEnum.OMS_DELIVERY_PROXY_ORDER_LIST.getValue()));
        BasePageResultDTO<OrderExportDTO> orderExportRes = orderExportClient.findOrderExportList(query);
        BasePageResultDTO<OrderExcelInfoResponseDTO> basePageResultDTO = orderExportService.getBasePageResultDTO(orderExportRes);
        return RemoteResponse.<BasePageResultDTO<OrderExcelInfoResponseDTO>>custom().setData(basePageResultDTO).setSuccess();
    }

    /**
     * 检验是否有查看某个模块的权限
     * @param guid 登录进来的session的guid
     * @param accessCode 某个模块的权限
     */
    @Override
    public void checkUserHasAccessOMS(String guid, String accessCode) {
        List<OmsAccessDTO> accessInfoList = userClient.findAccessByGuid(guid);
        List<String> accessCodeList = accessInfoList.stream().map(OmsAccessDTO::getCode).collect(Collectors.toList());
        BusinessErrUtil.isTrue(accessCodeList.contains(accessCode), ExecptionMessageEnum.NO_PERMISSION_VIEW_MODULE);
    }

    /**
     * 检验是否有查看某个模块的权限
     * @param guid 登录进来的session的guid
     * @param accessCodeList 某个模块的权限
     */
    @Override
    public void checkUserHasAccessOMS(String guid, List<String> accessCodeList) {
        List<OmsAccessDTO> accessInfoList = userClient.findAccessByGuid(guid);
        List<String> accessList = accessInfoList.stream().map(OmsAccessDTO::getCode).collect(Collectors.toList());
        for(String accessCode : accessCodeList){
            if(accessList.contains(accessCode)){
                return;
            }
        }
        throw new BusinessInterceptException(ExecptionMessageEnum.NO_PERMISSION_VIEW_MODULE);
    }

    /**
     * 删除已导出的代配送列表项
     *
     * @param exportedItemId
     * @return
     */
    @Override
    public Boolean deleteExportedDeliveryProxyItem(Integer exportedItemId) {
        // 先判断是否为代配送的条目
        OrderExportDTO exportedItem = orderExportClient.findById(exportedItemId);
        BusinessErrUtil.isTrue(exportedItem != null
                && ExportFileTypeEnum.OMS_DELIVERY_PROXY_ORDER_LIST.getValue().equals(exportedItem.getFileType()),
                ExecptionMessageEnum.NOT_DROPSHIPPING_EXPORT_ITEM);

        // 真正删除
        orderExportClient.deleteOrderExportInfoById(exportedItemId);
        return true;
    }

    @Override
    public Boolean updateDelivery(RjSessionInfo rjSessionInfo, DeliveryUpdateRequest request) {
        List<OrderAddressDTO> orderAddressDTOList = orderAddressRPCClient.findByOrderId(New.list(request.getOrderId()));
        BusinessErrUtil.notEmpty(orderAddressDTOList, ExecptionMessageEnum.DELIVERY_INFO_NOT_FOUND);
        OrderAddressDTO orderAddressDTO = orderAddressDTOList.get(0);
        DeliveryOperationEnum deliveryOperationEnum = DeliveryOperationEnum.getByValue(request.getOperationType());
        BusinessErrUtil.notNull(deliveryOperationEnum, ExecptionMessageEnum.UNRECOGNIZED_OPERATION_TYPE);
        OrderAddressDTO addressUpdateDTO = new OrderAddressDTO();
        addressUpdateDTO.setId(orderAddressDTO.getId());
        addressUpdateDTO.setOrderNo(orderAddressDTO.getOrderNo());
        LoginUserInfoBO userDTO = userClient.getOMSLoginUserInfo(rjSessionInfo.getUserId(RjUserTypeEnum.OMS_USER).intValue());
        FileBusinessTypeEnum fileBusinessTypeEnum = null;
        switch (deliveryOperationEnum){
            case SORTED:
                checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list("DELIVERY_ORDER_MANAGE_SORT","DELIVERY_ORDER_BROWSE_SORT"));
                addressUpdateDTO.setSortedUser(userDTO.getUserName());
                BusinessErrUtil.isTrue(DeliveryStatusEnum.SORT.getValue().equals(orderAddressDTO.getDeliveryStatus())
                        || DeliveryStatusEnum.INIT.getValue().equals(orderAddressDTO.getDeliveryStatus())
                        || DeliveryStatusEnum.SUPP_APPLY_CANCEL.getValue().equals(orderAddressDTO.getDeliveryStatus())
                        || DeliveryStatusEnum.BUYER_REJECT_CANCEL.getValue().equals(orderAddressDTO.getDeliveryStatus())
                        , ExecptionMessageEnum.SORTING_NOT_ALLOWED_CURRENT_STATUS);
                fileBusinessTypeEnum = FileBusinessTypeEnum.SORTED;
                addressUpdateDTO.setDeliveryStatus(DeliveryStatusEnum.DELIVER.getValue());
                orderAddressRPCClient.updateAddress(addressUpdateDTO);
                break;
            case DELIVERED:
                checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list("DELIVERY_ORDER_BROWSE_DELIVER","DELIVERY_ORDER_MANAGE_DELIVER"));
                addressUpdateDTO.setDeliveryUser(userDTO.getUserName());
                BusinessErrUtil.isTrue(DeliveryStatusEnum.DELIVER.getValue().equals(orderAddressDTO.getDeliveryStatus()), ExecptionMessageEnum.DELIVERY_NOT_ALLOWED_CURRENT_STATUS);
                fileBusinessTypeEnum = FileBusinessTypeEnum.DELIVERY;
                addressUpdateDTO.setDeliveryStatus(DeliveryStatusEnum.DELIVERED.getValue());
                orderAddressRPCClient.updateAddress(addressUpdateDTO);
                break;
            case SORT_APPEND:
                checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list("DELIVERY_ORDER_MANAGE_SORT","DELIVERY_ORDER_BROWSE_SORT"));
                BusinessErrUtil.isTrue(
                        DeliveryStatusEnum.DELIVER.getValue().equals(orderAddressDTO.getDeliveryStatus())
                        || DeliveryStatusEnum.DELIVERED.getValue().equals(orderAddressDTO.getDeliveryStatus()),
                        ExecptionMessageEnum.ADD_SORTING_IMAGES_NOT_ALLOWED);
                fileBusinessTypeEnum = FileBusinessTypeEnum.SORTED;
                break;
            case DELIVERY_APPEND:
                checkUserHasAccessOMS(rjSessionInfo.getGuid(), New.list("DELIVERY_ORDER_BROWSE_DELIVER","DELIVERY_ORDER_MANAGE_DELIVER"));
                BusinessErrUtil.isTrue(DeliveryStatusEnum.DELIVERED.getValue().equals(orderAddressDTO.getDeliveryStatus()), ExecptionMessageEnum.ADD_DELIVERY_IMAGES_NOT_ALLOWED);
                fileBusinessTypeEnum = FileBusinessTypeEnum.DELIVERY;
                break;
            default:
                throw new IllegalStateException( "无法识别的操作类型");
        }
        // 更新附件信息
        if(CollectionUtils.isNotEmpty(request.getFileUrlList())) {
            List<OrderUploadFileDTO> orderUploadFileDTOList = New.list();
            for (String fileUrl : request.getFileUrlList()) {
                OrderUploadFileDTO orderUploadFileDTO = new OrderUploadFileDTO();
                orderUploadFileDTO.setOrderId(orderAddressDTO.getId());
                orderUploadFileDTO.setOrderNo(orderAddressDTO.getOrderNo());
                orderUploadFileDTO.setFileName("代配送图片-" + orderAddressDTO.getId() + "-" + System.currentTimeMillis());
                orderUploadFileDTO.setUrl(fileUrl);
                orderUploadFileDTO.setOrderNo(orderAddressDTO.getOrderNo());
                orderUploadFileDTO.setFileBusinessType(fileBusinessTypeEnum.getCode());
                orderUploadFileDTOList.add(orderUploadFileDTO);
            }
            orderUploadFileRpcClient.overWriteList(orderUploadFileDTOList);
        }
        // 插入操作日志
        DeliveryOperationLogDTO deliveryOperationLogDTO = new DeliveryOperationLogDTO();
        deliveryOperationLogDTO.setOrderId(orderAddressDTO.getId());
        deliveryOperationLogDTO.setNote(request.getNote());
        deliveryOperationLogDTO.setOperateDate(new Date());
        deliveryOperationLogDTO.setOperatorType(OperatorTypeEnum.OMS.getValue());
        deliveryOperationLogDTO.setOperatorName(userDTO.getUserName());
        deliveryOperationLogDTO.setOperatorGuid(userDTO.getUserGuid());
        deliveryOperationLogDTO.setOperateType(deliveryOperationEnum.getValue());
        deliveryOperationLogDTO.setOperation(deliveryOperationEnum.getDescription());
        deliveryOperationLogClient.insertDeliveryOperationLog(deliveryOperationLogDTO);
        return true;
    }

    @Override
    public PageableResponse<List<DeliveryOperationLogDTO>> listDeliveryOperationLog(RjSessionInfo rjSessionInfo, DeliveryOperationLogRequestDTO request) {
        List<OrderAddressDTO> orderAddressDTOList = orderAddressRPCClient.findByOrderId(New.list(request.getOrderIdList()));
        BusinessErrUtil.notEmpty(orderAddressDTOList, ExecptionMessageEnum.DELIVERY_INFO_NOT_FOUND);
        orderAddressDTOList = orderAddressDTOList.stream().sorted(Comparator.comparingInt(OrderAddressDTO::getId)).collect(Collectors.toList());
        OrderAddressDTO orderAddressDTO = orderAddressDTOList.get(0);
        // 权限校验
        if(DeliveryStatusEnum.DELIVER.getValue().equals(orderAddressDTO.getDeliveryStatus())){
            checkUserHasAccessOMS(rjSessionInfo.getGuid(), "DELIVERY_ORDER_BROWSE_SORT");
        }else {
            checkUserHasAccessOMS(rjSessionInfo.getGuid(), "DELIVERY_ORDER_BROWSE_DELIVER");
        }
        return  deliveryOperationLogClient.listOperationLogInOrderId(request);
    }


    @Override
    public PageableResponse<List<OrderInfoVO>> getReceiptOrderList(OrderListRequest request) {
        int pageNo = Objects.nonNull(request.getPageNo()) ? request.getPageNo() : 1;
        int pageSize = Objects.nonNull(request.getPageSize()) ? request.getPageSize() : 20;
        OrderSearchParamDTO orderSearchParamDTO = new OrderSearchParamDTO();
        orderSearchParamDTO.setStatusList(request.getStatusList());
        orderSearchParamDTO.setOrderNo(request.getOrderNo());
        orderSearchParamDTO.setDepartmentName(request.getDepartmentName());
        orderSearchParamDTO.setOrgName(request.getOrgName());
        orderSearchParamDTO.setOrgNameFullMatch(true);
        orderSearchParamDTO.setBuyerName(request.getFbuyername());
        orderSearchParamDTO.setSuppName(request.getSuppname());
        orderSearchParamDTO.setGoodsName(request.getProductName());
        orderSearchParamDTO.setGoodsCode(request.getProductCode());
        orderSearchParamDTO.setBrandName(request.getBrand());
        orderSearchParamDTO.setSpecies(ProcessSpeciesEnum.getByValue(request.getProcessSpecies()));
        FieldRangeDTO orderDateRange = new FieldRangeDTO("forderdate", request.getStartDate()
                , request.getEndDate(), true, true);
        List<FieldRangeDTO> rangeList = New.list();
        rangeList.add(orderDateRange);

        orderSearchParamDTO.setFieldRangeList(rangeList);
        orderSearchParamDTO.setOrderDateSort(SortOrderEnum.DESC);

        Integer startHit = (pageNo - 1) * pageSize;
        orderSearchParamDTO.setStartHit(startHit);
        orderSearchParamDTO.setPageSize(pageSize);

        // 去除已拆单的母单
        orderSearchParamDTO.setExcludeStatusList(New.list(OrderStatusEnum.ORDER_SPLIT_UP.getValue()));
        SearchPageResultDTO<OrderMasterSearchDTO> pageResult = orderSearchBoostService.commonSearch(orderSearchParamDTO);
        List<OrderMasterSearchDTO> masterSearchList = pageResult.getRecordList();

        if (CollectionUtils.isEmpty(masterSearchList)) {
            return PageableResponse.<List<OrderInfoVO>>custom().setData(New.emptyList()).setPageSize(request.getPageSize()).setPageNo(request.getPageNo()).setTotal(0).setSuccess();
        }

        // Convert to OrderInfoVO list
        List<OrderInfoVO> resultList = masterSearchList.stream()
                .map(this::convertToOrderInfoVO)
                .collect(Collectors.toList());

        long totalHits = Math.min(10000, pageResult.getTotalHits());

        return PageableResponse.<List<OrderInfoVO>>custom()
                .setData(resultList)
                .setPageNo(pageNo)
                .setPageSize(pageSize)
                .setTotal(totalHits)
                .setSuccess();
    }

    private OrderInfoVO convertToOrderInfoVO(OrderMasterSearchDTO masterSearch) {
        OrderMasterVO masterVO = createOrderMasterVo(masterSearch);
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        orderInfoVO.setOrder(masterVO);

        // Convert order details
        if (CollectionUtils.isNotEmpty(masterSearch.getOrderDetail())) {
            List<OrderDetailVO> detailList = masterSearch.getOrderDetail().stream()
                    .map(this::createOrderDetailVo)
                    .collect(Collectors.toList());
            orderInfoVO.setOrderDetails(detailList);
        }

        return orderInfoVO;
    }

    private OrderMasterVO createOrderMasterVo(OrderMasterSearchDTO masterSearch) {
        OrderMasterVO masterVO = new OrderMasterVO();
        masterVO.setId(masterSearch.getId());
        masterVO.setOrderNo(masterSearch.getForderno());
        masterVO.setOrderDate(masterSearch.getForderdate());
        masterVO.setTotalPrice(BigDecimal.valueOf(masterSearch.getForderamounttotal()));
        masterVO.setStatus(masterSearch.getStatus());
        masterVO.setOrgName(masterSearch.getFusername());
        masterVO.setOrgId(masterSearch.getFuserid());
        masterVO.setDepartmentName(masterSearch.getFbuydepartment());
        masterVO.setBuyDepartmentId(masterSearch.getFbuydepartmentid());
        masterVO.setBuyerName(masterSearch.getFbuyername());
        masterVO.setBuyUserId(masterSearch.getFbuyerid());
        masterVO.setSupplierId(masterSearch.getFsuppid());
        masterVO.setSupplierName(masterSearch.getFsuppname());
        masterVO.setSpecies(masterSearch.getSpecies());
        masterVO.setStatementStatus(masterSearch.getStatementStatus());
        return masterVO;
    }

    private OrderDetailVO createOrderDetailVo(OrderDetailSearchDTO dto) {
        OrderDetailVO orderDetailVO = new OrderDetailVO();
        orderDetailVO.setBrand(StringUtils.defaultString(dto.getFbrand(), StringUtils.EMPTY));
        orderDetailVO.setGoodsName(StringUtils.defaultString(dto.getFgoodname(), StringUtils.EMPTY));
        orderDetailVO.setGoodsCode(StringUtils.defaultString(dto.getFgoodcode(), StringUtils.EMPTY));
        orderDetailVO.setProductSn(dto.getProductId());
        orderDetailVO.setQuantity(Double.valueOf(dto.getFquantity()));
        orderDetailVO.setPrice(dto.getOriginalPrice());
        orderDetailVO.setTotalPrice(dto.getOriginalAmount());
        orderDetailVO.setCategoryId(Objects.nonNull(dto.getCategoryId()) ? dto.getCategoryId() : -1);
        orderDetailVO.setCategoryName(StringUtils.defaultString(dto.getCategoryName(), StringUtils.EMPTY));
        return orderDetailVO;
    }
}
