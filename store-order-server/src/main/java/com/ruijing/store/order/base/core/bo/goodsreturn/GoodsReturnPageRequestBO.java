package com.ruijing.store.order.base.core.bo.goodsreturn;

import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;

import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 采购人中心分页查询退货单列表入参bo
 * @author: zhong<PERSON><PERSON>i
 * @create: 2021/1/4 14:28
 **/
public class GoodsReturnPageRequestBO {

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 单位id数组
     */
    private List<Integer> orgIds;

    /**
     * 退货单号
     */
    private String returnNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单号模糊搜索
     */
    private String orderNoLike;

    /**
     * 采购部门id
     */
    private List<Integer> departmentIdList;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 退货状态
     * {@link GoodsReturnStatusEnum}
     */
    private List<Integer> returnStatusList;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * <AUTHOR>
     * @Param
     * @return
     * @Description //采购人
     **/
    private String buyerName;

    /**
     * 供应商名称
     */
    private String supplierName;

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public List<Integer> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Integer> orgIds) {
        this.orgIds = orgIds;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderNoLike() {
        return orderNoLike;
    }

    public void setOrderNoLike(String orderNoLike) {
        this.orderNoLike = orderNoLike;
    }

    public List<Integer> getDepartmentIdList() {
        return departmentIdList;
    }

    public void setDepartmentIdList(List<Integer> departmentIdList) {
        this.departmentIdList = departmentIdList;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public List<Integer> getReturnStatusList() {
        return returnStatusList;
    }

    public void setReturnStatusList(List<Integer> returnStatusList) {
        this.returnStatusList = returnStatusList;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GoodsReturnPageRequestBO.class.getSimpleName() + "[", "]")
                .add("orgId=" + orgId)
                .add("orgIds=" + orgIds)
                .add("returnNo='" + returnNo + "'")
                .add("orderNo='" + orderNo + "'")
                .add("orderNoLike='" + orderNoLike + "'")
                .add("departmentIdList=" + departmentIdList)
                .add("supplierId=" + supplierId)
                .add("returnStatusList=" + returnStatusList)
                .add("startDate=" + startDate)
                .add("endDate=" + endDate)
                .add("buyerName='" + buyerName + "'")
                .add("supplierName='" + supplierName + "'")
                .toString();
    }
}
