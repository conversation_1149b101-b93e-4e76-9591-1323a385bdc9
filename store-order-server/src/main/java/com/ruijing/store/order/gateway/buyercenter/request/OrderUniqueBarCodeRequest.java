package com.ruijing.store.order.gateway.buyercenter.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.other.dto.UniqueBarCodeDTO;

import java.io.Serializable;
import java.util.List;

@RpcModel("一物一码model")
public class OrderUniqueBarCodeRequest implements Serializable {

    private static final long serialVersionUID = 5162666120673233260L;

    @RpcModelProperty("码批次信息，orderNo，orderDetailId，productName，spec，brand，total必填，修改时barCode必填")
    private List<UniqueBarCodeDTO> orderUniqueBarCodeList;

    public List<UniqueBarCodeDTO> getOrderUniqueBarCodeList() {
        return orderUniqueBarCodeList;
    }

    public OrderUniqueBarCodeRequest setOrderUniqueBarCodeList(List<UniqueBarCodeDTO> orderUniqueBarCodeList) {
        this.orderUniqueBarCodeList = orderUniqueBarCodeList;
        return this;
    }
}
