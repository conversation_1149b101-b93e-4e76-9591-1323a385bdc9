package com.ruijing.store.order.other.service;

import com.ruijing.store.order.base.core.model.OrderMasterDO;

import java.util.Date;

/**
 * @author: liwenyu
 * @createTime: 2023-10-27 17:02
 * @description: 汇卡对接
 **/
public interface ReimbursementService {

    /**
     * 配置变更
     * @param orgId 机构id
     * @param suppId 供应商id
     */
    void configChange(Integer orgId, Integer suppId);

    /**
     * 重新拉取配置到缓存
     */
    void reloadAllConfig();

    /**
     * 保存汇卡记录到轮询列表
     * @param orderMasterDO 订单
     */
    void saveRecord(OrderMasterDO orderMasterDO);

    /**
     * 失效汇卡数据
     * @param orderMasterDO 订单
     */
    void invalidRecord(OrderMasterDO orderMasterDO);

    /**
     * 恢复失效的汇卡数据
     * @param orderId 订单id
     */
    void validRecord(Integer orderId);

    /**
     * 删除汇卡数据
     * @param orderMasterDO 订单数据
     */
    void deleteRecord(OrderMasterDO orderMasterDO);
}
