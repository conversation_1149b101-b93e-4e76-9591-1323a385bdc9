package com.ruijing.store.order.gateway.fundcard.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderUnFreezeRequestDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.RefFundcardOrderTranslator;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardService;
import com.ruijing.store.order.gateway.fundcard.service.OrderFundCardUnfreezeService;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;
import com.ruijing.store.order.rpc.client.UserClient;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: Liwenyu
 * @create: 2024-12-20 16:50
 * @description:
 */
@Service
public class OrderFundCardUnfreezeServiceImpl implements OrderFundCardUnfreezeService {

    private final String CHANGE_TO_SELF_STATEMENT_LOCK_KEY = "CHANGE_TO_SELF_STATEMENT_LOCK_KEY_";

    @Resource
    private CacheClient cacheClient;

    @Resource
    private OrderFundCardService orderFundCardService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private BuyerGoodsReturnService buyerGoodsReturnService;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private ApplicationBaseService applicationBaseService;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private OrderStatementService orderStatementService;

    @Resource
    private UserClient userClient;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "解冻并修改订单为自结算", serviceType = ServiceType.COMMON_SERVICE)
    public void unFreezeAndChangeOrderToSelfStatement(Integer orgId, Integer operateUserId, Integer orderId) {
        final String lockKey = CHANGE_TO_SELF_STATEMENT_LOCK_KEY + orderId;
        boolean getLock = false;
        try{
            // 1.获取当前订单验收的执行锁
            getLock = cacheClient.tryLock(lockKey, 10);
            BusinessErrUtil.isTrue(getLock, "正在处理中，请稍后");
            OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
            // 暨南大学才有的操作
            BusinessErrUtil.isTrue(OrgEnum.JI_NAN_DA_XUE.getValue() == orderMasterDO.getFuserid() && orderMasterDO.getFuserid().equals(orgId), ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
            BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus()), "当前订单状态不在待结算状态，不可进行此操作");
            Integer fundStatus = orderMasterDO.getFundStatus();
            boolean alreadySelfStatement = OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(fundStatus) || OrderFundStatusEnum.DISTRIBUTE_STATEMENT_NO_TAG.getValue().equals(fundStatus);
            if(alreadySelfStatement){
                return;
            }

            // 查询当前绑定经费卡
            String cardId = null;
            List<RefFundcardOrderDO> refFundCardOrderDOS = refFundcardOrderMapper.findByOrderId(String.valueOf(orderId));
            if (CollectionUtils.isNotEmpty(refFundCardOrderDOS)) {
                cardId = RefFundcardOrderTranslator.getLastLevelCardId(refFundCardOrderDOS.get(0));
            }

            if(OrderFundStatusEnum.Freezed.getValue().equals(fundStatus)
                    || OrderFundStatusEnum.ThrawFailed.getValue().equals(fundStatus)){
                // 冻结中/解冻失败，透传改为自结算操作到解冻，待解冻成功后再次调用此方法改为自结算
                OrderUnFreezeRequestDTO orderUnFreezeRequestDTO = new OrderUnFreezeRequestDTO();
                orderUnFreezeRequestDTO.setOrderId(orderId);
                orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.CHANGE_TO_SELF_STATEMENT);
                orderUnFreezeRequestDTO.setFreezeAmount(orderMasterDO.getForderamounttotal());
                orderUnFreezeRequestDTO.setOperateUserId(operateUserId);
                orderFundCardService.orderFundCardUnFreeze(orderMasterDO, orderUnFreezeRequestDTO);
                if (OrderFundStatusEnum.Freezed.getValue().equals(fundStatus) && StringUtils.isNotBlank(cardId)) {
                    // 采购金额管控
                    applicationBaseService.updateApplyManageProductUsage(orderMasterDO, ApplyManageOperationEnum.UPDATE_STATEMENT_TYPE.getValue(), cardId, null);
                }
            }else if(OrderFundStatusEnum.ThrawSuccessed.getValue().equals(fundStatus)){
                this.changeOrderToSelfStatement(operateUserId, orderMasterDO);
            }else {
                OrderFundStatusEnum orderFundStatusEnum = OrderFundStatusEnum.get(fundStatus);
                if(orderFundStatusEnum != null){
                    throw new BusinessInterceptException("当前经费状态为" + orderFundStatusEnum.getName() +"，不可进行此操作");
                }
            }

        } finally {
            if (getLock) {
                // 如果获取到执行锁成功，则释放
                cacheClient.unlock(lockKey);
            }
        }
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "修改订单为自结算", serviceType = ServiceType.COMMON_SERVICE)
    public void changeOrderToSelfStatement(Integer operateUserId, Integer orderId){
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        this.changeOrderToSelfStatement(operateUserId, orderMasterDO);
    }

    /**
     * 订单转换为自结算
     * @param operateUserId 操作人
     * @param orderMasterDO 订单
     */
    private void changeOrderToSelfStatement(Integer operateUserId, OrderMasterDO orderMasterDO){
        // 暨南大学才有的操作
        BusinessErrUtil.isTrue(OrgEnum.JI_NAN_DA_XUE.getValue() == orderMasterDO.getFuserid(), ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        BusinessErrUtil.isTrue(OrderStatusEnum.WaitingForStatement_1.getValue().equals(orderMasterDO.getStatus()), "当前订单状态不在待结算状态，不可进行此操作");
        Integer fundStatus = orderMasterDO.getFundStatus();
        boolean alreadySelfStatement = OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue().equals(fundStatus) || OrderFundStatusEnum.DISTRIBUTE_STATEMENT_NO_TAG.getValue().equals(fundStatus);
        if(alreadySelfStatement){
            return;
        }
        Integer orderId = orderMasterDO.getId();
        if(OrderFundStatusEnum.ThrawSuccessed.getValue().equals(orderMasterDO.getFundStatus())){
            // 其他途径解冻完了，再回来点改为自结算的。不需要再进行解冻了，改为自结算状态
            UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
            updateOrderParamDTO.setOrderId(orderId);
            updateOrderParamDTO.setFundStatus(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue());
            orderMasterMapper.updateOrderById(updateOrderParamDTO);
            // 其他途径解冻完了，再回来点改为自结算的。不需要再进行解冻了
            List<Integer> orderIdList = New.list(orderId);
            // 发起结算,需要取消退货单，然后发起结算
            buyerGoodsReturnService.systemCancelGoodsReturn(orderIdList, GoodsReturnOperationTypeEnum.PURCHASE_CANCEL_GOODS_RETURN, "更换结算方式为自结算，订单自动完成");
            List<UserBaseInfoDTO> oprUserList = userClient.getUserByIdsAndOrgId(New.list(operateUserId), orderMasterDO.getFuserid());
            orderMasterDO.setFundStatus(OrderFundStatusEnum.DISTRIBUTE_STATEMENT.getValue());
            orderStatementService.createStatement(orderMasterDO, operateUserId, oprUserList.get(0).getName(), orderMasterDO.getInventoryStatus().intValue());
        }else {
            throw new BusinessInterceptException("当前经费状态为" + OrderFundStatusEnum.get(fundStatus) + "，不可进行此操作");
        }
    }
}
