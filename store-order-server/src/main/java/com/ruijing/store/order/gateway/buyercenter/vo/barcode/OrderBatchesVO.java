package com.ruijing.store.order.gateway.buyercenter.vo.barcode;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Liwenyu
 * @create: 2024-05-10 09:59
 * @description:
 */
public class OrderBatchesVO implements Serializable {

    private static final long serialVersionUID = -8289677314300412558L;

    private List<OrderDetailBatchesVO> orderDetailBathes;

    public List<OrderDetailBatchesVO> getOrderDetailBathes() {
        return orderDetailBathes;
    }

    public OrderBatchesVO setOrderDetailBathes(List<OrderDetailBatchesVO> orderDetailBathes) {
        this.orderDetailBathes = orderDetailBathes;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderBatchesVO.class.getSimpleName() + "[", "]")
                .add("orderDetailBathes=" + orderDetailBathes)
                .toString();
    }
}
