package com.ruijing.store.order.business.service.impl;

import com.reagent.commonbase.constant.org.OrgConst;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.research.statement.api.enums.StatementStatusEnum;
import com.reagent.research.statement.api.statement.dto.StatementResultDTO;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.env.Environment;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.order.saturn.api.contract.constant.ContractOperationLogConstant;
import com.ruijing.order.saturn.api.contract.enums.OrderContractStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderFundStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.SyncStatementStatusDTO;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.mapper.RefFundcardOrderMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.model.RefFundcardOrderDO;
import com.ruijing.store.order.business.service.OrderStatementService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.rpc.client.OrderContractClient;
import com.ruijing.store.order.rpc.client.OrderExtraClient;
import com.ruijing.store.order.rpc.client.ResearchStatementClient;
import com.ruijing.store.order.rpc.client.SysConfigClient;
import com.ruijing.store.order.statement.service.WaitingStatementService;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class OrderStatementServiceImpl implements OrderStatementService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private WaitingStatementService waitingStatementService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private ResearchStatementClient researchStatementClient;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Override
    @ServiceLog(description = "订单结算核心逻辑", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void orderStatementCore(OrderMasterDO orderSnapshot, Integer operatorId,
                                   String operatorName, boolean isUsedStatement, Integer inventoryStatus) {
        //清远人医 对方系统结算
        //经费状态是【不检查经费状态】 订单状态变完成
        if(OrgEnum.QING_YUAN_SHI_REN_MIN_YI_YUAN.getCode().equals(orderSnapshot.getFusercode()) && !OrderFundStatusEnum.UN_FREEZE.getValue().equals(orderSnapshot.getFundStatus())){
            return;
        }
        if (isUsedStatement) {
            // 如果使用结算系统
            // 自动发起结算 订单状态--结算中  结算状态--待开票
            boolean isAutoStatement = sysConfigClient.isAutoStatement(orderSnapshot.getFusercode());
            if (isAutoStatement) {
                this.createStatement(orderSnapshot, operatorId, operatorName, inventoryStatus);
            } else {
                // 推入到待结算，订单状态已经前置设置为待结算了，不用更新订单状态
                waitingStatementService.pushWaitingStatement(orderSnapshot.getFusercode(), New.list(orderSnapshot.getId()), inventoryStatus);
                logger.info("订单{}调用结算的RPC服务推送到待结算成功",orderSnapshot.getId());
            }
        } else {
            UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
            updated.setOrderId(orderSnapshot.getId());
            // 不用结算系统，那就直接结束
            updated.setStatus(OrderStatusEnum.Finish.value);
            updated.setFinishDate(new Date());
            orderMasterMapper.updateOrderById(updated);
            logger.info("订单{}不使用结算系统，更新订单状态为结束成功",orderSnapshot.getId());
        }
    }

    @Override
    @ServiceLog(description = "订单发起结算", serviceType = ServiceType.RPC_CLIENT, operationType = OperationType.WRITE)
    public void createStatement(OrderMasterDO orderSnapshot, Integer operatorId, String operatorName, Integer inventoryStatus) {
        UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
        updated.setOrderId(orderSnapshot.getId());
        // 先更新为结算中状态，以防状态还没更新但结算那边已经调用了统计的计数导致统计出问题
        updated.setStatus(OrderStatusEnum.Statementing_1.value);
        updated.setStatementStatus(StatementStatusEnum.WaitingToDrawBills.getStatus());
        orderMasterMapper.updateOrderById(updated);
        // 推到结算中
        StatementResultDTO statementDTO = researchStatementClient.createStatementSingle(operatorId, operatorName, orderSnapshot);
        logger.info("订单{}调用结算的RPC服务发起结算成功,返回参数:{}", orderSnapshot.getId(), JsonUtils.toJsonIgnoreNull(statementDTO));
    }

    /**
     * 用于同步订单结算状态，状态不一致才修改，防止刷新表更新时间
     * @param syncStatementStatusDTOS 批量更新结算状态DTO（订单id、结算状态）
     */
    @Override
    public Integer batchUpdateStatementStatus(List<SyncStatementStatusDTO> syncStatementStatusDTOS) {
        return orderMasterMapper.batchUpdateStatementStatus(syncStatementStatusDTOS);
    }

    @Override
    public boolean isUseStatement(Map<String, String> receiptConfigMap, OrderMasterDO orderMasterDO) {
        if(OrgEnum.CHNEG_DU_YI_XUE_YUAN.getValue() == orderMasterDO.getFuserid()){
            // 成都医学院，经费类型是教学经费(编码1，要求线上线下保持一致)，则不走结算流程
            List<RefFundcardOrderDO> refFundcardOrderDOList = refFundcardOrderMapper.findByOrderId(orderMasterDO.getId().toString());
            boolean isTeachingFund = refFundcardOrderDOList.stream().anyMatch(card->card.getFundType() == 1);
            if(isTeachingFund){
                return false;
            }
        }

        // 结算方式配置为不使用结算系统
        List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderMasterDO.getId()), OrderExtraEnum.STATEMENT_WAY_USE_STATEMENT.getValue());
        if(CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.FALSE_NUMBER_STR.equals(orderExtraDTOList.get(0).getExtraValue())){
            return false;
        }

        if (ProcessSpeciesEnum.NORMAL.getValue().byteValue() == orderMasterDO.getSpecies()) {
            //线上单是否使用结算系统
            String usedStatementConfig = receiptConfigMap.get(ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM);
            return ConfigConstant.ONLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(usedStatementConfig);
        } else {
            //线下单是否使用结算系统
            String usedStatementConfig = receiptConfigMap.get(ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM);
            return ConfigConstant.OFFLINE_ORDER_USE_STATEMENT_SYSTEM_VALUE.equals(usedStatementConfig);
        }
    }
}
