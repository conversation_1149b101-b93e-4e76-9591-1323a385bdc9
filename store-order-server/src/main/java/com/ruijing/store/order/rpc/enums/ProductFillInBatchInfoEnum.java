package com.ruijing.store.order.rpc.enums;

/**
 * Name: ProductFillInBatchInfoEnum
 * Description: 订单批次信息枚举
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2024/2/20
 */
public enum ProductFillInBatchInfoEnum {
    BATCHES("批次号", "1"),
    EXPIRATION("有效期", "2"),
    MANUFACTURER("生产厂家", "3"),
    PRODUCTION_DATE("生产日期", "4"),
    ;


     ProductFillInBatchInfoEnum(String name, String key){
        this.name = name;
        this.key = key;
    }

    private String name;


    private String key;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String  getKey() {
        return key;
    }

    public void setKey(String  key) {
        this.key = key;
    }
}
