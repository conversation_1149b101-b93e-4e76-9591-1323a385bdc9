package com.ruijing.store.order.gateway.buyercenter.request.orderlist;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/26 14:24
 * @Description
 **/
@RpcModel("上传图片请求体")
public class OrderAcceptPicRequest implements Serializable {

    private static final long serialVersionUID = 5935245888500886143L;

    /**
     * 图片路径集合
     */
    @RpcModelProperty("图片路径集合")
    private List<String> filePaths;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty("跳过日志记录")
    private Boolean skipLog;

    public Boolean getSkipLog() {
        return skipLog;
    }

    public OrderAcceptPicRequest setSkipLog(Boolean skipLog) {
        this.skipLog = skipLog;
        return this;
    }

    public List<String> getFilePaths() {
        return filePaths;
    }

    public void setFilePaths(List<String> filePaths) {
        this.filePaths = filePaths;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Override
    public String toString() {
        return "OrderAcceptPicRequest{" +
                "filePaths=" + filePaths +
                ", orderId=" + orderId +
                ", skipLog=" + skipLog +
                '}';
    }
}
