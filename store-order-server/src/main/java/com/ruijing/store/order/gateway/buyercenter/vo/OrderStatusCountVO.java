package com.ruijing.store.order.gateway.buyercenter.vo;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

/**
 * @author: z<PERSON><PERSON>
 * @date : 2020/12/25 下午7:55
 * @description: 工作台订单管理数量VO
 */
public class OrderStatusCountVO implements Serializable {

    private static final Long serialVersionUID = 1585079374840215811L;

    @RpcModelProperty("待发货数量")
    private Integer waiteDeliverCount;

    @RpcModelProperty("待收货数量")
    private Integer waiteReceiveCount;

    @RpcModelProperty("待入库数量")
    private Integer waiteInboundCount;

    @RpcModelProperty("待结算数量")
    private Integer waiteStatementCount;

    @RpcModelProperty("待确认数量")
    private Integer waitingForConfirmCount;

    @RpcModelProperty("结算中数量")
    private Integer statementingCount;

    public Integer getWaitingForConfirmCount() {
        return waitingForConfirmCount;
    }

    public void setWaitingForConfirmCount(Integer waitingForConfirmCount) {
        this.waitingForConfirmCount = waitingForConfirmCount;
    }

    public Integer getWaiteDeliverCount() {
        return waiteDeliverCount;
    }

    public void setWaiteDeliverCount(Integer waiteDeliverCount) {
        this.waiteDeliverCount = waiteDeliverCount;
    }

    public Integer getWaiteReceiveCount() {
        return waiteReceiveCount;
    }

    public void setWaiteReceiveCount(Integer waiteReceiveCount) {
        this.waiteReceiveCount = waiteReceiveCount;
    }

    public Integer getWaiteInboundCount() {
        return waiteInboundCount;
    }

    public void setWaiteInboundCount(Integer waiteInboundCount) {
        this.waiteInboundCount = waiteInboundCount;
    }

    public Integer getWaiteStatementCount() {
        return waiteStatementCount;
    }

    public void setWaiteStatementCount(Integer waiteStatementCount) {
        this.waiteStatementCount = waiteStatementCount;
    }

    public Integer getStatementingCount() {
        return statementingCount;
    }

    public void setStatementingCount(Integer statementingCount) {
        this.statementingCount = statementingCount;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderStatusCountVO.class.getSimpleName() + "[", "]")
                .add("waiteDeliverCount=" + waiteDeliverCount)
                .add("waiteReceiveCount=" + waiteReceiveCount)
                .add("waiteInboundCount=" + waiteInboundCount)
                .add("waiteStatementCount=" + waiteStatementCount)
                .add("waitingForConfirmCount=" + waitingForConfirmCount)
                .add("statementingCount=" + statementingCount)
                .toString();
    }
}
