package com.ruijing.store.order.service;

import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationRecordDTO;
import com.ruijing.store.electronicsign.api.dto.OperationListDTO;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderReceiptParamDTO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.ElectronicSignBO;

import java.util.List;

/**
 * @description: 基础业务组业务方法集成接口
 * @author: zhangzhifeng
 * @date: 2021-07-08 14:26
 **/
public interface BizBaseService {

    /**
     *
     * 获取电子签名数据
     * @param orderMasterDTOList 订单数据
     * @param operationListDTO 查询条件
     * @return List<ElectronicSignOperationRecordDTO>
     */
    List<ElectronicSignOperationRecordDTO> getElectronicSignData(List<OrderMasterDTO> orderMasterDTOList, OperationListDTO operationListDTO);
    
    /**
     *
     * 保存电子签名，打印时用
     * @param orderReceiptParamDTO 订单验收入参
     * @param orderMasterDO 订单主表
     */
    void saveAcceptElectronicSign(OrderReceiptParamDTO orderReceiptParamDTO, OrderMasterDO orderMasterDO);


    /**
     * 保存订单验收电子签名，打印时用
     * 
     * @param electronicSignBO 电子签名入参
     */
    void checkAndSaveElectronicSign(ElectronicSignBO electronicSignBO);
    
    /**
     * 校验电子签名密码
     * @param guid
     * @param orgCode
     */
    void checkESignPassWord(String passWord, String orgCode, String guid);

    /**
     * @param userGuid 用户id
     * @param userName  用户姓名
     * @param orgCode   单位code
     * @param operation 操作
     * @param businessId 业务id(单据Id)
     * @param interactionId 上层业务操作id（用作纪录单据关联的日志Id或者父单据Id等，这边不会使用，调用方自己决定存什么）
     * @param groupCode 部门id
     */
    void saveElectronicSign(String userGuid, String userName, String orgCode, ElectronicSignatureOperationEnum operation
            , String businessId ,String interactionId,String groupCode,String password);
}
