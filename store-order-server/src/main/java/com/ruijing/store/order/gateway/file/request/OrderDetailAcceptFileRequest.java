package com.ruijing.store.order.gateway.file.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.ordermaster.dto.AcceptAttachmentDTO;

import java.io.Serializable;
import java.util.List;

@RpcModel("追加 上传详情文件关联文件信息")
public class OrderDetailAcceptFileRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @RpcModelProperty("订单id")
    private Integer orderId;

    @RpcModelProperty(value = "增量-验收附件-关联订单详情 列表")
    private List<AcceptAttachmentDTO> detailAttachmentDTOList;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public List<AcceptAttachmentDTO> getDetailAttachmentDTOList() {
        return detailAttachmentDTOList;
    }

    public void setDetailAttachmentDTOList(List<AcceptAttachmentDTO> detailAttachmentDTOList) {
        this.detailAttachmentDTOList = detailAttachmentDTOList;
    }

    @Override
    public String toString() {
        return "OrderDetailAcceptFileRequest{" +
                "orderId=" + orderId +
                ", OrderDetailAcceptFileRequest=" + detailAttachmentDTOList +
                '}';
    }
}
