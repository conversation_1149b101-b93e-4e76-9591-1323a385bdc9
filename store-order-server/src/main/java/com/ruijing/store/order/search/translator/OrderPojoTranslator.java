package com.ruijing.store.order.search.translator;

import com.reagent.commonbase.enums.OrgEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.search.client.response.Record;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderLogSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.api.general.enums.OrderNestedEnum;
import com.ruijing.store.order.api.search.dto.SuppOrderPullDTO;
import com.ruijing.store.order.constant.OrderDateConstant;
import com.ruijing.store.order.other.service.impl.ReimbursementServiceImpl;
import com.ruijing.store.order.util.CommonValueUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;

/**
 * @program: store-order-service
 * @description: 订单对象的转换类
 * @author: zhuk
 * @create: 2019-06-17 13:52
 **/
public class OrderPojoTranslator {

    public static SuppOrderPullDTO createSuppOrderPull(OrderMasterSearchDTO orderMasterSearchDTO) {
        if (orderMasterSearchDTO == null) {
            return null;
        }
        SuppOrderPullDTO suppOrderPullDTO = new SuppOrderPullDTO();
        suppOrderPullDTO.setOrderNo(orderMasterSearchDTO.getForderno());
        suppOrderPullDTO.setSuppId(orderMasterSearchDTO.getFsuppid());
        suppOrderPullDTO.setOrderAmount(orderMasterSearchDTO.getForderamounttotal());
        suppOrderPullDTO.setConfirmTime(orderMasterSearchDTO.getFconfirmdate());
        suppOrderPullDTO.setOrderGenerateTime(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderMasterSearchDTO.getForderdate()));
        suppOrderPullDTO.setDeliveryTime(orderMasterSearchDTO.getFdeliverydate());
        suppOrderPullDTO.setStatus(orderMasterSearchDTO.getStatus());
        suppOrderPullDTO.setOrderUpdateTime(orderMasterSearchDTO.getUpdateTime());
        return suppOrderPullDTO;
    }

    /**
     * map 转 OrderLogSearchDTO
     *
     * @param orderLogMap
     * @return orderLogSearchDTO
     */
    public static OrderLogSearchDTO mapToOrderLogSearchDTO(Map<String, String> orderLogMap) {
        if (MapUtils.isEmpty(orderLogMap)) {
            return null;
        }
        OrderLogSearchDTO orderLogSearchDTO = new OrderLogSearchDTO();
        orderLogSearchDTO.setLogId(NumberUtils.toInt(String.valueOf(orderLogMap.get("log_id")).trim()));
        orderLogSearchDTO.setApproveStatus(NumberUtils.toInt(String.valueOf(orderLogMap.get("approve_status")).trim()));
        orderLogSearchDTO.setOperatorId(NumberUtils.toInt(String.valueOf(orderLogMap.get("operator_id")).trim()));
        orderLogSearchDTO.setCreationTime(DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderLogMap.get("creation_time")));
        return orderLogSearchDTO;
    }

    /**
     * map转为 FundCardSearchDTO
     *
     * @param fundCardMap
     * @return FundCardSearchDTO
     */
    public static FundCardSearchDTO mapToFundCardSearchDTO(Map<String, String> fundCardMap) {
        if (MapUtils.isEmpty(fundCardMap)) {
            return null;
        }
        FundCardSearchDTO fundCardSearchDTO = new FundCardSearchDTO();
        fundCardSearchDTO.setFundCardId(fundCardMap.get("card_id"));
        fundCardSearchDTO.setCardNo(fundCardMap.get("card_no"));
        fundCardSearchDTO.setCampusName(fundCardMap.get("campus_name"));
        return fundCardSearchDTO;
    }

    /**
     * map 转为 order extra dto
     * @param orderExtraMap
     * @return
     */
    public static OrderExtraDTO mapToOrderExtraSearchDTO(Map orderExtraMap) {
        if (MapUtils.isEmpty(orderExtraMap)) {
            return null;
        }
        OrderExtraDTO extraDTO = new OrderExtraDTO();
        extraDTO.setExtraKey(orderExtraMap.get("extra_key") == null ? null : NumberUtils.toInt(String.valueOf(orderExtraMap.get("extra_key")).trim()));
        extraDTO.setExtraValue(orderExtraMap.get("extra_value") == null ? null : String.valueOf(orderExtraMap.get("extra_value")).trim());
        return extraDTO;
    }

    /**
     * map转为 OrderDetailSearchDTO
     *
     * @param orderDetailMap
     * @return OrderDetailSearchDTO
     */
    public static OrderDetailSearchDTO mapToOrderDetailSearchDTO(Map orderDetailMap) {

        if (MapUtils.isEmpty(orderDetailMap)) {
            return null;
        }
        OrderDetailSearchDTO orderDetailSearchDTO = new OrderDetailSearchDTO();
        orderDetailSearchDTO.setFgoodcode(String.valueOf(orderDetailMap.get("fgoodcode")));
        orderDetailSearchDTO.setFbrandid(orderDetailMap.get("fbrandid") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("fbrandid")).trim()) : null);
        orderDetailSearchDTO.setFbidprice(orderDetailMap.get("fbidprice") != null ? NumberUtils.toDouble(String.valueOf(orderDetailMap.get("fbidprice")).trim()) : null);
        orderDetailSearchDTO.setFquantity(orderDetailMap.get("fquantity") != null ? (int) NumberUtils.toDouble(String.valueOf(orderDetailMap.get("fquantity")).trim()) : null);
        orderDetailSearchDTO.setReturnStatus(orderDetailMap.get("return_status") == null ? null : NumberUtils.toInt(String.valueOf(orderDetailMap.get("return_status"))));
        orderDetailSearchDTO.setOriginalAmount(orderDetailMap.get("original_amount") != null ? NumberUtils.toDouble(String.valueOf(orderDetailMap.get("original_amount")).trim()) : null);
        orderDetailSearchDTO.setFbrand(orderDetailMap.get("fbrand") == null ? null : String.valueOf(orderDetailMap.get("fbrand")));
        orderDetailSearchDTO.setFspec(orderDetailMap.get("fspec") != null ? String.valueOf(orderDetailMap.get("fspec")) : null);
        orderDetailSearchDTO.setFgoodname(orderDetailMap.get("fgoodname") != null ? String.valueOf(orderDetailMap.get("fgoodname")) : null);
        orderDetailSearchDTO.setDetailId(orderDetailMap.get("detail_id") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("detail_id")).trim()) : null);
        orderDetailSearchDTO.setOriginalPrice(orderDetailMap.get("original_price") != null ? NumberUtils.toDouble(String.valueOf(orderDetailMap.get("original_price")).trim()) : null);
        orderDetailSearchDTO.setFbidamount(orderDetailMap.get("fbidamount") != null ? NumberUtils.toDouble(String.valueOf(orderDetailMap.get("fbidamount")).trim()) : null);
        orderDetailSearchDTO.setProductId(orderDetailMap.get("product_id") != null ? NumberUtils.toLong(String.valueOf(orderDetailMap.get("product_id")).trim()) : null);
        orderDetailSearchDTO.setCategoryId(orderDetailMap.get("categoryID") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("categoryID")).trim()) : null);
        orderDetailSearchDTO.setCategoryName(orderDetailMap.get("fclassification") != null ? String.valueOf(orderDetailMap.get("fclassification")) : null);
        orderDetailSearchDTO.setCategoryDirectoryId(orderDetailMap.get("category_directory_id") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("category_directory_id")).trim()) : null);
        orderDetailSearchDTO.setCasNo(orderDetailMap.get("cas_no") != null ? String.valueOf(orderDetailMap.get("cas_no")) : null);
        orderDetailSearchDTO.setDangerousType(orderDetailMap.get("dangerous_type") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("dangerous_type")).trim()) : null);
        orderDetailSearchDTO.setRegulatoryType(orderDetailMap.get("regulatory_type") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("regulatory_type")).trim()) : null);
        orderDetailSearchDTO.setFunit(orderDetailMap.get("funit") != null ? String.valueOf(orderDetailMap.get("funit")) : null);
        orderDetailSearchDTO.setFcancelquantity(orderDetailMap.get("fcancelquantity") != null ? NumberUtils.toDouble(String.valueOf(orderDetailMap.get("fcancelquantity")).trim()) : null);

        orderDetailSearchDTO.setFirstCategoryId(orderDetailMap.get("first_category_id") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("first_category_id")).trim()) : null);
        orderDetailSearchDTO.setSecondCategoryId(orderDetailMap.get("second_category_id") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("second_category_id")).trim()) : null);
        orderDetailSearchDTO.setFirstCategoryName(orderDetailMap.get("first_category_name") != null ? String.valueOf(orderDetailMap.get("first_category_name")) : null);
        orderDetailSearchDTO.setSecondCategoryName(orderDetailMap.get("second_category_name") != null ? String.valueOf(orderDetailMap.get("second_category_name")) : null);
        orderDetailSearchDTO.setDangerousTypeName(orderDetailMap.get("dangerous_type_name") != null ? String.valueOf(orderDetailMap.get("dangerous_type_name")) : null);
        orderDetailSearchDTO.setRegulatoryTypeName(orderDetailMap.get("regulatory_type_name") != null ? String.valueOf(orderDetailMap.get("regulatory_type_name")) : null);
        orderDetailSearchDTO.setFeeTypeTag(orderDetailMap.get("fee_type_tag") != null ? String.valueOf(orderDetailMap.get("fee_type_tag")) : null);
        orderDetailSearchDTO.setCategoryTag(orderDetailMap.get("category_tag") != null ? String.valueOf(orderDetailMap.get("category_tag")) : null);
        orderDetailSearchDTO.setModifyPrice(orderDetailMap.get("modify_price") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("modify_price"))) : null);
        orderDetailSearchDTO.setPicPath(orderDetailMap.get("fpicpath") != null ? String.valueOf(orderDetailMap.get("fpicpath")) : null);
        orderDetailSearchDTO.setSuppId(orderDetailMap.get("supp_id") != null ? NumberUtils.toInt(String.valueOf(orderDetailMap.get("supp_id"))) : null);
        orderDetailSearchDTO.setProductCode(orderDetailMap.get("product_code") != null ? String.valueOf(orderDetailMap.get("product_code")) : null);
        return orderDetailSearchDTO;
    }


    /**
     * 搜索返回的订单结果Map对象 填充 OrderMasterDTO
     *
     * @param recordsMap
     */
    public static OrderMasterSearchDTO orderMapCreateOrderMasterSearchDTO(Map<String, String> recordsMap) {
        if (MapUtils.isEmpty(recordsMap)) {
            return null;
        }
        OrderMasterSearchDTO orderMasterSearchDTO = new OrderMasterSearchDTO();
        orderMasterSearchDTO.setId(NumberUtils.toInt(String.valueOf(recordsMap.get("id")).trim()));
        orderMasterSearchDTO.setForderno(recordsMap.get("forderno"));
        orderMasterSearchDTO.setFbuydepartmentid(NumberUtils.toInt(String.valueOf(recordsMap.get("fbuydepartmentid")).trim()));
        orderMasterSearchDTO.setFbuydepartment(recordsMap.get("fbuydepartment"));
        orderMasterSearchDTO.setFsuppname(recordsMap.get("fsuppname"));
        orderMasterSearchDTO.setFsuppid(recordsMap.get("fsuppid") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("fsuppid")).trim()) : null);
        orderMasterSearchDTO.setForderamounttotal(recordsMap.get("forderamounttotal") != null ? NumberUtils.toDouble(recordsMap.get("forderamounttotal")) : null);
        orderMasterSearchDTO.setOrderType(recordsMap.get("order_type") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("order_type")).trim()) : null);
        orderMasterSearchDTO.setFuserid(recordsMap.get("fuserid") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("fuserid")).trim()) : null);
        orderMasterSearchDTO.setSpecies(recordsMap.get("species") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("species")).trim()) : null);
        orderMasterSearchDTO.setFtbuyappid(recordsMap.get("ftbuyappid") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("ftbuyappid")).trim()) : null);
        orderMasterSearchDTO.setFbuyapplicationno(recordsMap.get("fbuyapplicationno"));
        orderMasterSearchDTO.setProjectNumber(recordsMap.get("projectNumber"));
        orderMasterSearchDTO.setExtraInfo(recordsMap.get("extra_info"));
        orderMasterSearchDTO.setFbuyercontactman(recordsMap.get("fbuyercontactman"));
        orderMasterSearchDTO.setFusername(recordsMap.get("fusername"));
        orderMasterSearchDTO.setForderdate(recordsMap.get("forderdate"));
        orderMasterSearchDTO.setFbiderdeliveryplace(recordsMap.get("fbiderdeliveryplace"));
        orderMasterSearchDTO.setDepartmentParentId(recordsMap.get("department_parent_id") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("department_parent_id")).trim()) : null);
        orderMasterSearchDTO.setDepartmentParentName(recordsMap.get("dept_parent_name") != null ? String.valueOf(recordsMap.get("dept_parent_name")).trim() : null);
        orderMasterSearchDTO.setFbuyername(recordsMap.get("fbuyername"));
        orderMasterSearchDTO.setStatus(recordsMap.get("status") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("status")).trim()) : null);
        orderMasterSearchDTO.setConfirmType(recordsMap.get("confirm_type") != null ? NumberUtils.toInt(recordsMap.get("confirm_type")) : null);
        orderMasterSearchDTO.setRelateInfo(recordsMap.get("relateInfo"));
        orderMasterSearchDTO.setStatementId(recordsMap.get("statement_id") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("statement_id")).trim()) : null);
        orderMasterSearchDTO.setStatementStatus(recordsMap.get("statement_status") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("statement_status")).trim()) : null);
        orderMasterSearchDTO.setFbuyerid(recordsMap.get("fbuyerid") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("fbuyerid")).trim()) : null);
        orderMasterSearchDTO.setConfirm(recordsMap.get("is_confirm") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("is_confirm")).trim()) : null);
        orderMasterSearchDTO.setFconfirmdate(recordsMap.get("fconfirmdate") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, recordsMap.get("fconfirmdate")) : null);
        orderMasterSearchDTO.setFdeliverydate(recordsMap.get("fdeliverydate") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, recordsMap.get("fdeliverydate")) : null);
        orderMasterSearchDTO.setFlastreceivedate(recordsMap.get("flastreceivedate") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, recordsMap.get("flastreceivedate")) : null);
        orderMasterSearchDTO.setFlastreceiveman(recordsMap.get("flastreceiveman"));
        orderMasterSearchDTO.setFbuyertelephone(recordsMap.get("fbuyertelephone"));
        orderMasterSearchDTO.setBidOrderId(recordsMap.get("bid_order_id"));
        orderMasterSearchDTO.setUpdateTime(recordsMap.get("update_time") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, recordsMap.get("update_time")) : null);
        orderMasterSearchDTO.setFcanceldate(recordsMap.get("fcanceldate") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, recordsMap.get("fcanceldate")) : null);
        orderMasterSearchDTO.setFundStatus(recordsMap.get("fund_status") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("fund_status")).trim()) : null);
        orderMasterSearchDTO.setInventoryStatus(recordsMap.get("inventory_status") != null ? NumberUtils.toInt(String.valueOf(recordsMap.get("inventory_status")).trim()) : null);
        orderMasterSearchDTO.setCarryFee(recordsMap.get("carry_fee") != null ? NumberUtils.toDouble(String.valueOf(recordsMap.get("carry_fee")).trim()) : null);
        orderMasterSearchDTO.setFlastreceivemanid(recordsMap.get("flastreceivemanid") != null ? NumberUtils.toInt(recordsMap.get("flastreceivemanid").trim()) : null);
        orderMasterSearchDTO.setReturnAmount(recordsMap.get("return_amount") != null ? NumberUtils.toDouble(recordsMap.get("return_amount").trim()) : null);
        orderMasterSearchDTO.setDepartmentParentId(recordsMap.get("dept_parent_id") != null ? NumberUtils.toInt(recordsMap.get("dept_parent_id")) : null);
        orderMasterSearchDTO.setDepartmentParentName(recordsMap.get("dept_parent_name") != null ? String.valueOf(recordsMap.get("dept_parent_name")).trim() : null);
        orderMasterSearchDTO.setDeliveryStatus(recordsMap.get("delivery_status") != null ? Integer.valueOf(recordsMap.get("delivery_status")) : null);
        orderMasterSearchDTO.setSortedUser(recordsMap.get("sorted_user") != null ? String.valueOf(recordsMap.get("sorted_user")).trim() : null);
        orderMasterSearchDTO.setDeliveryUser(recordsMap.get("delivery_user") != null ? String.valueOf(recordsMap.get("delivery_user")).trim() : null);
        orderMasterSearchDTO.setDeliveredTime(recordsMap.get("delivered_time") != null ? DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, recordsMap.get("delivered_time")) : null);
        orderMasterSearchDTO.setDeliveryType(recordsMap.get("deliveryType") != null ? Integer.valueOf(recordsMap.get("deliveryType")) : null);

        if(orderMasterSearchDTO.getForderdate() != null){
            Date forderDate = DateUtils.parse(DateUtils.SIMPLE_DATE_FORMAT, orderMasterSearchDTO.getForderdate());
            boolean oldFlag;
            try {
                oldFlag = OrderDateConstant.isOldOrderForView(OrgEnum.getOrgEnumById(orderMasterSearchDTO.getFuserid()).getCode(), forderDate);
            }catch (Exception e){
                oldFlag = false;
            }
            orderMasterSearchDTO.setOldFlag(oldFlag);
            // 如果后面转换orderExtra后发现是试用订单，则不是代结算订单
            orderMasterSearchDTO.setAgentStatement(ReimbursementServiceImpl.getIsAgentStatement(orderMasterSearchDTO.getFuserid(), orderMasterSearchDTO.getFsuppid(), forderDate));
        }

        return orderMasterSearchDTO;
    }

    /**
     * @Description: search返回的records转为订单FTO
     * @Param:
     * @return:
     * @Author: zhuk
     * @Date: 2019/5/31
     */
    public static List<OrderMasterSearchDTO> recordsToOrderMasterDTOS(List<Record> records) {
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = new ArrayList<>();
        for (Record record : records) {
            Map<String, String> orderMasterMap = record.getValueMap();

            // 将搜索中返回的Map转为orderMasterDto
            OrderMasterSearchDTO orderMasterSearchDTO = OrderPojoTranslator.orderMapCreateOrderMasterSearchDTO(orderMasterMap);
            if (orderMasterSearchDTO == null) {
                continue;
            }
            //解析订单详情,下划线 转驼峰
            String orderDetailJson = orderMasterMap.get(OrderNestedEnum.NESTED_TABLE_DETAIL.getName());
            List<Map> orderDetailMaps = jsonToMapList(orderDetailJson);
            List<OrderDetailSearchDTO> orderDetails = new ArrayList<>();
            orderDetailMaps.stream().forEach(orderDetail
                    -> orderDetails.add(OrderPojoTranslator.mapToOrderDetailSearchDTO(orderDetail)));

            orderMasterSearchDTO.setOrderDetail(orderDetails);

            // 订单额外信息
            String orderExtraJson = orderMasterMap.get(OrderNestedEnum.ORDER_EXTRA.getName());
            List<Map> orderExtraMaps = jsonToMapList(orderExtraJson);
            List<OrderExtraDTO> orderExtras = New.list();
            orderExtraMaps.stream().forEach(orderExtra -> orderExtras.add(OrderPojoTranslator.mapToOrderExtraSearchDTO(orderExtra)));
            orderMasterSearchDTO.setOrderExtra(orderExtras);
            if(orderExtras.stream().anyMatch(orderExtraDTO -> OrderExtraEnum.IS_TRIAL_ORDER.getValue().equals(orderExtraDTO.getExtraKey()) && CommonValueUtils.TRUE_NUMBER_STR.equals(orderExtraDTO.getExtraValue()))){
                // 试用订单，不是代结算订单
                orderMasterSearchDTO.setAgentStatement(false);
            }

            //解析经费卡
            String fundCardJson = orderMasterMap.get(OrderNestedEnum.NESTED_TABLE_CARD.getName());
            List<Map> fundCardMaps = jsonToMapList(fundCardJson);
            List<FundCardSearchDTO> fundCards = new ArrayList<>();
            fundCardMaps.stream().forEach(fundCard
                    -> fundCards.add(OrderPojoTranslator.mapToFundCardSearchDTO(fundCard)));
            orderMasterSearchDTO.setCard(fundCards);

            //解析订单日志
            String orderLogJson = orderMasterMap.get(OrderNestedEnum.NESTED_TABLE_LOG.getName());
            List<Map> orderLogMaps = jsonToMapList(orderLogJson);
            List<OrderLogSearchDTO> orderLogs = new ArrayList<>();
            orderLogMaps.stream().forEach(orderLog
                    -> orderLogs.add(OrderPojoTranslator.mapToOrderLogSearchDTO(orderLog)));
            orderMasterSearchDTO.setLog(orderLogs);
            orderMasterSearchDTOList.add(orderMasterSearchDTO);
        }
        return orderMasterSearchDTOList;
    }

    /**
     * json 转为 list
     *
     * @param detailJson
     * @return
     */
    public static List<Map> jsonToMapList(String detailJson) {
        List<Map> mapList = null;
        try {
            mapList = JsonUtils.parseList(detailJson, Map.class);
        } catch (Exception e) {
            Cat.logError("OrderPojoTranslator", "jsonToMapList", e.getMessage(), e);
        }
        if (CollectionUtils.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        return mapList;
    }
}
