package com.ruijing.store.order.gateway.print.service;

import com.ruijing.store.order.api.base.common.OrderCommonPrintParamDTO;
import com.ruijing.store.order.gateway.print.dto.PrintDataRequestDTO;
import com.ruijing.store.order.gateway.print.dto.order.OrderPrintDataItemDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/27 10:57
 * @description
 */
public interface OrderPrintDataService {

    /**
     * 获取订单打印数据
     * @param request 请求
     * @return 打印数据
     */
    List<OrderPrintDataItemDTO> getOrderPrintData(PrintDataRequestDTO request);
}
