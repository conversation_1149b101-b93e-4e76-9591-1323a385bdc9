package com.ruijing.store.order.gateway.buyercenter.vo.orderdetail;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: chenzhanliang
 * @createTime: 2024-06-28 14:59
 * @description:
 **/
@RpcModel("订单详情-发票信息返回体")
public class OrderInvoiceInfoRespVO implements Serializable {

    private static final long serialVersionUID = 3310521233336757548L;

    @RpcModelProperty(value = "发票信息列表")
    private List<OrderInvoiceInfoVO> orderInvoiceInfoVOList;

    /**
     * 是否能修改发票信息
     */
    @RpcModelProperty(value = "是否能修改发票信息")
    private Boolean canModifyInvoice;

    public List<OrderInvoiceInfoVO> getOrderInvoiceInfoVOList() {
        return orderInvoiceInfoVOList;
    }

    public void setOrderInvoiceInfoVOList(List<OrderInvoiceInfoVO> orderInvoiceInfoVOList) {
        this.orderInvoiceInfoVOList = orderInvoiceInfoVOList;
    }

    public Boolean getCanModifyInvoice() {
        return canModifyInvoice;
    }

    public void setCanModifyInvoice(Boolean canModifyInvoice) {
        this.canModifyInvoice = canModifyInvoice;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", OrderInvoiceInfoRespVO.class.getSimpleName() + "[", "]")
                .add("orderInvoiceInfoVOList=" + orderInvoiceInfoVOList)
                .add("canModifyInvoice=" + canModifyInvoice)
                .toString();
    }
}
