package com.ruijing.store.exception;

/**
 * Name: RiskRuleLimitException
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/5/22
 */
public class CodeException extends RuntimeException {

    /**
     * 确认弹窗异常代码
     */
    public static final int SHOW_DIALOG_CODE = 510;

    /**
     * 配伍禁忌
     */
    public static final int WAREHOUSE_INCOMPATIBILITY_CODE = 587;

    /**
     * 入库含量校验
     */
    public static final int CHECK_MEASUREMENT_NUMBER_CODE = 588;

    private Object errorData;

    private Integer errorCode;

    public CodeException(){}

    public CodeException(Integer errorCode, Object errorData){
        this.errorCode = errorCode;
        this.errorData = errorData;
    }

    public CodeException(Integer errorCode, String errorMsg, Object errorData){
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorData = errorData;
    }

    public Object getErrorData() {
        return errorData;
    }

    public void setErrorData(Object errorData) {
        this.errorData = errorData;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }
}
