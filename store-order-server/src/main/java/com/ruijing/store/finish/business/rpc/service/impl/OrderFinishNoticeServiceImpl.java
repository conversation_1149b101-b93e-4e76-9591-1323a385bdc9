package com.ruijing.store.finish.business.rpc.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.api.outer.buyer.OrderFinishNoticeService;
import com.reagent.order.dto.outer.buyer.OuterBuyerCommonProcessDTO;
import com.reagent.order.enums.config.OrderDockingStrategyEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpService;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.rpc.client.StatementPlatformClient;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/2 17:10
 * @description
 */
@MSharpService
public class OrderFinishNoticeServiceImpl implements OrderFinishNoticeService {


    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Override
    @ServiceLog(description = "管理平台通知订单完成",operationType = OperationType.WRITE)
    public RemoteResponse<Boolean> finishOrder(OuterBuyerCommonProcessDTO outerBuyerCommonProcessDTO) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(outerBuyerCommonProcessDTO.getOrderNo());
        Preconditions.notNull(orderMasterDO, "没有找到订单号为"+ outerBuyerCommonProcessDTO.getOrderNo() + "的订单");
        Integer currentStatus = orderMasterDO.getStatus();
        BusinessErrUtil.isTrue(
                OrderStatusEnum.WaitingForStatement_1.getValue().equals(currentStatus),
                ExecptionMessageEnum.UPDATE_ORDER_STATUS_FAILED, currentStatus
        );
        // 限定了只有待结算状态可以发起完成订单，删除待结算订单列表数据
        statementPlatformClient.deleteWaitingStatementByOrderId(New.list(orderMasterDO.getId()));
        // 写操作日志
        orderApprovalLogService.saveApprovalLog(orderMasterDO.getId(), OrderApprovalEnum.FINISH_ORDER.getValue(), DockingConstant.SYSTEM_OPERATOR_ID, "完成报账");
        // 修改订单为完成状态,写入完成事件
        OrderMasterDO updated = new OrderMasterDO();
        updated.setStatus(OrderStatusEnum.Finish.getValue());
        updated.setFinishDate(new Date());
        orderMasterMapper.updateStatusByIdIn(updated, New.list(orderMasterDO.getId()));
        return OrderFinishNoticeService.super.finishOrder(outerBuyerCommonProcessDTO);
    }
}
