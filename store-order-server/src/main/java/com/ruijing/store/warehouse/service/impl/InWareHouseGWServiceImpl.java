package com.ruijing.store.warehouse.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.reagent.bid.api.rpc.dto.BidApprovalLogDTO;
import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.product.OrderProductInventoryStatusEnum;
import com.reagent.research.fundcard.dto.v2.FundCardDTO;
import com.reagent.research.statement.api.enums.InvoiceTypeEnum;
import com.reagent.research.statement.api.invoice.dto.InvoiceDTO;
import com.reagent.research.statement.api.invoice.dto.InvoiceQueryDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.ListUtils;
import com.ruijing.order.utils.LocaleUtils;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.category.api.constant.CategoryConstant;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.category.api.dto.CategoryQueryDTO;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.shopcommon.base.PageApiResult;
import com.ruijing.shop.wms.api.dto.GasBottleDTO;
import com.ruijing.store.apply.dto.ApplicationMasterDTO;
import com.ruijing.store.apply.dto.ApplyRefWarehouseDTO;
import com.ruijing.store.approval.api.dto.PurchaseApprovalLogDTO;
import com.ruijing.store.baseconfig.api.msg.BaseConfigDTO;
import com.ruijing.store.electronicsign.api.dto.ElectronicSignOperationRecordDTO;
import com.ruijing.store.electronicsign.api.dto.OperationListDTO;
import com.ruijing.store.electronicsign.api.enums.BusinessTypeEnum;
import com.ruijing.store.electronicsign.api.enums.ElectronicSignatureOperationEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderapprovallog.dto.OrderApprovalRequestDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.OrderMasterDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderMasterCommonService;
import com.ruijing.store.order.api.base.other.dto.OrderApprovalLogDTO;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogDTO;
import com.ruijing.store.order.api.enums.SortOrderEnum;
import com.ruijing.store.order.api.general.dto.FundCardSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderDetailSearchDTO;
import com.ruijing.store.order.api.general.dto.OrderMasterSearchDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.base.core.model.OrderDetailDO;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.base.core.translator.OrderDetailTranslator;
import com.ruijing.store.order.base.core.translator.OrderMasterTranslator;
import com.ruijing.store.order.business.service.impl.InboundSucceedCallbackService;
import com.ruijing.store.order.constant.PrintConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.orderdetail.OrderFundcardVO;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.rpc.impl.OrderManageRpcServiceImpl;
import com.ruijing.store.order.search.service.OrderSearchBoostService;
import com.ruijing.store.order.service.BizBaseService;
import com.ruijing.store.order.service.ResearchBaseService;
import com.ruijing.store.order.util.BarCodeUtils;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.dto.department.MiniDepartmentDTO;
import com.ruijing.store.user.api.dto.extend.UserExtendInfoDTO;
import com.ruijing.store.user.api.enums.user.UserExtendInfoTypeEnum;
import com.ruijing.store.warehouse.client.WarehouseRpcClient;
import com.ruijing.store.warehouse.message.bean.*;
import com.ruijing.store.warehouse.message.constant.WarehouseConstant;
import com.ruijing.store.warehouse.message.enums.WarehouseApplicationSubmitWayEnum;
import com.ruijing.store.warehouse.message.vo.ElectronicSignDataVO;
import com.ruijing.store.warehouse.message.vo.claim.DepartmentVO;
import com.ruijing.store.warehouse.message.vo.inwarehouse.*;
import com.ruijing.store.warehouse.message.vo.outwarehouse.OutWarehouseApplicationDetailVO;
import com.ruijing.store.warehouse.service.InWareHouseGWService;
import com.ruijing.store.warehouse.service.OutWarehouseGWService;
import com.ruijing.store.warehouse.service.WarehouseStockOccupyService;
import com.ruijing.store.warehouse.utils.CategoryUtil;
import com.ruijing.store.warehouse.utils.GateWayMessageUtil;
import com.ruijing.store.warehouse.utils.OrderDetailsUtil;
import com.ruijing.store.warehouse.utils.PriceUtil;
import com.ruijing.store.warehouse.utils.translator.*;
import com.ruijing.store.wms.api.dto.*;
import com.ruijing.store.wms.api.dto.docking.AHSLDockIngDTO;
import com.ruijing.store.wms.api.dto.docking.BaseDockingDTO;
import com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum;
import com.ruijing.store.wms.api.enums.EntryBusinessTypeEnum;
import com.ruijing.store.wms.api.enums.InboundStatus;
import com.ruijing.store.wms.api.query.StockMultiQueryDTO;
import com.ruijing.store.wms.api.query.WmsEntryApprovalQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruijing.store.warehouse.message.vo.inwarehouse.WarehouseProductInfoVO.*;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2020/12/30 10:29
 */
@Service
public class InWareHouseGWServiceImpl implements InWareHouseGWService {

    private static final Logger LOGGER = LoggerFactory.getLogger(InWareHouseGWServiceImpl.class);

    private static final String CAT_TYPE = "InWareHouseGWServiceImpl";

    /**
     * thunder是否监听生成订单状态，如果是则关闭生成订单推送的开关
     */
    @PearlValue(key = "order.thunder.listen.warehouse", defaultValue = "false")
    private Boolean enableMqPushWarehouse;

    /**
     * 商品分类分隔符表达式
     */
    private static final String CATEGORY_PATH_SEPARATOR_REGEX = "\\.";

    /**
     * 订单关联发票号分割符
     */
    private static final String ORDER_INVOICE_SEPARATOR = ",";

    /**
     * 默认可选库房列表大小
     */
    private static final int DEFAULT_WAREHOUSE_SIZE_TO_CHOOSE = 1;

    /**
     * 无效的库存重量，当库存取此值时，不用判s断阈值
     */
    private static final BigDecimal INVALIDATE_WEIGHT = BigDecimal.valueOf(-1L);

    /**
     * 默认当前页面大小
     */
    private static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 默认当前页码
     */
    private static final int DEFAULT_CURRENT_PAGE = 1;

    /**
     * 需要打印入库单条形码的单位code
     */
    private static final List<String> ORG_CODE_NEED_ENTRY_NO_BARCODE = Lists.newArrayList(OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getCode(),
            OrgEnum.SHEN_ZHEN_LONG_GANG_ER_BI_HOU_YI_YUAN.getCode());

    /**
     * 需要获取入库审核人的单位code
     */
    private static List<String> ORG_CODE_NEED_WAREHOUSE_APPROVER_TO_PRINT = Lists.newArrayList(OrgEnum.YUE_BEI_REN_MIN_YI_YUAN.getCode(),
            OrgEnum.GUANG_DONG_YI_KE_DA_XUE.getCode(), OrgEnum.ZHONG_SHAN_DA_XUE_FU_SHU_DI_YI_YI_YUAN.getCode(),
            OrgEnum.GUANG_ZHOU_HONG_SHI_ZI_HUI_YI_YUAN.getCode(), "ZHONG_SHAN_SHI_REN_MIN_YI_YUAN",
            OrgEnum.ZHENG_ZHOU_ER_TONG_YI_YUAN.getCode());

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private OrderSearchBoostService orderSearchBoostService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private ApplyRefWarehouseServiceClient applyRefWarehouseServiceClient;

    @Resource
    private BizWarehouseRoomServiceClient bizWarehouseRoomServiceClient;

    @Resource
    private CategoryServiceClient categoryServiceClient;

    @Resource
    private UserDepartmentRoleRpcServiceClient userDepartmentRoleRpcServiceClient;

    @Resource
    private UserClient userClient;

    @Resource
    private PurchaseApprovalLogClient purchaseApprovalLogClient;

    @Resource
    private ResearchFundCardServiceClient researchFundCardServiceClient;

    @Resource
    private InvoiceClient invoiceClient;

    @Resource
    private BizExitServiceClient bizExitServiceClient;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private OrganizationClient organizationClient;

    @Resource
    private OutWarehouseGWService outWarehouseGWService;

    @Resource
    private OrderMasterCommonService orderMasterCommonService;

    @Resource
    private DepartmentRpcClient departmentRpcClient;

    @Resource
    private BidClient bidClient;

    @Autowired
    private ResearchBaseService researchBaseService;

    @Resource
    private OrderManageRpcServiceImpl orderManageRpcService;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;
    
    @Resource
    private InboundSucceedCallbackService inboundSucceedCallbackService;
    
    @Resource
    private BizBaseService bizBaseService;
    
    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private ElectronicSignServiceClient electronicSignServiceClient;

    @Resource
    private UserExtendInfoRpcClient userExtendInfoRpcClient;
    
    @Resource
    private WarehouseRpcClient warehouseRpcClient;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private WarehouseStockOccupyService warehouseStockOccupyService;

    @Resource
    private ApplicationBaseClient applicationBaseClient;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Override
    public WarehouseApplicationPrepareSubmitVO getPrepareSubmitInfoByOrderId(OrderRequestVO request) {
        Integer orderId = request.getOrderId();
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(orderId, "订单Id不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        //获取订单基本信息
        OrderMasterDTO orderMasterDTO = getNotNullOrderMasterById(orderId);
        BusinessErrUtil.notNull(sessionUser.getUserId(RjUserTypeEnum.STORE_USER), ExecptionMessageEnum.USER_NO_DIRECT_ACCESS_PORTAL);
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), orderMasterDTO.getFbuydepartmentid(), orderMasterDTO.getFuserid());
        OrderBean orderBean = OrderBeanTranslator.orderMasterDTO2BaseOrderBean(orderMasterDTO);
        //根据课题组Id查找用户可选择的库房
        List<OrderDetailDTO> orderDetailDTOList = getNotReturnOrderDetailList(New.list(orderId));
        List<BizWarehouseRoomDTO> warehouseListCanBeChosen = bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(Lists.newArrayList(orderMasterDTO.getFbuydepartmentid()));
        this.populateWarehousesCanBeChosenByDepartment(orderBean, warehouseListCanBeChosen);

        return wrapperPrepareSubmitVO(orderBean, orderMasterDTO, orderDetailDTOList);
    }

    @Override
    public WarehouseApplicationPrepareSubmitBatchVO getBatchPrepareSubmitInfoByOrderId(OrderBasicParamDTO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notEmpty(request.getOrderIdList(), "订单Id不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        BusinessErrUtil.notNull(sessionUser.getUserId(RjUserTypeEnum.STORE_USER), ExecptionMessageEnum.USER_NO_DIRECT_ACCESS_PORTAL);
        //获取订单基本信息
        List<OrderMasterDTO> orderList = getNotNullOrderMasterByIdList(request.getOrderIdList());
        Set<Integer> departmentIdSet = orderList.stream().map(OrderMasterDTO::getFbuydepartmentid).collect(Collectors.toSet());
        BusinessErrUtil.isTrue(departmentIdSet.size() == 1, ExecptionMessageEnum.NO_CROSS_GROUP_STORAGE);
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), orderList.get(0).getFbuydepartmentid(), orderList.get(0).getFuserid());
        List<OrderBean> orderBeans = ListUtils.toList(orderList, OrderBeanTranslator::orderMasterDTO2BaseOrderBean);

        //根据课题组Id查找用户可选择的库房
        List<BizWarehouseRoomDTO> warehouseListCanBeChosen = bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(Lists.newArrayList(departmentIdSet));
        orderBeans.forEach(o -> this.populateWarehousesCanBeChosenByDepartment(o, warehouseListCanBeChosen));

        List<WarehouseApplicationPrepareSubmitVO> data = new ArrayList<>(orderList.size());
        Map<Integer, OrderMasterDTO> orderIdIdentityMapper = DictionaryUtils.toMap(orderList, OrderMasterDTO::getId, Function.identity());
        Map<Integer, List<OrderDetailDTO>> orderIdDetailMapper = DictionaryUtils.groupBy(this.getNotReturnOrderDetailList(request.getOrderIdList()), OrderDetailDTO::getFmasterid);
        for (OrderBean orderBean : orderBeans) {
            OrderMasterDTO orderMasterDTO = orderIdIdentityMapper.get(orderBean.getOrderId());
            if (orderMasterDTO == null) {
                continue;
            }
            List<OrderDetailDTO> orderDetailList = Optional.ofNullable(orderIdDetailMapper.get(orderBean.getOrderId())).orElse(Collections.emptyList());
            WarehouseApplicationPrepareSubmitVO item = wrapperPrepareSubmitVO(orderBean, orderMasterDTO, orderDetailList);
            data.add(item);
        }
        WarehouseApplicationPrepareSubmitBatchVO result = new WarehouseApplicationPrepareSubmitBatchVO();
        result.setData(data);
        result.setInvoiceInfo(wrapperInvoiceInfo(request.getOrderIdList(), orderList.get(0).getFuserid()));

        return result;
    }

    /**
     * 获取汇总单发票信息
     * @param orderIdList   订单id
     * @return              发票信息
     */
    private List<InvoiceInfoVO> wrapperInvoiceInfo(List<Integer> orderIdList, Integer orgId) {
        List<InvoiceDTO> invoiceList = invoiceClient.findInvoiceList(orderIdList, orgId);
        return ListUtils.toList(invoiceList, i -> {
            InvoiceInfoVO item = new InvoiceInfoVO();
            item.setInvoiceNo(i.getInvoiceNo());
            item.setId(i.getId());
            return item;
        });
    }

    /**
     * 封装准备提交入库的订单明细
     *
     * @param orderBean       订单信息
     * @param orderMasterDTO  订单快照
     * @param orderDetailList 订单商品快照
     * @return 准备提交入库的订单明细
     */
    private WarehouseApplicationPrepareSubmitVO wrapperPrepareSubmitVO(OrderBean orderBean, OrderMasterDTO orderMasterDTO, List<OrderDetailDTO> orderDetailList) {
        List<EntryDetailRoomDTO> entryDetailRoomDTOList = bizWareHouseClient.queryEntryDetailRoom(orderDetailList, orderMasterDTO);
        Map<Integer, EntryDetailRoomDTO> detailIdEntryRoomMap = DictionaryUtils.toMap(entryDetailRoomDTOList, EntryDetailRoomDTO::getOrderDetailId, Function.identity());
        List<ProductBean> productBeans = new ArrayList<>(orderDetailList.size());
        for (OrderDetailDTO orderDetail : orderDetailList) {
            ProductBean orderDetailBean = ProductBeanTranslator.orderDetailDTO2ProductBean(orderDetail);
            if (orderDetailBean.getQuantityWithoutReturn().equals(0)) {
                continue;
            }
            this.populateSupplier(orderDetailBean, orderMasterDTO);
            productBeans.add(orderDetailBean);
        }
        // 判断商品是否需要使用样式“入库申请页-危化品”(这个样式会多要求用户输入计量含量等信息)
        this.selfPopulateNeedShowDangerousInputFlag(productBeans, orderMasterDTO.getForderno(), null, orderMasterDTO.getFuserid());
        // 获取默认入库id
        this.populateDefaultWarehouseId(productBeans, orderMasterDTO, detailIdEntryRoomMap);

        Map<Long, BizWarehouseDangerousOccupyStockDetailDTO> productIdOccupyDetailMap;
        if(orderMasterDTO.getFtbuyappid() != null){
            ApplicationMasterDTO applicationMasterDTO = applicationBaseClient.getApplicationMasterByApplyId(orderMasterDTO.getFtbuyappid(), false);
            List<BizWarehouseDangerousOccupyStockDetailDTO> occupyStockDetailDTOList = bizWareHouseClient.getDangerousOccupyStockDetail(applicationMasterDTO.getApplyNumber());
            productIdOccupyDetailMap = DictionaryUtils.toMap(occupyStockDetailDTOList, BizWarehouseDangerousOccupyStockDetailDTO::getProductId, Function.identity());
        }else {
            productIdOccupyDetailMap = New.emptyMap();
        }

        WarehouseApplicationPrepareSubmitVO item = new WarehouseApplicationPrepareSubmitVO();
        this.populateWarehouseApplicationPrepareSubmitVO(item, orderBean, productBeans);
        //  QA要求取实际金额
        item.setTotalPrice(orderMasterDTO.getForderamounttotal().subtract(BigDecimal.valueOf(orderMasterDTO.getReturnAmount())).doubleValue());
        // 气瓶数据获取
        Map<Integer, List<String>> detailIdBindGasBottlesMap = orderDetailList.stream().filter(detail->CollectionUtils.isNotEmpty(detail.getBindGasBottleBarcodes()))
                .collect(Collectors.toMap(OrderDetailDTO::getId, OrderDetailDTO::getBindGasBottleBarcodes, (o,n)->n));
        Map<String, GasBottleVO> gasBottleBarcodeIdentityMap;
        if(!detailIdBindGasBottlesMap.isEmpty()){
            List<String> bindGasBottleBarcodes = detailIdBindGasBottlesMap.values().stream().flatMap(List::stream).collect(toList());
            List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(bindGasBottleBarcodes);
            gasBottleBarcodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
        } else {
            gasBottleBarcodeIdentityMap = New.emptyMap();
        }

        // 填充可选库房列表
        item.getWarehouseProductInfoVOList().forEach(warehouseProductInfoVO -> {
            EntryDetailRoomDTO entryDetailRoomDTO = detailIdEntryRoomMap.get(warehouseProductInfoVO.getOrderDetailId());
            // 设置是否无须入库
            warehouseProductInfoVO.setNeedSubmitWarehouseTag(entryDetailRoomDTO.isNeedInbound() ? NEED_SUBMIT_WAREHOUSE : NEED_NOT_SUBMIT_WAREHOUSE);
            List<BizWarehouseRoomDTO> roomDTOList = Optional.ofNullable(entryDetailRoomDTO.getRooms()).orElse(New.list());
            List<WarehouseVO> warehouseVOList = roomDTOList.stream().map(room -> new WarehouseVO(room.getId(), room.getRoomName())).collect(toList());
            warehouseProductInfoVO.setWarehouseVOList(warehouseVOList);
        });

        // 没有预占， 查询默认库房的同类商品入库过的库存信息
        final Map<String, StockDTO> casNo2StockDTOMap = New.map();
        if (MapUtils.isEmpty(productIdOccupyDetailMap)) {
            List<StockMultiQueryDTO> query = item.getWarehouseProductInfoVOList().stream()
                    .filter(warehouseProductInfoVO -> Objects.nonNull(warehouseProductInfoVO.getDefaultWarehouseId()))
                    .map(warehouseProductInfoVO -> {
                        StockMultiQueryDTO dto = new StockMultiQueryDTO();
                        dto.setCasNo(warehouseProductInfoVO.getCasNo());
                        dto.setRoomId(warehouseProductInfoVO.getDefaultWarehouseId());
                        dto.setProductName(warehouseProductInfoVO.getProductName());
                        return dto;
                    }).collect(toList());
            List<StockDTO> stockDTOS = bizWarehouseRoomServiceClient.queryDangerousByMultiKey(query);
            if (CollectionUtils.isNotEmpty(stockDTOS)) {
                stockDTOS.forEach(stockDTO -> casNo2StockDTOMap.put(stockDTO.getCasNo(), stockDTO));
            }
        }

        // 设置每个商品详情的入库列表
        item.getWarehouseProductInfoVOList().forEach(warehouseProductInfoVO -> {
            // 如果有预占，填充预占数据
            BizWarehouseDangerousOccupyStockDetailDTO occupyStockDetailDTO = productIdOccupyDetailMap.get(warehouseProductInfoVO.getProductId());
            if (occupyStockDetailDTO != null) {
                warehouseProductInfoVO.setForm(occupyStockDetailDTO.getForm());
                warehouseProductInfoVO.setDefaultWarehouseId(occupyStockDetailDTO.getRoomId());
                warehouseProductInfoVO.setUnitMeasurementNum(occupyStockDetailDTO.getUnitMeasurementNum());
                warehouseProductInfoVO.setQuantityUnit(occupyStockDetailDTO.getMeasurementUnit());
                warehouseProductInfoVO.setTotalQuantityAfterReturn(
                        BigDecimal.valueOf(warehouseProductInfoVO.getQuantityAfterReturn())
                                .multiply(warehouseProductInfoVO.getUnitMeasurementNum())
                                .doubleValue());
            } else {
                // 未预占时,从已入库商品信息
                StockDTO stockDTO = casNo2StockDTOMap.get(warehouseProductInfoVO.getCasNo());
                if (Objects.nonNull(stockDTO)) {
                    warehouseProductInfoVO.setForm(stockDTO.getForm());
                }
            }
            // 气瓶数据绑到商品上
            Integer detailId = warehouseProductInfoVO.getOrderDetailId();
            List<String> bindGasBottleBarcodes = detailIdBindGasBottlesMap.get(detailId);
            if(CollectionUtils.isNotEmpty(bindGasBottleBarcodes)){
                List<GasBottleVO> bindGasBottles = bindGasBottleBarcodes.stream().map(gasBottleBarcodeIdentityMap::get).filter(Objects::nonNull).collect(toList());
                warehouseProductInfoVO.setBindGasBottles(bindGasBottles);
            }
        });

        // 中国科学技术大学附属第一医院（安徽省立医院）定制需求，经费卡校区字段需要传到前端
        if(orderMasterDTO.getFuserid() == OrgEnum.AN_HUI_SHENG_LI_YI_YUAN.getValue()){
            OrderMasterSearchDTO orderMasterSearchDTO = this.getNotNullSearchOrder(item.getOrderId());
            item.setFundCard(this.getOrderFundCardVOFromSearchDTO(orderMasterSearchDTO));
        }
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderMasterDTO.getId()), New.list(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue(), OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue()));
        Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        item.setEachProductEachCode(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue())));
        item.setSuppNeedFillBatchesData(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue())));

        return item;
    }

    @Override
    @ServiceLog(description = "提交入库单", operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public SubmitWarehouseVO submitWarehouseApplication(WarehouseSubmitApplicationRequestVO request) {
        SubmitWarehouseVO submitWarehouseVO = new SubmitWarehouseVO();
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        UserBaseInfoDTO baseUser = userClient.getNotNullUserBaseInfoByGuid(sessionUser.getGuid());
        //常规验证
        validateIntegrityForSubmit(request);
        OrderMasterSearchDTO searchOrder = getNotNullSearchOrder(request.getOrderId());
        //要有权限的人才能提交入库
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), searchOrder.getFbuydepartmentid(), searchOrder.getFuserid());
        //验证订单是否能提交入库
        this.validateOrderCanSubmit(baseUser.getId(), searchOrder, request.getSubmitWay());

        //无需入库
        if (isNeedNotInboundForSubmit(request)) {
            //如果该订单不存在入库单，更新入库状态为无需入库
            checkAndUpdateOrderStatusToNotNeedInbound(searchOrder.getId(), searchOrder.getForderno());
            // 无须入库，调用不到库房接口，需要我们这边调一下释放预占库存
            warehouseStockOccupyService.releaseAll(searchOrder.getId(), searchOrder.getForderno());
            // 未入库的商品就判定为无需入库
            this.updateNoNeedInboundForBarcode(searchOrder.getForderno(), request.getWarehouseProductRequestList());
            return submitWarehouseVO;
        }

        //商品验证
        List<BizWarehouseEntryDTO> warehouseEntryList = getNotEmptyWarehouseEntryList(baseUser, searchOrder, request);
        validateWarehouseRoomThreshold(warehouseEntryList, searchOrder.getFuserid(), searchOrder.getForderno());
        if (CollectionUtils.isNotEmpty(request.getInvoiceInfo())) {
            warehouseEntryList.forEach(it -> {
                //设置发票信息
                it.setInvoiceNos(ListUtils.toList(request.getInvoiceInfo(), InvoiceInfoVO::getInvoiceNo));
            });
        }
        // 配伍禁忌
        bizWareHouseClient.checkIncompatibility(warehouseEntryList, request.getIncompatibilityVerify());
        // 校验计量含量
        if (BooleanUtils.isNotTrue(request.isCheckMeasurementNum())) {
            bizWareHouseClient.checkMeasurementNum(warehouseEntryList);
        }
        //保存入库单，入库状态由库房调用做修改
        boolean saveResult = bizWareHouseClient.batchSaveWarehouseApplication(warehouseEntryList);
        BusinessErrUtil.isTrue(saveResult, ExecptionMessageEnum.WAREHOUSE_SERVICE_SAVE_FAIL);
        // 未入库的商品就判定为无需入库
        this.updateNoNeedInboundForBarcode(searchOrder.getForderno(), request.getWarehouseProductRequestList());
        // 通过订单查询入库单信息
        List<BizWarehouseEntryDTO> entryByOrderNoList = bizWareHouseClient.findEntryByOrderNoList(New.list(searchOrder.getForderno()));
        List<BizWarehouseEntryVO> bizWarehouseEntryVOList = entryByOrderNoList.stream().map(bizWarehouseEntryDTO -> {
            BizWarehouseEntryVO vo = new BizWarehouseEntryVO();
            vo.setId(bizWarehouseEntryDTO.getId());
            return vo;
        }).collect(toList());
        submitWarehouseVO.setWarehouseEntryVOList(bizWarehouseEntryVOList);
        return submitWarehouseVO;
    }

    @Override
    public SubmitWarehouseVO batchSubmitWarehouseApplication(WarehouseSubmitBatchRequestVO request) {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        UserBaseInfoDTO baseUser = userClient.getNotNullUserBaseInfoByGuid(sessionUser.getGuid());
        SubmitWarehouseVO submitWarehouseVO = new SubmitWarehouseVO();
        List<OrderMasterSearchDTO> searchOrders = getNotNullSearchOrderList(ListUtils.toList(request.getData(), WarehouseSubmitApplicationRequestVO::getOrderId));
        Integer orgId = searchOrders.get(0).getFuserid();
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), searchOrders.get(0).getFbuydepartmentid(), orgId);
        Map<Integer, OrderMasterSearchDTO> orderIdOrderMapper = DictionaryUtils.toMap(searchOrders, OrderMasterSearchDTO::getId, Function.identity());
        //常规验证
        for (WarehouseSubmitApplicationRequestVO param : request.getData()) {
            validateIntegrityForSubmit(param);
            OrderMasterSearchDTO orderItem = orderIdOrderMapper.get(param.getOrderId());
            if (orderItem == null) {
                continue;
            }
            // 中山眼科，结算时入库，特殊处理已入库单
            if (Objects.equals(OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getValue(), orgId)
                    && orderItem.getInventoryStatus().equals(InventoryStatusEnum.COMPLETE.getCode())) {
                continue;
            }
            //验证订单是否能提交入库
            this.validateOrderCanSubmit(baseUser.getId(), orderItem, param.getSubmitWay());
            //无需入库
            if (isNeedNotInboundForSubmit(param)) {
                //如果该订单不存在入库单，更新入库状态为无需入库
                checkAndUpdateOrderStatusToNotNeedInbound(orderItem.getId(), orderItem.getForderno());
                // 无须入库，调用不到库房接口，需要我们这边调一下释放预占库存
                warehouseStockOccupyService.releaseAll(orderItem.getId(), orderItem.getForderno());
                // 未入库的商品就判定为无需入库
                this.updateNoNeedInboundForBarcode(orderItem.getForderno(), param.getWarehouseProductRequestList());
                continue;
            }

            List<BizWarehouseEntryDTO> warehouseEntryList = getNotEmptyWarehouseEntryList(baseUser, orderItem, param);
            if (CollectionUtils.isNotEmpty(param.getInvoiceInfo())) {
                warehouseEntryList.forEach(it -> {
                    //设置发票信息
                    it.setInvoiceNos(ListUtils.toList(param.getInvoiceInfo(), InvoiceInfoVO::getInvoiceNo));
                });
            }
            //商品验证
            validateWarehouseRoomThreshold(warehouseEntryList, orderItem.getFuserid(), orderItem.getForderno());
            // 配伍禁忌
            bizWareHouseClient.checkIncompatibility(warehouseEntryList, param.getIncompatibilityVerify());
            // 校验计量含量
            if (BooleanUtils.isNotTrue(param.isCheckMeasurementNum())) {
                bizWareHouseClient.checkMeasurementNum(warehouseEntryList);
            }
            //保存入库单，更新订单状态(备注:必须先改订单状态再入库，因为入库自动审核的情况下会令订单入库状态直接变成"已入库"，这时再改的话会覆盖这个状态)
            boolean saveResult = bizWareHouseClient.batchSaveWarehouseApplication(warehouseEntryList);
            BusinessErrUtil.isTrue(saveResult, ExecptionMessageEnum.WAREHOUSE_SERVICE_SAVE_FAIL);
            // 未入库的商品就判定为无需入库
            this.updateNoNeedInboundForBarcode(orderItem.getForderno(), param.getWarehouseProductRequestList());
        }
        // 通过订单编号查询入库单
        List<String> orderNoList = searchOrders.stream().map(OrderMasterSearchDTO::getForderno).collect(toList());
        List<BizWarehouseEntryDTO> warehouseEntryDTOS = bizWareHouseClient.findEntryByOrderNoList(orderNoList);
        List<BizWarehouseEntryVO> bizWarehouseEntryVOList = warehouseEntryDTOS.stream().map(bizWarehouseEntryDTO -> {
            BizWarehouseEntryVO vo = new BizWarehouseEntryVO();
            vo.setId(bizWarehouseEntryDTO.getId());
            return vo;
        }).collect(toList());
        submitWarehouseVO.setWarehouseEntryVOList(bizWarehouseEntryVOList);
        return submitWarehouseVO;
    }

    @Override
    @ServiceLog(operationType = OperationType.WRITE, serviceType = ServiceType.COMMON_SERVICE)
    public SubmitWarehouseVO reSubmitWarehouseApplication(WarehouseReSubmitApplicationRequestVO request) {
        Preconditions.notNull(request, "请求参数为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        UserBaseInfoDTO baseUser = userClient.getNotNullUserBaseInfoByGuid(sessionUser.getGuid());
        SubmitWarehouseVO submitWarehouseVO = new SubmitWarehouseVO();
        //常规验证
        validateIntegrityForReSubmit(request);
        OrderMasterSearchDTO searchOrder = getNotNullSearchOrder(request.getOrderId());
        //要有权限的人才能提交入库
        this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), searchOrder.getFbuydepartmentid(), searchOrder.getFuserid());
        this.throwIfExistUnFinishGoodsReturn(request.getOrderId());

        //获取已保存的入库申请单
        BizWarehouseEntryDTO existBizWarehouseEntryDTO = getNotEmptyProductBizWarehouseEntry(request.getWarehouseApplicationId());

        //判断是否需要重新生成申请单
        if (!isNeedToRebuildWarehouseApplication(existBizWarehouseEntryDTO, request)) {
            List<BizWarehouseEntryVO> bizWarehouseEntryVOS = reSubmitWithExistWarehouseApplication(request, existBizWarehouseEntryDTO,
                                                                                                   baseUser, searchOrder);
            submitWarehouseVO.setWarehouseEntryVOList(bizWarehouseEntryVOS);
            return submitWarehouseVO;
        }

        //无需入库
        if (isNeedNotInboundForReSubmit(request)) {
            LOGGER.info("重新提交入库但无须入库" + searchOrder.getForderno());
            abandonExistWarehouseApplication(existBizWarehouseEntryDTO.getEntryNo());
            // 获取所有未撤销的入库单
            List<BizWarehouseEntryDTO> list = bizWareHouseClient.findEntryByOrderNoList(New.list(searchOrder.getForderno()));
            list = Optional.ofNullable(list).orElse(New.emptyList()).stream().filter(item->!InboundStatus.HAVE_BEEN_WITHDRAW.getValue().equals(item.getStatus())).collect(toList());
            if(CollectionUtils.isEmpty(list)){
                // 没有其他的入库单，就是无须入库了
                warehouseRpcClient.changeInventoryStatus(searchOrder.getId(), InventoryStatusEnum.NOT_INBOUND.getCode());
            } else if(list.stream().allMatch(item->InboundStatus.WAREHOUSING.getValue().equals(item.getStatus()))){
                // 否则，若所有还存在的入库单都已入库了，则改为已入库状态
                warehouseRpcClient.changeInventoryStatus(searchOrder.getId(), InventoryStatusEnum.COMPLETE.getCode());
            }
            // 无须入库，调用不到库房接口，需要我们这边调一下释放预占库存
            warehouseStockOccupyService.releaseAll(searchOrder.getId(), searchOrder.getForderno());
            // 未入库的商品就判定为无需入库
            this.updateNoNeedInboundForBarcode(searchOrder.getForderno(), request.getWarehouseProductRequestList());
            return submitWarehouseVO;
        }

        //生成新入库单
        List<BizWarehouseEntryDTO> warehouseEntryList = getNotEmptyWarehouseEntryListForReSubmit(baseUser, searchOrder, request);
        if (CollectionUtils.isNotEmpty(request.getInvoiceInfo())) {
            warehouseEntryList.forEach(it -> {
                //设置发票信息
                it.setInvoiceNos(ListUtils.toList(request.getInvoiceInfo(), InvoiceInfoVO::getInvoiceNo));
            });
        }
        //商品验证
        validateWarehouseRoomThreshold(warehouseEntryList, sessionUser.getOrgId(), searchOrder.getForderno());
        // 配伍禁忌
        bizWareHouseClient.checkIncompatibility(warehouseEntryList, request.getIncompatibilityVerify());
        // 校验计量含量
        if (BooleanUtils.isNotTrue(request.isCheckMeasurementNum())) {
            bizWareHouseClient.checkMeasurementNum(warehouseEntryList);
        }
        // 设置需要作废的入库单号,生成并保存新入库单，更新订单状态
        warehouseEntryList.forEach(e->e.setInvalidEntryNo(existBizWarehouseEntryDTO.getEntryNo()));
        boolean saveResult = bizWareHouseClient.reBatchPutInStorage(warehouseEntryList);
        // 未入库的商品就判定为无需入库
        this.updateNoNeedInboundForBarcode(searchOrder.getForderno(), request.getWarehouseProductRequestList());
        BusinessErrUtil.isTrue(saveResult, ExecptionMessageEnum.STORAGE_SERVICE_SAVE_FAIL);
        // 订单编号查询入库单
        List<BizWarehouseEntryDTO> entryByOrderNoList = bizWareHouseClient.findEntryByOrderNoList(New.list(searchOrder.getForderno()));
        List<BizWarehouseEntryVO> bizWarehouseEntryVOList = entryByOrderNoList.stream().map(bizWarehouseEntryDTO -> {
            BizWarehouseEntryVO vo = new BizWarehouseEntryVO();
            vo.setId(bizWarehouseEntryDTO.getId());
            return vo;
        }).collect(toList());
        submitWarehouseVO.setWarehouseEntryVOList(bizWarehouseEntryVOList);
        return submitWarehouseVO;
    }

    /**
     * 重新推送第三方平台入库状态
     * @param request 订单数据
     * @return boolean 是否成功
     */
    @Override
    public boolean rePushWarehousingToThirdPlatForm(OrderRequestVO request) {
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(request.getOrderId());
        BusinessErrUtil.isTrue(InventoryStatusEnum.FAILED_TO_PUSH.getCode().byteValue() == orderMasterDO.getInventoryStatus(), ExecptionMessageEnum.NON_PUSH_STORAGE_FAILURE_STATUS);
        if(enableMqPushWarehouse){
           return thirdPartOrderRPCClient.redoPushOrderInfo(orderMasterDO.getForderno()); 
        }
        return inboundSucceedCallbackService.pushWarehousingToThirdPlatForm(request.getOrderId());
    }

    @Override
    public WarehouseApplicationPageVO getPersonalWarehouseApplicationList(WarehouseApplicationPersonalPageRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserViewAccessInOrganization(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId());
        //根据入参信息查询入库申请单
        BizWarehouseEntryDTO bizWarehouseEntryParam = personalPageRequest2BizWarehouseEntryDTO(request, sessionUser);
        //查当前用户的入库申请单
        PageApiResult<List<BizWarehouseEntryDTO>> warehouseListResult = bizWareHouseClient.queryEntryPage(bizWarehouseEntryParam);
        //提取入库申请单信息返回给前端
        WarehouseApplicationPageVO warehouseApplicationPageVO = this.applicationPagingDTO2applicationPagingVO(warehouseListResult);
        this.setShowPrintButton(warehouseApplicationPageVO.getWarehouseApplicationVOList(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        return warehouseApplicationPageVO;
    }

    @Override
    public WarehouseApplicationPageVO getDepartmentWarehouseApplicationList(WarehouseApplicationDeptPageRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        this.validateUserViewAccessInOrganization(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId());
        //构造查询入参
        BizWarehouseEntryDTO bizWarehouseEntryParam = departmentPageRequest2BizWarehouseEntryDTO(request, sessionUser.getOrgId());
        //部门限制
        if (CollectionUtils.isEmpty(bizWarehouseEntryParam.getDeptIdList())) {
            List<MiniDepartmentDTO> departmentsForUser = departmentRpcClient.getDeptTreeForUserByAccess(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(), WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
            BusinessErrUtil.notEmpty(departmentsForUser, ExecptionMessageEnum.USER_NO_GROUP_BINDING);
            bizWarehouseEntryParam.setDeptIdList(departmentsForUser.stream().map(MiniDepartmentDTO::getId).collect(Collectors.toList()));
        }
        //查课题组的入库申请单
        PageApiResult<List<BizWarehouseEntryDTO>> warehouseListResult = bizWareHouseClient.queryEntryPage(bizWarehouseEntryParam);
        //提取入库申请单信息返回给前端
        WarehouseApplicationPageVO warehouseApplicationPageVO = this.applicationPagingDTO2applicationPagingVO(warehouseListResult);
        this.setShowPrintButton(warehouseApplicationPageVO.getWarehouseApplicationVOList(), sessionUser.getOrgId(RjUserTypeEnum.STORE_USER));
        return warehouseApplicationPageVO;
    }

    @Override
    public WarehouseApplicationDetailVO getWarehouseApplicationDetail(WarehouseApplicationDetailRequestVO request) {
        Preconditions.notNull(request, "请求参数不能为空");
        Preconditions.notNull(request.getWarehouseApplicationId(), "入库申请单Id不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        //入库单信息
        BizWarehouseEntryDTO warehouseApplicationInfo = this.getNotNullWarehouseApplicationById(request.getWarehouseApplicationId());
        if (EntryBusinessTypeEnum.RETURN_ENTRY.getValue().equals(warehouseApplicationInfo.getBusinessType())
                || EntryBusinessTypeEnum.WITHDRAW_ENTRY.getValue().equals(warehouseApplicationInfo.getBusinessType())) {
            return this.warehouseAppToDetailWithoutOrder(warehouseApplicationInfo);
        }
        //这里,临床三院的入库单数据结构改了,多个订单对应一个入库单, 订单要改成批量 modified by zhongyulei 2021/11/22
        if (EntryBusinessTypeEnum.SCAN_CODE_ENTRY.getValue().equals(warehouseApplicationInfo.getBusinessType()) || EntryBusinessTypeEnum.PHOTO_ENTRY.getValue().equals(warehouseApplicationInfo.getBusinessType())) {
            return this.getWarehouseApplicationOneToMany(warehouseApplicationInfo, sessionUser);
        } else {
            return this.getWarehouseApplicationOneToOne(warehouseApplicationInfo, sessionUser);
        }
    }

    /**
     * 从申领单来源的入库单详情，完全与订单隔离
     * @param input
     * @return
     */
    private WarehouseApplicationDetailVO warehouseAppToDetailWithoutOrder(BizWarehouseEntryDTO input) {
        WarehouseApplicationDetailVO res = new WarehouseApplicationDetailVO();
        // 基础
        res.setWarehouseApplicationId(input.getId());
        res.setWarehouseApplicationNo(input.getEntryNo());
        res.setWarehouseApplicationTime(input.getCreateTime().getTime());
        res.setWarehouseApplicant(input.getApplyUserName());
        res.setWarehouseId(input.getRoomId());
        res.setWarehouseName(input.getRoomName());
        res.setApprovalStatus(input.getApprovalStatus());
        res.setBusinessType(input.getBusinessType());
        res.setOrderNo(input.getOrderNo());
        res.setRecommit(input.isRecommit());
        res.setFirstReverter(input.getFirstReverter());
        res.setSecondReverter(input.getSecondReverter());
        String approvalStatusName = "";
        if (Objects.equals(0, input.getApprovalStatus())) {
            approvalStatusName = "审批中";
        } else if (Objects.equals(1, input.getApprovalStatus())) {
            approvalStatusName = "审批通过";
        } else if (Objects.equals(2, input.getApprovalStatus())) {
            approvalStatusName = "审批驳回";
        }
        res.setApprovalStatusName(approvalStatusName);
        res.setStatus(input.getStatus());
        res.setStatusName(input.getStatus() == null ? null : InboundStatus.valueOf(input.getStatus()).getDesc());
        res.setDepartmentName(input.getDeptName());
        res.setAcceptor(input.getApplyUserName());
        res.setPurchaserName(input.getPurchaseUserName());
        res.setRemark(input.getRemark());
        res.setPushStatus(input.getPushStatus());
        // 找采购人的电话
        if (StringUtils.isNotBlank(input.getPurchaseUserGuid())) {
            UserBaseInfoDTO purchaser = userClient.getUserBaseInfoByGuid(input.getPurchaseUserGuid());
            res.setPurchaserPhone(purchaser.getMobile());
        }
        // 商品
        List<BizWarehouseEntryDetailDTO> detailDTOList = input.getDetailDTOList();
        List<WarehouseProductInfoVO> warehouseProductInfoVOList = New.list();
        List<EntryDangerousDTO> entryDangerousDTOList = bizWareHouseClient.queryEntryDangerous(null, input.getId(), input.getOrgId());
        Map<Integer, Boolean> detailIdDangerousFlagMap = DictionaryUtils.toMap(entryDangerousDTOList, EntryDangerousDTO::getEntryDetailId, EntryDangerousDTO::isDangerous);
        for (BizWarehouseEntryDetailDTO detail : detailDTOList) {
            WarehouseProductInfoVO productInfoVO = new WarehouseProductInfoVO();
            productInfoVO.setSupplierId(detail.getSuppId());
            productInfoVO.setProductName(detail.getProductName());
            productInfoVO.setSpecifications(detail.getSpecifications());
            productInfoVO.setBrand(detail.getBrandName());
            productInfoVO.setDangerousType(detail.getDangerousType());
            productInfoVO.setDangerousTypeName(detail.getDangerousType() == null ? null : DangerousTypeEnum.get(detail.getDangerousType()).getName());
            productInfoVO.setUnit(detail.getReceivedUnit());
            productInfoVO.setQuantity(detail.getReceivedNum());
            productInfoVO.setSupplierName(detail.getSuppName());
            productInfoVO.setGoodCode(detail.getProductCode());
            productInfoVO.setCasNo(detail.getCasNo());
            productInfoVO.setQuantityUnit(detail.getMeasurementUnit());
            productInfoVO.setTotalQuantity(detail.getMeasurementNum().doubleValue());
            productInfoVO.setSinglePrice(detail.getUnitPrice().toString());
            productInfoVO.setTotalPrice(detail.getPrice().toString());
            productInfoVO.setCategoryId(detail.getCategoryId());
            productInfoVO.setRegulatoryFlag(detail.getControlFlag());
            productInfoVO.setForm(detail.getForm());
            productInfoVO.setProductPhoto(detail.getFpicpath());
            productInfoVO.setNeedShowDangerousInputFlag(Boolean.TRUE.equals(detailIdDangerousFlagMap.get(detail.getId())) ? NEED_SHOW_DANGEROUS_INPUT_TRUE : NEED_SHOW_DANGEROUS_INPUT_FALSE);
            warehouseProductInfoVOList.add(productInfoVO);
        }
        res.setWarehouseProductInfoVOList(warehouseProductInfoVOList);
        // 收货照片
        res.setReceivedPictures(Arrays.asList(input.getReceivePicUrls().split(";")));
        // 日志
        List<BizWarehouseEntryLogDTO> logDTOList = input.getLogDTOList();
        List<WarehouseOperationLogVO> resLogList = New.list();
        for (BizWarehouseEntryLogDTO inputLog : logDTOList) {
            WarehouseOperationLogVO resLog = new WarehouseOperationLogVO();
            resLog.setOperator(inputLog.getUserName());
            resLog.setOperation(inputLog.getBusinessDesc());
            resLog.setOperationTime(inputLog.getCreateTime().getTime());
            resLog.setComment(inputLog.getRemark());
            resLog.setApprovalOpinion(inputLog.getRemark());
            resLog.setSign(inputLog.getSign());
            resLog.setSignPhoto(inputLog.getSignPhoto());
            resLogList.add(resLog);
        }
        res.setOperationLogVOList(resLogList);

        //是否一物一码
        boolean needEachProductEachCode = detailDTOList.stream()
                .map(BizWarehouseEntryDetailDTO::getBarCodeList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .anyMatch(orderUniqueBarCodeDTO -> StringUtils.isNotEmpty(orderUniqueBarCodeDTO.getBarCode()));

        res.setEachProductEachCode(needEachProductEachCode);

        return res;
    }

    /**
     * 一对多，入库单与订单，查看逻辑
     * @param warehouseApplicationInfo
     * @param sessionUser
     * @return
     */
    private WarehouseApplicationDetailVO getWarehouseApplicationOneToMany(BizWarehouseEntryDTO warehouseApplicationInfo, RjSessionInfo sessionUser) {
        List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOList = warehouseApplicationInfo.getDetailDTOList();
        //根据入库单的订单编号查订单(含验收图片但不含订单详情列表)
        List<String> orderNoList = bizWarehouseEntryDetailDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getOrderNo())).map(BizWarehouseEntryDetailDTO::getOrderNo).collect(Collectors.toList());
        Preconditions.notEmpty(orderNoList, "查看失败，入库单：" + warehouseApplicationInfo.getEntryNo() + "对应的订单号为空");
        List<OrderMasterDO> queryResult = orderMasterMapper.findByFordernoIn(orderNoList);
        List<OrderMasterDTO> orderList = queryResult.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderList)) {
            return new WarehouseApplicationDetailVO();
        }
        OrderMasterDTO orderMasterDTO = orderList.get(0);
        this.validateUserViewAccessInOrganization(sessionUser.getUserId().intValue(), orderMasterDTO.getFuserid());
        //根据订单Id查订单（含订单详情列表但不含验收图片）
        List<OrderMasterSearchDTO> searchDTOList = orderSearchBoostService.searchOrderByIdList(orderList.stream().map(OrderMasterDTO::getId).collect(Collectors.toList()), SortOrderEnum.DESC);
        List<OrderDetailSearchDTO> detailSearchDTOList = searchDTOList.stream().map(OrderMasterSearchDTO::getOrderDetail).flatMap(List::stream).collect(Collectors.toList());
        //入库单Bean
        WarehouseApplicationBean warehouseApplicationBean = this.getWarehouseApplicationBean(warehouseApplicationInfo, detailSearchDTOList, orderMasterDTO.getFusercode(), orderMasterDTO.getFuserid());
        OrderBean orderBean = this.getOrderBean(orderMasterDTO, searchDTOList.get(0));
        // 针对一对多单据的特殊处理，屏蔽订单号、采购部门、采购人、采购人电话
        orderBean.setOrderNo("-");
        orderBean.setDepartmentName("-");
        orderBean.setPurchaserName("-");
        orderBean.setPurchaserPhone("-");
        WarehouseApplicationDetailVO result = this.getWarehouseApplicationDetailVO(warehouseApplicationBean, orderBean);
        result.setPushStatus(warehouseApplicationInfo.getPushStatus());
        //如果入库单有对应的即入即出的出库单，也返回
        BizWarehouseExitDTO bizWarehouseExitDTO = this.getImmediatelyOutWarehouseApplication(warehouseApplicationInfo.getExitId(), warehouseApplicationInfo.getOrderNo(), warehouseApplicationInfo.getRoomId());
        if (bizWarehouseExitDTO != null) {
            OutWarehouseApplicationBean outWarehouseApplicationBean = outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(bizWarehouseExitDTO, detailSearchDTOList, orderMasterDTO.getFusercode());
            OutWarehouseApplicationDetailVO outWarehouseApplicationDetailVO = outWarehouseGWService.getImmediatelyOutWarehouseApplicationDetail(outWarehouseApplicationBean, orderBean);
            this.populateInWarehouseApprover(outWarehouseApplicationDetailVO, warehouseApplicationBean);
            result.setImmediatelyOutWarehouseApplicationVO(outWarehouseApplicationDetailVO);
        }
        long orderCount = bizWarehouseEntryDetailDTOList.stream().map(BizWarehouseEntryDetailDTO::getOrderNo).distinct().count();
        // 1对1时, 正常显示部门信息
        if (orderCount == 1) {
            result.setDepartmentName(Optional.ofNullable(bizWarehouseEntryDetailDTOList).map(it -> it.get(0)).map(BizWarehouseEntryDetailDTO::getDeptName).orElse(StringUtils.EMPTY));
        } else {
            result.setDepartmentName(null);
            result.setPurchaserName(null);
            result.setPurchaserPhone(null);
        }
        // 扫码入库 和 拍照入库 一定有二维码
        result.setEachProductEachCode(true);
        return result;
    }

    /**
     * 一对一，入库单与订单，查看逻辑
     * @param warehouseApplicationInfo
     * @return
     */
    private WarehouseApplicationDetailVO getWarehouseApplicationOneToOne(BizWarehouseEntryDTO warehouseApplicationInfo, RjSessionInfo sessionUser) {
        //根据入库单的订单编号查订单(含验收图片但不含订单详情列表)
        OrderMasterDTO orderMasterDTO = getNotNullOrderMasterByOrderNo(warehouseApplicationInfo.getOrderNo());
        this.validateUserViewAccessInOrganization(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), orderMasterDTO.getFuserid());
        //根据订单Id查订单（含订单详情列表但不含验收图片）
        OrderMasterSearchDTO orderMasterSearchDTO = getNotNullSearchOrder(orderMasterDTO.getId());
        //入库单Bean
        WarehouseApplicationBean warehouseApplicationBean = this.getWarehouseApplicationBean(warehouseApplicationInfo, orderMasterSearchDTO.getOrderDetail(), orderMasterDTO.getFusercode(), orderMasterDTO.getFuserid());
        //订单Bean
        OrderBean orderBean = this.getOrderBean(orderMasterDTO, orderMasterSearchDTO);
        WarehouseApplicationDetailVO result = this.getWarehouseApplicationDetailVOWithRoom(warehouseApplicationBean, orderBean, orderMasterDTO);
        //如果入库单有对应的即入即出的出库单，也返回
        BizWarehouseExitDTO bizWarehouseExitDTO = this.getImmediatelyOutWarehouseApplication(warehouseApplicationInfo.getExitId(), warehouseApplicationInfo.getOrderNo(), warehouseApplicationInfo.getRoomId());
        if (bizWarehouseExitDTO != null) {
            OutWarehouseApplicationBean outWarehouseApplicationBean = outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(bizWarehouseExitDTO, orderMasterSearchDTO.getOrderDetail(), orderMasterDTO.getFusercode());
            OutWarehouseApplicationDetailVO outWarehouseApplicationDetailVO = outWarehouseGWService.getImmediatelyOutWarehouseApplicationDetail(outWarehouseApplicationBean, orderBean);
            this.populateInWarehouseApprover(outWarehouseApplicationDetailVO, warehouseApplicationBean);
            result.setImmediatelyOutWarehouseApplicationVO(outWarehouseApplicationDetailVO);
            if(OrgEnum.HE_BEI_SHENG_REN_MIN_YI_YUAN.getValue() == sessionUser.getOrgId(RjUserTypeEnum.STORE_USER)){
                // http://chandao.rj-info.com/story-view-2380.html?tid=3d6sr9wm
                result.getImmediatelyOutWarehouseApplicationVO().setOutWarehouseTime(result.getWarehouseApplicationTime() + 24 * 60 * 60 * 1000);
            }
        }
        // 获取最后一次订单验收审批通过日志（带电子签名)
        result.setOrderApprovalLogs(this.getLastPassOrderApprovalLogVoWithESign(orderMasterDTO));
        // 获取单位logo
        OrganizationDTO organizationDTO = organizationClient.findByIdWithCache(orderMasterDTO.getFuserid());
        result.setOrgLogo(Objects.nonNull(organizationDTO) ? organizationDTO.getTransparentLogo() : null);
        //眼科的发票，从库房系统取的
        if (OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getValue() == sessionUser.getOrgId(RjUserTypeEnum.STORE_USER)) {
            result.setInvoiceNoList(warehouseApplicationInfo.getInvoiceNos());
        }

        if (OrgEnum.AN_HUI_SHENG_LI_YI_YUAN.getValue() == sessionUser.getOrgId(RjUserTypeEnum.STORE_USER)) {
            // 中国科学技术大学附属第一医院（安徽省立医院）需求，返回推送数据和经费卡的校区数据给前端
            result.setDockingInfo(WareHouseDockingDataTranslator.ahslDockingDtoToWareHouseDataDto((AHSLDockIngDTO) warehouseApplicationInfo.getDockingInfo()));
        }
        result.setFundCard(this.getOrderFundCardVOFromSearchDTO(orderMasterSearchDTO));
        result.setPushStatus(warehouseApplicationInfo.getPushStatus());

        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderMasterDTO.getId()), New.list(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue(), OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue()));
        Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        result.setEachProductEachCode(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue())));
        result.setSuppNeedFillBatchesData(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue())));
        return result;
    }

    @Override
    public WarehouseConstantListVO getWarehouseConstantList() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        WarehouseConstantListVO result = new WarehouseConstantListVO();
        List<MiniDepartmentDTO> departmentsForUser = departmentRpcClient.getDeptTreeForUserByAccess(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(), WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
        if (CollectionUtils.isNotEmpty(departmentsForUser)) {
            this.populateDepartmentList(result, departmentsForUser);
            this.populateWarehouseList(result, departmentsForUser);
        }
        //封装入库状态
        List<WarehouseStatusVO> warehouseStatusVOList = new ArrayList<>();
        for (InboundStatus inboundStatus : InboundStatus.values()) {
            WarehouseStatusVO warehouseStatusVO = new WarehouseStatusVO();
            warehouseStatusVO.setStatus(inboundStatus.getValue());
            warehouseStatusVO.setStatusName(inboundStatus.getDesc());
            warehouseStatusVOList.add(warehouseStatusVO);
        }
        result.setWarehouseStatusVOList(warehouseStatusVOList);
        //审批状态
        List<WarehouseApprovalStatusVO> warehouseApprovalStatusVOList = new ArrayList<>();
        for (ApprovalTaskStatusEnum approvalTaskStatusEnum : ApprovalTaskStatusEnum.values()) {
            WarehouseApprovalStatusVO warehouseApprovalStatusVO = new WarehouseApprovalStatusVO();
            warehouseApprovalStatusVO.setApprovalStatus(approvalTaskStatusEnum.getValue());
            warehouseApprovalStatusVO.setApprovalStatusName(approvalTaskStatusEnum.getDesc());
            warehouseApprovalStatusVOList.add(warehouseApprovalStatusVO);
        }
        result.setWarehouseApprovalStatusVOList(warehouseApprovalStatusVOList);
        return result;
    }

    @Override
    public CountWarehouseApplicationVO getPersonalWarehouseApplicationCount() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        //获取各入库状态的入库单数量分布
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setApplyUserGuid(sessionUser.getGuid());
        bizWarehouseEntryDTO.setOrgId(sessionUser.getOrgId());
        CountDTO countDTO = bizWareHouseClient.getCount(bizWarehouseEntryDTO);
        BusinessErrUtil.notNull(countDTO, ExecptionMessageEnum.UNABLE_TO_OBTAIN_INVENTORY_STATUS_DISTRIBUTION);
        CountWarehouseApplicationVO result = this.countDTO2CountWarehouseApplicationVO(countDTO);

        //获取各审批状态的入库单数量分布
        WmsEntryApprovalQuery wmsEntryApprovalQuery = new WmsEntryApprovalQuery();
        wmsEntryApprovalQuery.setApplyUserGuid(sessionUser.getGuid());
        wmsEntryApprovalQuery.setBusinessType(EntryBusinessTypeEnum.STOREENTRY.getValue());
        wmsEntryApprovalQuery.setOrgId(sessionUser.getOrgId());
        ApprovalCountDTO approvalCountDTO = bizWareHouseClient.selectApprovalCount(wmsEntryApprovalQuery);
        BusinessErrUtil.notNull(approvalCountDTO, ExecptionMessageEnum.UNABLE_TO_OBTAIN_APPROVAL_STATUS_DISTRIBUTION);
        this.populateApprovalStatusCount(result, approvalCountDTO);
        return result;
    }

    @Override
    public CountWarehouseApplicationVO getDepartmentWarehouseApplicationCount() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setOrgId(sessionUser.getOrgId());
        List<MiniDepartmentDTO> departmentsForUser = departmentRpcClient.getDeptTreeForUserByAccess(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), sessionUser.getOrgId(), WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
        BusinessErrUtil.notEmpty(departmentsForUser, ExecptionMessageEnum.USER_NO_GROUP_BINDING_QUANTITY);
        bizWarehouseEntryDTO.setDeptIdList(departmentsForUser.stream().map(MiniDepartmentDTO::getId).collect(Collectors.toList()));
        CountDTO countDTO = bizWareHouseClient.getCount(bizWarehouseEntryDTO);
        BusinessErrUtil.notNull(countDTO, ExecptionMessageEnum.UNABLE_TO_OBTAIN_INVENTORY_APPLICATION_DISTRIBUTION);
        return this.countDTO2CountWarehouseApplicationVO(countDTO);
    }

    @Override
    public boolean isUseNewWarehouseSystem() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(sessionUser.getOrgId());
        BusinessErrUtil.notNull(simpleOrgDTO, ExecptionMessageEnum.CURRENT_UNIT_NOT_FOUND, sessionUser.getOrgId());
        return this.isUseNewWarehouseSystem(simpleOrgDTO.getCode());
    }

    @Override
    public InAndOutWarehouseApplicationDetailVO getInAndOutWarehouseInfoForOrder(OrderRequestVO request) {
        Preconditions.hasText(request.getOrderNo(), "入参不完整,订单号不能为空");
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        List<BizWarehouseEntryDTO> warehouseApplicationInfoList = this.getBizWarehouseEntryDTOListByOrderNo(request.getOrderNo());
        // 根据订单号查找出库单列表
        List<BizWarehouseExitDTO> outWarehouseApplicationInfoList = bizExitServiceClient.queryExitByOrderNo(request.getOrderNo());
        // 撤销入库需求——单据打印去除已撤销、撤销中、未入库的出入库单据；
        if (CollectionUtils.isNotEmpty(warehouseApplicationInfoList)) {
            warehouseApplicationInfoList = warehouseApplicationInfoList.stream().filter(s -> (!InboundStatus.HAVE_BEEN_WITHDRAW.getValue().equals(s.getStatus()))
                    &&(!InboundStatus.HAVING_WITHDRAW.getValue().equals(s.getStatus()))
                    &&(!InboundStatus.NOTINSTORAGE.getValue().equals(s.getStatus()))).collect(toList());
        }
        List<Integer> validExitIdList = warehouseApplicationInfoList.stream().map(BizWarehouseEntryDTO::getExitId).collect(toList());
        if (CollectionUtils.isNotEmpty(outWarehouseApplicationInfoList)) {
            outWarehouseApplicationInfoList = outWarehouseApplicationInfoList.stream().filter(s -> validExitIdList.contains(s.getId())).collect(toList());
        }

        //根据订单编号查订单信息(含验收图片但不含订单详情列表)
        OrderMasterDTO orderMasterDTO = this.getNotNullOrderMasterByOrderNo(request.getOrderNo());

        // 采购与竞价审批日志
        CompletableFuture<List<WarehouseApprovalLogVO>> whPurchaseApproveLogFuture = AsyncExecutor.callAsync(() -> {
            OrderMasterDO order = new OrderMasterDO();
            order.setFtbuyappid(orderMasterDTO.getFtbuyappid());
            order.setBidOrderId(orderMasterDTO.getBidOrderId());
            order.setForderno(orderMasterDTO.getForderno());
            order.setFusercode(orderMasterDTO.getFusercode());
            List<OrderPurchaseApprovalLogDTO> purchaseRelatedApproveLogList = orderManageRpcService.getApproveLogInfo(order);
            return WarehouseApproveLogTranslator.toPurchaseApproveLogs(purchaseRelatedApproveLogList);
        });

        // 获取最后一次订单验收审批通过日志（带电子签名）
        CompletableFuture<List<WarehouseApprovalLogVO>> whOrderApproveLogFuture = AsyncExecutor.callAsync(() -> this.getLastPassOrderApprovalLogVoWithESign(orderMasterDTO));

        //验证权限
        this.validateUserViewAccessInOrganization(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), orderMasterDTO.getFuserid());
        //根据订单Id查订单信息（含订单详情列表但不含验收图片）
        OrderMasterSearchDTO orderMasterSearchDTO = this.getNotNullSearchOrder(orderMasterDTO.getId());

        OrderBean orderBean = this.getOrderBean(orderMasterDTO, orderMasterSearchDTO);
        // 获取异步审批日志结果
        List<WarehouseApprovalLogVO> orderApproveList = New.list();
        List<WarehouseApprovalLogVO> purchaseApproveList = New.list();
        try {
            orderApproveList = whOrderApproveLogFuture.get();
            purchaseApproveList = whPurchaseApproveLogFuture.get();
        } catch (Exception e) {
            Cat.logError(CAT_TYPE, "getInAndOutWarehouseInfoForOrder",e.getMessage(), e);
        }
        
        // 获取电子签名
        List<ElectronicSignDataVO> electronicSignDataVOList = new ArrayList<>(4);
        // 山东省省立 需求，返回订单所属采购单一级审批人电子签名图片（竞价单取一级初审电子签名图片）和订单验收审批人电子签名图片
        if (Objects.equals(OrgEnum.SHANG_DONG_SHENG_YI.getValue(), orderMasterDTO.getFuserid())) {
            List<OrderMasterDTO> orderMasterDTOList = New.list(orderMasterDTO);
            if(OrderTypeEnum.BID_ORDER.getCode().equals(orderMasterDTO.getOrderType())){
                electronicSignDataVOList.addAll(this.getElectronicSignDataList(orderMasterDTOList, BusinessTypeEnum.BID, New.list(ElectronicSignatureOperationEnum.BID_INITIAL_AUDIT)));    
            }else{
                electronicSignDataVOList.addAll(this.getElectronicSignDataList(orderMasterDTOList, BusinessTypeEnum.APPLICATION, New.list(ElectronicSignatureOperationEnum.PURCHASING_APPROVAL)));
            }
            electronicSignDataVOList.addAll(this.getElectronicSignDataList(orderMasterDTOList, BusinessTypeEnum.ORDER, New.list(ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL)));
        }
        // 合肥第二人民医院，电子签名取入库审批人
        if (Objects.equals(OrgEnum.HE_FEI_SHI_DI_ER_REN_MIN_YI_YUAN.getValue(), orderMasterDTO.getFuserid())) {
            List<String> warehouseApplyIdList = warehouseApplicationInfoList.stream().filter(s -> s != null && s.getId() != null)
                    .map(s -> s.getId().toString()).collect(Collectors.toList());
            OperationListDTO operationListDTO = new OperationListDTO();
            operationListDTO.setBusinessIdList(warehouseApplyIdList);
            operationListDTO.setBusinessType(BusinessTypeEnum.IN_BOUND);
            operationListDTO.setOperation(New.list(ElectronicSignatureOperationEnum.WAREHOUSE_INITIAL_AUDIT));
            List<ElectronicSignOperationRecordDTO> electronicSignList = electronicSignServiceClient.getElectronicSignData(operationListDTO);

            List<ElectronicSignDataVO> elecSignVOList = electronicSignList.stream().map(item -> {
                ElectronicSignDataVO electronicSignDataVO = new ElectronicSignDataVO();
                electronicSignDataVO.setType(Integer.parseInt(item.getOperation()));
                electronicSignDataVO.setUrl(item.getSignPhoto());
                return electronicSignDataVO;
            }).collect(Collectors.toList());
            electronicSignDataVOList.addAll(elecSignVOList);
        }
        // 展示验收审批日志的单位
        final Set<Integer> orgIdSetShowAcceptApproveLog = New.set(OrgEnum.SHAN_DONG_SHENG_ZHONG_YI_YUAN.getValue(), 176);
        //封装数据返回

        List<OrderFundcardVO> orderFundcardVOList = this.getFundCardVO(orderMasterSearchDTO, orderMasterDTO.getFusercode());

        List<WarehouseApplicationDetailVO> warehouseApplicationDetailVOList = new ArrayList<>();
        Map<Integer, BizWarehouseEntryDTO> exitIdEntryMap = warehouseApplicationInfoList.stream().filter(item->item.getExitId() != null).collect(Collectors.toMap(BizWarehouseEntryDTO::getExitId, item->item, (o,n)->n));
        for (BizWarehouseEntryDTO bizWarehouseEntryDTO : warehouseApplicationInfoList) {
            if (InboundStatus.WAREHOUSING.getValue().equals(bizWarehouseEntryDTO.getStatus())) {
                WarehouseApplicationBean warehouseApplicationBean = this.getWarehouseApplicationBean(bizWarehouseEntryDTO, orderMasterSearchDTO.getOrderDetail(), orderMasterDTO.getFusercode(), orderMasterDTO.getFuserid());
                WarehouseApplicationDetailVO warehouseApplicationDetailVO = this.getWarehouseApplicationDetailVO(warehouseApplicationBean, orderBean);
                // 获取单位logo
                OrganizationDTO organizationDTO = organizationClient.findByIdWithCache(orderMasterDTO.getFuserid());
                warehouseApplicationDetailVO.setOrgLogo(Objects.nonNull(organizationDTO) ? organizationDTO.getTransparentLogo() : null);
                // 山东省中医院89的个性化
                if (orgIdSetShowAcceptApproveLog.contains(orderMasterDTO.getFuserid())) {
                    warehouseApplicationDetailVO.setOrderApprovalLogs(orderApproveList);
                }
                if (OrgEnum.SHAN_DONG_SHENG_ZHONG_YI_YUAN.getValue() == orderMasterDTO.getFuserid()) {
                    warehouseApplicationDetailVO.setPurchaseApprovalLogs(purchaseApproveList);
                }
                warehouseApplicationDetailVO.setFundCard(orderFundcardVOList);
                warehouseApplicationDetailVO.setElectronicSignDataVOList(electronicSignDataVOList);
                warehouseApplicationDetailVOList.add(warehouseApplicationDetailVO);
            }
        }
        List<OutWarehouseApplicationDetailVO> outWarehouseApplicationDetailVOList = new ArrayList<>();
        for (BizWarehouseExitDTO bizWarehouseExitDTO : outWarehouseApplicationInfoList) {
            //出库单状态（状态0未出库，1已出库）
            if (bizWarehouseExitDTO.getStatus() == 1) {
                OutWarehouseApplicationBean outWarehouseApplicationBean = outWarehouseGWService.getImmediatelyOutWarehouseApplicationBean(bizWarehouseExitDTO, orderMasterSearchDTO.getOrderDetail(), orderMasterDTO.getFusercode());
                OutWarehouseApplicationDetailVO outWarehouseApplicationDetailVO = outWarehouseGWService.getImmediatelyOutWarehouseApplicationDetail(outWarehouseApplicationBean, orderBean);
                // 山东省中医院89的个性化
                if (Objects.equals(OrgEnum.SHAN_DONG_SHENG_ZHONG_YI_YUAN.getValue(), orderMasterDTO.getFuserid())) {
                    outWarehouseApplicationDetailVO.setOrderApprovalLogs(orderApproveList);
                    outWarehouseApplicationDetailVO.setPurchaseApprovalLogs(purchaseApproveList);
                }
                outWarehouseApplicationDetailVO.setElectronicSignDataVOList(electronicSignDataVOList);
                if(OrgEnum.HE_BEI_SHENG_REN_MIN_YI_YUAN.getValue() == orderMasterDTO.getFuserid() && exitIdEntryMap.get(bizWarehouseExitDTO.getId()) != null){
                    // http://chandao.rj-info.com/story-view-2380.html?tid=3d6sr9wm
                    outWarehouseApplicationDetailVO.setOutWarehouseTime(exitIdEntryMap.get(bizWarehouseExitDTO.getId()).getCreateTime().getTime() + 24 * 60 * 60 * 1000);
                }
                outWarehouseApplicationDetailVOList.add(outWarehouseApplicationDetailVO);
            }
        }
        if(orderMasterDTO.getFuserid() == OrgEnum.SHANG_HAI_JIAO_DA_FU_SHU_JIU_YUAN.getValue()){
            // 上海九院定制需求，出库单增加出库科室
            DepartmentDTO departmentDTO = userClient.getDepartmentInfo(orderMasterDTO.getFbuydepartmentid());
            List<UserExtendInfoDTO> userExtendInfoDTOList = userExtendInfoRpcClient.findByUserIdList(New.list(departmentDTO.getManagerId()), orderMasterDTO.getFuserid(), UserExtendInfoTypeEnum.OA_DEPARTMENT);
            if(CollectionUtils.isNotEmpty(userExtendInfoDTOList)){
                outWarehouseApplicationDetailVOList.forEach(item->item.setExitDeptName(userExtendInfoDTOList.get(0).getInfo()));
            }
        }
        this.populateInWarehouseApprover(outWarehouseApplicationDetailVOList, warehouseApplicationDetailVOList);

        InAndOutWarehouseApplicationDetailVO inAndOutWarehouseApplicationDetailVO = new InAndOutWarehouseApplicationDetailVO();
        inAndOutWarehouseApplicationDetailVO.setWarehouseApplicationVOList(warehouseApplicationDetailVOList);
        inAndOutWarehouseApplicationDetailVO.setOutWarehouseApplicationVoList(outWarehouseApplicationDetailVOList);
        return inAndOutWarehouseApplicationDetailVO;
    }

    @Override
    public boolean haveWarehouseAccess() {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setApplyUserGuid(sessionUser.getGuid());
        bizWarehouseEntryDTO.setOrgId(sessionUser.getOrgId());
        return bizWareHouseClient.haveAccess(bizWarehouseEntryDTO);
    }

    @Override
    public boolean checkCanSubmitWarehouse(OrderRequestVO request) {
        RjSessionInfo sessionUser = GateWayMessageUtil.validateAndGetStoreUser();
        OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(sessionUser.getOrgId());
        BusinessErrUtil.notNull(simpleOrgDTO, ExecptionMessageEnum.CURRENT_UNIT_NOT_FOUND, sessionUser.getOrgId());
        boolean isUseNewWarehouseSystem = this.isUseNewWarehouseSystem(simpleOrgDTO.getCode());
        if (!isUseNewWarehouseSystem) {
            return false;
        }
        OrderMasterDTO orderMasterDTO;
        if (StringUtils.isNotBlank(request.getOrderNo())) {
            orderMasterDTO = this.getNotNullOrderMasterByOrderNo(request.getOrderNo());
        } else if (request.getOrderId() != null) {
            orderMasterDTO = this.getNotNullOrderMasterById(request.getOrderId());
        } else {
            throw new IllegalArgumentException("订单号或订单Id不能为空");
        }
        //要有权限的人才能提交入库
        try {
            this.validateUserSubmitAccessInDepartment(sessionUser.getUserId(RjUserTypeEnum.STORE_USER).intValue(), orderMasterDTO.getFbuydepartmentid(), orderMasterDTO.getFuserid());
            validateCanSubmitWarehouseApplication(orderMasterDTO.getForderno(), orderMasterDTO.getStatus(), orderMasterDTO.getInventoryStatus().intValue(), orderMasterDTO.getFuserid());
        } catch (IllegalStateException | BusinessInterceptException e) {
            LOGGER.info("订单号：{}，对应用户id:{}不能提交入库，原因：{}", orderMasterDTO.getForderno(), sessionUser.getUserId(RjUserTypeEnum.STORE_USER), e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public WarehouseProductInfoVO checkProductHasInWarehouse(WarehouseProductRequestVO request) {
        Preconditions.notNull(request.getSupplierId(), "供应商Id不能为空");
        Preconditions.notNull(request.getWarehouseId(), "库房Id不能为空");
        if (request.getGoodCode() == null && request.getCasNo() == null) {
            return null;
        }
        // 判断商品是否已经在库存中存在，规则是：供应商Id和货号相同 或 CAS号相同。每个条件都封装一个入参查询参数，则作为查询条件
        List<StockMultiQueryDTO> queryParamList = New.list();
        StockMultiQueryDTO param = new StockMultiQueryDTO();
        param.setRoomId(request.getWarehouseId());
        param.setSuppId(request.getSupplierId());
        param.setProductCode(request.getGoodCode());
        param.setCasNo(request.getCasNo());
        queryParamList.add(param);

        List<StockDTO> matchDataList = bizWarehouseRoomServiceClient.queryByMultiKey(queryParamList);
        if (CollectionUtils.isEmpty(matchDataList) || matchDataList.get(0) == null) {
            return null;
        }
        return WarehouseProductTranslator.stockDto2ProductInfoVo(matchDataList.get(0));
    }

    private boolean isUseNewWarehouseSystem(String orgCode) {
        List<BaseConfigDTO> baseConfigDTOList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, Lists.newArrayList(WarehouseConstant.USE_WAREHOUSE_SYSTEM_CODE, WarehouseConstant.WAREHOUSE_SYSTEM_VERSION_CODE));
        if (CollectionUtils.isEmpty(baseConfigDTOList)) {
            return false;
        }
        String useWarehouseSystem = null;
        String warehouseSystemVersion = null;
        for (BaseConfigDTO baseConfigDTO : baseConfigDTOList) {
            if (WarehouseConstant.USE_WAREHOUSE_SYSTEM_CODE.equals(baseConfigDTO.getConfigCode())) {
                useWarehouseSystem = baseConfigDTO.getConfigValue();
            } else if (WarehouseConstant.WAREHOUSE_SYSTEM_VERSION_CODE.equals(baseConfigDTO.getConfigCode())) {
                warehouseSystemVersion = baseConfigDTO.getConfigValue();
            }
        }
        if (StringUtils.isNotBlank(useWarehouseSystem) && useWarehouseSystem.equals(WarehouseConstant.USE_WAREHOUSE_SYSTEM_VALUE) && StringUtils.isNotBlank(warehouseSystemVersion) && warehouseSystemVersion.equals(WarehouseConstant.WAREHOUSE_SYSTEM_VERSION_VALUE)) {
            return true;
        }
        return false;
    }

    /**
     * 获取带可入库列表的入库详情数据
     * @param applicationBean
     * @param orderBean
     * @param orderMasterDTO
     * @return
     */
    private WarehouseApplicationDetailVO getWarehouseApplicationDetailVOWithRoom(WarehouseApplicationBean applicationBean, OrderBean orderBean, OrderMasterDTO orderMasterDTO){
        WarehouseApplicationDetailVO warehouseApplicationDetailVO = this.getWarehouseApplicationDetailVO(applicationBean, orderBean);
        List<OrderDetailDTO> orderDetailList = this.getNotEmptyOrderDetailList(orderMasterDTO.getId());
        List<EntryDetailRoomDTO> entryDetailRoomDTOList = bizWareHouseClient.queryEntryDetailRoom(orderDetailList, orderMasterDTO);
        Map<Integer, EntryDetailRoomDTO> detailIdEntryRoomMap = DictionaryUtils.toMap(entryDetailRoomDTOList, EntryDetailRoomDTO::getOrderDetailId, Function.identity());
        Map<Integer, OrderDetailDTO> detailIdDetailMap = DictionaryUtils.toMap(orderDetailList, OrderDetailDTO::getId, Function.identity());
        List<String> gasBottleBarcodes = applicationBean.getProductBeans().stream().map(ProductBean::getBindGasBottleBarcodes).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(toList());
        List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(gasBottleBarcodes);
        Map<String, GasBottleVO> barcodeGasBottleMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
        // 查询采购单占用的商品信息
        Map<Long, BizWarehouseDangerousOccupyStockDetailDTO> productIdOccupyDetailMap;
        if(Objects.nonNull(orderMasterDTO.getFtbuyappid())){
            ApplicationMasterDTO applicationMasterDTO = applicationBaseClient.getApplicationMasterByApplyId(orderMasterDTO.getFtbuyappid(), false);
            List<BizWarehouseDangerousOccupyStockDetailDTO> occupyStockDetailDTOList = bizWareHouseClient.getDangerousOccupyStockDetail(applicationMasterDTO.getApplyNumber());
            productIdOccupyDetailMap = DictionaryUtils.toMap(occupyStockDetailDTOList, BizWarehouseDangerousOccupyStockDetailDTO::getProductId, Function.identity());
        } else {
            productIdOccupyDetailMap = New.emptyMap();
        }
        // 设置每个商品详情的入库列表
        warehouseApplicationDetailVO.getWarehouseProductInfoVOList().forEach(warehouseProductInfoVO -> {
            EntryDetailRoomDTO entryDetailRoomDTO = detailIdEntryRoomMap.get(warehouseProductInfoVO.getOrderDetailId());
            OrderDetailDTO orderDetailDTO = detailIdDetailMap.get(warehouseProductInfoVO.getOrderDetailId());
            // 设置是否无须入库
            warehouseProductInfoVO.setNeedSubmitWarehouseTag(entryDetailRoomDTO.isNeedInbound() ? NEED_SUBMIT_WAREHOUSE : NEED_NOT_SUBMIT_WAREHOUSE);
            List<BizWarehouseRoomDTO> roomDTOList = Optional.ofNullable(entryDetailRoomDTO.getRooms()).orElse(New.list());
            List<WarehouseVO> warehouseVOList = roomDTOList.stream().map(room -> new WarehouseVO(room.getId(), room.getRoomName())).collect(toList());
            warehouseProductInfoVO.setWarehouseVOList(warehouseVOList);
            if(orderDetailDTO != null){
                warehouseProductInfoVO.setFirstLevelCategoryId(orderDetailDTO.getFirstCategoryId());
                warehouseProductInfoVO.setFirstLevelCategoryName(orderDetailDTO.getFirstCategoryName());
            }
            // 如果有预占，填充预占数据
            BizWarehouseDangerousOccupyStockDetailDTO occupyStockDetailDTO = productIdOccupyDetailMap.get(warehouseProductInfoVO.getProductId());
            if (Objects.nonNull(occupyStockDetailDTO)) {
                warehouseProductInfoVO.setForm(occupyStockDetailDTO.getForm());
                warehouseProductInfoVO.setDefaultWarehouseId(occupyStockDetailDTO.getRoomId());
                warehouseProductInfoVO.setUnitMeasurementNum(occupyStockDetailDTO.getUnitMeasurementNum());
                warehouseProductInfoVO.setQuantityUnit(occupyStockDetailDTO.getMeasurementUnit());
                warehouseProductInfoVO.setTotalQuantityAfterReturn(
                        BigDecimal.valueOf(warehouseProductInfoVO.getQuantityAfterReturn())
                                .multiply(warehouseProductInfoVO.getUnitMeasurementNum())
                                .doubleValue());
            }
            if(CollectionUtils.isNotEmpty(warehouseProductInfoVO.getBindGasBottleBarcodes())){
                warehouseProductInfoVO.setBindGasBottles(warehouseProductInfoVO.getBindGasBottleBarcodes().stream().map(barcodeGasBottleMap::get).filter(Objects::nonNull).collect(toList()));
            }
        });
        return warehouseApplicationDetailVO;
    }

    private WarehouseApplicationDetailVO getWarehouseApplicationDetailVO(WarehouseApplicationBean applicationBean, OrderBean orderBean) {
        WarehouseApplicationDetailVO receiver = new WarehouseApplicationDetailVO();
        receiver.setOrderNo(orderBean.getOrderNo());
        receiver.setOrderId(orderBean.getOrderId());
        receiver.setOrderType(orderBean.getOrderType());
        receiver.setOrderTotalPrice(orderBean.getTotalPrice());
        receiver.setPurchaserName(orderBean.getPurchaserName());
        receiver.setOrderSpecies(orderBean.getSpecies());
        receiver.setRecommit(applicationBean.getRecommit());
        //根据入库单的订单编号查订单的验收图片、机构名称、供应商编码、供应商名称、采购人联系方式、
        receiver.setReceivedPictures(orderBean.getReceivingPhotos());
        receiver.setOrgName(orderBean.getOrgName());
        receiver.setSupplierCode(orderBean.getSupplierCode());
        receiver.setSupplierName(orderBean.getSupplierName());
        receiver.setAcceptor(orderBean.getAcceptor());
        receiver.setPurchaserPhone(orderBean.getPurchaserPhone());
        //获取入库单对应的订单的经费项目的项目编码、项目名称、经费卡号
        receiver.setProjectCode(orderBean.getProjectCode());
        receiver.setProjectName(orderBean.getProjectName());
        receiver.setFunCardNo(orderBean.getFunCardNo());
        //根据订单对应的采购申请单或竞价单获取二级审批人(别名科长)
        receiver.setSectionChief(orderBean.getSectionChief());
        //根据订单对应部门获取部门负责人
        receiver.setDepartmentDirector(orderBean.getDepartmentDirector());
        //获取订单号对应的条形码
        receiver.setOrderNoBarcode(orderBean.getOrderNoBarcode());
        //获取订单对应的发票信息
        receiver.setInvoiceNo(orderBean.getInvoiceNo());
        receiver.setInvoiceNoList(orderBean.getInvoiceNoList());
        receiver.setInvoiceDateTimeList(orderBean.getInvoiceDateTimeList());
        //封装入库单信息
        receiver.setWarehouseApplicationNo(applicationBean.getWarehouseApplicationNo());
        receiver.setWarehouseApplicationId(applicationBean.getWarehouseApplicationId());
        receiver.setApprovalStatus(applicationBean.getApprovalStatus());
        receiver.setApprovalStatusName(applicationBean.getApprovalStatusName());
        receiver.setStatus(applicationBean.getStatus());
        receiver.setStatusName(applicationBean.getStatusName());
        receiver.setWarehouseApplicant(applicationBean.getWarehouseApplicant());
        receiver.setWarehouseApplicationTime(applicationBean.getWarehouseApplicationDate() == null ? null : applicationBean.getWarehouseApplicationDate().getTime());
        receiver.setWarehouseId(applicationBean.getWarehouseId());
        receiver.setWarehouseName(applicationBean.getWarehouseName());
        receiver.setInWarehouseTime(applicationBean.getInWarehouseDate() == null ? null : applicationBean.getInWarehouseDate().getTime());
        receiver.setRemark(applicationBean.getRemark());
        receiver.setInWarehousePictureUrlList(applicationBean.getInWarehousePictureUrlList());
        //部门
        receiver.setDepartmentName(applicationBean.getDepartmentName());
        receiver.setDepartmentParentName(orderBean.getDepartmentParentName());
        //提取申请单相关日志信息
        receiver.setOperationLogVOList(WarehouseApplicationBeanTranslator.warehouseOperationLogBeanList2WarehouseOperationLogVOList(applicationBean.getOperationLogBeans()));
        //提取可选择的库房列表返回给前端
        receiver.setWarehouseVOList(WarehouseApplicationBeanTranslator.warehouseBeanList2WarehouseVOList(orderBean.getWarehousesCanBeChosenByDepartment()));
        //获取入库审批进度列表
        receiver.setApprovalProgressVOList(WarehouseApplicationBeanTranslator.approvalProgressBeanList2ApprovalProgressVOList(applicationBean.getApprovalProgressBeans()));
        //获取入库审核人和审核时间
        receiver.setApproverName(applicationBean.getApproverName());
        receiver.setApprovalTimeString(applicationBean.getApprovalTimeString());
        //获取入库单号对应条形码
        receiver.setEntryNoBarcode(applicationBean.getEntryNoBarcode());
        //提取申请单相关商品信息
        receiver.setWarehouseProductInfoVOList(applicationBean.getProductBeans().stream().map(ProductBeanTranslator::productBean2WarehouseProductInfoVO).collect(Collectors.toList()));
        receiver.setTotalPrice(PriceUtil.formatDouble2TwoDecimal(applicationBean.getTotalPrice()));
        receiver.setTotalPriceInChinese(PriceUtil.convert(String.valueOf(applicationBean.getTotalPrice())));
        receiver.setBusinessType(applicationBean.getBusinessType());
        receiver.setDocCreator(applicationBean.getDocCreator());
        receiver.setPickingDept(applicationBean.getPickingDept());
        receiver.setReceiveWarehouse(applicationBean.getReceiveWarehouse());
        //  第一第二归还人
        receiver.setFirstReverter(applicationBean.getFirstReverter());
        receiver.setSecondReverter(applicationBean.getSecondReverter());
        return receiver;
    }

    private void populateWarehouseApplicationPrice(WarehouseApplicationBean receiver, List<ProductBean> provider) {
        double totalPriceForWarehouseApplication = 0;
        for (ProductBean productBean : provider) {
            totalPriceForWarehouseApplication += productBean.getTotalPrice();
        }
        receiver.setTotalPrice(totalPriceForWarehouseApplication);
    }

    private void selfPopulateFirstLevelCategory(List<ProductBean> productBeansWithCateGoryId) {
        //获取商品一级分类(个性化)
        List<Integer> categoryIds = productBeansWithCateGoryId.stream().map(ProductBean::getCategoryId).distinct().collect(Collectors.toList());
        List<CategoryDTO> categoryDTOList = categoryServiceClient.getAllCategoryByIds(categoryIds);
        BusinessErrUtil.notEmpty(categoryDTOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_PRODUCT_CATEGORY_INFO);
        for (ProductBean productBean : productBeansWithCateGoryId) {
            CategoryDTO categoryDTO = CategoryUtil.getFirstLevelCategory(productBean.getCategoryId(), categoryDTOList);
            BusinessErrUtil.notNull(categoryDTO, ExecptionMessageEnum.PRIMARY_CATEGORY_INFO_NOT_FOUND, productBean.getCategoryId());
            productBean.setFirstLevelCategoryId(categoryDTO.getId().intValue());
            productBean.setFirstLevelCategoryName(categoryDTO.getName());
        }
    }

    private void populateProductPrice(ProductBean receiver, BizWarehouseEntryDetailDTO entryDetail, double orderDetailRealPrice) {
        //商品单价
        double singleProductPrice = entryDetail.getUnitPrice() != null && entryDetail.getUnitPrice().compareTo(BigDecimal.ZERO) > 0 ? entryDetail.getUnitPrice().doubleValue() : orderDetailRealPrice;
        receiver.setSinglePrice(singleProductPrice);
        //商品总价
        double singleProductTotalPrice = entryDetail.getPrice() != null && entryDetail.getPrice().compareTo(BigDecimal.ZERO) > 0 ? entryDetail.getPrice().doubleValue()
                : singleProductPrice * entryDetail.getReceivedNum() + entryDetail.getRemainderPrice().doubleValue();
        receiver.setTotalPrice(singleProductTotalPrice);
    }

    private String getSectionChief(OrderMasterDTO orderMasterDTO) {
        OrderTypeEnum orderType = OrderTypeEnum.getByCode(orderMasterDTO.getOrderType());
        BusinessErrUtil.notNull(orderType, ExecptionMessageEnum.ORDER_TYPE_ERROR, orderMasterDTO.getOrderType());
        Integer purchaseApplicationId = orderMasterDTO.getFbuyerid();
        //科长(对宁波二院：入库单对应的订单对应的采购申请单或竞价单的二级审批人姓名）
        String sectionChiefName;
        switch (orderType) {
            case PURCHASE_ORDER:
            case CLINICAL_PURCHASE_ORDER:
                sectionChiefName = this.getPurchaseApprover(WarehouseConstant.SECTION_CHIEF_APPROVAL_LEVEL, purchaseApplicationId);
                break;
            case BID_ORDER:
                sectionChiefName = this.getBidApprover(WarehouseConstant.SECTION_CHIEF_APPROVAL_LEVEL, orderMasterDTO);
                break;
            default:
                throw new BusinessInterceptException(ExecptionMessageEnum.UNSUPPORTED_ORDER_TYPE, orderMasterDTO.getOrderType());
        }
        return sectionChiefName;
    }

    private String getPurchaseApprover(int approvalLevel, Integer purchaseApplicationId) {
        List<PurchaseApprovalLogDTO> purchaseApprovalLogDTOList = purchaseApprovalLogClient.getApprovalLogByIdList(Sets.newHashSet(purchaseApplicationId));
        if (CollectionUtils.isEmpty(purchaseApprovalLogDTOList)) {
            return StringUtils.EMPTY;
        }
        for (PurchaseApprovalLogDTO purchaseApprovalLogDTO : purchaseApprovalLogDTOList) {
            if (approvalLevel == purchaseApprovalLogDTO.getApproveLevel()) {
                return purchaseApprovalLogDTO.getApprover();
            }
        }
        return StringUtils.EMPTY;
    }

    private String getBidApprover(int approvalLevel, OrderMasterDTO orderMasterDTO) {
        List<BidApprovalLogDTO> bidApprovalLogDTOList = bidClient.findApprovalLogInfo(orderMasterDTO.getForderno(), orderMasterDTO.getBidOrderId(), orderMasterDTO.getFusercode());
        if (CollectionUtils.isEmpty(bidApprovalLogDTOList)) {
            return StringUtils.EMPTY;
        }
        for (BidApprovalLogDTO bidApprovalLogDTO : bidApprovalLogDTOList) {
            if (approvalLevel == bidApprovalLogDTO.getLevel()) {
                return bidApprovalLogDTO.getOperatorName();
            }
        }
        return StringUtils.EMPTY;
    }

    private BizWarehouseEntryDTO getNotNullWarehouseApplicationById(Integer warehouseApplicationId) {
        //根据入库单号查找入库单信息
        BizWarehouseEntryDTO warehouseApplicationInfo = bizWareHouseClient.queryEntryDetailById(warehouseApplicationId);
        BusinessErrUtil.notNull(warehouseApplicationInfo, ExecptionMessageEnum.RECEIPT_NOT_FOUND, warehouseApplicationId);
        return warehouseApplicationInfo;
    }

    private List<FundCardDTO> getNotNullFundCardList(String orgCode, List<String> cardIdList) {
        List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.findAllCardByOrgCodeAndCardId(orgCode, cardIdList);
        BusinessErrUtil.notEmpty(fundCardDTOList, ExecptionMessageEnum.FUNDING_CARD_NOT_FOUND + JsonUtils.toJson(cardIdList));
        return fundCardDTOList;
    }

    private String getDepartmentDirectorName(Integer departmentId) {
        //根据订单对应部门获取部门负责人信息
        DepartmentDTO departmentDTO = userClient.getDepartmentInfo(departmentId);
        BusinessErrUtil.notNull(departmentDTO, ExecptionMessageEnum.DEPARTMENT_INFO_NOT_FOUND_FOR_ORDER, departmentId);
        Integer managerId = departmentDTO.getManagerId();
        if (managerId != null) {
            UserBaseInfoDTO departmentDirectorInfo = userClient.getNotNullUserDetailById(managerId);
            return departmentDirectorInfo.getName();
        }
        return StringUtils.EMPTY;
    }

    private WmsPersionDTO getPersonalizedApprover(String orgCode, String entryNo) {
        if (ORG_CODE_NEED_WAREHOUSE_APPROVER_TO_PRINT.contains(orgCode)) {
            List<WmsPersionDTO> wmsPersionDTOList = bizWareHouseClient.queryEntryPersonByEntryNoList(Collections.singletonList(entryNo));
            if (CollectionUtils.isNotEmpty(wmsPersionDTOList)) {
                return wmsPersionDTOList.get(0);
            }
        }
        return null;
    }

    private void populateApprover(WarehouseApplicationBean receiver, WmsPersionDTO provider) {
        receiver.setApproverName(provider.getOperator());
        receiver.setApprovalTimeString(provider.getOptTime());
    }

    private String getPersonalizedBarcodeByOrderNo(String orgCode, String orderNo) {
        if (WarehouseConstant.ORG_CODE_NEED_ORDER_NO_BARCODE.contains(orgCode)) {
            try {
                return BarCodeUtils.getBase64Img(orderNo);
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "getPersonalizedBarcodeByOrderNo", "条形码生成异常，入参,orgCode:" + orgCode + ";orderNo:" + orderNo, e);
                return StringUtils.EMPTY;
            }
        }
        return StringUtils.EMPTY;
    }

    private String getPersonalizedBarcodeByEntryNo(String orgCode, String entryNo) {
        if (ORG_CODE_NEED_ENTRY_NO_BARCODE.contains(orgCode)) {
            try {
                return BarCodeUtils.getBase64Img(entryNo);
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "getPersonalizedBarcodeByEntryNo", "条形码生成异常，入参,orgCode:" + orgCode + ";entryNo:" + entryNo, e);
                return StringUtils.EMPTY;
            }
        }
        return StringUtils.EMPTY;
    }

    private boolean needFirstLevelCategory(String orgCode) {
        return WarehouseConstant.ORG_CODE_NEED_FIRST_LEVEL_CATEGORY.contains(orgCode);
    }

    private List<InvoiceDTO> getPersonalizedInvoice(Integer orderId) {
        InvoiceQueryDTO queryDTO = new InvoiceQueryDTO();
        queryDTO.setSourceIds(Collections.singletonList(orderId.longValue()));
        queryDTO.setInvoiceType(InvoiceTypeEnum.ORDER);
        List<InvoiceDTO> invoiceList = invoiceClient.findInvoiceList(queryDTO);
        return invoiceList;
    }

    private void populateInvoice(OrderBean receiver, List<InvoiceDTO> provider) {
        Set<String> invoiceNoSet = provider.stream().map(InvoiceDTO::getInvoiceNo).collect(Collectors.toSet());
        receiver.setInvoiceNo(String.join(ORDER_INVOICE_SEPARATOR, invoiceNoSet));
    }

    private void populatePurchaser(OrderBean receiver, UserBaseInfoDTO provider) {
        receiver.setPurchaserName(provider.getName());
        receiver.setPurchaserPhone(provider.getMobile());
    }

    private WarehouseApplicationPageVO applicationPagingDTO2applicationPagingVO(PageApiResult<List<BizWarehouseEntryDTO>> from) {
        WarehouseApplicationPageVO result = new WarehouseApplicationPageVO();
        int total = (int) from.getTotal();
        int pageSize = from.getSize();
        int currentPage = from.getCurrent();
        int totalPage = (total + pageSize - 1) / pageSize;
        //判空封装申领单列表
        if (CollectionUtils.isEmpty(from.getData())) {
            result.setWarehouseApplicationVOList(Collections.emptyList());
        } else {
            List<WarehouseApplicationVO> applicationListToReturn = new ArrayList<>(from.getData().size());
            List<BizWarehouseEntryDTO> bizWarehouseEntryDTOList = from.getData();
            List<String> orderNoList = bizWarehouseEntryDTOList.stream().map(BizWarehouseEntryDTO::getOrderNo).collect(Collectors.toList());
            List<OrderMasterDO> orderMasterDOList = orderMasterMapper.findByFordernoIn(orderNoList);
            Map<String, OrderMasterDO> orderNoMasterMap = DictionaryUtils.toMap(orderMasterDOList, OrderMasterDO::getForderno, Function.identity());
            for (BizWarehouseEntryDTO bizWarehouseEntryDTO : bizWarehouseEntryDTOList) {
                WarehouseApplicationVO applicationToReturn = this.bizWarehouseEntryDTO2WarehouseApplicationVO(bizWarehouseEntryDTO);
                
                OrderMasterDO orderMasterDO = orderNoMasterMap.get(applicationToReturn.getOrderNo());
                if(orderMasterDO != null){
                    applicationToReturn.setInventoryStatus(orderMasterDO.getInventoryStatus().intValue());    
                }
                applicationListToReturn.add(applicationToReturn);
            }
            result.setWarehouseApplicationVOList(applicationListToReturn);
        }
        result.setTotal(total);
        result.setTotalPage(totalPage);
        result.setCurrentPage(currentPage);
        result.setPageSize(pageSize);
        return result;
    }

    private List<BizWarehouseEntryVO> reSubmitWithExistWarehouseApplication(WarehouseReSubmitApplicationRequestVO request, BizWarehouseEntryDTO existBizWarehouseEntryDTO, UserBaseInfoDTO baseUser, OrderMasterSearchDTO searchOrder) {
        //商品验证
        List<WarehouseProductRequestVO> warehouseProductRequestList = request.getWarehouseProductRequestList();
        Preconditions.notEmpty(warehouseProductRequestList, "重新提交入库，请求的入库单的关联商品信息列表为空");

        //生成要更新的入库申请单
        BizWarehouseEntryDTO bizWarehouseEntryDTOToUpdate = getBizWarehouseEntryFromExistAndRequest(request, existBizWarehouseEntryDTO, baseUser, searchOrder);
        if (CollectionUtils.isNotEmpty(request.getInvoiceInfo())) {
            bizWarehouseEntryDTOToUpdate.setInvoiceNos(ListUtils.toList(request.getInvoiceInfo(), InvoiceInfoVO::getInvoiceNo));
        }
        List<BizWarehouseEntryDTO> warehouseEntryList = Collections.singletonList(bizWarehouseEntryDTOToUpdate);
        validateWarehouseRoomThreshold(warehouseEntryList, existBizWarehouseEntryDTO.getOrgId(), searchOrder.getForderno());
        // 配伍禁忌
        bizWareHouseClient.checkIncompatibility(warehouseEntryList, request.getIncompatibilityVerify());
        //更新入库申请单，更新订单状态
        boolean saveResult = bizWareHouseClient.reBatchPutInStorage(warehouseEntryList);
        BusinessErrUtil.isTrue(saveResult, ExecptionMessageEnum.WAREHOUSE_SERVICE_SAVE_FAIL);
        // 订单编号查询入库单
        List<BizWarehouseEntryDTO> entryByOrderNoList = bizWareHouseClient.findEntryByOrderNoList(New.list(searchOrder.getForderno()));
        List<BizWarehouseEntryVO> bizWarehouseEntryVOList = entryByOrderNoList.stream().map(bizWarehouseEntryDTO -> {
            BizWarehouseEntryVO vo = new BizWarehouseEntryVO();
            vo.setId(bizWarehouseEntryDTO.getId());
            return vo;
        }).collect(toList());
        return bizWarehouseEntryVOList;
    }

    private void populateWarehouseApplicationPrepareSubmitVO(WarehouseApplicationPrepareSubmitVO receiver, OrderBean orderBean, List<ProductBean> orderDetailBeanList) {
        receiver.setOrderId(orderBean.getOrderId());
        receiver.setDepartmentName(orderBean.getDepartmentName());
        receiver.setWarehouseVOList(WarehouseApplicationBeanTranslator.warehouseBeanList2WarehouseVOList(orderBean.getWarehousesCanBeChosenByDepartment()));
        receiver.setWarehouseProductInfoVOList(ProductBeanTranslator.productBeanList2WarehouseProductInfoVOList(orderDetailBeanList));
        receiver.setTotalPrice(orderBean.getTotalPrice());
        receiver.setReceivingPhotos(orderBean.getReceivingPhotos());
        receiver.setPurchaserName(orderBean.getPurchaserName());
        receiver.setOrderNo(orderBean.getOrderNo());
    }

    private void populateWarehousesCanBeChosenByDepartment(OrderBean receiver, List<BizWarehouseRoomDTO> provider) {
        List<WarehouseBean> warehouseBeanList = new ArrayList<>();
        for (BizWarehouseRoomDTO bizWarehouseRoomDTO : provider) {
            WarehouseBean warehouseBean = new WarehouseBean(bizWarehouseRoomDTO.getId(), bizWarehouseRoomDTO.getRoomName());
            warehouseBeanList.add(warehouseBean);
        }
        receiver.setWarehousesCanBeChosenByDepartment(warehouseBeanList);
    }

    private void populateApprovalProgress(WarehouseApplicationBean receiver, List<ApprovalProgressDTO> provider) {
        List<ApprovalProgressBean> approvalProgressBeans = new ArrayList<>();
        for (ApprovalProgressDTO approvalProgressDTO : provider) {
            ApprovalProgressBean approvalProgressBean = new ApprovalProgressBean();
            approvalProgressBean.setStatus(approvalProgressDTO.getStatus());
            approvalProgressBean.setApprovalProgressName(approvalProgressDTO.getApprovalNodeName());
            approvalProgressBean.setOperatorList(approvalProgressDTO.getOperatorList());
            ApprovalLogDTO approvalLogDTO = approvalProgressDTO.getApprovalLogDTO();
            if (approvalLogDTO != null) {
                approvalProgressBean.setOperator(approvalLogDTO.getOperator());
                approvalProgressBean.setOptTime(approvalLogDTO.getOptTime());
                approvalProgressBean.setAutoExecute(approvalLogDTO.getAutoExecute());
            }
            approvalProgressBeans.add(approvalProgressBean);
        }
        receiver.setApprovalProgressBeans(approvalProgressBeans);
    }

    /**
     * 设置默认库房
     * @param receiverWithCategoryId 订单商品详情数据
     * @param orderMasterDTO 订单数据
     * @param detailIdEntryRoomMap 订单商品详情id-能入的库房映射
     */
    private void populateDefaultWarehouseId(List<ProductBean> receiverWithCategoryId, OrderMasterDTO orderMasterDTO, Map<Integer, EntryDetailRoomDTO> detailIdEntryRoomMap) {
        //如果没有库房可选，就不设置
        if(detailIdEntryRoomMap.isEmpty()){
            return;
        }
        ApplyRefWarehouseDTO applyWarehouse = applyRefWarehouseServiceClient.getWarehouseIdByApplicationId(orderMasterDTO.getFtbuyappid());
        //采购时选了就用采购的
        if (applyWarehouse != null) {
            for (ProductBean orderDetailBean : receiverWithCategoryId) {
                EntryDetailRoomDTO entryDetailRoomDTO = detailIdEntryRoomMap.get(orderDetailBean.getOrderDetailId());
                // 如果采购时选的库房是能进行入库的库房，才设置为默认库房id
                if(entryDetailRoomDTO != null && entryDetailRoomDTO.getRooms() != null
                        && entryDetailRoomDTO.getRooms().stream().anyMatch(room-> room.getId().equals(applyWarehouse.getWarehouseId()))){
                    orderDetailBean.setDefaultWarehouseId(applyWarehouse.getWarehouseId());
                }
            }
            return;
        }
        // 采购时没选库房的，如果能入库的库房数量为默认值（1），则设置它为默认库房
        for (ProductBean orderDetailBean : receiverWithCategoryId) {
            EntryDetailRoomDTO entryDetailRoomDTO = detailIdEntryRoomMap.get(orderDetailBean.getOrderDetailId());
            if (entryDetailRoomDTO == null || CollectionUtils.isEmpty(entryDetailRoomDTO.getRooms())) {
                continue;
            }
            orderDetailBean.setDefaultWarehouseId(entryDetailRoomDTO.getRooms().size() == DEFAULT_WAREHOUSE_SIZE_TO_CHOOSE ? entryDetailRoomDTO.getRooms().get(0).getId() : null);
        }
    }

    private void selfPopulateNeedSubmitWarehouseTag(List<ProductBean> receiverWithCategoryId, Integer orgId) {
        //根据订单的机构查找需要入库的商品分类
        List<BizWarehouseEntryDetailDTO> categoryNeedSubmitDTOList = bizWareHouseClient.queryKind(orgId);
        List<Integer> categoryNeedSubmitList = categoryNeedSubmitDTOList.stream().map(BizWarehouseEntryDetailDTO::getCategoryId).collect(Collectors.toList());
        for (ProductBean productBean : receiverWithCategoryId) {
            if (categoryNeedSubmitList.contains(productBean.getCategoryId())) {
                productBean.setNeedSubmitWarehouseTag(NEED_SUBMIT_WAREHOUSE);
            } else {
                productBean.setNeedSubmitWarehouseTag(NEED_NOT_SUBMIT_WAREHOUSE);
            }
        }
    }

    private void selfPopulateNeedShowDangerousInputFlag(List<ProductBean> productBeanList, String orderNo, Integer entryId, Integer orgId) {
        if(OrgEnum.ZHONG_SHAN_SAN_YUAN_LIN_CHUANG.getValue() == orgId){
            // 三院临床特殊处理，库房那没有订单号，且单位不用危化品。不调用接口，直接返回非危化品
            productBeanList.forEach(productBean -> productBean.setNeedShowDangerousInputFlag(CommonValueUtils.FALSE_INT));
            return;
        }
        List<EntryDangerousDTO> entryDangerousDTOList = bizWareHouseClient.queryEntryDangerous(orderNo, entryId, orgId);
        Map<Integer, Boolean> detailIdDangerousFlagMap = DictionaryUtils.toMap(entryDangerousDTOList, EntryDangerousDTO::getOrderDetailId, EntryDangerousDTO::isDangerous);
        productBeanList.forEach(productBean -> productBean.setNeedShowDangerousInputFlag(CommonValueUtils.parseBoolean2Number(detailIdDangerousFlagMap.get(productBean.getOrderDetailId()))));
    }

    private void populateSupplier(ProductBean receiver, OrderMasterDTO provider) {
        receiver.setSupplierId(provider.getFsuppid());
        receiver.setSupplierName(provider.getFsuppname());
    }

    private OrderMasterDTO getNotNullOrderMasterById(Integer orderId) {
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_NO, orderId);
        return OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO);
    }

    private List<OrderMasterDTO> getNotNullOrderMasterByIdList(List<Integer> orderId) {
        List<OrderMasterDO> list = orderMasterMapper.findByIdIn(orderId);
        BusinessErrUtil.notEmpty(list, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_NO, orderId);
        return list.stream().map(OrderMasterTranslator::orderMasterDO2OrderMasterDTO).collect(Collectors.toList());
    }

    private OrderMasterDTO getNotNullOrderMasterByOrderNo(String orderNo) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_NO, orderNo);
        return OrderMasterTranslator.orderMasterDO2OrderMasterDTO(orderMasterDO);
    }

    private List<OrderDetailDTO> getNotEmptyOrderDetailList(Integer orderId) {
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
        BusinessErrUtil.notEmpty(orderDetailDOList, ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND, orderId);
        return orderDetailDOList.stream().map(OrderDetailTranslator::orderDetailDOToTorderDetailDTO).collect(Collectors.toList());
    }

    /**
     * 获取未退货完成的商品
     */
    private List<OrderDetailDTO> getNotReturnOrderDetailList(List<Integer> orderIdList){
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setOrderMasterIdList(orderIdList);
        orderDetailReq.setExcludeReturnStatusList(New.list(GoodsReturnStatusEnum.SUCCESS.getCode()));
        orderDetailReq.setReturnBindGasBottle(true);
        return orderDetailService.findDetailByOrderIdListExcludeReturnStatus(orderDetailReq).getData();
    }

    private List<OrderDetailDTO> getNotEmptyOrderDetailList(List<Integer> orderIdList) {
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findAllByFmasteridIn(orderIdList);
        BusinessErrUtil.notEmpty(orderDetailDOList, ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND, orderIdList);
        return orderDetailDOList.stream().map(OrderDetailTranslator::orderDetailDOToTorderDetailDTO).collect(Collectors.toList());
    }

    private Map<Integer, List<OrderDetailDTO>> getNotEmptyOrderDetailMapper(List<Integer> orderIdList) {
        List<OrderDetailDTO> orderDetailList = getNotEmptyOrderDetailList(orderIdList);
        return DictionaryUtils.groupBy(orderDetailList, OrderDetailDTO::getFmasterid);
    }

    private BizWarehouseEntryDetailDTO getBizWarehouseEntryDetailFromExist(BizWarehouseEntryDetailDTO existBizWarehouseEntryDetailDTO) {
        BizWarehouseEntryDetailDTO bizWarehouseEntryDetailDTOToUpdate = new BizWarehouseEntryDetailDTO();
        bizWarehouseEntryDetailDTOToUpdate.setControlFlag(existBizWarehouseEntryDetailDTO.getControlFlag());
        bizWarehouseEntryDetailDTOToUpdate.setSuppName(existBizWarehouseEntryDetailDTO.getSuppName());
        bizWarehouseEntryDetailDTOToUpdate.setSuppId(existBizWarehouseEntryDetailDTO.getSuppId());
        bizWarehouseEntryDetailDTOToUpdate.setSpecifications(existBizWarehouseEntryDetailDTO.getSpecifications());
        bizWarehouseEntryDetailDTOToUpdate.setProductName(existBizWarehouseEntryDetailDTO.getProductName());
        bizWarehouseEntryDetailDTOToUpdate.setDangerousType(existBizWarehouseEntryDetailDTO.getDangerousType());
        bizWarehouseEntryDetailDTOToUpdate.setCasNo(existBizWarehouseEntryDetailDTO.getCasNo());
        bizWarehouseEntryDetailDTOToUpdate.setBrandName(existBizWarehouseEntryDetailDTO.getBrandName());
        bizWarehouseEntryDetailDTOToUpdate.setProductCode(existBizWarehouseEntryDetailDTO.getProductCode());
        bizWarehouseEntryDetailDTOToUpdate.setEntryId(existBizWarehouseEntryDetailDTO.getEntryId());
        bizWarehouseEntryDetailDTOToUpdate.setId(existBizWarehouseEntryDetailDTO.getId());
        bizWarehouseEntryDetailDTOToUpdate.setFpicpath(existBizWarehouseEntryDetailDTO.getFpicpath());
        bizWarehouseEntryDetailDTOToUpdate.setCategoryId(existBizWarehouseEntryDetailDTO.getCategoryId());
        return bizWarehouseEntryDetailDTOToUpdate;
    }

    private BizWarehouseEntryDTO getBizWarehouseEntryDTOCopy(BizWarehouseEntryDTO existBizWarehouseEntryDTO) {
        BizWarehouseEntryDTO bizWarehouseEntryDTOToUpdate = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTOToUpdate.setCreateTime(existBizWarehouseEntryDTO.getCreateTime());
        bizWarehouseEntryDTOToUpdate.setEntryNo(existBizWarehouseEntryDTO.getEntryNo());
        bizWarehouseEntryDTOToUpdate.setId(existBizWarehouseEntryDTO.getId());
        bizWarehouseEntryDTOToUpdate.setRoomId(existBizWarehouseEntryDTO.getRoomId());
        bizWarehouseEntryDTOToUpdate.setOrderNo(existBizWarehouseEntryDTO.getOrderNo());
        bizWarehouseEntryDTOToUpdate.setPurchaseUserGuid(existBizWarehouseEntryDTO.getPurchaseUserGuid());
        bizWarehouseEntryDTOToUpdate.setPurchaseUserName(existBizWarehouseEntryDTO.getPurchaseUserName());
        bizWarehouseEntryDTOToUpdate.setDeptId(existBizWarehouseEntryDTO.getDeptId());
        bizWarehouseEntryDTOToUpdate.setDeptName(existBizWarehouseEntryDTO.getDeptName());
        bizWarehouseEntryDTOToUpdate.setOrgId(existBizWarehouseEntryDTO.getOrgId());
        bizWarehouseEntryDTOToUpdate.setRoomName(existBizWarehouseEntryDTO.getRoomName());
        LoginUserInfoBO loginUser = userClient.getLoginUserInfoBySessionInfo(GateWayMessageUtil.validateAndGetStoreUser());
        bizWarehouseEntryDTOToUpdate.setApplyUserGuid(loginUser.getUserGuid());
        bizWarehouseEntryDTOToUpdate.setApplyUserName(loginUser.getUserName());
        bizWarehouseEntryDTOToUpdate.setStatus(existBizWarehouseEntryDTO.getStatus());
        bizWarehouseEntryDTOToUpdate.setApprovalStatus(existBizWarehouseEntryDTO.getApprovalStatus());
        bizWarehouseEntryDTOToUpdate.setSpecies(existBizWarehouseEntryDTO.getSpecies());
        return bizWarehouseEntryDTOToUpdate;
    }

    private WarehouseApplicationVO bizWarehouseEntryDTO2WarehouseApplicationVO(BizWarehouseEntryDTO bizWarehouseEntryDTO) {
        WarehouseApplicationVO warehouseApplicationVO = new WarehouseApplicationVO();
        warehouseApplicationVO.setWarehouseApplicationNo(bizWarehouseEntryDTO.getEntryNo());
        warehouseApplicationVO.setWarehouseApplicationId(bizWarehouseEntryDTO.getId());
        warehouseApplicationVO.setOrderNo(bizWarehouseEntryDTO.getOrderNo());
        if (bizWarehouseEntryDTO.getCreateTime() != null) {
            warehouseApplicationVO.setWarehouseApplicationTime(bizWarehouseEntryDTO.getCreateTime().getTime());
        }
        warehouseApplicationVO.setWarehouseApplicant(bizWarehouseEntryDTO.getApplyUserName());
        warehouseApplicationVO.setWarehouseName(bizWarehouseEntryDTO.getRoomName());
        warehouseApplicationVO.setApprovalStatus(bizWarehouseEntryDTO.getApprovalStatus());
        if (bizWarehouseEntryDTO.getApprovalStatus() != null) {
            warehouseApplicationVO.setApprovalStatusName(ApprovalTaskStatusEnum.valueOf(bizWarehouseEntryDTO.getApprovalStatus()).getDesc());
        }
        warehouseApplicationVO.setStatus(bizWarehouseEntryDTO.getStatus());
        if (bizWarehouseEntryDTO.getStatus() != null) {
            warehouseApplicationVO.setStatusName(InboundStatus.valueOf(bizWarehouseEntryDTO.getStatus()).getDesc());
        }
        warehouseApplicationVO.setDepartmentName(bizWarehouseEntryDTO.getDeptName());
        warehouseApplicationVO.setRefNo(bizWarehouseEntryDTO.getRefNo());
        warehouseApplicationVO.setRecommit(bizWarehouseEntryDTO.isRecommit());
        warehouseApplicationVO.setBusinessType(bizWarehouseEntryDTO.getBusinessType());
        warehouseApplicationVO.setPushStatus(bizWarehouseEntryDTO.getPushStatus());
        return warehouseApplicationVO;
    }

    private BizWarehouseEntryDTO personalPageRequest2BizWarehouseEntryDTO(WarehouseApplicationPersonalPageRequestVO from, RjSessionInfo sessionUser) {
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setEntryNo(from.getWarehouseApplicationNo());
        bizWarehouseEntryDTO.setOrderNo(from.getOrderNo());
        bizWarehouseEntryDTO.setApprovalStatus(from.getApprovalStatus());
        bizWarehouseEntryDTO.setStatus(from.getStatus());
        bizWarehouseEntryDTO.setRoomId(from.getWarehouseId());
        bizWarehouseEntryDTO.setRoomIds(from.getWarehouseIdList());
        if (from.getApplicationLowerTime() != null) {
            bizWarehouseEntryDTO.setBeginTime(new Date(from.getApplicationLowerTime()));
        }
        if (from.getApplicationUpperTime() != null) {
            bizWarehouseEntryDTO.setEndTime(new Date(from.getApplicationUpperTime()));
        }
        //页面参数,为空时设置默认值
        bizWarehouseEntryDTO.setPage(from.getCurrentPage() == null ? DEFAULT_CURRENT_PAGE : from.getCurrentPage());
        bizWarehouseEntryDTO.setSize(from.getPageSize() == null ? DEFAULT_PAGE_SIZE : from.getPageSize());
        //当前用户信息
        bizWarehouseEntryDTO.setApplyUserGuid(sessionUser.getGuid());
        bizWarehouseEntryDTO.setOrgId(sessionUser.getOrgId());
        bizWarehouseEntryDTO.setPushStatus(from.getPushStatus());
        return bizWarehouseEntryDTO;
    }

    private BizWarehouseEntryDTO departmentPageRequest2BizWarehouseEntryDTO(WarehouseApplicationDeptPageRequestVO from, Integer orgId) {
        BizWarehouseEntryDTO bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
        bizWarehouseEntryDTO.setEntryNo(from.getWarehouseApplicationNo());
        bizWarehouseEntryDTO.setOrderNo(from.getOrderNo());
        bizWarehouseEntryDTO.setApprovalStatus(from.getApprovalStatus());
        bizWarehouseEntryDTO.setStatus(from.getStatus());
        bizWarehouseEntryDTO.setRoomId(from.getWarehouseId());
        bizWarehouseEntryDTO.setRoomIds(from.getWarehouseIdList());
        if (from.getApplicationLowerTime() != null) {
            bizWarehouseEntryDTO.setBeginTime(new Date(from.getApplicationLowerTime()));
        }
        if (from.getApplicationUpperTime() != null) {
            bizWarehouseEntryDTO.setEndTime(new Date(from.getApplicationUpperTime()));
        }
        //页面参数,为空时设置默认值
        bizWarehouseEntryDTO.setPage(from.getCurrentPage() == null ? DEFAULT_CURRENT_PAGE : from.getCurrentPage());
        bizWarehouseEntryDTO.setSize(from.getPageSize() == null ? DEFAULT_PAGE_SIZE : from.getPageSize());
        //当前用户信息
        bizWarehouseEntryDTO.setOrgId(orgId);
        if (from.getDepartmentId() != null) {
            bizWarehouseEntryDTO.setDeptIdList(Collections.singletonList(from.getDepartmentId()));
        }
        //设置用查询的入库申请人
        bizWarehouseEntryDTO.setApplyUserName(from.getApplyUserName());
        bizWarehouseEntryDTO.setPushStatus(from.getPushStatus());
        return bizWarehouseEntryDTO;
    }

    private List<BizWarehouseEntryDTO> mergeWarehouseApplicationByRoomId(UserBaseInfoDTO user, OrderMasterSearchDTO orderSearch, WarehouseSubmitApplicationRequestVO request) {
        BusinessErrUtil.notEmpty(orderSearch.getOrderDetail(), ExecptionMessageEnum.ORDER_ID_PRODUCT_NOT_FOUND, orderSearch.getId());
        // 找订单detail的remainder price
        CompletableFuture<List<OrderDetailDO>> remainderListFuture = AsyncExecutor.callAsync(() -> {
            List<Integer> orderDetailList = orderSearch.getOrderDetail().stream().map(OrderDetailSearchDTO::getDetailId).collect(Collectors.toList());
            return orderDetailMapper.selectRemainderPriceByIdIn(orderDetailList);
        });

        UserBaseInfoDTO purchaser = userClient.getNotNullUserDetailById(orderSearch.getFbuyerid());
        List<WarehouseProductRequestVO> productRequests = request.getWarehouseProductRequestList();
        List<CategoryDTO> categoryListForAllProduct = getNotEmptyCategoryList(request.getWarehouseProductRequestList(), CategoryConstant.QUERY_TAG3);
        Map<Integer, BizWarehouseEntryDTO> warehouseIdAndWarehouseEntryMap = new HashMap<>(productRequests.size());
        //把相同库房Id的入库商品放在同一个入库申请单中
        //根据货号区分订单详情
        Map<String, OrderDetailSearchDTO> goodCodeAndOrderDetailMap = new HashMap<>();
        Map<Integer, OrderDetailSearchDTO> detailIdAndOrderDetailMap = new HashMap<>();
        try {
            goodCodeAndOrderDetailMap = orderSearch.getOrderDetail().stream().collect(Collectors.toMap(o -> o.getFgoodcode().trim(), Function.identity(), (o,n) -> n));
            detailIdAndOrderDetailMap = orderSearch.getOrderDetail().stream().collect(Collectors.toMap(o -> o.getDetailId(), Function.identity(), (o,n) -> n));
        } catch (Exception e) {
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.UNABLE_TO_MATCH_PRODUCT_WITH_ITEM_OR_ORDER_DETAILS);
        }

        List<OrderDetailDO> remainderList = New.list();
        try {
            remainderList = remainderListFuture.get();
        } catch (Exception e) {
            LOGGER.error("无法正确查询到商品优惠价格，请联系客服", e);
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.UNABLE_TO_QUERY_DISCOUNTED_PRICE);
        }
        Map<Integer, BigDecimal> detailIdRemainderMap = remainderList.stream().filter(Objects::nonNull).collect(Collectors.toMap(OrderDetailDO::getId, OrderDetailDO
                ::getRemainderPrice, (ov, nv) -> nv));
        BusinessErrUtil.notNull(detailIdRemainderMap, ExecptionMessageEnum.UNABLE_TO_QUERY_DISCOUNTED_PRICE);
        
        //推送数据组装
        BaseDockingDTO baseDockingDTO = null;
        if(orderSearch.getFuserid() == OrgEnum.AN_HUI_SHENG_LI_YI_YUAN.getValue()){
            // 中国科学技术大学附属第一医院（安徽省立医院）推送数据定制
            baseDockingDTO = WareHouseDockingDataTranslator.wareHouseDataDtoToAhslDockingDto(request.getDockingInfo());
        }

        for (WarehouseProductRequestVO productRequest : productRequests) {
            BusinessErrUtil.notNull(productRequest.getWarehouseId(), ExecptionMessageEnum.PLEASE_SELECT_WAREHOUSE);
            BizWarehouseEntryDTO bizWarehouseEntryDTO = warehouseIdAndWarehouseEntryMap.get(productRequest.getWarehouseId());
            //库房Id对应的入库申请单对象如果为空，就新起一个
            if (bizWarehouseEntryDTO == null) {
                bizWarehouseEntryDTO = new BizWarehouseEntryDTO();
                bizWarehouseEntryDTO.setRoomId(productRequest.getWarehouseId());
                bizWarehouseEntryDTO.setStatus(InboundStatus.NOTINSTORAGE.getValue());
                bizWarehouseEntryDTO.setApplyUserName(user.getName());
                bizWarehouseEntryDTO.setApprovalStatus(ApprovalTaskStatusEnum.APPROVALING.getValue());
                bizWarehouseEntryDTO.setOrderNo(orderSearch.getForderno());
                bizWarehouseEntryDTO.setDeptId(orderSearch.getFbuydepartmentid());
                bizWarehouseEntryDTO.setDeptName(orderSearch.getFbuydepartment());
                bizWarehouseEntryDTO.setApplyUserGuid(user.getGuid());
                bizWarehouseEntryDTO.setOrgId(orderSearch.getFuserid());
                bizWarehouseEntryDTO.setPurchaseUserGuid(purchaser.getGuid());
                bizWarehouseEntryDTO.setPurchaseUserName(purchaser.getName());
                bizWarehouseEntryDTO.setRemark(request.getRemark());
                bizWarehouseEntryDTO.setReceivePicUrls(request.getInWarehousePictureUrls());
                bizWarehouseEntryDTO.setDockingInfo(baseDockingDTO);
                bizWarehouseEntryDTO.setSpecies(orderSearch.getSpecies());
                bizWarehouseEntryDTO.setIncompatibilityVerify(Boolean.TRUE.equals(request.getIncompatibilityVerify()));
                warehouseIdAndWarehouseEntryMap.put(productRequest.getWarehouseId(), bizWarehouseEntryDTO);
            }
            //入库申请单关联的商品列表如果为空就新起一个
            List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOList = bizWarehouseEntryDTO.getDetailDTOList();
            if (CollectionUtils.isEmpty(bizWarehouseEntryDetailDTOList)) {
                bizWarehouseEntryDetailDTOList = new ArrayList<>();
                bizWarehouseEntryDTO.setDetailDTOList(bizWarehouseEntryDetailDTOList);
            }
            if (productRequest.getGoodCode() == null) {
                productRequest.setGoodCode(StringUtils.EMPTY);
            }
            OrderDetailSearchDTO productDetail;
            if (productRequest.getOrderDetailId() != null) {
                productDetail = detailIdAndOrderDetailMap.get(productRequest.getOrderDetailId());
            } else {
                productDetail = goodCodeAndOrderDetailMap.get(productRequest.getGoodCode().trim());
            }

            BusinessErrUtil.notNull(productDetail, ExecptionMessageEnum.ORDER_DETAILS_NOT_FOUND_FOR_ITEM, orderSearch.getForderno());
            BizWarehouseEntryDetailDTO bizWarehouseEntryDetailDTO = new BizWarehouseEntryDetailDTO();
            bizWarehouseEntryDetailDTO.setProductCode(productDetail.getFgoodcode());
            bizWarehouseEntryDetailDTO.setBrandName(productDetail.getFbrand());
            bizWarehouseEntryDetailDTO.setCasNo(productDetail.getCasNo());
            bizWarehouseEntryDetailDTO.setDangerousType(OrderDetailsUtil.getDangerousType(productDetail.getDangerousType()));
            bizWarehouseEntryDetailDTO.setProductName(productDetail.getFgoodname());
            bizWarehouseEntryDetailDTO.setSpecifications(productDetail.getFspec());
            bizWarehouseEntryDetailDTO.setSuppId(orderSearch.getFsuppid());
            bizWarehouseEntryDetailDTO.setSuppName(orderSearch.getFsuppname());
            bizWarehouseEntryDetailDTO.setMeasurementNum(productRequest.getTotalQuantity() == null ? null : new BigDecimal("" + productRequest.getTotalQuantity()));
            bizWarehouseEntryDetailDTO.setMeasurementUnit(productRequest.getQuantityUnit());
            bizWarehouseEntryDetailDTO.setUnitMeasurementNum(productRequest.getUnitMeasurementNum());
            //应收数量暂时与实收数量相等
            int realQuantity = OrderDetailsUtil.getRealOrderProductQuantity(productDetail);
            bizWarehouseEntryDetailDTO.setReceivableNum(realQuantity);
            bizWarehouseEntryDetailDTO.setReceivedNum(realQuantity);
            bizWarehouseEntryDetailDTO.setReceivedUnit(productDetail.getFunit());
            //这里商品图片productDetail里没有，只能从请求参数获取
            bizWarehouseEntryDetailDTO.setFpicpath(productRequest.getProductPhoto());
            bizWarehouseEntryDetailDTO.setForm(productRequest.getForm());
            bizWarehouseEntryDetailDTO.setControlFlag(productDetail.getRegulatoryType());
            CategoryDTO firstLevelCategory = CategoryUtil.getFirstLevelCategory(productDetail.getCategoryId(), categoryListForAllProduct);
            Preconditions.notNull(firstLevelCategory, "找不到商品的一级分类信息，分类Id：" + productDetail.getCategoryId());
            bizWarehouseEntryDetailDTO.setCategoryId(firstLevelCategory.getId().intValue());
            bizWarehouseEntryDetailDTO.setSort(productRequest.getPersonalizedCategoryName());
            BigDecimal realTotalPrice = OrderDetailsUtil.getRealOrderProductPrice(productDetail);
            BigDecimal remainderPrice = detailIdRemainderMap.get(productRequest.getOrderDetailId());
            // 总价（含差价）
            BigDecimal totalPriceWithRemainderPrice = realTotalPrice.add(remainderPrice);
            bizWarehouseEntryDetailDTO.setPrice(totalPriceWithRemainderPrice);
            bizWarehouseEntryDetailDTO.setUnitPrice(BigDecimal.valueOf(productDetail.getFbidprice()));
            bizWarehouseEntryDetailDTO.setOrderDetailId(productDetail.getDetailId());
            bizWarehouseEntryDetailDTO.setRemainderPrice(remainderPrice);
            bizWarehouseEntryDetailDTO.setFirstCategoryId(productDetail.getFirstCategoryId());
            bizWarehouseEntryDetailDTO.setSecondCategoryId(productDetail.getSecondCategoryId());
            bizWarehouseEntryDetailDTO.setCbsdStorageAreaId(productRequest.getCbsdStorageAreaId());
            bizWarehouseEntryDetailDTO.setCbsdStorageArea(productRequest.getCbsdStorageArea());
            bizWarehouseEntryDetailDTO.setCbsdWastage(productRequest.getCbsdWastage());
            bizWarehouseEntryDetailDTO.setCbsdLegallyPurposes(productRequest.getCbsdLegallyPurposes());
            // 计量含量
            bizWarehouseEntryDetailDTO.setUnitMeasurementNum(productRequest.getUnitMeasurementNum());
            bizWarehouseEntryDetailDTO.setMeasurementUnit(productRequest.getQuantityUnit());
            bizWarehouseEntryDetailDTO.setForm(productRequest.getForm());

            bizWarehouseEntryDetailDTOList.add(bizWarehouseEntryDetailDTO);
        }
        return new ArrayList<>(warehouseIdAndWarehouseEntryMap.values());
    }

    /**
     * 判断当前用户是否可以提交入库
     *
     * @return
     */
    private void validateCanSubmitWarehouseApplication(String orderNo, Integer orderStatus, Integer orderInventoryStatus, Integer orgId) {
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        //判断订单状态
        if (OrgEnum.GUANG_DONG_YAO_KE_DA_XUE.getValue() == orgId) {
            BusinessErrUtil.isTrue(Objects.equals(OrderStatusEnum.Finish.getValue(), orderStatus), ExecptionMessageEnum.NON_COMPLETED_ORDER_NO_STORAGE);
        } else {
            // 当前方法只有当 当前机构有平台运行经费 && 当前订单为平台运行经费支付 时才返回true
            // 平台经费 && 新单
            if(researchBaseService.isPlatformFound(orderMasterDO)){
                // 对于使用了平台经费，需要判断订单状态是否已完成，只有已完成订单才能入库
                BusinessErrUtil.isTrue(Objects.equals(OrderStatusEnum.Finish.getValue(), orderStatus), ExecptionMessageEnum.ORDER_INCOMPLETE_NO_STORAGE);
            }else{
                // 针对旧单 如果状态为已完成，则不需要进行结算判断  --> 中山五院收货时兼容简单，去除平台管理经费的校验 add 20210512  禅道 18920
                if(!Objects.equals(OrderStatusEnum.Finish.getValue(),orderStatus)){
                    BusinessErrUtil.isTrue(Objects.equals(OrderStatusEnum.WaitingForStatement_1.getValue(), orderStatus) || Objects.equals(OrderStatusEnum.Statementing_1.getValue(), orderStatus), ExecptionMessageEnum.NON_SETTLEMENT_ORDER_NO_STORAGE);
                }
            }
        }
        //订单入库状态是 待入库才能提交入库
        BusinessErrUtil.isTrue(InventoryStatusEnum.WAITING_FOR_STORE.getCode().equals(orderInventoryStatus), ExecptionMessageEnum.ORDER_NOT_PENDING_INVENTORY_STATUS);
        //查看入库申请单是否有订单对应,有则不能提交入库
        BusinessErrUtil.isTrue(!hasWarehouseApplicationForOrder(orderNo), ExecptionMessageEnum.ORDER_ALREADY_HAS_WAREHOUSE_APPLICATION);
        // 如果存在退货单，抛出异常
        this.throwIfExistUnFinishGoodsReturn(orderMasterDO.getId());
    }

    private void throwIfExistUnFinishGoodsReturn(Integer orderId){
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByOrderId(orderId);
        boolean existUnFinishGoodsReturn = goodsReturns.stream().anyMatch(item->!GoodsReturnStatusEnum.SUCCESS.getCode().equals(item.getGoodsReturnStatus())
                && !GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(item.getGoodsReturnStatus()));
        BusinessErrUtil.isTrue(!existUnFinishGoodsReturn, ExecptionMessageEnum.UNFINISHED_RETURN_ORDER_EXISTS);
    }

    /**
     * 特殊情况补救入库单，判断当前订单是否可以提交入库
     *
     * @return 错误信息;返回空字符串时代表可以提交入库
     */
    private void validateCanSubmitWarehouseApplicationForRescue(OrderMasterSearchDTO order) {
        //查看入库申请单是否有订单对应,有则不能提交入库
        BusinessErrUtil.isTrue(!hasWarehouseApplicationForOrder(order.getForderno()), ExecptionMessageEnum.ORDER_ALREADY_HAS_WAREHOUSE_APPLICATION);
    }

    private boolean hasWarehouseApplicationForOrder(String orderNo) {
        List<BizWarehouseEntryDTO> list = bizWareHouseClient.findEntryByOrderNoList(New.list(orderNo));
        // 有不是已作废和已撤销入库单，就是还有入库单
        return !CollectionUtils.isEmpty(list) && list.stream().noneMatch(item->InboundStatus.HAVE_BEEN_WITHDRAW.getValue().equals(item.getStatus()));
    }

    private String getThresholdString(List<BizWarehouseEntryDTO> warehouseEntryList, Integer orgId, String orderNo) {
        List<Integer> roomIds = warehouseEntryList.stream().map(BizWarehouseEntryDTO::getRoomId).distinct().collect(Collectors.toList());
        BusinessErrUtil.notEmpty(roomIds, ExecptionMessageEnum.THRESHOLD_QUERY_NO_WAREHOUSE);

        //单位是否启用危化品管理
        boolean isUseChemicalManagement = bizWareHouseClient.chemicalManagement(orgId);
        if (!isUseChemicalManagement) {
            return StringUtils.EMPTY;
        }

        //如果没有查询出值 , 说明这家单位没有设置密度 , 就不启用阀值管理这个功能
        WarehouseThresholdFormulaDTO formulaDTO = bizWarehouseRoomServiceClient.queryThresholdFormula(orgId);
        if (formulaDTO == null) {
            return StringUtils.EMPTY;
        }
        if (formulaDTO.getGasDensity() == null || formulaDTO.getLiquidDensity() == null) {
            return LocaleUtils.translate(ExecptionMessageEnum.DENSITY_NOT_SET_ERROR, orgId);
        }

        List<BizWarehouseRoomRefThresholdDTO> roomStoreList = bizWarehouseRoomServiceClient.queryRoomThreshold(roomIds);
        //如果查不到库房阈值信息，则无需校验
        if (CollectionUtils.isEmpty(roomStoreList)) {
            return StringUtils.EMPTY;
        }
        // 危化品阈值校验
        bizWareHouseClient.checkThresholds(warehouseEntryList);
        return StringUtils.EMPTY;
    }

    private BizWarehouseExitDTO getImmediatelyOutWarehouseApplication(Integer exitId, String orderNo, Integer roomId) {
        //即入即出的新入库单会带上出库单Id，根据这个Id查找出库单
        if (exitId != null && exitId > 0) {
            BizWarehouseExitDTO bizWarehouseExitDTO = bizExitServiceClient.queryExitById(exitId);
            if (bizWarehouseExitDTO == null) {
                throw new RuntimeException("找不到入库单对应的出库单：" + exitId);
            }
            return bizWarehouseExitDTO;
        }
        //旧即入即出入库单没带上出库单Id，要根据订单号查出库单
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        List<BizWarehouseExitDTO> outWarehouseApplicationInfoList = bizExitServiceClient.queryExitByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(outWarehouseApplicationInfoList)) {
            return null;
        }
        for (BizWarehouseExitDTO bizWarehouseExitDTO : outWarehouseApplicationInfoList) {
            if (bizWarehouseExitDTO.getRoomId().equals(roomId)) {
                return bizWarehouseExitDTO;
            }
        }
        return null;
    }

    private void validateIntegrityForSubmit(WarehouseSubmitApplicationRequestVO request) {
        BusinessErrUtil.isTrue(request != null && request.getOrderId() != null, "入参不完整,订单Id不能为空");
    }

    private OrderMasterSearchDTO getNotNullSearchOrder(Integer orderId) {
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = orderSearchBoostService.searchOrderById(orderId);
        BusinessErrUtil.notEmpty(orderMasterSearchDTOList, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_ID, orderId);
        return orderMasterSearchDTOList.get(0);
    }

    private List<OrderMasterSearchDTO> getNotNullSearchOrderList(List<Integer> orderIds) {
        List<OrderMasterSearchDTO> orderMasterSearchDTOList = orderSearchBoostService.searchOrderByIdList(orderIds, SortOrderEnum.DESC);
        BusinessErrUtil.notEmpty(orderMasterSearchDTOList, ExecptionMessageEnum.ORDER_INFO_NOT_FOUND_FOR_ORDER_ID, orderIds);
        return orderMasterSearchDTOList;
    }

    private void validateOrderCanSubmit(Integer userId, OrderMasterSearchDTO searchOrder, Integer submitWay) {
        if (WarehouseApplicationSubmitWayEnum.RESCUED_SUBMIT_WAY.getValue().equals(submitWay)) {
            validateCanSubmitWarehouseApplicationForRescue(searchOrder);
        } else {
            validateCanSubmitWarehouseApplication(searchOrder.getForderno(), searchOrder.getStatus(), searchOrder.getInventoryStatus(), searchOrder.getFuserid());
        }
    }

    private boolean isNeedNotInboundForSubmit(WarehouseSubmitApplicationRequestVO requestVO) {
        //如果要入库商品信息为空，说明订单无需入库
        if (CollectionUtils.isEmpty(requestVO.getWarehouseProductRequestList())) {
            return true;
        }
        return false;
    }

    private void checkAndUpdateOrderStatusToNotNeedInbound(Integer orderId, String orderNo) {
        if (!hasWarehouseApplicationForOrder(orderNo)) {
            updateOrderStatusToNotNeedInbound(orderId);
        }
    }

    private void updateOrderStatusToNotNeedInbound(Integer orderId) {
        //更新订单的入库状态为“无需入库”状态
        warehouseRpcClient.changeInventoryStatus(orderId, InventoryStatusEnum.NOT_INBOUND.getCode());
    }

    private void validateWarehouseRoomThreshold(List<BizWarehouseEntryDTO> warehouseEntryList, Integer orgId, String orderNo) {
        String thresholdErrorString = this.getThresholdString(warehouseEntryList, orgId, orderNo);
        Preconditions.isTrue(StringUtils.isBlank(thresholdErrorString), thresholdErrorString);
    }

    private List<CategoryDTO> getNotEmptyCategoryList(List<WarehouseProductRequestVO> productRequests, Integer categoryQueryTag) {
        CategoryQueryDTO categoryQueryForProducts = new CategoryQueryDTO();
        categoryQueryForProducts.setCategoryIdList(productRequests.stream().map(o -> o.getCategoryId().longValue()).collect(Collectors.toList()));
        categoryQueryForProducts.setQueryTag(categoryQueryTag);
        List<CategoryDTO> categoryListForAllProduct = categoryServiceClient.queryForList(categoryQueryForProducts);
        BusinessErrUtil.notEmpty(categoryListForAllProduct, ExecptionMessageEnum.PRODUCT_CLASSIFICATION_DETAILS_NOT_FOUND);
        return categoryListForAllProduct;
    }

    private List<BizWarehouseEntryDTO> getNotEmptyWarehouseEntryList(UserBaseInfoDTO user, OrderMasterSearchDTO searchOrder, WarehouseSubmitApplicationRequestVO request) {
        //库房Id与要插入的实体Map，用于合并相同库房的申请入库单
        List<BizWarehouseEntryDTO> warehouseEntryList = mergeWarehouseApplicationByRoomId(user, searchOrder, request);
        BusinessErrUtil.notEmpty(warehouseEntryList, "要保存的入库申请单为空");
        return warehouseEntryList;
    }

    private boolean isNeedToRebuildWarehouseApplication(BizWarehouseEntryDTO existBizWarehouseEntryDTO, WarehouseReSubmitApplicationRequestVO request) {
        List<WarehouseProductRequestVO> productRequestList = request.getWarehouseProductRequestList();
        List<BizWarehouseEntryDetailDTO> existProductList = existBizWarehouseEntryDTO.getDetailDTOList();
        //数量不对应的需要新建单
        if (productRequestList == null || productRequestList.size() != existProductList.size()) {
            return true;
        }
        //入库商品选择库房不是同一个需要新建单
        Integer requestRoomId = productRequestList.get(0).getWarehouseId();
        for (WarehouseProductRequestVO productRequest : productRequestList) {
            BusinessErrUtil.notNull(productRequest.getWarehouseId(), ExecptionMessageEnum.PRODUCT_NO_WAREHOUSE, productRequest.getProductName());
            if (!productRequest.getWarehouseId().equals(requestRoomId)) {
                return true;
            }
        }
        return false;
    }

    private BizWarehouseEntryDTO getNotEmptyProductBizWarehouseEntry(Integer warehouseApplicationId) {
        //获取已保存的入库申请单
        BizWarehouseEntryDTO existBizWarehouseEntryDTO = bizWareHouseClient.queryEntryDetailById(warehouseApplicationId);
        BusinessErrUtil.notNull(existBizWarehouseEntryDTO, ExecptionMessageEnum.RECEIPT_ID_NOT_FOUND, warehouseApplicationId);
        Preconditions.notEmpty(existBizWarehouseEntryDTO.getDetailDTOList(), "查找到入库单的关联商品信息列表为空，入库单Id：" + warehouseApplicationId);
        return existBizWarehouseEntryDTO;
    }

    private BizWarehouseEntryDTO getBizWarehouseEntryFromExistAndRequest(WarehouseReSubmitApplicationRequestVO request, BizWarehouseEntryDTO existBizWarehouseEntryDTO, UserBaseInfoDTO baseUser, OrderMasterSearchDTO searchOrder) {
        //根据已有入库申请单新建要更新的入库申请单
        BizWarehouseEntryDTO bizWarehouseEntryDTOToUpdate = getBizWarehouseEntryDTOCopy(existBizWarehouseEntryDTO);
        // 入库撤销用，线上下订单标识
        if (bizWarehouseEntryDTOToUpdate.getSpecies() == null) {
            bizWarehouseEntryDTOToUpdate.setSpecies(searchOrder.getSpecies());
        }
        //前端可变动信息
        bizWarehouseEntryDTOToUpdate.setStatus(InboundStatus.NOTINSTORAGE.getValue());
        bizWarehouseEntryDTOToUpdate.setApprovalStatus(ApprovalTaskStatusEnum.APPROVALING.getValue());
        bizWarehouseEntryDTOToUpdate.setReceivePicUrls(request.getInWarehousePictureUrls());
        bizWarehouseEntryDTOToUpdate.setRoomId(request.getWarehouseProductRequestList().get(0).getWarehouseId());
        bizWarehouseEntryDTOToUpdate.setRoomName(request.getWarehouseProductRequestList().get(0).getWarehouseName());
        bizWarehouseEntryDTOToUpdate.setIncompatibilityVerify(Boolean.TRUE.equals(request.getIncompatibilityVerify()));
        if (StringUtils.isNotBlank(request.getRemark())) {
            bizWarehouseEntryDTOToUpdate.setRemark(request.getRemark());
        }
        //封装前端商品参数
        List<BizWarehouseEntryDetailDTO> entryDetailDTOListToUpdate = new ArrayList<>();
        //这里校验传进来的商品信息与已存在的入库申请单的商品信息是否一致，因为没有商品Id，只能根据商品货号来判断
        List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOList = existBizWarehouseEntryDTO.getDetailDTOList();
        Map<Integer, BizWarehouseEntryDetailDTO> existDetailIdEntryDetailMap = bizWarehouseEntryDetailDTOList.stream().filter(item->item.getOrderDetailId() != null).collect(Collectors.toMap(BizWarehouseEntryDetailDTO::getOrderDetailId, Function.identity(), (o, n)->n));
        Map<String, BizWarehouseEntryDetailDTO> existProductCodeAndEntryDetailMap = bizWarehouseEntryDetailDTOList.stream().collect(Collectors.toMap(o -> o.getProductCode().trim(), Function.identity(), (o, n)->n));

        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(searchOrder.getId());
        Map<Integer, OrderDetailDO> detailIdIdentityMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
        for (WarehouseProductRequestVO warehouseProductRequest : request.getWarehouseProductRequestList()) {
            BizWarehouseEntryDetailDTO existBizWarehouseEntryDetailDTO = existDetailIdEntryDetailMap.get(warehouseProductRequest.getOrderDetailId());
            if(existBizWarehouseEntryDetailDTO == null){
                // 按detailId取不到，就用货号匹配
                existBizWarehouseEntryDetailDTO = existProductCodeAndEntryDetailMap.get(warehouseProductRequest.getGoodCode().trim());
            }
            if (existBizWarehouseEntryDetailDTO == null) {
                throw new BusinessInterceptException(ExecptionMessageEnum.PRODUCT_CODE_MISMATCH, warehouseProductRequest.getGoodCode());
            }
            Preconditions.notNull(warehouseProductRequest.getOrderDetailId(), "需要入库的商品详情id为空，请联系客服！");
            OrderDetailDO matchDetail = detailIdIdentityMap.get(warehouseProductRequest.getOrderDetailId());
            BizWarehouseEntryDetailDTO bizWarehouseEntryDetailDTOToUpdate = getBizWarehouseEntryDetailFromExist(existBizWarehouseEntryDetailDTO);
            //应收数量暂时与实收数量相等
            int realQuantity = OrderDetailsUtil.getRealOrderProductQuantity(matchDetail);
            BigDecimal realOrderProductPrice = OrderDetailsUtil.getRealOrderProductPrice(matchDetail);
            bizWarehouseEntryDetailDTOToUpdate.setReceivedUnit(matchDetail.getFunit());
            bizWarehouseEntryDetailDTOToUpdate.setReceivedNum(realQuantity);
            bizWarehouseEntryDetailDTOToUpdate.setReceivableNum(realQuantity);
            bizWarehouseEntryDetailDTOToUpdate.setMeasurementUnit(warehouseProductRequest.getQuantityUnit());
            bizWarehouseEntryDetailDTOToUpdate.setMeasurementNum(warehouseProductRequest.getTotalQuantity() == null ? null : new BigDecimal("" + warehouseProductRequest.getTotalQuantity()));
            bizWarehouseEntryDetailDTOToUpdate.setForm(warehouseProductRequest.getForm());
            bizWarehouseEntryDetailDTOToUpdate.setPrice(realOrderProductPrice);
            bizWarehouseEntryDetailDTOToUpdate.setUnitPrice(matchDetail.getFbidprice());
            bizWarehouseEntryDetailDTOToUpdate.setRemainderPrice(matchDetail.getRemainderPrice());
            bizWarehouseEntryDetailDTOToUpdate.setOrderDetailId(matchDetail.getId());
            bizWarehouseEntryDetailDTOToUpdate.setSort(warehouseProductRequest.getPersonalizedCategoryName());
            bizWarehouseEntryDetailDTOToUpdate.setFirstCategoryId(matchDetail.getFirstCategoryId());
            bizWarehouseEntryDetailDTOToUpdate.setSecondCategoryId(matchDetail.getSecondCategoryId());
            bizWarehouseEntryDetailDTOToUpdate.setUnitMeasurementNum(warehouseProductRequest.getUnitMeasurementNum());
            // 中爆字段
            bizWarehouseEntryDetailDTOToUpdate.setCbsdWastage(warehouseProductRequest.getCbsdWastage());
            bizWarehouseEntryDetailDTOToUpdate.setCbsdStorageAreaId(warehouseProductRequest.getCbsdStorageAreaId());
            bizWarehouseEntryDetailDTOToUpdate.setCbsdStorageArea(warehouseProductRequest.getCbsdStorageArea());
            bizWarehouseEntryDetailDTOToUpdate.setCbsdLegallyPurposes(warehouseProductRequest.getCbsdLegallyPurposes());

            entryDetailDTOListToUpdate.add(bizWarehouseEntryDetailDTOToUpdate);
        }
        bizWarehouseEntryDTOToUpdate.setDetailDTOList(entryDetailDTOListToUpdate);
        //推送数据组装
        BaseDockingDTO baseDockingDTO = null;
        if(baseUser.getOrganizationId() == OrgEnum.AN_HUI_SHENG_LI_YI_YUAN.getValue()){
            // 中国科学技术大学附属第一医院（安徽省立医院）推送数据定制
            baseDockingDTO = WareHouseDockingDataTranslator.wareHouseDataDtoToAhslDockingDto(request.getDockingInfo());
            bizWarehouseEntryDTOToUpdate.setDockingInfo(baseDockingDTO);
        }
        return bizWarehouseEntryDTOToUpdate;
    }

    private void validateIntegrityForReSubmit(WarehouseReSubmitApplicationRequestVO request) {
        Preconditions.isTrue(request != null && request.getOrderId() != null && request.getWarehouseApplicationId() != null, "入参不完整,订单Id、入库单Id不能为空");
    }

    private List<BizWarehouseEntryDTO> getNotEmptyWarehouseEntryListForReSubmit(UserBaseInfoDTO user, OrderMasterSearchDTO searchOrder, WarehouseReSubmitApplicationRequestVO request) {
        return getNotEmptyWarehouseEntryList(user, searchOrder, reSubmitRequestVO2submitRequestVO(request));
    }

    private boolean isNeedNotInboundForReSubmit(WarehouseReSubmitApplicationRequestVO requestVO) {
        //如果要入库商品信息为空，说明订单无需入库
        if (CollectionUtils.isEmpty(requestVO.getWarehouseProductRequestList())) {
            return true;
        }
        return false;
    }

    private void abandonExistWarehouseApplication(String entryNo) {
        boolean invalidateResult = bizWareHouseClient.invalidateEntryByEntryNo(entryNo);
        BusinessErrUtil.isTrue(invalidateResult, ExecptionMessageEnum.VOID_RECEIPT_FAIL, entryNo);
    }

    private void validateUserSubmitAccessInDepartment(Integer userId, Integer departmentId, Integer orgId) {
        boolean hasAccess = userDepartmentRoleRpcServiceClient.findUserHasAccess(orgId, userId, departmentId, WarehouseConstant.BUYER_CENTER_SUBMISSION_STORAGE);
        BusinessErrUtil.isTrue(hasAccess, ExecptionMessageEnum.USER_LACKS_PERMISSION_TO_SUBMIT_INVENTORY);
    }

    private void validateUserViewAccessInOrganization(Integer userId, Integer orgId) {
        List<DepartmentDTO> departmentDTOList = userDepartmentRoleRpcServiceClient.findUserHasAccessDepartment(orgId, userId, WarehouseConstant.BUYER_CENTER_WAREHOUSE_VIEW);
        BusinessErrUtil.notEmpty(departmentDTOList, ExecptionMessageEnum.USER_LACKS_PERMISSION_FOR_WAREHOUSE_VIEW);
    }

    /**
     * 获取含categoryId或其父分类Id的库房
     *
     * @param categoryId              查询的分类Id
     * @param categoryDTOList         包含categoryId的额分类列表
     * @param roomStorageScopeDTOList 待过滤的库房列表
     * @return
     */
    private List<RoomStorageScopeDTO> getAllowWarehouseListByCategoryId(Integer categoryId, List<CategoryDTO> categoryDTOList, List<RoomStorageScopeDTO> roomStorageScopeDTOList) {
        //寻找分类列表中对应分类Id的那个对象
        CategoryDTO categoryDTOToFind = null;
        for (CategoryDTO categoryDTO : categoryDTOList) {
            if (categoryDTO.getId().equals(categoryId.longValue())) {
                categoryDTOToFind = categoryDTO;
                break;
            }
        }
        if (categoryDTOToFind == null) {
            throw new BusinessInterceptException(ExecptionMessageEnum.CATEGORY_ID_NOT_FOUND, JsonUtils.toJson(categoryDTOList), categoryId);
        }

        //获取这个分类自身及父分类Id
        List<Integer> categoryIdList = new ArrayList<>();
        categoryIdList.add(categoryDTOToFind.getId().intValue());
        if (!CategoryConstant.FIRST_LEVEL.equals(categoryDTOToFind.getLevel())) {
            String[] parentCategoryIds = categoryDTOToFind.getPath().split(CATEGORY_PATH_SEPARATOR_REGEX);
            for (String parentCategoryId : parentCategoryIds) {
                categoryIdList.add(Integer.valueOf(parentCategoryId));
            }
        }

        //筛选含有分类Id列表中任一分类Id的库房
        //分类id与库房可存储分类
        Map<Integer, List<RoomStorageScopeDTO>> roomStorageScopeDTOMap = roomStorageScopeDTOList.stream().collect(Collectors.groupingBy(RoomStorageScopeDTO::getCategoryId));
        List<RoomStorageScopeDTO> warehouseListToReturn = new ArrayList<>();
        for (Integer categoryIdInList : categoryIdList) {
            List<RoomStorageScopeDTO> warehouseCategoryIdList = roomStorageScopeDTOMap.get(categoryIdInList);
            if (warehouseCategoryIdList != null) {
                warehouseListToReturn.addAll(warehouseCategoryIdList);
            }
        }
        return warehouseListToReturn;
    }

    private WarehouseSubmitApplicationRequestVO reSubmitRequestVO2submitRequestVO(WarehouseReSubmitApplicationRequestVO reSubmitApplicationRequestVO) {
        if (reSubmitApplicationRequestVO == null) {
            return null;
        }
        WarehouseSubmitApplicationRequestVO submitApplicationRequestVO = new WarehouseSubmitApplicationRequestVO();
        submitApplicationRequestVO.setInWarehousePictureUrls(reSubmitApplicationRequestVO.getInWarehousePictureUrls());
        submitApplicationRequestVO.setOrderId(reSubmitApplicationRequestVO.getOrderId());
        submitApplicationRequestVO.setRemark(reSubmitApplicationRequestVO.getRemark());
        submitApplicationRequestVO.setWarehouseProductRequestList(reSubmitApplicationRequestVO.getWarehouseProductRequestList());
        return submitApplicationRequestVO;
    }

    private List<BizWarehouseEntryDTO> getBizWarehouseEntryDTOListByOrderNo(String orderNo) {
        //根据订单号查找入库单列表,入库单查询接口比较特殊，商品详情需要额外根据入库单号查找
        List<BizWarehouseEntryDTO> warehouseApplicationInfoList = bizWareHouseClient.queryEntryByOrderNo(orderNo);
        //根据入库单id列表查找入库单商品信息
        if (CollectionUtils.isEmpty(warehouseApplicationInfoList)) {
            return warehouseApplicationInfoList;
        }
        List<Integer> bizWarehouseEntryDetailIdList = warehouseApplicationInfoList.stream().map(o -> o.getId()).collect(Collectors.toList());
        List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOList = bizWareHouseClient.findEntryDetailByIdList(bizWarehouseEntryDetailIdList);
        Preconditions.notEmpty(bizWarehouseEntryDetailDTOList, "查询入库单失败：" + JsonUtils.toJson(bizWarehouseEntryDetailIdList));
        //把入库单商品信息放到入库单列表中
        for (BizWarehouseEntryDTO bizWarehouseEntryDTO : warehouseApplicationInfoList) {
            List<BizWarehouseEntryDetailDTO> bizWarehouseEntryDetailDTOListForSingle = new ArrayList<>();
            for (BizWarehouseEntryDetailDTO bizWarehouseEntryDetailDTO : bizWarehouseEntryDetailDTOList) {
                if (Objects.equals(bizWarehouseEntryDetailDTO.getEntryId(), bizWarehouseEntryDTO.getId())) {
                    bizWarehouseEntryDetailDTOListForSingle.add(bizWarehouseEntryDetailDTO);
                }
            }
            bizWarehouseEntryDTO.setDetailDTOList(bizWarehouseEntryDetailDTOListForSingle);
        }
        return warehouseApplicationInfoList;
    }


    private void populateInWarehouseApprover(List<OutWarehouseApplicationDetailVO> receiver, List<WarehouseApplicationDetailVO> provider) {
        Map<Integer, WarehouseApplicationDetailVO> entryDetailMap = provider.stream().filter(s -> s.getWarehouseId() != null).collect(Collectors.toMap(WarehouseApplicationDetailVO::getWarehouseId, o -> o, (o1, o2) -> o1));
        for (OutWarehouseApplicationDetailVO outWarehouseApplicationDetailVO : receiver) {
            //如果出库单与入库单对应的库房id相同，则认为他们是即入即出对应的入库单与出库单
            WarehouseApplicationDetailVO entryDetail = entryDetailMap.get(outWarehouseApplicationDetailVO.getWarehouseId());
            if (entryDetail == null) {
                continue;
            }
            outWarehouseApplicationDetailVO.setApproverName(entryDetail.getApproverName());
            outWarehouseApplicationDetailVO.setApprovalTimeString(entryDetail.getApprovalTimeString());
        }
    }

    private void populateDepartmentList(WarehouseConstantListVO receiver, List<MiniDepartmentDTO> provider) {
        List<DepartmentVO> departmentVOList = new ArrayList<>();
        //提取用户部门信息返回给前端
        for (MiniDepartmentDTO departmentItem : provider) {
            DepartmentVO departmentVO = new DepartmentVO(departmentItem.getId(), departmentItem.getName());
            departmentVOList.add(departmentVO);
        }
        receiver.setDepartmentVOList(departmentVOList);
    }

    private void populateWarehouseList(WarehouseConstantListVO receiver, List<MiniDepartmentDTO> provider) {
        List<Integer> departmentIds = provider.stream().map(MiniDepartmentDTO::getId).collect(Collectors.toList());
        //获取部门可选库房列表
        List<BizWarehouseRoomDTO> warehouseListCanBeChosen = bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(departmentIds);
        List<WarehouseVO> warehouseVOList = new ArrayList<>();
        for (BizWarehouseRoomDTO warehouseCanBeChosen : warehouseListCanBeChosen) {
            WarehouseVO warehouseVO = new WarehouseVO(warehouseCanBeChosen.getId(), warehouseCanBeChosen.getRoomName());
            warehouseVOList.add(warehouseVO);
        }
        receiver.setWarehouseVOList(warehouseVOList);
    }

    private CountWarehouseApplicationVO countDTO2CountWarehouseApplicationVO(CountDTO from) {
        if (from == null) {
            return null;
        }
        CountWarehouseApplicationVO to = new CountWarehouseApplicationVO();
        to.setAllWarehouseCount(from.getAllCount());
        to.setInWarehouseCount(from.getPassCount());
        to.setNotInWarehouseCount(from.getPendCount());
        return to;
    }

    private void populateApprovalStatusCount(CountWarehouseApplicationVO receiver, ApprovalCountDTO provider) {
        receiver.setRejectApprovalCount(provider.getApprovalReject());
        receiver.setWaitingForApprovalCount(provider.getPendingApproval());
    }

    private void populateFundCard(OrderBean receiver, List<FundCardSearchDTO> fundCardSearchDTOList, String orgCode) {
        if(CollectionUtils.isEmpty(fundCardSearchDTOList)){
            receiver.setFunCardNo(StringUtils.EMPTY);
            receiver.setProjectCode(StringUtils.EMPTY);
            receiver.setProjectName(StringUtils.EMPTY);
            return;
        }
        List<String> cardIds = fundCardSearchDTOList.stream().map(FundCardSearchDTO::getFundCardId).collect(Collectors.toList());
        List<FundCardDTO> currentLevelCards = researchFundCardServiceClient.findCurrentCardByOrgCodeAndCardId(orgCode, cardIds);
        String fundCardNoStr = currentLevelCards.stream()
                .map(item->item.getSecondLevelCode() != null ? item.getSecondLevelCode() : item.getFirstLevelCode())
                .collect(Collectors.joining(WarehouseConstant.ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR));
        receiver.setFunCardNo(fundCardNoStr);

        List<FundCardDTO> allLevelCards = this.getNotNullFundCardList(orgCode, cardIds);
        Set<String> projectNameSet = allLevelCards.stream().map(FundCardDTO::getName).collect(Collectors.toSet());
        receiver.setProjectName(String.join(WarehouseConstant.ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR, projectNameSet));
        Set<String> projectCodeSet = allLevelCards.stream().map(FundCardDTO::getDisplayCode).collect(Collectors.toSet());
        receiver.setProjectCode(String.join(WarehouseConstant.ORDER_PROJECT_NAME_OR_CODE_OR_FUND_CARD_NO_SEPARATOR, projectCodeSet));

        if(OrgEnum.QI_LU_GONG_YE_DA_XUE.getCode().equals(orgCode)){
            // 齐鲁工业大学要用到displayCode 且为一级卡，但目前findCurrentCardByOrgCodeAndCardId不返回2、3级经费displayCode，先特殊处理
            receiver.setFunCardNo(receiver.getProjectCode());
        }
    }

    private OrderBean getOrderBean(OrderMasterDTO orderMasterDTO, OrderMasterSearchDTO orderMasterSearchDTO) {
        //订单信息
        OrderBean orderBean = OrderBeanTranslator.orderMasterDTO2BaseOrderBean(orderMasterDTO);
        if(OrgEnum.ZHENG_ZHOU_ER_TONG_YI_YUAN.getValue() == orderMasterDTO.getFuserid()){
            // 河南省儿童医院郑州儿童医院验收人定制
            OrderAddressDTO orderAddressDTO = orderAddressRPCClient.findByOrderId(orderMasterDTO.getId());
            if("东三街院区".equals(orderAddressDTO.getLabel())){
                orderBean.setAcceptor("杨威利");
            }else if("郑东院区".equals(orderAddressDTO.getLabel())){
                orderBean.setAcceptor("张曼");
            }else {
                // 取一级验收审批人
                OrderApprovalRequestDTO request = new OrderApprovalRequestDTO();
                request.setOrderIdList(New.list(orderMasterDTO.getId()));
                request.setTypeList(New.list(OrderApprovalEnum.PASS.getValue()));
                List<OrderApprovalLogDTO> orderApprovalLogDTOList = orderApprovalLogService.findByOrderIdListAndStatus(request);
                if(CollectionUtils.isNotEmpty(orderApprovalLogDTOList)){
                    orderBean.setAcceptor(orderApprovalLogDTOList.get(0).getOperatorName());
                }
            }
        }
        //获取订单对应的经费卡项目
        if (CollectionUtils.isNotEmpty(orderMasterSearchDTO.getCard())) {
            this.populateFundCard(orderBean, orderMasterSearchDTO.getCard(), orderMasterDTO.getFusercode());
        }
        //科长
        String sectionChief = this.getSectionChief(orderMasterDTO);
        orderBean.setSectionChief(sectionChief);
        //部门负责人
        BusinessErrUtil.notNull(orderMasterDTO.getFbuydepartmentid(), ExecptionMessageEnum.DEPARTMENT_NOT_FOUND_FOR_ORDER, orderMasterDTO.getForderno());
        orderBean.setDepartmentDirector(getDepartmentDirectorName(orderMasterDTO.getFbuydepartmentid()));
        //根据订单的部门Id获取可选库房
        List<BizWarehouseRoomDTO> warehouseListCanBeChosen = bizWarehouseRoomServiceClient.getWarehouseByDepartmentIds(Collections.singletonList(orderMasterDTO.getFbuydepartmentid()));
        this.populateWarehousesCanBeChosenByDepartment(orderBean, warehouseListCanBeChosen);
        //获取订单对应的条形码（个性化）
        String orderNoBarcode = this.getPersonalizedBarcodeByOrderNo(orderMasterDTO.getFusercode(), orderMasterDTO.getForderno());
        orderBean.setOrderNoBarcode(orderNoBarcode);
        //获取订单发票+时间(个性化)
        List<InvoiceDTO> invoiceDTOList = this.getPersonalizedInvoice(orderMasterDTO.getId());
        List<Long> invoiceDateTimeList = New.list();
        List<String> invoiceNoList = New.list();
        for (InvoiceDTO invoiceDTO : invoiceDTOList) {
            Long invoiceDateTime = invoiceDTO.getInvoiceDate() == null ? null : invoiceDTO.getInvoiceDate().getTime();
            invoiceDateTimeList.add(invoiceDateTime);
            invoiceNoList.add(invoiceDTO.getInvoiceNo());
        }
        orderBean.setInvoiceDateTimeList(invoiceDateTimeList);
        orderBean.setInvoiceNoList(invoiceNoList);
        if (CollectionUtils.isNotEmpty(invoiceDTOList)) {
            this.populateInvoice(orderBean, invoiceDTOList);
        }
        //找采购人信息
        UserBaseInfoDTO purchaser = userClient.getNotNullUserDetailById(orderMasterDTO.getFbuyerid());
        // 课题组上一级课题组
        orderBean.setDepartmentParentName(this.getDeptParentName(orderMasterDTO.getFbuydepartmentid()));
        this.populatePurchaser(orderBean, purchaser);
        return orderBean;
    }

    /**
     * 获取当前部门的父级部门
     * @param deptId
     * @return
     */
    private String getDeptParentName(Integer deptId) {
        DepartmentDTO deptParentInfo = departmentRpcClient.getDepartmentParentInfo(deptId);
        return deptParentInfo.getName();
    }

    private WarehouseApplicationBean getWarehouseApplicationBean(BizWarehouseEntryDTO warehouseApplicationInfo, List<OrderDetailSearchDTO> orderDetailSearchDTOList, String orgCode, Integer orgId) {
        //入库单信息
        WarehouseApplicationBean warehouseApplicationBean = WarehouseApplicationBeanTranslator.bizWarehouseEntryDTO2WarehouseApplicationBean(warehouseApplicationInfo);
        //获取审批进度列表
        List<ApprovalProgressDTO> approvalProgressDTOList = bizWareHouseClient.queryApprovalProgressByEntryNo(warehouseApplicationInfo.getEntryNo());
        if (CollectionUtils.isNotEmpty(approvalProgressDTOList)) {
            populateApprovalProgress(warehouseApplicationBean, approvalProgressDTOList);
        }
        //获取入库审核人（个性化）,自动入库则取自动入库日志上的记录
        WmsPersionDTO wmsPersonDTOList = this.getPersonalizedApprover(orgCode, warehouseApplicationInfo.getEntryNo());
        if (wmsPersonDTOList != null) {
            populateApprover(warehouseApplicationBean, wmsPersonDTOList);
        }

        //获取入库单对应条形码(个性化)
        String entryNoBarcode = this.getPersonalizedBarcodeByEntryNo(orgCode, warehouseApplicationInfo.getEntryNo());
        warehouseApplicationBean.setEntryNoBarcode(entryNoBarcode);
        List<ProductBean> productBeans = this.getProductBeans(warehouseApplicationInfo, orderDetailSearchDTOList, orgCode, orgId);
        this.populateWarehouseApplicationPrice(warehouseApplicationBean, productBeans);
        warehouseApplicationBean.setProductBeans(productBeans);
        return warehouseApplicationBean;
    }

    private List<ProductBean> getProductBeans(BizWarehouseEntryDTO entry, List<OrderDetailSearchDTO> orderDetails, String orgCode, Integer orgId) {
        List<BizWarehouseEntryDetailDTO> entryDetailDTOList = entry.getDetailDTOList();
        Map<String, OrderDetailSearchDTO> goodCodeAndOrderDetailMap = new HashMap<>();
        Map<Integer, OrderDetailSearchDTO> detailIdAndOrderDetailMap = new HashMap<>();
        try {
            goodCodeAndOrderDetailMap = orderDetails.stream().collect(Collectors.toMap(o -> o.getFgoodcode().trim(), Function.identity(), (o,n) -> n));
            detailIdAndOrderDetailMap = orderDetails.stream().collect(Collectors.toMap(o -> o.getDetailId(), Function.identity(), (o,n) -> n));
        } catch (Exception e) {
            BusinessErrUtil.isTrue(false, ExecptionMessageEnum.ITEM_AND_ORDER_DETAILS_CANNOT_MATCH_PRODUCT);
        }
        List<ProductBean> productBeans = new ArrayList<>();
        for (BizWarehouseEntryDetailDTO entryDetail : entryDetailDTOList) {
            Preconditions.notNull(entryDetail.getProductCode(), "入库商品的货号为空：" + entryDetail.getId());
            OrderDetailSearchDTO orderDetailSearchDTO;
            if (entryDetail.getOrderDetailId() != null) {
                orderDetailSearchDTO = detailIdAndOrderDetailMap.get(entryDetail.getOrderDetailId());
            } else {
                orderDetailSearchDTO = goodCodeAndOrderDetailMap.get(entryDetail.getProductCode().trim());
            }
            BusinessErrUtil.notNull(orderDetailSearchDTO, ExecptionMessageEnum.INBOUND_ORDER_DETAILS_NOT_FOUND, entryDetail.getProductCode().trim(), entryDetail.getOrderDetailId());
            ProductBean productBean = ProductBeanTranslator.bizWarehouseEntryDetailDTO2ProductBean(entryDetail);
            this.populateProductPrice(productBean, entryDetail, orderDetailSearchDTO.getFbidprice());
            productBean.setQuantityAfterReturn(orderDetailSearchDTO.getFquantity() - orderDetailSearchDTO.getFcancelquantity().intValue());
            BigDecimal totalReturnAmount = BigDecimal.valueOf(orderDetailSearchDTO.getFbidprice()).multiply(BigDecimal.valueOf(orderDetailSearchDTO.getFcancelquantity()));
            productBean.setTotalPriceAfterReturn(BigDecimal.valueOf(orderDetailSearchDTO.getFbidamount()).subtract(totalReturnAmount));
            productBean.setCategoryId(orderDetailSearchDTO.getCategoryId());
            productBean.setOrderDetailId(orderDetailSearchDTO.getDetailId());
            productBean.setProductId(orderDetailSearchDTO.getProductId());
            productBean.setFirstLevelCategoryId(orderDetailSearchDTO.getFirstCategoryId());
            productBean.setFirstLevelCategoryName(orderDetailSearchDTO.getFirstCategoryName());
            if(CollectionUtils.isNotEmpty(entryDetail.getGasBottleDTOList())){
                productBean.setBindGasBottleBarcodes(entryDetail.getGasBottleDTOList().stream().map(GasBottleDTO::getQrCode).filter(Objects::nonNull).collect(toList()));
            }
            productBeans.add(productBean);
        }
        this.selfPopulateNeedSubmitWarehouseTag(productBeans, orgId);
        this.selfPopulateNeedShowDangerousInputFlag(productBeans, entry.getOrderNo(), entry.getId(), orgId);
        //获取商品一级分类(个性化)
        if (needFirstLevelCategory(orgCode)) {
            this.selfPopulateFirstLevelCategory(productBeans);
        }
        return productBeans;
    }

    private void populateInWarehouseApprover(OutWarehouseApplicationDetailVO receiver, WarehouseApplicationBean provider) {
        receiver.setApproverName(provider.getApproverName());
        receiver.setApprovalTimeString(provider.getApprovalTimeString());
    }

    /**
     * 获取电子签名数据
     * @param orderMasterDTOList 订单id
     * @param businessType 业务类型
     * @param operationList 业务操作
     * @return
     */
    private List<ElectronicSignDataVO> getElectronicSignDataList(List<OrderMasterDTO> orderMasterDTOList, BusinessTypeEnum businessType, List<ElectronicSignatureOperationEnum> operationList){
        OperationListDTO operationListDTO = new OperationListDTO();
        operationListDTO.setBusinessType(businessType);
        operationListDTO.setOperation(operationList);
        List<ElectronicSignOperationRecordDTO> electronicSignOperationRecordDTOList = bizBaseService.getElectronicSignData(orderMasterDTOList,operationListDTO);
        return electronicSignOperationRecordDTOList.stream().map(item->{
            ElectronicSignDataVO electronicSignDataVO = new ElectronicSignDataVO();
            electronicSignDataVO.setType(Integer.parseInt(item.getOperation()));
            electronicSignDataVO.setUrl(item.getSignPhoto());
            return electronicSignDataVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取单个订单最后一次通过的验收审批日志VO（带电子签名）
     *
     * @param orderMasterDTO 订单主表数据
     * @return 验收审批日志VO
     */
    private List<WarehouseApprovalLogVO> getLastPassOrderApprovalLogVoWithESign(OrderMasterDTO orderMasterDTO) {
        // 1.获取验收审批日志记录（含验收审批通过和不通过）
        OrderApprovalRequestDTO request = new OrderApprovalRequestDTO();
        request.setOrderIdList(Collections.singletonList(orderMasterDTO.getId()));
        request.setTypeList(New.list(OrderApprovalEnum.PASS.getValue(), OrderApprovalEnum.REJECT.getValue()));
        List<OrderApprovalLogDTO> orderApprovalLogDTOList = orderApprovalLogService.findByOrderIdListAndStatus(request);
        if (CollectionUtils.isEmpty(orderApprovalLogDTOList)) {
            return New.emptyList();
        }
        // 2.按照日志id排序,找到最后一次验收审批不通过的记录
        orderApprovalLogDTOList.sort(Comparator.comparing(OrderApprovalLogDTO::getId));
        // 如果有不通过的记录，则从最后一次驳回后的记录才是最后一次通过的验收审批(lastIndexOfReject+1->size)。如无，则所有记录都是最后一次通过的记录(0->size)。
        int lastIndexOfReject = -1;
        for (int i = orderApprovalLogDTOList.size() - 1; i > -1; i--) {
            if (OrderApprovalEnum.REJECT.getValue().equals(orderApprovalLogDTOList.get(i).getApproveStatus())) {
                lastIndexOfReject = i;
                break;
            }
        }
        if (lastIndexOfReject == orderApprovalLogDTOList.size() - 1) {
            // 最后一次是验收审批不通过
            return New.emptyList();
        }

        // 3.获取验收审批电子签名
        OperationListDTO operationListDTO = new OperationListDTO();
        operationListDTO.setBusinessType(BusinessTypeEnum.ORDER);
        operationListDTO.setOperation(New.list(ElectronicSignatureOperationEnum.ACCEPTANCE_APPROVAL));
        operationListDTO.setBusinessIdList(Collections.singletonList(orderMasterDTO.getId().toString()));
        List<ElectronicSignOperationRecordDTO> electronicSignOperationRecordDTOList = electronicSignServiceClient.getElectronicSignData(operationListDTO);
        // 转为验收审批日志id-电子签名图片的映射
        Map<String, String> logIdSignUrlMap = DictionaryUtils.toMap(electronicSignOperationRecordDTOList, ElectronicSignOperationRecordDTO::getInteractionId, ElectronicSignOperationRecordDTO::getSignPhoto);

        // 将最后的通过记录封装电子签名参数后返回
        return orderApprovalLogDTOList.subList(lastIndexOfReject + 1, orderApprovalLogDTOList.size()).stream()
                .map(item -> {
                    WarehouseApprovalLogVO warehouseApprovalLogVO = WarehouseApproveLogTranslator.toOrderApproveLog(item);
                    // 审批人id是系统，没有电子签名（兼容以前下一级验收审批自动但还存了电子签名的问题）
                    if (item.getOperatorId() == -1) {
                        return warehouseApprovalLogVO;
                    }
                    warehouseApprovalLogVO.setApproveUrl(logIdSignUrlMap.get(item.getId().toString()));
                    return warehouseApprovalLogVO;
                }).sorted(Comparator.comparing(WarehouseApprovalLogVO::getApproveLevel)).collect(Collectors.toList());
    }

    /**
     * 设置打印按钮显隐
     *
     * @param warehouseApplicationVOList 出入库列表数据
     * @param orgId                      单位id
     */
    private void setShowPrintButton(List<WarehouseApplicationVO> warehouseApplicationVOList, Integer orgId) {
        if (CollectionUtils.isEmpty(warehouseApplicationVOList)) {
            return;
        }
        // 获取orgId
        OrganizationClient.SimpleOrgDTO simpleOrgDTO = organizationClient.findSimpleOrgDTOById(orgId);
        String orgCode = simpleOrgDTO.getCode();
        // 获取入库单按钮状态配置
        String confShowPrintWareHouseApplicationStatus = PrintConfigConstant.SHOW_PRINT_ENTRY_WAREHOUSE_APPLICATION_BUTTON_STATUS;
        List<BaseConfigDTO> baseConfigList = sysConfigClient.getValueByOrgCodeAndConfigCode(orgCode, New.list(confShowPrintWareHouseApplicationStatus));
        List<Integer> printWareHouseApplicationStatusList = New.emptyList();
        if (CollectionUtils.isNotEmpty(baseConfigList)) {
            printWareHouseApplicationStatusList = baseConfigList.stream().map(BaseConfigDTO::getConfigValue)
                    .map(item -> item.split(",")).flatMap(Arrays::stream).filter(StringUtils::isNoneBlank).map(Integer::valueOf).collect(toList());
        }

        for (WarehouseApplicationVO warehouseApplicationVO : warehouseApplicationVOList) {
            // 根据入库单数据的状态判断
            Integer status = warehouseApplicationVO.getStatus();
            boolean showPrintWareHouseApplication = printWareHouseApplicationStatusList.contains(status);
            warehouseApplicationVO.setShowPrintWareHouseApplication(showPrintWareHouseApplication);
        }
    }

    /**
     * 从搜索数据中获取OrderFundcardVO（目前仅安徽省立用）
     *
     * @param orderMasterSearchDTO 搜索数据
     * @return OrderFundcardVO数组
     */
    private List<OrderFundcardVO> getOrderFundCardVOFromSearchDTO(OrderMasterSearchDTO orderMasterSearchDTO) {
        if (CollectionUtils.isNotEmpty(orderMasterSearchDTO.getCard())) {
            return orderMasterSearchDTO.getCard().stream().map(card -> {
                OrderFundcardVO orderFundcardVO = new OrderFundcardVO();
                orderFundcardVO.setCardId(card.getFundCardId());
                orderFundcardVO.setCampusName(card.getCampusName());
                return orderFundcardVO;
            }).collect(Collectors.toList());
        }
        return null;
    }

    private List<OrderFundcardVO> getFundCardVO(OrderMasterSearchDTO orderMasterSearchDTO, String orgCode){
        if(OrgEnum.SHEN_ZHEN_LONG_GANG_ER_BI_HOU_YI_YUAN.getValue() == orderMasterSearchDTO.getFuserid()){
            // 深圳市龙岗区耳鼻咽喉医院,获取经费卡信息
            List<String> cardIdList = CollectionUtils.isNotEmpty(orderMasterSearchDTO.getCard()) ? orderMasterSearchDTO.getCard().stream().map(FundCardSearchDTO::getFundCardId).collect(toList()) : null;
            if(cardIdList != null){
                Map<String, FundCardSearchDTO> cardIdCardSearchMap = DictionaryUtils.toMap(orderMasterSearchDTO.getCard(), FundCardSearchDTO::getFundCardId, Function.identity());
                List<FundCardDTO> fundCardDTOList = researchFundCardServiceClient.getFundCardListByCardIds(cardIdList, orgCode);
                Map<Integer, String> fundTypeMap = researchFundCardServiceClient.getFundTypeMap(orgCode);
                return fundCardDTOList.stream().map(fundCardDTO -> {
                    OrderFundcardVO orderFundcardVO = new OrderFundcardVO();
                    orderFundcardVO.setCardNo(cardIdCardSearchMap.get(fundCardDTO.getId()).getCardNo());
                    orderFundcardVO.setFundType(fundTypeMap.get(fundCardDTO.getFundType()));
                    orderFundcardVO.setCardName(fundCardDTO.getName());
                    return orderFundcardVO;
                }).collect(toList());
            }
        }
        return null;
    }

    /**
     * 一物一码更新无需入库相关信息
     * @param products 需要无需入库的商品
     */
    private void updateNoNeedInboundForBarcode(String orderNo, List<WarehouseProductRequestVO> products){
        List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByOrderNo(orderNo, null);
        if(CollectionUtils.isEmpty(orderUniqueBarCodeDTOList)){
            return;
        }
        Set<Integer> relatedDetailIds = products.stream().map(WarehouseProductRequestVO::getOrderDetailId).collect(Collectors.toSet());
        // 本次非已入库且不提交入库的商品，统统认为是无需入库
        List<String> needUpdateBarcodes = orderUniqueBarCodeDTOList.stream()
                .filter(orderUniqueBarCodeDTO -> !relatedDetailIds.contains(orderUniqueBarCodeDTO.getOrderDetailId())
                        && OrderProductInventoryStatusEnum.WAITING_FOR_INBOUND.getCode() == orderUniqueBarCodeDTO.getInventoryStatus())
                .map(OrderUniqueBarCodeDTO::getUniBarCode)
                .collect(toList());
        if(CollectionUtils.isEmpty(needUpdateBarcodes)){
            return;
        }
        // 更新为无需入库
        orderUniqueBarCodeRPCClient.updateStatusByBarcode(needUpdateBarcodes, null, OrderProductInventoryStatusEnum.NO_NEED.getCode());
    }

    /**
     * 查询出入库单信息
     *
     * @param request
     */
    @Override
    public List<BizWarehouseEntryExitDTO> queryEntryExitByOrderNo(OrderRequestVO request) {
        Preconditions.hasLength(request.getOrderNo(), "订单号不能为空");
        return bizWareHouseClient.queryEntryExitByOrderNo(request.getOrderNo());
    }


}
