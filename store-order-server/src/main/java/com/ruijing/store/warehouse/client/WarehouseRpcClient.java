package com.ruijing.store.warehouse.client;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.annotation.ServiceClient;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.enums.ServiceType;
import com.ruijing.order.saturn.api.warehouse.dto.InventoryStatusChangeDTO;
import com.ruijing.order.saturn.api.warehouse.service.WareHouseChangeOrderRpcService;

/**
 * <AUTHOR>
 * @date 2023/2/27 15:01
 * @description
 */
@ServiceClient
public class WarehouseRpcClient {

    @MSharpReference(remoteAppkey = "order-saturn-service")
    private WareHouseChangeOrderRpcService wareHouseChangeOrderRpcService;
    
    @ServiceLog(description = "修改库房状态",operationType = OperationType.WRITE, serviceType = ServiceType.RPC_CLIENT)
    public void changeInventoryStatus(Integer orderId, Integer inventoryStatus){
        InventoryStatusChangeDTO inventoryStatusChangeDTO = new InventoryStatusChangeDTO();
        inventoryStatusChangeDTO.setOrderId(orderId);
        inventoryStatusChangeDTO.setInventoryStatus(inventoryStatus);
        RemoteResponse<Boolean> response = wareHouseChangeOrderRpcService.inventoryStatusChange(inventoryStatusChangeDTO);
        Preconditions.isTrue(response.isSuccess(), response.getMsg());
    }
}
