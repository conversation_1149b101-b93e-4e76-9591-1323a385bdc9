package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:11 2020/12/24.
 */
@RpcModel(description="申领单状态键值")
public class ClaimStatusVO implements Serializable {

    private static final long serialVersionUID = 5367119242456007521L;

    @RpcModelProperty(value = "申领单状态（0未完成，1已完成，2已取消）")
    private  Integer status;

    @RpcModelProperty(value = "申领单状态（未完成、已完成、已取消）")
    private String statusName;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("WarehouseStatusVO{");
        sb.append("status=").append(status);
        sb.append(", statusName='").append(statusName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
