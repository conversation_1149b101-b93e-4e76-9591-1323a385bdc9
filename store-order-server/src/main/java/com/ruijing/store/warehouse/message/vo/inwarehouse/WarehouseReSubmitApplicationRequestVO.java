package com.ruijing.store.warehouse.message.vo.inwarehouse;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.warehouse.message.dto.WarehouseDockingDataDTO;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="重新提交入库请求")
public class WarehouseReSubmitApplicationRequestVO implements Serializable {

    private static final long serialVersionUID = 6960008228658812432L;

    @RpcModelProperty(value = "订单Id", example = "95998")
    private Integer orderId;

    @RpcModelProperty(value = "订单号", example = "DC201911215296401")
    private String orderNo;

    @RpcModelProperty(value = "入库申请单Id", example = "463")
    private Integer warehouseApplicationId;

    @RpcModelProperty(value = "入库申请单号", example = "RK280665006482132997")
    private String warehouseApplicationNo;

    @RpcModelProperty(value = "备注", example = "正常入库申请")
    private String remark;

    @RpcModelProperty(value = "入库申请图片", example = "http://images-test.rjmart.cn/image/c19bd248/a8a4188f-22db-4146-8561-6a231f9bff75.jpg;https://www.rjmart.cn/download/reagent/image/d15682ec/773f37a6-8a1c-4b9c-830b-abf5682a7d05.png")
    private String inWarehousePictureUrls;

    @RpcModelProperty(value = "订单关联商品信息列表")
    private List<WarehouseProductRequestVO> warehouseProductRequestList;

    @RpcModelProperty(value = "发票信息")
    private List<InvoiceInfoVO> invoiceInfo;

    @RpcModelProperty("入库对接推送数据")
    private WarehouseDockingDataDTO dockingInfo;

    @RpcModelProperty(value = "是否确认了仅提醒配伍禁忌")
    public Boolean incompatibilityVerify;

    @RpcModelProperty(value = "是否确认了校验计量含量")
    public boolean checkMeasurementNum;

    public boolean isCheckMeasurementNum() {
        return checkMeasurementNum;
    }

    public void setCheckMeasurementNum(boolean checkMeasurementNum) {
        this.checkMeasurementNum = checkMeasurementNum;
    }

    public Integer getWarehouseApplicationId() {
        return warehouseApplicationId;
    }

    public void setWarehouseApplicationId(Integer warehouseApplicationId) {
        this.warehouseApplicationId = warehouseApplicationId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getWarehouseApplicationNo() {
        return warehouseApplicationNo;
    }

    public void setWarehouseApplicationNo(String warehouseApplicationNo) {
        this.warehouseApplicationNo = warehouseApplicationNo;
    }

    public List<WarehouseProductRequestVO> getWarehouseProductRequestList() {
        return warehouseProductRequestList;
    }

    public void setWarehouseProductRequestList(List<WarehouseProductRequestVO> warehouseProductRequestList) {
        this.warehouseProductRequestList = warehouseProductRequestList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInWarehousePictureUrls() {
        return inWarehousePictureUrls;
    }

    public void setInWarehousePictureUrls(String inWarehousePictureUrls) {
        this.inWarehousePictureUrls = inWarehousePictureUrls;
    }

    public List<InvoiceInfoVO> getInvoiceInfo() {
        return invoiceInfo;
    }

    public void setInvoiceInfo(List<InvoiceInfoVO> invoiceInfo) {
        this.invoiceInfo = invoiceInfo;
    }

    public WarehouseDockingDataDTO getDockingInfo() {
        return dockingInfo;
    }

    public void setDockingInfo(WarehouseDockingDataDTO dockingInfo) {
        this.dockingInfo = dockingInfo;
    }

    public Boolean getIncompatibilityVerify() {
        return incompatibilityVerify;
    }

    public WarehouseReSubmitApplicationRequestVO setIncompatibilityVerify(Boolean incompatibilityVerify) {
        this.incompatibilityVerify = incompatibilityVerify;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", WarehouseReSubmitApplicationRequestVO.class.getSimpleName() + "[", "]")
                .add("orderId=" + orderId)
                .add("orderNo='" + orderNo + "'")
                .add("warehouseApplicationId=" + warehouseApplicationId)
                .add("warehouseApplicationNo='" + warehouseApplicationNo + "'")
                .add("remark='" + remark + "'")
                .add("inWarehousePictureUrls='" + inWarehousePictureUrls + "'")
                .add("warehouseProductRequestList=" + warehouseProductRequestList)
                .add("invoiceInfo=" + invoiceInfo)
                .add("dockingInfo=" + dockingInfo)
                .add("incompatibilityVerify=" + incompatibilityVerify)
                .toString();
    }
}
