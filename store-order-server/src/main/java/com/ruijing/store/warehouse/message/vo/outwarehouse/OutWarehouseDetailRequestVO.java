package com.ruijing.store.warehouse.message.vo.outwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="出库信息查询")
public class OutWarehouseDetailRequestVO implements Serializable {

    private static final long serialVersionUID = 30686779386154109L;

    /**
     * 出库申请单Id
     */
    @RpcModelProperty("出库申请单Id")
    private Integer outWarehouseApplicationId;

    public Integer getOutWarehouseApplicationId() {
        return outWarehouseApplicationId;
    }

    public void setOutWarehouseApplicationId(Integer outWarehouseApplicationId) {
        this.outWarehouseApplicationId = outWarehouseApplicationId;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("OutWarehouseDetailRequestVO{");
        sb.append(", outWarehouseApplicationId=").append(outWarehouseApplicationId);
        sb.append('}');
        return sb.toString();
    }
}
