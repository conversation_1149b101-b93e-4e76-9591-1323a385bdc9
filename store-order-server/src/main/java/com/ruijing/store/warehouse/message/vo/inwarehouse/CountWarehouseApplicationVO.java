package com.ruijing.store.warehouse.message.vo.inwarehouse;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="入库单列表获取已入库、未入库、总数信息返回")
public class CountWarehouseApplicationVO implements Serializable {
    private static final long serialVersionUID = 1973841251027377553L;

    @RpcModelProperty(value = "未入库数量")
    private Integer notInWarehouseCount;

    @RpcModelProperty(value = "已入库数量")
    private Integer inWarehouseCount;

    @RpcModelProperty(value = "总申请数量")
    private Integer allWarehouseCount;

    @RpcModelProperty(value = "待审批数量")
    private Integer waitingForApprovalCount;

    @RpcModelProperty(value = "审批驳回数量")
    private Integer rejectApprovalCount;

    public Integer getNotInWarehouseCount() {
        return notInWarehouseCount;
    }

    public void setNotInWarehouseCount(Integer notInWarehouseCount) {
        this.notInWarehouseCount = notInWarehouseCount;
    }

    public Integer getInWarehouseCount() {
        return inWarehouseCount;
    }

    public void setInWarehouseCount(Integer inWarehouseCount) {
        this.inWarehouseCount = inWarehouseCount;
    }

    public Integer getAllWarehouseCount() {
        return allWarehouseCount;
    }

    public void setAllWarehouseCount(Integer allWarehouseCount) {
        this.allWarehouseCount = allWarehouseCount;
    }

    public Integer getWaitingForApprovalCount() {
        return waitingForApprovalCount;
    }

    public void setWaitingForApprovalCount(Integer waitingForApprovalCount) {
        this.waitingForApprovalCount = waitingForApprovalCount;
    }

    public Integer getRejectApprovalCount() {
        return rejectApprovalCount;
    }

    public void setRejectApprovalCount(Integer rejectApprovalCount) {
        this.rejectApprovalCount = rejectApprovalCount;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("CountWarehouseApplicationVO{");
        sb.append("notInWarehouseCount=").append(notInWarehouseCount);
        sb.append(", inWarehouseCount=").append(inWarehouseCount);
        sb.append(", allWarehouseCount=").append(allWarehouseCount);
        sb.append(", waitingForApprovalCount=").append(waitingForApprovalCount);
        sb.append(", rejectApprovalCount=").append(rejectApprovalCount);
        sb.append('}');
        return sb.toString();
    }
}
