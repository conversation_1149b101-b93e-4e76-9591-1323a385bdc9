package com.ruijing.store.warehouse.message.vo.claim;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date Created in 14:09 2020/12/24.
 */
@RpcModel(description="课题组申领单分页查询请求对象")
public class ClaimDeptPageRequestVO implements Serializable {

    private static final long serialVersionUID = 4717454454475281942L;

    @RpcModelProperty(value = "申领单号", example = "")
    private String claimNo;

    @RpcModelProperty("订单号")
    private String orderNo;

    /**
     * {@link com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum}
     */
    @RpcModelProperty(value = "审批状态（0待审批、1审核通过、2审核驳回）")
    private Integer approvalStatus;

    /**
     * {@link com.ruijing.store.wms.api.enums.ReceiceStatus}
     */
    @RpcModelProperty(value = "申领状态（0未完成，1已完成）", example = "0")
    private Integer status;

    @RpcModelProperty(value = "课题组Id", example = "10")
    private Integer departmentId;

    @RpcModelProperty(value = "申领库房Id列表, 查询申领单时使用", example = "[94,95,96]")
    private List<Integer> roomIdList;

    @RpcModelProperty(value = "申领时间下限", example = "1580486400000")
    private Long applicationLowerTime;

    @RpcModelProperty(value = "申领时间上限", example = "1581868799000")
    private Long applicationUpperTime;

    @RpcModelProperty(value = "申领人姓名")
    private String claimUserName;

    @RpcModelProperty(value = "当前页，>=1", example = "1")
    private Integer currentPage;

    @RpcModelProperty(value = "页面大小", example = "10")
    private Integer pageSize;

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(Integer approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public List<Integer> getRoomIdList() {
        return roomIdList;
    }

    public void setRoomIdList(List<Integer> roomIdList) {
        this.roomIdList = roomIdList;
    }

    public Long getApplicationLowerTime() {
        return applicationLowerTime;
    }

    public void setApplicationLowerTime(Long applicationLowerTime) {
        this.applicationLowerTime = applicationLowerTime;
    }

    public Long getApplicationUpperTime() {
        return applicationUpperTime;
    }

    public void setApplicationUpperTime(Long applicationUpperTime) {
        this.applicationUpperTime = applicationUpperTime;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getClaimUserName() {
        return claimUserName;
    }

    public void setClaimUserName(String claimUserName) {
        this.claimUserName = claimUserName;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ClaimDeptPageRequestVO.class.getSimpleName() + "[", "]")
                .add("claimNo='" + claimNo + "'")
                .add("orderNo='" + orderNo + "'")
                .add("approvalStatus=" + approvalStatus)
                .add("status=" + status)
                .add("departmentId=" + departmentId)
                .add("roomIdList=" + roomIdList)
                .add("applicationLowerTime=" + applicationLowerTime)
                .add("applicationUpperTime=" + applicationUpperTime)
                .add("claimUserName='" + claimUserName + "'")
                .add("currentPage=" + currentPage)
                .add("pageSize=" + pageSize)
                .toString();
    }
}
