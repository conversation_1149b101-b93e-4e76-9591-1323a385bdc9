package com.ruijing.store.cancel.business.rpc.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.dto.response.OrderEventPushResultResponseDTO;
import com.ruijing.fundamental.cat.common.collections.New;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.store.cancel.business.rpc.service.CancelOrderCallbackService;
import com.ruijing.store.order.api.base.enums.OrderApprovalEnum;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.OrderMasterDO;
import com.ruijing.store.order.base.core.service.OrderApprovalLogService;
import com.ruijing.store.order.constant.DockingConstant;
import com.ruijing.store.order.log.annotation.ServiceLog;
import com.ruijing.store.order.log.enums.OperationType;
import com.ruijing.store.order.log.enums.ServiceType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: liwenyu
 * @createTime: 2023-03-16 15:17
 * @description:
 **/
@Service
public class CancelOrderCallbackServiceImpl implements CancelOrderCallbackService {

    /**
     * 推送入库记日志的单位
     */
    private static final List<String> ORG_LOG_PUSH_STATUS = New.list(
            OrgEnum.NING_BO_ER_YUAN.getCode()
    );

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderApprovalLogService orderApprovalLogService;

    @Override
    @ServiceLog(operationType = OperationType.WRITE, description = "订单取消事件回调", serviceType = ServiceType.COMMON_SERVICE)
    public void handleCallback(OrderEventPushResultResponseDTO orderEventPushResultResponseDTO) {
        String orderNo = orderEventPushResultResponseDTO.getOrderNo();
        OrderMasterDO orderMaster = orderMasterMapper.findByForderno(orderNo);
        Preconditions.notNull(orderMaster, "没有查到" + orderNo + "对应的订单");
        Integer orderId = orderMaster.getId();
        // 记日志
        if (ORG_LOG_PUSH_STATUS.contains(orderMaster.getFusercode())) {
            OrderApprovalEnum orderApprovalEnum = null;
            switch (orderEventPushResultResponseDTO.getOrderEventStatusEnum()) {
                case PUSHING:
                    orderApprovalEnum = OrderApprovalEnum.PUSHING_ORDER_STATUS_TO_THIRD;
                    break;
                case COMPLETE:
                    orderApprovalEnum = OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_SUCCESS;
                    break;
                case FAILED:
                    orderApprovalEnum = OrderApprovalEnum.PUSH_ORDER_STATUS_TO_THIRD_FAILURE;
                    break;
                default:
                    break;
            }
            if (orderApprovalEnum != null) {
                orderApprovalLogService.saveApprovalLog(orderId, orderApprovalEnum.getValue(), DockingConstant.SYSTEM_OPERATOR_ID, orderEventPushResultResponseDTO.getFailReason());
            }
        }
    }


}
