package com.ruijing.store.goodsreturn.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.common.BasePageParamDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description: 退货单列表request
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 14:37
 **/
public class GoodsReturnPageRequestDTO extends BasePageParamDTO implements Serializable {

    private static final long serialVersionUID = -5641428883374925489L;

    @RpcModelProperty("退货单号")
    private String returnNo;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("部门/课题组id")
    private Integer departmentId;

    @RpcModelProperty("供应商id")
    private Integer supplierId;

    @RpcModelProperty("供应商名称")
    private String supplierName;

    @RpcModelProperty("退货状态, 0待确认, 1退货中, 2已退货, 3拒绝退货, 4取消退货")
    private Integer returnStatus;

    @RpcModelProperty("开始时间")
    private Date startDate;

    @RpcModelProperty("结束时间")
    private Date endDate;

    @RpcModelProperty("采购单位")
    private Integer orgId;

    @RpcModelProperty("采购人")
    private String buyerName;

    @RpcModelProperty("退货单id")
    private Integer id;

    private List<Integer> orgIds;

    public List<Integer> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Integer> orgIds) {
        this.orgIds = orgIds;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnPageRquestDTO{");
        sb.append("returnNo='").append(returnNo).append('\'');
        sb.append("orderNo='").append(orderNo).append('\'');
        sb.append(", departmentId=").append(departmentId);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", returnStatus=").append(returnStatus);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append('}');
        return sb.toString();
    }
}
