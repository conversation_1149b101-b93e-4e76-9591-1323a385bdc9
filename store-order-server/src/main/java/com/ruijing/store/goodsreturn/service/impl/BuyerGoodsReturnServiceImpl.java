package com.ruijing.store.goodsreturn.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.order.dto.config.OrgDockingConfigDTO;
import com.reagent.order.dto.request.OrderEventStatusRequestDTO;
import com.reagent.order.dto.response.OrderEventStatusResponseDTO;
import com.reagent.order.enums.OrderEventStatusEnum;
import com.reagent.order.enums.OrderPushEventEnum;
import com.reagent.order.enums.config.OmsDockingConfigValueEnum;
import com.reagent.order.enums.event.OrderReturnEventEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.order.annotation.ServiceLog;
import com.ruijing.order.enums.OperationType;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.order.utils.DictionaryUtils;
import com.ruijing.shop.crm.api.pojo.dto.QualificationDTO;
import com.ruijing.shop.crm.api.support.enums.SuppContactTypeEnum;
import com.ruijing.shop.goods.api.enums.DangerousTypeEnum;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.enums.GoodsReturnOperatorTypeEnum;
import com.ruijing.store.goodsreturn.request.*;
import com.ruijing.store.goodsreturn.service.BarCodeGoodsReturnService;
import com.ruijing.store.goodsreturn.service.BuyerGoodsReturnService;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.*;
import com.ruijing.store.notice.enums.NoticeEventEnum;
import com.ruijing.store.notice.service.GenericCallService;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.GoodsReturnOperationTypeEnum;
import com.ruijing.store.order.api.base.enums.GoodsReturnStatusEnum;
import com.ruijing.store.order.api.base.enums.OrderStatusEnum;
import com.ruijing.store.order.api.base.enums.ProcessSpeciesEnum;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnCancelRequstDTO;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnNoticeDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailExtraDTO;
import com.ruijing.store.order.api.base.orderdetail.dto.OrderDetailReq;
import com.ruijing.store.order.api.base.orderdetail.service.OrderDetailService;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderDetailExtraEnum;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.GoodsReturnParamDTO;
import com.ruijing.store.order.api.base.ordermaster.service.OrderManageRpcService;
import com.ruijing.store.order.api.base.other.dto.RefFundcardOrderDTO;
import com.ruijing.store.order.base.core.bo.LoginUserInfoBO;
import com.ruijing.store.order.base.core.bo.goodsreturn.GoodsReturnPageRequestBO;
import com.ruijing.store.order.base.core.mapper.GoodsReturnLogDOMapper;
import com.ruijing.store.order.base.core.mapper.GoodsReturnMapper;
import com.ruijing.store.order.base.core.mapper.OrderDetailMapper;
import com.ruijing.store.order.base.core.mapper.OrderMasterMapper;
import com.ruijing.store.order.base.core.model.*;
import com.ruijing.store.order.base.core.service.RefFundcardOrderService;
import com.ruijing.store.order.base.core.translator.GoodsReturnLogTranslator;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.base.docking.service.DockingConfigCommonService;
import com.ruijing.store.order.base.docking.service.DockingExtraService;
import com.ruijing.store.order.business.bo.buyercenter.myorderlist.SuppShopInfoBO;
import com.ruijing.store.order.business.handler.OrderEmailHandler;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.CategoryConstant;
import com.ruijing.store.order.constant.ConfigConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;
import com.ruijing.store.order.other.service.ReimbursementService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.rpc.util.SuppQualificationUtils;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.ListUtils;
import com.ruijing.store.order.util.PageResponseUtils;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.wms.api.dto.EntryWithDrawDTO;
import com.ruijing.store.wms.api.dto.entry.BizWmsEntryDTO;
import com.ruijing.store.wms.api.dto.entry.BizWmsEntryDetailDTO;
import com.ruijing.store.wms.api.dto.entry.BizWmsOrderEntryDTO;
import com.ruijing.store.wms.api.dto.requset.BizWarehouseEntryReq;
import com.ruijing.store.wms.api.enums.ApprovalTaskStatusEnum;
import com.ruijing.store.wms.api.enums.InboundStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/1/4 12:48
 **/
@Service
@CatAnnotation
public class BuyerGoodsReturnServiceImpl implements BuyerGoodsReturnService {

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    private static final String CAT_TYPE = "BuyerGoodsReturnService";

    /**
     * 退货中或退货完成的状态
     */
    private final List<Integer> RETURNED_OR_RETURNING_STATUS = New.list(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode(),
            GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode(),
            GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode(),
            GoodsReturnStatusEnum.RETURNED_GOODS.getCode(),
            GoodsReturnStatusEnum.SUCCESS.getCode());

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private UserClient userClient;

    @Resource
    private CommonGoodsReturnService commonGoodsReturnService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private OrderManageRpcService orderManageRpcService;

    @Resource
    private OrderEmailHandler orderEmailHandler;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private BarCodeGoodsReturnService barCodeGoodsReturnService;

    @Resource
    private DockingExtraService dockingExtraService;

    @Resource
    private GenericCallService genericCallService;

    @Resource
    private RefFundcardOrderService refFundcardOrderService;

    @Resource
    private OrderPushEventStatusClient orderPushEventStatusClient;

    @Resource
    private ReimbursementService reimbursementService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private DockingConfigCommonService dockingConfigCommonService;

    @Resource
    private SysConfigClient sysConfigClient;

    @Resource
    private ThirdPartOrderRPCClient thirdPartOrderRPCClient;

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private BizWareHouseClient bizWareHouseClient;

    @Resource
    private OrderDetailExtraClient orderDetailExtraClient;

    @Override
    public BasePageResponseDTO<GoodsReturnPageVO> getPageGoodsReturn(GoodsReturnPageRequestDTO request, RjSessionInfo rjSessionInfo) {
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        // 获取当前登录用户的部门，单位，个人等信息
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
        if (CollectionUtils.isEmpty(loginUserInfo.getDeptIdList())) {
            // 无归属部门需要直接返回空
            return new BasePageResponseDTO<>(pageNo, pageSize, 0L, New.list());
        }

        GoodsReturnPageRequestBO pageParams = this.wrapGoodsReturnPageParams(request, rjSessionInfo, loginUserInfo);
        BasePageResponseDTO<GoodsReturnPageVO> result = PageResponseUtils.pageInvoke(
                () -> goodsReturnMapper.findByPageParams(pageParams),
                GoodsReturnTranslator::doToVO,
                pageNo, pageSize
        );
        // 最多显示10000条
        result.setTotal(Math.min(result.getTotal(), 10000L));

        // 处理订单额外信息
        fillOrderExtraInfo(result.getData());
        // 填充订单详情信息
        fillOrderDetailInfo(result.getData());

        // 补充供应商QQ
        List<Integer> supplierIdList = ListUtils.toList(result.getData(), GoodsReturnPageVO::getSupplierId);
        if (CollectionUtils.isNotEmpty(supplierIdList)) {
            // 兼容联系方式出错情况
            Map<Integer, SuppShopInfoBO> supplierContactInfoMap = null;
            try {
                supplierContactInfoMap = suppClient.getSupplierContactInfoMap(supplierIdList, rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "getPageGoodsReturn", e.getMessage(), e);
                supplierContactInfoMap = new HashMap<>();
            }
            Map<Integer, SuppShopInfoBO> finalSupplierContactInfoMap = supplierContactInfoMap;
            result.getData().forEach(it -> {
                SuppShopInfoBO supplier = finalSupplierContactInfoMap.get(it.getSupplierId());
                it.setSuppQQ(supplier != null ? supplier.getQq() : StringUtils.EMPTY);
            });
        }

        return result;
    }

    @Override
    public GoodsReturnApplyResponseVO applyGoodsReturn(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        Preconditions.notNull(request.getOrderNo(), "申请失败！订单号不可为空！");
        String cacheKey = "apply_goods_return_" + request.getOrderNo();
        try {
            cacheClient.controlRepeatOperation(cacheKey, 30);
            return this.applyGoodsReturnMain(request, rjSessionInfo);
        } catch (Exception e) {
            LOGGER.error(request.getOrderNo() + "退货失败", e);
            Cat.logError(e);
            String msg = e instanceof CompletionException ? e.getCause().getMessage() : e.getMessage();
            Preconditions.isTrue(false, msg);
        } finally {
            cacheClient.removeCache(cacheKey);
        }
        return null;
    }

    public GoodsReturnApplyResponseVO applyGoodsReturnMain(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        if (request.getId() != null) {
            GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(request.getId());
            // 校验退货单状态是否可编辑
            Set<Integer> allowEditStatus = New.set(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode(), GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode());
            BusinessErrUtil.isTrue(allowEditStatus.contains(goodsReturn.getGoodsReturnStatus()), ExecptionMessageEnum.ONLY_PENDING_OR_REJECTED_RETURN_ORDER_CAN_EDIT);
            // 编辑退货单
            return editGoodsReturnCore(request, rjSessionInfo);
        }
        // 申请退货逻辑
        String orderNo = request.getOrderNo();
        Preconditions.notNull(orderNo, "申请失败！订单号不可为空！");

        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.NO_MATCHING_ORDER_FOR_RETURN);
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        BusinessErrUtil.notNull(orderDetailDOList, ExecptionMessageEnum.NO_PRODUCT_DETAILS_FOR_ORDER);
        // 1.校验申请退货条件
        this.checkApplyGoodsReturnCondition(request, orderMasterDO, orderDetailDOList, rjSessionInfo);
        // 2.request 转换为 goodsReturnDO
        GoodsReturn goodsReturn = GoodsReturnTranslator.applyRequestDTOToDO(request, orderMasterDO, orderDetailDOList);
        // 中大的申请退货传的是订单号
        List<GoodsReturn> returnByOrderNo = goodsReturnMapper.findByOrderNo(orderNo);
        Integer orgId = rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER);
        Long userId = rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER);
        boolean returnNotEmpty = CollectionUtils.isNotEmpty(returnByOrderNo);
        // 中大的订单之前退货单未撤销，则直接更新退货单信息。如果已经都撤销才生成新的退货单
        if (returnNotEmpty) {
            boolean isZhongDa = ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(orgId);
            boolean unCancel = !returnByOrderNo.stream().allMatch(g -> GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(g.getGoodsReturnStatus()));
            if (isZhongDa && unCancel) {
                saveReturnOperationLog(request, returnByOrderNo.get(0).getId(), rjSessionInfo);
                // 泛化调用中大(非办公用品)接口推送申请退货成功
                this.noticeApplyGoodsReturnComplete(orgId, userId, orderNo, New.list(returnByOrderNo.get(0).getReturnNo()), NoticeEventEnum.BUYER_APPLY_RETURN_ORDER_COMPLETE);
                request.setId(returnByOrderNo.get(0).getId());
                return this.applyGoodsReturnSuccess(request);
            }
        }
        AtomicReference<LoginUserInfoBO> loginUserInfo = new AtomicReference<>();

        // 申请退货凭证
        String imageListString = StringUtils.EMPTY;
        List<String> goodsImageList = request.getGoodsImageList();
        if (CollectionUtils.isNotEmpty(goodsImageList)) {
            imageListString = String.join(";", goodsImageList);
        }
        // 2.设置其他退货信息
        // 2.1设置退货单号
        CompletableFuture<?> generateReturnNoTask = AsyncExecutor.listenableRunAsync(() -> {
            goodsReturn.setReturnNo(request.getReturnNo());
            if (request.getReturnNo() == null) {
                String returnNo = commonGoodsReturnService.createReturnNo(orderNo);
                goodsReturn.setReturnNo(returnNo);
            }
        }).addFailureCallback(throwable -> {
            LOGGER.error("初始化退货单号失败:" + throwable);
            Cat.logError(CAT_TYPE, "createReturnNo", "初始化退货单号失败！", throwable);
        }).completable();

        // 2.2设置用户信息
        CompletableFuture<?> setUserInfoTask = AsyncExecutor.listenableRunAsync(() -> {
            // 获取当前登录用户的部门，单位，个人等信息
            LoginUserInfoBO loginUserItem = userClient.getLoginUserInfoBySessionInfo(rjSessionInfo);
            goodsReturn.setOrgId(loginUserItem.getOrgId());
            goodsReturn.setOrgName(loginUserItem.getOrgName());
            goodsReturn.setApplyName(loginUserItem.getUserName());
            goodsReturn.setUserId(loginUserItem.getUserId());
            loginUserInfo.set(loginUserItem);
        }).addFailureCallback(throwable -> {
            LOGGER.error("获取登录用户信息失败:" + throwable);
            Cat.logError(CAT_TYPE, "getLoginUserInfoBySessionInfo", "获取登录用户信息失败！", throwable);
        }).completable();
        // 等待所有任务完成
        CompletableFuture.allOf(generateReturnNoTask, setUserInfoTask).join();

        // 3.更新order_detail的状态, 退货数量和退货金额, 控制好并发调用
        this.accumulateOrderDetailReturnCount(request, goodsReturn);
        // 4.保存退货申请单信息
        goodsReturnMapper.insertSelective(goodsReturn);

        // 5.插入操作日志
        commonGoodsReturnService.saveReturnOperationLog(
                GoodsReturnLogTranslator.buildDO(loginUserInfo.get().getUserId(), loginUserInfo.get().getUserName(), goodsReturn.getId(), GoodsReturnOperatorTypeEnum.PURCHASE, GoodsReturnOperationTypeEnum.PURCHASE_APPLY_GOODS_RETURN, StringUtils.EMPTY, imageListString)
        );
        // 6.将汇卡记录更新为失效状态
        reimbursementService.invalidRecord(orderMasterDO);
        // 7.更新超时订单统计数据
        this.updateTimeOutStatisticsByApply(loginUserInfo, goodsReturn);
        // 发送邮件/短信通知
        AsyncExecutor.listenableRunAsync(() -> {
            orderMessageHandler.sendApplyReturnEmailToSupplier(goodsReturn, loginUserInfo.get().getUserName());
        }).addFailureCallback(throwable -> {
            LOGGER.error("发送邮件/短信通知失败:" + throwable);
            Cat.logError(CAT_TYPE, "orderEmailHandler", "发送邮件/短信通知失败！", throwable);
        });
        // 8.泛化调用中大(非办公用品)接口推送申请退货成功
        this.noticeApplyGoodsReturnComplete(orgId, userId, orderNo, New.list(goodsReturn.getReturnNo()), NoticeEventEnum.BUYER_APPLY_RETURN_ORDER_COMPLETE);
        // 通知thunder退货
        thirdPartOrderRPCClient.noticeReturnEvent(goodsReturn.getReturnNo(), OrderReturnEventEnum.BUYER_APPLY_RETURN_ORDER);
        // 9.返回退货申请信息
        return this.wrapGoodsReturnApplyResponseVO(goodsReturn);
    }

    /**
     * 编辑退货单接口
     * 编辑退货单只允许修改：退货原因、退货说明、退货凭证
     */
    public GoodsReturnApplyResponseVO doEditGoodsReturn(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        BusinessErrUtil.notNull(request, ExecptionMessageEnum.PARAMETER_CANNOT_BE_EMPTY);
        BusinessErrUtil.notNull(request.getId(), ExecptionMessageEnum.PARAMETER_CANNOT_BE_EMPTY);
        // 获取退货单信息
        GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(request.getId());
        BusinessErrUtil.notNull(goodsReturn, ExecptionMessageEnum.GOOD_RETURN_NOT_FOUND);

        // 校验退货单状态是否可编辑
        Set<Integer> allowEditStatus = New.set(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode(), GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode());
        BusinessErrUtil.isTrue(allowEditStatus.contains(goodsReturn.getGoodsReturnStatus()), ExecptionMessageEnum.ONLY_PENDING_OR_REJECTED_RETURN_ORDER_CAN_EDIT);
        List<GoodsReturnInfoDetailVO> returnDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturn.getGoodsReturnDetailJSON());
        BusinessErrUtil.notEmpty(returnDetailVOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_RETURN_DETAILS);

        // 需求不允许编辑退货商品数量和种类，所有商品详情直接从退货单拿就行
        List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailRequestDTOS = GoodsReturnTranslator.convertReturnDetail2Request(returnDetailVOList);
        for (GoodsReturnApplyDetailRequestDTO requestDTO : returnApplyDetailRequestDTOS) {
            requestDTO.setReturnReason(request.getReturnReason());
            requestDTO.setRemark(request.getRemark());
        }
        request.setReturnApplyDetailList(returnApplyDetailRequestDTOS);
        request.setOrderNo(goodsReturn.getOrderNo());
        return editGoodsReturnCore(request, rjSessionInfo);
    }

    @Override
    public GoodsReturnApplyResponseVO editGoodsReturn(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        BusinessErrUtil.notNull(request.getId(), ExecptionMessageEnum.PARAMETER_CANNOT_BE_EMPTY);
        final String lockKey = "GOODS_RETURN_EDIT_LOCK" + request.getId();
        boolean getLock = false;
        try {
            getLock = cacheClient.tryLock(lockKey, 10);
            BusinessErrUtil.isTrue(getLock, "操作过于频繁,请稍后再试");

            return doEditGoodsReturn(request, rjSessionInfo);
        } catch (Exception e) {
            LOGGER.error("退货单id为{}的退货单编辑失败", request.getId(), e);
            throw e;
        } finally {
            if (getLock) {
                cacheClient.unlock(lockKey);
            }
        }
    }

    /**
     * 执行编辑退货单的核心逻辑
     * @param request 退货申请请求
     * @param rjSessionInfo 登录信息
     * @return 退货申请响应
     */
    private GoodsReturnApplyResponseVO editGoodsReturnCore(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        // 记录操作日志
        insertReturnOperationLog(request, request.getId(), rjSessionInfo);
        // 返回更新后的退货单信息
        return applyGoodsReturnSuccess(request);
    }

    void updateTimeOutStatisticsByApply(AtomicReference<LoginUserInfoBO> loginUserInfo, GoodsReturn goodsReturn) {
        GoodsReturnParamDTO returnParamDTO = new GoodsReturnParamDTO();
        returnParamDTO.setDepartmentId(goodsReturn.getDepartmentId());
        returnParamDTO.setOrganizationId(loginUserInfo.get().getOrgId());
        returnParamDTO.setOrderMasterId(goodsReturn.getOrderId());
        returnParamDTO.setOrgCode(loginUserInfo.get().getOrgCode());

        orderManageRpcService.applyGoodsReturnWithTimeOut(returnParamDTO);
    }

    private void checkApplyGoodsReturnCondition(GoodsReturnApplyRequestDTO request, OrderMasterDO orderMasterDO, List<OrderDetailDO> orderDetailDOList, RjSessionInfo rjSessionInfo) {
        // 1.判断商品
        BusinessErrUtil.notEmpty(request.getReturnApplyDetailList(), "需要退货的商品不可为空！");
        Map<Integer, OrderDetailDO> detailIdIdentityMap = DictionaryUtils.toMap(orderDetailDOList, OrderDetailDO::getId, Function.identity());
        // 这里防止有绕过前端直接进行退货请求的，必须要与对应订单的商品详情匹配上
        boolean haveNoneMatchDetail = request.getReturnApplyDetailList().stream().anyMatch(returnItem-> detailIdIdentityMap.get(returnItem.getDetailId()) == null);
        BusinessErrUtil.isTrue(!haveNoneMatchDetail, ExecptionMessageEnum.RETURN_ITEMS_NOT_IN_ORDER);

        // 2.判断状态
        Integer status = orderMasterDO.getStatus();
        boolean isStatusCanReturn = OrderStatusEnum.WaitingForReceive.getValue().equals(status)
                || OrderStatusEnum.WaitingForStatement_1.getValue().equals(status)
                || OrderStatusEnum.OrderReceiveApproval.getValue().equals(status)
                || OrderStatusEnum.OrderReceiveApprovalTwo.getValue().equals(status)
                || OrderStatusEnum.ORDER_RECEIVE_APPROVAL_LEVEL_TWO_REJECT.getValue().equals(status)
                || OrderStatusEnum.Finish.getValue().equals(status)
                || OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL.getValue().equals(status)
                || OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL_REJECT.getValue().equals(status);
        if (OrgEnum.ZHONG_SHAN_SAN_YUAN_LIN_CHUANG.getCode().equals(orderMasterDO.getFusercode())) {
            isStatusCanReturn = OrderStatusEnum.WaitingForReceive.getValue().equals(status);
        }
        BusinessErrUtil.isTrue(isStatusCanReturn, ExecptionMessageEnum.CANNOT_RETURN_CURRENT_ORDER_STATUS, Optional.ofNullable(OrderStatusEnum.get(status)).map(OrderStatusEnum::getName).orElse("未知"));

        // 3.判断供应商资质
        List<QualificationDTO> qualificationDTOList = suppClient.getQualificationList(New.list(orderMasterDO.getFsuppid()));
        BusinessErrUtil.notEmpty(qualificationDTOList, ExecptionMessageEnum.FAILED_TO_OBTAIN_SUPPLIER_QUALIFICATION, orderMasterDO.getFsuppid());
        BusinessErrUtil.isTrue(!SuppQualificationUtils.CANCELED.test(qualificationDTOList.get(0)), ExecptionMessageEnum.MERCHANT_CANCELLED_CANNOT_RETURN);

        // 4.判断是否整单退货
        List<RefFundcardOrderDTO> refFundCardOrderDTOList = refFundcardOrderService.findByOrderIdList(New.list(orderMasterDO.getId()));
        if (refFundCardOrderDTOList != null && refFundCardOrderDTOList.size() > 1) {
            BusinessErrUtil.isTrue(this.getIfAllReturn(orderDetailDOList, request.getReturnApplyDetailList()), ExecptionMessageEnum.MULTIPLE_BUDGET_CARDS_FULL_RETURN_ONLY);
        }
        // 暨南大学只能整单退货
        if (orderMasterDO.getFuserid().equals(OrgEnum.JI_NAN_DA_XUE.getValue())){
            BusinessErrUtil.isTrue(this.getIfAllReturn(orderDetailDOList, request.getReturnApplyDetailList()), ExecptionMessageEnum.JINAN_UNIVERSITY_FULL_RETURN_ONLY);
        }

        // 5.判断是否线下单已完成
        BusinessErrUtil.isTrue(!(orderMasterDO.getSpecies().intValue() != ProcessSpeciesEnum.NORMAL.getValue() && OrderStatusEnum.Finish.getValue().equals(status)), ExecptionMessageEnum.OFFLINE_ORDER_COMPLETED_CANNOT_RETURN);

        // 6.特殊校验
        // 6.1 广西肿瘤的订单无法在推送失败下申请退货
        if (OrgEnum.GUANG_XI_ZHONG_LIU.getValue() == rjSessionInfo.getOrgId()) {
            dockingExtraService.customValidationDockingStatus(orderMasterDO.getForderno());
        }
        // 6.2 通知其他服务做退货前置校验--中大校验
        this.noticeBeforeApplyGoodsReturnAndVerify(rjSessionInfo.getOrgId(), rjSessionInfo.getUserId(RjUserTypeEnum.STORE_USER), orderMasterDO.getForderno());
        // 6.3 暨南大学特殊校验，一级分类为危化品/二级分类为常规化学品的订单需要确认验收信息推送回调成功，才可以取消订单
        boolean dangerous = false;
        for (OrderDetailDO orderDetailDO : orderDetailDOList) {
            if (orderDetailDO.getFirstCategoryId() == 460 || orderDetailDO.getSecondCategoryId() == 216) {
                dangerous = true;
                break;
            }
        }
        if (orderMasterDO.getFuserid().equals(OrgEnum.JI_NAN_DA_XUE.getValue()) && dangerous){
            // 查询订单推送记录表
            OrderEventStatusRequestDTO requestDTO = new OrderEventStatusRequestDTO();
            requestDTO.setOrderNoList(New.list(orderMasterDO.getForderno()));
            requestDTO.setOrderPushEventEnumList(New.list(OrderPushEventEnum.NOTICE_RECEIVE_TO_OUTER_BUYER));
            List<OrderEventStatusResponseDTO> orderEventStatusResponseDTOS = orderPushEventStatusClient.listEventPushStatus(requestDTO);
            if(CollectionUtils.isNotEmpty(orderEventStatusResponseDTOS)){
                // 推送过验收，需要校验是否验收推送成功了，若未成功则不允许发起退货
                BusinessErrUtil.isTrue(orderEventStatusResponseDTOS.get(0).getOrderEventStatusEnum().equals(OrderEventStatusEnum.COMPLETE),
                        ExecptionMessageEnum.ORDER_CONFIRMATION_PUSH_FAILED);
            }
        }
        // 7.气瓶校验
        this.applyReturnGasBottleVerify(request, orderMasterDO);
    }

    /**
     * 编辑/更新退货单详情信息
     * @param request
     */
    public GoodsReturn updateGoodsReturnDetailInfo(GoodsReturnApplyRequestDTO request) {
        Integer returnId = request.getId();
        GoodsReturn updated = goodsReturnMapper.selectByPrimaryKey(returnId);
        Date applyDate = new Date();
        updated.setCreationTime(applyDate);
        Integer returnStatus = request.getReturnStatus();
        if (returnStatus != null) {
            updated.setGoodsReturnStatus(returnStatus);
        }
        List<String> goodsImageList = request.getGoodsImageList();
        if (CollectionUtils.isNotEmpty(goodsImageList)) {
            String images = String.join(";", goodsImageList);
            updated.setProductPath(images);
        }
        // 填充订单粒度退货原因
        if (StringUtils.isNotBlank(request.getReturnReason())) {
            updated.setReturnReason(request.getReturnReason());
        }
        // 填充订单粒度退货说明
        if (Objects.nonNull(request.getRemark())) {
            updated.setRemark(request.getRemark());
        }
        // 填充detail表信息
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(request.getOrderNo());
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        GoodsReturnTranslator.buildReturnApplyDetails(request.getReturnApplyDetailList(), orderDetailDOList);
        updated.setGoodsReturnDetailJSON(JsonUtils.toJson(request.getReturnApplyDetailList()));
        int affect = goodsReturnMapper.updateGoodsDetailRemarkById(updated);
        if (affect > 0) {
            this.accumulateOrderDetailReturnCount(request, updated, true);
            // 临床更新二维码信息
            barCodeGoodsReturnService.updateOrderReturnForBarCode(updated, OrderProductTransactionStatusEnum.RETURNING.getCode());
        }
        return updated;
    }

    @Override
    public GoodsReturnStatisticsVO getGoodsReturnStatistics(RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId(RjUserTypeEnum.STORE_USER));
        if (CollectionUtils.isEmpty(loginUserInfo.getDeptIdList())) {
            // 无归属部门隐藏数据
            GoodsReturnStatisticsVO emptyRes = new GoodsReturnStatisticsVO();
            emptyRes.setReturnAcceptanceCount(0);
            emptyRes.setTotal(0);
            emptyRes.setWaitingConfirmCount(0);
            emptyRes.setAgreeReturnCount(0);
            emptyRes.setRefusedReturnCount(0);
            emptyRes.setCancelReturnCount(0);
            emptyRes.setReturnedCount(0);
            emptyRes.setSuccessfulCount(0);
            return emptyRes;
        }

        GoodsReturnPageRequestBO params = this.wrapGoodsReturnPageParams(new GoodsReturnPageRequestDTO(), rjSessionInfo, loginUserInfo);
        List<GoodsReturnCountDO> goodsReturnCountList = goodsReturnMapper.countGroupByGoodsReturnStatus(params);

        int total = 0;
        GoodsReturnStatisticsVO result = new GoodsReturnStatisticsVO().setAgreeReturnCount(0).setReturnedCount(0).setSuccessfulCount(0).setWaitingConfirmCount(0)
                .setCancelReturnCount(0).setRefusedReturnCount(0).setReturnAcceptanceCount(0).setTotal(total);
        for (GoodsReturnCountDO countDO : goodsReturnCountList) {
            Integer goodsReturnStatus = countDO.getGoodsReturnStatus();
            if (GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode().equals(goodsReturnStatus)) {
                result.setWaitingConfirmCount(countDO.getCount());
            } else if (GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(goodsReturnStatus)) {
                result.setAgreeReturnCount(countDO.getCount());
            } else if (GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(goodsReturnStatus)) {
                result.setRefusedReturnCount(countDO.getCount());
            } else if (GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(goodsReturnStatus)) {
                result.setCancelReturnCount(countDO.getCount());
            } else if (GoodsReturnStatusEnum.RETURNED_GOODS.getCode().equals(goodsReturnStatus)) {
                result.setReturnedCount(countDO.getCount());
            } else if (GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturnStatus)) {
                result.setSuccessfulCount(countDO.getCount());
            }
            total += countDO.getCount();
            result.setTotal(total);
        }

        return result;
    }

    @Override
    public GoodsReturnInfoVO getGoodsReturnInfo(GoodsReturnBaseRequestDTO request) {
        GoodsReturn goodsReturn = null;
        Integer orderId = request.getOrderId();
        if (orderId != null) {
            List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderId(orderId);
            // 查无结果返回null给前端
            if (CollectionUtils.isEmpty(goodsReturnList)) {
                return null;
            }
            goodsReturn = goodsReturnList.get(0);
        } else {
            // 获取退货单详情信息
            Integer returnId = request.getReturnId();
            Preconditions.notNull(returnId, "查询详情失败！id不可为空！");
            goodsReturn = goodsReturnMapper.selectByPrimaryKey(returnId);
        }
        // 查无结果返回null给前端
        if (goodsReturn == null) {
            return null;
        }
        orderId = goodsReturn.getOrderId();
        GoodsReturnInfoVO result = GoodsReturnTranslator.doToGoodsReturnInfoVO(goodsReturn);
        // 设置现货仓标识
        Map<Integer, String> orderExtraMap = getOrderExtraMap(result.getOrderId());
        String stockWarehouseType = orderExtraMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), String.valueOf(StockTypeEnum.DEFAULT.getValue()));
        result.setStockWarehouseType(Integer.valueOf(stockWarehouseType));
        //1. 设置退货单详情
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        // 设置采购收货地址和联系方式
        result.setDeliveryAddress(orderMasterDO.getFbiderdeliveryplace());
        result.setOrgCode(orderMasterDO.getFusercode());
        UserBaseInfoDTO userInfo = userClient.getUserInfo(orderMasterDO.getFbuyerid(), orderMasterDO.getFuserid());
        result.setPhone(userInfo.getMobile());
        result.setBuyerContactMan(orderMasterDO.getFbuyercontactman());
        result.setBuyerTelephone(orderMasterDO.getFbuyertelephone());
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(result.getOrgId())) {
            Map<Integer, SuppShopInfoBO> supplierContactInfoMap = null;
            try {
                supplierContactInfoMap = suppClient.getSupplierContactInfoMap(Collections.singletonList(result.getSupplierId()), result.getOrgId(), SuppContactTypeEnum.SUPER.getValue());
            } catch (Exception e) {
                Cat.logError(CAT_TYPE, "getGoodsReturnInfo", e.getMessage(), e);
                supplierContactInfoMap = new HashMap<>();
            }
            result.setSupplierContactTelephone(Optional.ofNullable(supplierContactInfoMap.get(result.getSupplierId())).map(SuppShopInfoBO::getMobile).orElse(StringUtils.EMPTY));
        }
        // 返回是否限制只能整单退货，先判断是否对接限制，如果否再判断一般的OMS配置
        OrgDockingConfigDTO config = dockingConfigCommonService.getConfig(orderMasterDO.getFusercode());
        boolean limitOnlyWholeReturn = OmsDockingConfigValueEnum.WHOLE_ORDER.name().equals(config.getOrderDockingConfigDTO().getReturnGoodsRange());
        limitOnlyWholeReturn = limitOnlyWholeReturn && dockingConfigCommonService.isNewDockingEnable(config, orderMasterDO, null);
        if(!limitOnlyWholeReturn){
            String val = sysConfigClient.getConfigByOrgCodeAndConfigCode(orderMasterDO.getFusercode(), ConfigConstant.ORDER_RETURN_ONLY_WHOLE);
            limitOnlyWholeReturn = CommonValueUtils.TRUE_NUMBER_STR.equals(val);
        }
        result.setLimitOnlyWholeReturn(limitOnlyWholeReturn);
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue(), OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue()));
        Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        result.setEachProductEachCode(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue())));
        result.setSuppNeedFillBatchesData(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue())));
        // 构造气瓶数据
        this.constructReturnDetailGasBottle(result);
        // 填充商品信息
        this.fillGoodsReturnDetailInfo(result);
        return result;
    }
    
    /**
     * 填充退货详情商品信息
     *
     * @param goodsReturnInfoVO 退货单详情
     */
    private void fillGoodsReturnDetailInfo(GoodsReturnInfoVO goodsReturnInfoVO) {
        if (Objects.isNull(goodsReturnInfoVO) || CollectionUtils.isEmpty(goodsReturnInfoVO.getReturnInfoDetailList())) {
            return;
        }
        List<GoodsReturnInfoDetailVO> returnInfoDetailList = goodsReturnInfoVO.getReturnInfoDetailList();
        List<Integer> orderDetailIds = returnInfoDetailList.stream().map(GoodsReturnInfoDetailVO::getDetailId).map(Integer::valueOf).collect(Collectors.toList());

                
        if (CollectionUtils.isEmpty(orderDetailIds)) {
            return;
        }

        // 获取订单详情扩展信息
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(null, orderDetailIds);
        if (CollectionUtils.isEmpty(orderDetailExtraDTOList)) {
            return;
        }

        // 按detailId分组
        Map<Integer, List<OrderDetailExtraDTO>> detailId2ExtraMap = orderDetailExtraDTOList.stream()
                .collect(Collectors.groupingBy(OrderDetailExtraDTO::getOrderDetailId));

        // 查询订单详情主表
        List<OrderDetailDO> orderDetailDOS = orderDetailMapper.findByIdIn(orderDetailIds);

        if(CollectionUtils.isEmpty(orderDetailDOS)){
            return;
        }

        // 根据详情ID分组
        Map<Integer, OrderDetailDO> detailId2OrderDetailMap = orderDetailDOS.stream()
                .collect(Collectors.toMap(OrderDetailDO::getId, Function.identity(), (v1, v2) -> v1));
                
        // 填充商品信息
        for (GoodsReturnInfoDetailVO detailVO : returnInfoDetailList) {
            if (Objects.isNull(detailVO.getDetailId())) {
                continue;
            }
            Integer detailId = Integer.valueOf(detailVO.getDetailId());
            
            List<OrderDetailExtraDTO> extraDTOList = detailId2ExtraMap.get(detailId);
            OrderDetailDO orderDetailDO = detailId2OrderDetailMap.get(detailId);
            if (Objects.isNull(orderDetailDO)) {
                continue;
            }

            detailVO.setFirstCategoryId(orderDetailDO.getFirstCategoryId());
            detailVO.setUnit(orderDetailDO.getFunit());
            detailVO.setCasNo(orderDetailDO.getCasno());
            // 填充危化品标签
            Integer dangerousTypeId = orderDetailDO.getDangerousTypeId();
            if (Objects.isNull(dangerousTypeId)) {
                dangerousTypeId = DangerousTypeEnum.UN_DANGEROUS.getValue();
            }
            detailVO.setDangerousType(dangerousTypeId);
            detailVO.setDangerousTag(DangerousTypeEnum.get(dangerousTypeId).getName());

            // 填充商品拓展字段
            setGoodsReturnDetailExtraInfo(detailVO, extraDTOList, orderDetailDO);
        }
    }
    
    /**
     * 填充退货单详情扩展字段
     *
     * @param orderDetailExtraDTOS 订单详情扩展信息
     * @param detailVO             退货单详情VO
     */
    private void setGoodsReturnDetailExtraInfo(GoodsReturnInfoDetailVO detailVO, List<OrderDetailExtraDTO> orderDetailExtraDTOS, OrderDetailDO orderDetailDO) {
        if (CollectionUtils.isEmpty(orderDetailExtraDTOS) || Objects.isNull(orderDetailDO)) {
            return;
        }

        Map<Integer, OrderDetailExtraDTO> type2DetailExtraMap = orderDetailExtraDTOS.stream()
                .collect(Collectors.toMap(OrderDetailExtraDTO::getExtraKeyType, Function.identity(), (v1, v2) -> v1));
                
        // 包装规格 , 化学试剂和危险化学品 取值详情的specification，其他分类取PACKING_UNIT
        if (New.set(CategoryConstant.DANGEROUS_ID, CategoryConstant.CHEMICAL_REAGENTS_ID).contains(orderDetailDO.getFirstCategoryId())) {
            detailVO.setPackingSpec(detailVO.getSpecification());
        } else {
            OrderDetailExtraDTO packUnitExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PACKING_UNIT.getType());
            if (Objects.nonNull(packUnitExtraDTO)) {
                detailVO.setPackingSpec(packUnitExtraDTO.getExtraValue());
            }
        }
        
        // 浓度
        OrderDetailExtraDTO concentrationExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.CONCENTRATION.getType());
        if (Objects.nonNull(concentrationExtraDTO)) {
            detailVO.setPurity(concentrationExtraDTO.getExtraValue());
        }

        // 注册编码
        OrderDetailExtraDTO registCertExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
        if (Objects.nonNull(registCertExtraDTO)) {
            detailVO.setMedicalDeviceRegisCertNumber(registCertExtraDTO.getExtraValue());
        }

        // 完成周期
        OrderDetailExtraDTO completionCycleExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.COMPLETION_CYCLE.getType());
        if (Objects.nonNull(completionCycleExtraDTO)) {
            detailVO.setCompletionCycle(completionCycleExtraDTO.getExtraValue());
        }

        // 出版社
        OrderDetailExtraDTO pressExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PRESS.getType());
        if (Objects.nonNull(pressExtraDTO)) {
            detailVO.setPress(pressExtraDTO.getExtraValue());
        }
        
        // 产品规格
        OrderDetailExtraDTO productSpecExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PRODUCT_SPECIFICATION.getType());
        if (Objects.nonNull(productSpecExtraDTO)) {
            detailVO.setProductSpec(productSpecExtraDTO.getExtraValue());
        }
        
        // 型号
        OrderDetailExtraDTO modelExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.MODEL_NUMBER.getType());
        if (Objects.nonNull(modelExtraDTO)) {
            detailVO.setModelNumber(modelExtraDTO.getExtraValue());
        }
    }


    @Override
    @ServiceLog(operationType = OperationType.WRITE,description = "取消退货申请")
    public boolean cancelGoodsReturn(GoodsReturnBaseRequestDTO request, RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfoBySessionInfo(rjSessionInfo);
        
        Integer returnId = request.getReturnId();
        List<Integer> returnIdList = request.getReturnIdList();
        if (CollectionUtils.isEmpty(returnIdList) && Objects.nonNull(returnId)) {
            returnIdList = new ArrayList<>();
            returnIdList.add(returnId);
        }
        if(CollectionUtils.isEmpty(returnIdList) && ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(loginUserInfo.getOrgId())){
            Integer orderId = request.getOrderId();
            List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(New.list(orderId));
            BusinessErrUtil.notEmpty(goodsReturnList, "查询不到退货单信息");
            //中大是整单退货 因此退货记录只要有一条拒绝退货记录即可
            returnIdList = goodsReturnList.stream()
                    .filter(goodsReturn -> GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(goodsReturn.getGoodsReturnStatus()))
                    .map(GoodsReturn::getId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(returnIdList)){
                return false;
            }
        }
        Preconditions.notEmpty(returnIdList, "撤销失败，退货单id不可为空！");

        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByIdIn(returnIdList);
        return cancelGoodsReturnCore(goodsReturnList,loginUserInfo.getUserId(), loginUserInfo.getUserName(),GoodsReturnOperationTypeEnum.PURCHASE_CANCEL_GOODS_RETURN, StringUtils.EMPTY);
    }

    /**
     * 取消/撤销退货, 支持批量取消
     *
     * @param goodsReturnCancelRequstDTO 入参
     * @return 是否成功
     */
    @Override
    public boolean forceCancelGoodsReturn(GoodsReturnCancelRequstDTO goodsReturnCancelRequstDTO) {
        List<Integer> orderIdList = goodsReturnCancelRequstDTO.getOrderIdList();
        Preconditions.notEmpty(orderIdList, "传入的订单id不能为空");

        Integer userId = goodsReturnCancelRequstDTO.getUserId();
        Integer orgId = goodsReturnCancelRequstDTO.getOrgId();
        Preconditions.notNull(userId, "传入的用户id不能为空");
        Preconditions.notNull(orgId, "传入的单位id不能为空");
        UserBaseInfoDTO userInfo = userClient.getUserInfo(goodsReturnCancelRequstDTO.getUserId(), goodsReturnCancelRequstDTO.getOrgId());

        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIdList);
        // 过滤掉已经完成取消或已经完成的退货单
        goodsReturnList = goodsReturnList.stream().filter(item-> !GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(item.getGoodsReturnStatus())
                        && !GoodsReturnStatusEnum.SUCCESS.getCode().equals(item.getGoodsReturnStatus())).collect(Collectors.toList());
        // 如果没有退货单都处于取消申请/完成状态，就不用继续操作了
        if (CollectionUtils.isEmpty(goodsReturnList)) {
            return true;
        }
        return cancelGoodsReturnCore(goodsReturnList, userId, userInfo.getName(), GoodsReturnOperationTypeEnum.FORCE_CANCEL_GOODS_RETURN, goodsReturnCancelRequstDTO.getReason());
    }

    @Override
    public boolean cancelGoodsReturnByThunder(GoodsReturnBaseRequestDTO request) {
        final String CANCEL_MAN = "系统";
        final Integer CANCEL_MAN_ID = -1;
        
        List<String> returnNoList = request.getReturnNoList();

        Preconditions.notEmpty(returnNoList, "撤销失败，退货单号不可为空！");
        
        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByReturnNoIn(returnNoList);
        return cancelGoodsReturnCore(goodsReturnList,CANCEL_MAN_ID, CANCEL_MAN,GoodsReturnOperationTypeEnum.SUPPLIER_REJECT_THEN_AUTO_CANCEL_GOODS_RETURN, StringUtils.EMPTY);
    }

    @Override
    public boolean systemCancelGoodsReturn(List<Integer> orderIdList, GoodsReturnOperationTypeEnum goodsReturnOperationTypeEnum, String reason) {
        final String CANCEL_MAN = "系统";
        final Integer CANCEL_MAN_ID = -1;

        Preconditions.notEmpty(orderIdList, "撤销失败，退货订单id列表不可为空！");

        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderIds(orderIdList);
        // 非退货完成或已取消的才需要取消
        goodsReturnList = goodsReturnList.stream().filter(goodsReturn ->
                !(GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturn.getGoodsReturnStatus())
                        || GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(goodsReturn.getGoodsReturnStatus())))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(goodsReturnList)){
            return true;
        }
        return cancelGoodsReturnCore(goodsReturnList,CANCEL_MAN_ID, CANCEL_MAN,goodsReturnOperationTypeEnum, reason);
    }

    @Override
    public boolean updateGoodsReturn(GoodsReturnBaseRequestDTO request, RjSessionInfo rjSessionInfo) {
        Integer returnId = request.getReturnId();
        Integer returnStatus = request.getReturnStatus();
        BusinessErrUtil.notNull(rjSessionInfo, ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        Preconditions.notNull(returnId, "更新退货订单失败！退货单id不可空！");
        Preconditions.notNull(returnStatus, "更新退货订单失败！退货单状态参数不可为空！");

        GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(returnId);
        // 如果非供应商同意退货采购人不能点击'已退还货物'
        if (!GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(goodsReturn.getGoodsReturnStatus())) {
            return false;
        }
        GoodsReturn updated = new GoodsReturn();
        updated.setId(returnId);
        updated.setGoodsReturnStatus(returnStatus);
        goodsReturnMapper.updateByPrimaryKeySelective(updated);
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        // 保存退货操作日志
        commonGoodsReturnService.saveReturnOperationLog(
                GoodsReturnLogTranslator.buildDO(loginUserInfo.getUserId(), loginUserInfo.getUserName(), returnId, GoodsReturnOperatorTypeEnum.PURCHASE, GoodsReturnOperationTypeEnum.PURCHASE_MANUAL_GOODS_RETURN, StringUtils.EMPTY, StringUtils.EMPTY)
        );
        return true;
    }

    @Override
    public List<GoodsReturnLogVO> getGoodsReturnLog(GoodsReturnBaseRequestDTO request) {
        Integer returnId = request.getReturnId();
        Integer orderId = request.getOrderId();
        if (orderId != null) {
            List<GoodsReturn> byOrderId = goodsReturnMapper.findByOrderId(orderId);
            if (CollectionUtils.isEmpty(byOrderId)) {
                return Collections.emptyList();
            }
            returnId = byOrderId.get(0).getId();
        }
        Preconditions.notNull(returnId, "查询退货操作日志失败！退货单id不可空！");

        List<GoodsReturnLogDO> list = goodsReturnLogDOMapper.findByReturnId(returnId);
        List<GoodsReturnLogVO> result = list.stream().map(GoodsReturnLogTranslator::doToVO).collect(Collectors.toList());
        return result;
    }

    @Override
    public int getReturnCountByDetailId(GoodsReturnDetailBaseRequestDTO request) {
        Integer orderId = request.getOrderId();
        Integer detailId = request.getDetailId();
        Preconditions.notNull(orderId, "获取退货明细退货数量失败，订单id不可为空");
        Preconditions.notNull(detailId, "获取退货明细退货数量失败，订单明细id不可为空");

        List<GoodsReturn> goodsReturnList = goodsReturnMapper.findByOrderId(orderId);
        BusinessErrUtil.notEmpty(goodsReturnList, ExecptionMessageEnum.FAILED_TO_OBTAIN_RETURN_DETAILS);
        int sum = goodsReturnList.stream()
                .filter(it -> !GoodsReturnStatusEnum.CANCEL_REQUEST.getCode().equals(it.getGoodsReturnStatus()) && !GoodsReturnStatusEnum.SUCCESS.getCode().equals(it.getGoodsReturnStatus()))
                .map(it -> {
                    String goodsReturnDetailJSON = it.getGoodsReturnDetailJSON();
                    List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON);
                    for (GoodsReturnInfoDetailVO detailItem : goodsReturnInfoDetailVOList) {
                        if (detailItem.getDetailId().equals(detailId.toString())) {
                            return 1;
                        }
                    }
                    return 0;
                }).mapToInt(i -> i).sum();
        return sum;
    }

    @Override
    public ApplyGoodsReturnOrderVO getApplyGoodsReturnData(String orderNo, Integer orgId) {
        BusinessErrUtil.notNull(orderNo, "订单号不可空");
        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(orderNo);
        BusinessErrUtil.notNull(orderMasterDO, "该订单不存在！");
        BusinessErrUtil.isTrue(orderMasterDO.getFuserid().equals(orgId), ExecptionMessageEnum.NO_PERMISSION_TO_VIEW_UNIT_DATA);
        // 气瓶校验
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setReturnBindGasBottle(true);
        orderDetailReq.setOrderMasterIdList(New.list(orderMasterDO.getId()));
        // 排除掉退货中和退货完成的气瓶及商品
        orderDetailReq.setExcludeReturnStatusList(New.list(RETURNED_OR_RETURNING_STATUS));
        List<OrderDetailDTO> orderDetailDTOList = orderDetailService.findDetailByOrderIdListExcludeReturnStatus(orderDetailReq).getData();
        List<String> notReturnBarcodes = orderDetailDTOList.stream().map(OrderDetailDTO::getBindGasBottleBarcodes).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
        List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(notReturnBarcodes);
        Map<String, GasBottleVO> gasBottleBarcodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
        List<ApplyGoodsReturnOrderDetailVO> applyGoodsReturnOrderDetailVOList = orderDetailDTOList.stream().map(detail->{
            ApplyGoodsReturnOrderDetailVO applyGoodsReturnOrderDetailVO = new ApplyGoodsReturnOrderDetailVO();
            applyGoodsReturnOrderDetailVO.setDetailId(detail.getId());
            if(detail.getBindGasBottleBarcodes() != null){
                applyGoodsReturnOrderDetailVO.setGasBottles(detail.getBindGasBottleBarcodes().stream().map(gasBottleBarcodeIdentityMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            return applyGoodsReturnOrderDetailVO;
        }).collect(Collectors.toList());
        ApplyGoodsReturnOrderVO applyGoodsReturnOrderVO = new ApplyGoodsReturnOrderVO();
        applyGoodsReturnOrderVO.setOrderNo(orderNo);
        applyGoodsReturnOrderVO.setCanReturnOrderDetails(applyGoodsReturnOrderDetailVOList);
        return applyGoodsReturnOrderVO;
    }

    /**
     * 获取选中退货商品 关联的入库单号集合
     * @param request  退货申请请求
     * @return 正在入库中的入库单号集合
     */
    private List<String> getInStockEntryNos(GoodsReturnApplyRequestDTO request, OrderMasterDO orderMasterDO) {
        String orderNo = request.getOrderNo();
        Integer orgId = orderMasterDO.getFuserid();

        // 查询入库单信息
        BizWarehouseEntryReq bizWareHouseEntryReq = new BizWarehouseEntryReq();
        bizWareHouseEntryReq.setOrderNos(New.list(orderNo));
        bizWareHouseEntryReq.setOrgId(orgId);

        List<BizWmsOrderEntryDTO> wmsOrderEntryDTOS = bizWareHouseClient.queryEntryByOrderNos(bizWareHouseEntryReq);
        if (CollectionUtils.isEmpty(wmsOrderEntryDTOS)) {
            return New.emptyList();
        }

        List<BizWmsEntryDTO> entryList = wmsOrderEntryDTOS.get(0).getEntryList();
        if (CollectionUtils.isEmpty(entryList)) {
            return New.emptyList();
        }

        // 获取申请退货的订单详情ID集合
        Set<Integer> returnDetailIds = request.getReturnApplyDetailList().stream()
                .map(GoodsReturnApplyDetailRequestDTO::getDetailId)
                .collect(Collectors.toSet());

        // 获取正在入库中且包含退货商品的入库单ID集合
        List<String> entryNos = entryList.stream()
                .filter(entry -> InboundStatus.NOTINSTORAGE.getValue().equals(entry.getStatus())
                        && ApprovalTaskStatusEnum.APPROVALING.getValue().equals(entry.getApprovalStatus()))
                .filter(entry -> {
                    // 过滤出包含退货商品的入库单
                    if (CollectionUtils.isEmpty(entry.getDetailList())) {
                        return false;
                    }
                    return entry.getDetailList().stream()
                            .filter(Objects::nonNull)
                            .map(BizWmsEntryDetailDTO::getOrderDetailId)
                            .anyMatch(returnDetailIds::contains);
                })
                .map(BizWmsEntryDTO::getEntryNo)
                .distinct().collect(Collectors.toList());
        return entryNos;
    }

    /**
     * 查询选中的退货商品是否在入库中
     *
     * @param request 退货申请请求
     * @return 是否在入库中
     */
    @Override
    public Boolean checkGoodsReturnEntry(GoodsReturnApplyRequestDTO request) {
        BusinessErrUtil.isTrue(StringUtils.isNotBlank(request.getOrderNo()),ExecptionMessageEnum.ORDER_NUMBER_REQUIRED);
        BusinessErrUtil.notEmpty(request.getReturnApplyDetailList(), "需要退货的商品不可空");

        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(request.getOrderNo());
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);

        // 获取选中的退货商品关联的入库中的入库单ID集合
        List<String> inStockEntryNos = getInStockEntryNos(request,orderMasterDO);

        return CollectionUtils.isNotEmpty(inStockEntryNos);
    }

    /**
     * 发起退货前撤销入库
     *
     * @param request
     * @param rjSessionInfo
     */
    @Override
    public void entryWithDraw(GoodsReturnApplyRequestDTO request, RjSessionInfo rjSessionInfo) {
        BusinessErrUtil.isTrue(StringUtils.isNotBlank(request.getOrderNo()),ExecptionMessageEnum.ORDER_NUMBER_REQUIRED);
        BusinessErrUtil.notEmpty(request.getReturnApplyDetailList(), "需要退货的商品不可空");

        OrderMasterDO orderMasterDO = orderMasterMapper.findByForderno(request.getOrderNo());
        BusinessErrUtil.notNull(orderMasterDO, ExecptionMessageEnum.ORDER_DOES_NOT_EXIST);

        // 查询采购人信息
        List<UserBaseInfoDTO> userBaseInfoDTOS = userClient.getUserByUserIds(New.list(orderMasterDO.getFbuyerid()));
        BusinessErrUtil.notEmpty(userBaseInfoDTOS,ExecptionMessageEnum.PURCHASER_INFO_NOT_FOUND);
        UserBaseInfoDTO buyerInfo = userBaseInfoDTOS.get(0);

        // 获取选中的退货商品关联的入库中的入库单ID集合
        List<String> inStockEntryNos = getInStockEntryNos(request,orderMasterDO);

        if (CollectionUtils.isEmpty(inStockEntryNos)) {
            List<Integer> detailIdList = request.getReturnApplyDetailList().stream()
                    .map(GoodsReturnApplyDetailRequestDTO::getDetailId).collect(Collectors.toList());
            LOGGER.info("未发现需要撤销的入库单，无需撤销入库，订单id：{},选择退货的detailId集合：{}", orderMasterDO.getId(), detailIdList);
            return;
        }

        // 发起撤销入库
        EntryWithDrawDTO entryWithDrawDTO = new EntryWithDrawDTO();
        entryWithDrawDTO.setGuid(buyerInfo.getGuid());
        entryWithDrawDTO.setEntryNoList(inStockEntryNos);
        bizWareHouseClient.entryWithDraw(entryWithDrawDTO);
    }

    /**
     * 根据取消退货更新超时订单信息
     * @param orderId
     */
    private void updateTimeOutStatistics(Integer orderId) {
        AsyncExecutor.listenableRunAsync(() -> {
            OrderMasterDO master = orderMasterMapper.selectByPrimaryKey(orderId);
            GoodsReturnParamDTO request = new GoodsReturnParamDTO();
            request.setDepartmentId(master.getFbuydepartmentid());
            request.setOrganizationId(master.getFuserid());
            request.setOrderMasterId(master.getId());
            request.setOrgCode(master.getFusercode());

            orderManageRpcService.cancelGoodsReturnWithTimeOut(request);
        }).addFailureCallback(throwable -> {
            LOGGER.error("根据取消退货更新超时订单信息{}", throwable);
            Cat.logError(CAT_TYPE, "updateTimeOutStatistics", "待确认的订单更新订单详情商品名失败", throwable);
        });

    }

    /**
     * 更新订单明细退货数量和金额以及状态
     * @param goodsReturnList
     */
    private void updateOrderDetailReturnStatus(List<GoodsReturn> goodsReturnList) {
        if (CollectionUtils.isEmpty(goodsReturnList)) {
            return;
        }
        Integer orderId = goodsReturnList.get(0).getOrderId();
        // 更新订单明细的退货数量
        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = new ArrayList<>();
        for (GoodsReturn goodsItem : goodsReturnList) {
            String goodsReturnDetailJSON = goodsItem.getGoodsReturnDetailJSON();
            goodsReturnInfoDetailVOList.addAll(GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON));
        }
        List<OrderDetailDO> detailList = orderDetailMapper.findByFmasterid(orderId);
        // 查询已存在的退货单信息, 并按照 退货状态显示优先级 待确认0->同意退货1->拒绝退货2->采购人已退货4 排序
        List<GoodsReturn> existGoodsReturnList = goodsReturnMapper.findByOrderId(orderId);
        // 获取当前最优先展示的退货状态的map
        Map<Integer, Integer> detailIdReturnStatusMap = commonGoodsReturnService.getFirstReturnStatusMapByReturn(existGoodsReturnList);

        if (CollectionUtils.isNotEmpty(detailList)) {
            Map<String, List<GoodsReturnInfoDetailVO>> detailIdGoodsReturnVOMap = goodsReturnInfoDetailVOList.stream().collect(Collectors.groupingBy(GoodsReturnInfoDetailVO::getDetailId));
            for (OrderDetailDO detailDO : detailList) {
                // 卖家已收货，买家已确认退货的情况不能撤销退货单
                boolean canCancel = !GoodsReturnStatusEnum.SUCCESS.getCode().equals(detailDO.getReturnStatus()) && !GoodsReturnStatusEnum.RETURNED_GOODS.getCode().equals(detailDO.getReturnStatus());
                if (!canCancel) {
                    continue;
                }
                List<GoodsReturnInfoDetailVO> returnListItem = detailIdGoodsReturnVOMap.get(detailDO.getId().toString());
                if (CollectionUtils.isEmpty(returnListItem)) {
                    continue;
                }
                for (GoodsReturnInfoDetailVO returnItem : returnListItem) {
                    if (returnItem.getQuantity() != null && detailDO.getFcancelquantity().compareTo(returnItem.getQuantity()) >= 0 && canCancel) {
                        detailDO.setFcancelquantity(detailDO.getFcancelquantity().subtract(returnItem.getQuantity()));
                    }
                    if (returnItem.getAmount() != null && detailDO.getReturnAmount().compareTo(returnItem.getAmount().doubleValue()) >= 0 && canCancel) {
                        detailDO.setReturnAmount(detailDO.getReturnAmount() - returnItem.getAmount().doubleValue());
                    }
                }
                // 更新减去订单明细里的退货数量
                // 状态显示优先级 待确认0->同意退货1->拒绝退货2->取消退货3->采购人已退货4->已退货
                Integer returnStatus = detailIdReturnStatusMap.get(detailDO.getId());
                detailDO.setReturnStatus(returnStatus != null ? returnStatus : GoodsReturnStatusEnum.CANCEL_REQUEST.getCode());
            }
            orderDetailMapper.loopUpdateByIdIn(detailList);
        }
    }

    /**
     * 封装查询退货入参
     * @param request
     * @return
     */
    private GoodsReturnPageRequestBO wrapGoodsReturnPageParams(GoodsReturnPageRequestDTO request, RjSessionInfo rjSessionInfo, LoginUserInfoBO loginUserInfo) {
        // 分页查询退货单结果
        GoodsReturnPageRequestBO pageParams = GoodsReturnTranslator.requestDTO2BO(request);
        pageParams.setOrgId(loginUserInfo.getOrgId());
        if (CollectionUtils.isEmpty(pageParams.getDepartmentIdList())) {
            List<Integer> deptIdList = loginUserInfo.getDeptIdList();
            Integer rootDepartmentId = loginUserInfo.getRootDepartmentId();
            // 设置检索条件 1、部门id。存在检索条件，则按照部门名称来检索，如果不存在，则判断是否含有根部门（无需设置默认全部部门)，如果不含有根部门则用检索到的所有部门；2、若无归属部门，需要返回空信息（外部业务）
            if (!deptIdList.contains(rootDepartmentId)) {
                pageParams.setDepartmentIdList(deptIdList);
            }
        }

        Integer returnStatus = request.getReturnStatus();
        if (returnStatus != null) {
            pageParams.setReturnStatusList(Collections.singletonList(returnStatus));
        }
        return pageParams;
    }

    /**
     * 申请退货成功并返回退货申请信息
     * @param request 退货申请单入参
     * @return        退货申请信息
     */
    private GoodsReturnApplyResponseVO applyGoodsReturnSuccess(GoodsReturnApplyRequestDTO request) {
        request.setReturnStatus(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode());
        GoodsReturn goodsReturn = this.updateGoodsReturnDetailInfo(request);
        return this.wrapGoodsReturnApplyResponseVO(goodsReturn);
    }

    /**
     * 保存&更新日志
     * @param request
     * @param returnId
     * @param rjSessionInfo
     */
    private void saveReturnOperationLog(GoodsReturnApplyRequestDTO request, Integer returnId, RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        // 申请退货凭证
        List<String> goodsImageList = request.getGoodsImageList();
        String imageStringList = null;
        if (CollectionUtils.isNotEmpty(goodsImageList)) {
            imageStringList = String.join(";", goodsImageList);
        }
        // 插入操作日志
        commonGoodsReturnService.saveReturnOperationLog(
                GoodsReturnLogTranslator.buildDO(loginUserInfo.getUserId(), loginUserInfo.getUserName(), returnId, GoodsReturnOperatorTypeEnum.PURCHASE, GoodsReturnOperationTypeEnum.PURCHASE_APPLY_GOODS_RETURN, StringUtils.EMPTY, imageStringList)
        );
    }

    /**
     * 新增日志
     * @param request
     * @param returnId
     * @param rjSessionInfo
     */
    private void insertReturnOperationLog(GoodsReturnApplyRequestDTO request, Integer returnId, RjSessionInfo rjSessionInfo) {
        LoginUserInfoBO loginUserInfo = userClient.getLoginUserInfo(rjSessionInfo.getGuid(), rjSessionInfo.getOrgId());
        // 申请退货凭证
        List<String> goodsImageList = request.getGoodsImageList();
        String imageStringList = null;
        if (CollectionUtils.isNotEmpty(goodsImageList)) {
            imageStringList = String.join(";", goodsImageList);
        }
        // 插入操作日志
        goodsReturnLogDOMapper.insertSelective(
                GoodsReturnLogTranslator.buildDO(loginUserInfo.getUserId(), loginUserInfo.getUserName(), returnId, GoodsReturnOperatorTypeEnum.PURCHASE, GoodsReturnOperationTypeEnum.PURCHASE_APPLY_GOODS_RETURN, StringUtils.EMPTY, imageStringList)
        );
    }

    /**
     * 封装退货申请信息
     * @param goodsReturn
     * @return
     */
    private GoodsReturnApplyResponseVO wrapGoodsReturnApplyResponseVO(GoodsReturn goodsReturn) {
        GoodsReturnApplyResponseVO result = new GoodsReturnApplyResponseVO();
        result.setReturnId(goodsReturn.getId());
        result.setReturnNo(goodsReturn.getReturnNo());
        result.setApplyName(goodsReturn.getApplyName());
        result.setApplyDate(goodsReturn.getCreationTime() != null ? goodsReturn.getCreationTime() : new Date());
        return result;
    }

    /**
     * 累加更新order_detail中商品的退货数量
     * @param request       申请退货参数
     * @param goodsReturn   数据库中退货单快照
     */
    private void accumulateOrderDetailReturnCount(GoodsReturnApplyRequestDTO request, GoodsReturn goodsReturn) {
        this.accumulateOrderDetailReturnCount(request, goodsReturn, false);
    }

    /**
     * 累加更新order_detail中商品的退货数量
     * @param request       申请退货参数
     * @param goodsReturn   数据库中退货单快照
     * @param isRetry       是否修改退货单重新发起
     */
    private void accumulateOrderDetailReturnCount(GoodsReturnApplyRequestDTO request, GoodsReturn goodsReturn, boolean isRetry) {
        Integer orderId = goodsReturn.getOrderId();
        String cacheKey = "goodsReturn_update_cancel_quantity_" + orderId;
        cacheClient.controlRepeatOperation(cacheKey, 60);

        List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList = request.getReturnApplyDetailList();
        Map<Integer, GoodsReturnApplyDetailRequestDTO> detailIdApplyMap = DictionaryUtils.toMap(returnApplyDetailList, GoodsReturnApplyDetailRequestDTO::getDetailId, Function.identity());
        List<OrderDetailDO> orderDetailDOList = orderDetailMapper.findByFmasterid(orderId);
        try {
            // 需要增加单次请求的锁定
            for (OrderDetailDO detailItem : orderDetailDOList) {
                GoodsReturnApplyDetailRequestDTO applyItem = detailIdApplyMap.get(detailItem.getId());
                if (applyItem != null) {
                    // 修改退货申请，重新计算可退数量和金额
                    if (!isRetry) {
                        // 防止用户打开多个退货窗口, 重复发起退货
                        // 如果下单商品的商品是不是免费的且总金额等于退货金额，则退完了
                        BusinessErrUtil.isTrue(BigDecimal.ZERO.compareTo(detailItem.getFbidamount()) == 0 || detailItem.getFbidamount().compareTo(BigDecimal.valueOf(detailItem.getReturnAmount())) > 0, ExecptionMessageEnum.RETURN_REQUEST_FAILED_AMOUNT_ZERO);
                        // 可退数量判断
                        BigDecimal canReturnQuantity = detailItem.getFquantity().subtract(detailItem.getFcancelquantity());
                        BusinessErrUtil.isTrue(canReturnQuantity.compareTo(applyItem.getQuantity()) >= 0, ExecptionMessageEnum.RETURN_QUANTITY_EXCEEDS_REFUNDABLE);
                        detailItem.setFcancelquantity(detailItem.getFcancelquantity() != null ? detailItem.getFcancelquantity().add(applyItem.getQuantity()) : applyItem.getQuantity());
                        detailItem.setReturnAmount(detailItem.getReturnAmount() != null ? (detailItem.getReturnAmount() + applyItem.getAmount().doubleValue()) : applyItem.getAmount().doubleValue());
                    }

                    detailItem.setReturnStatus(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode());
                }
            }
            orderDetailMapper.loopUpdateByIdIn(orderDetailDOList);
        } catch (Exception e) {
            Preconditions.isTrue(false, e.getMessage());
        } finally {
            cacheClient.removeCache(cacheKey);
        }
    }

    /**
     * 取消退货申请核心代码
     * @param goodsReturnList
     * @param cancelManId
     * @param cancelMan
     * @return
     */
    private boolean cancelGoodsReturnCore(List<GoodsReturn> goodsReturnList, Integer cancelManId, String cancelMan, GoodsReturnOperationTypeEnum goodsReturnOperationTypeEnum, String cancelReason) {
        BusinessErrUtil.notEmpty(goodsReturnList, ExecptionMessageEnum.REVOCATION_FAILED_NO_RETURN_ORDER);
        Integer orderId = goodsReturnList.get(0).getOrderId();

        // 批量更新退货单为已作废
        List<GoodsReturn> updatedList = goodsReturnList.stream().map(it -> {
            GoodsReturn updated = new GoodsReturn();
            updated.setId(it.getId());
            updated.setGoodsReturnStatus(GoodsReturnStatusEnum.CANCEL_REQUEST.getCode());
            updated.setPrice(0d);
            return updated;
        }).collect(Collectors.toList());
        int affect = goodsReturnMapper.batchCancelGoodsReturn(updatedList);
        if (affect == 0) {
            return false;
        }

        // 更新订单明细退货数量和金额以及状态
        this.updateOrderDetailReturnStatus(goodsReturnList);

        // 还要根据取消退货更新超时订单信息
        this.updateTimeOutStatistics(orderId);

        // 保存退货操作日志
        goodsReturnList.forEach(g -> commonGoodsReturnService.saveReturnOperationLog(
                GoodsReturnLogTranslator.buildDO(cancelManId, cancelMan, g.getId(), GoodsReturnOperatorTypeEnum.PURCHASE, goodsReturnOperationTypeEnum, cancelReason, StringUtils.EMPTY)
        ));
        // 将汇卡记录恢复有效
        reimbursementService.validRecord(orderId);
        barCodeGoodsReturnService.resetBarcodeStatusAfterCancelReturn(goodsReturnList.get(0));
        // 通知退货单取消完成
        List<String> returnNoList = goodsReturnList.stream().map(GoodsReturn::getReturnNo).collect(Collectors.toList());
        this.noticeApplyGoodsReturnComplete(goodsReturnList.get(0).getOrgId(), cancelManId.longValue(), goodsReturnList.get(0).getOrderNo(), returnNoList, NoticeEventEnum.CANCEL_GOODS_RETURN_COMPLETE);
        return true;
    }

    /**
     * 通知中大发起退货成功
     *
     * @param orgId        用户信息
     * @param oprUserId    操作用户
     * @param orderNo      订单号
     * @param returnNoList 退货单号列表
     */
    private void noticeApplyGoodsReturnComplete(Integer orgId, Long oprUserId, String orderNo, List<String> returnNoList, NoticeEventEnum noticeEventEnum) {
        String orgCode;
        try {
            OrgEnum orgEnum = OrgEnum.getOrgEnumById(orgId);
            orgCode = orgEnum.getCode();
        } catch (Exception e) {
            // 中大办公已经在OrgEnum内，如果查不到，就不是目标用户了
            return;
        }

        try {
            GoodsReturnNoticeDTO goodsReturnNoticeDTO = new GoodsReturnNoticeDTO();
            goodsReturnNoticeDTO.setOrderNo(orderNo);
            goodsReturnNoticeDTO.setReturnNoList(returnNoList);
            goodsReturnNoticeDTO.setOrgId(orgId);
            goodsReturnNoticeDTO.setOprUserId(oprUserId);
            genericCallService.callMethodToApps(noticeEventEnum, orgCode, goodsReturnNoticeDTO, 10000, orderNo);
        } catch (Exception e) {
            LOGGER.error("调用通知申请退货完成接口", e);
        }
    }

    /**
     * 判断是否全部退货
     * @param orderDetailDOList 订单商品数据
     * @param goodsReturnApplyDetailRequestDTOList 退货请求商品数据
     * @return 是否全部退货
     */
    private boolean getIfAllReturn(List<OrderDetailDO> orderDetailDOList, List<GoodsReturnApplyDetailRequestDTO> goodsReturnApplyDetailRequestDTOList){
        Map<Integer, BigDecimal> detailIdCancelQuantityMap = DictionaryUtils.toMap(goodsReturnApplyDetailRequestDTOList, GoodsReturnApplyDetailRequestDTO::getDetailId, GoodsReturnApplyDetailRequestDTO::getQuantity);
        for(OrderDetailDO orderDetailDO : orderDetailDOList){
            BigDecimal cancelQuantity = detailIdCancelQuantityMap.get(orderDetailDO.getId());
            if(cancelQuantity == null || !(orderDetailDO.getFquantity().compareTo(cancelQuantity) == 0)){
                // 如果有订单详情里有但是传入的退货列表没有的或数量不一致的 就不是全部退货
                return false;
            }
        }
        return true;
    }

    /**
     * 通知中大采购人申请退货前置校验，获取校验结果，不通过则抛出
     *
     * @param orgId     机构id
     * @param oprUserId 操作人
     * @param orderNo   订单号
     */
    private void noticeBeforeApplyGoodsReturnAndVerify(Integer orgId, Long oprUserId, String orderNo) {
        String orgCode;
        try {
            OrgEnum orgEnum = OrgEnum.getOrgEnumById(orgId);
            orgCode = orgEnum.getCode();
        } catch (Exception e) {
            // 中大办公已经在OrgEnum内，如果查不到，就不是目标用户了
            return;
        }

        try {
            GoodsReturnNoticeDTO goodsReturnNoticeDTO = new GoodsReturnNoticeDTO();
            goodsReturnNoticeDTO.setOrderNo(orderNo);
            goodsReturnNoticeDTO.setOrgId(orgId);
            goodsReturnNoticeDTO.setOprUserId(oprUserId);
            RemoteResponse<Boolean> response = genericCallService.callMethodToApps(NoticeEventEnum.VERIFY_BEFORE_BUYER_APPLY_RETURN, orgCode, goodsReturnNoticeDTO, 10000, orderNo);
            Preconditions.isTrue(response.isSuccess(), response.getMsg());
        } catch (ExecutionException | InterruptedException e) {
            LOGGER.error(orderNo + "获取退货单前置校验结果失败", e);
            throw new BusinessInterceptException(ExecptionMessageEnum.FAILED_TO_OBTAIN_RETURN_VERIFICATION);
        } catch (Exception e) {
            LOGGER.error("调用申请退货前置校验通知接口", e);
            throw e;
        }
    }

    private void applyReturnGasBottleVerify(GoodsReturnApplyRequestDTO request, OrderMasterDO orderMasterDO){
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderMasterDO.getId()), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        boolean eachProductEachCode = CollectionUtils.isNotEmpty(baseOrderExtraDTOList) && CommonValueUtils.parseNumberStrToBoolean(baseOrderExtraDTOList.get(0).getExtraValue());
        if(eachProductEachCode){
            return;
        }
        List<BizWmsEntryDTO> wmsEntries = bizWareHouseClient.queryLatestEntry(orderMasterDO.getForderno());
        Map<String, BizWmsEntryDTO> gasBottleQrcodeWmsEntryMap = New.map();
        for(BizWmsEntryDTO wmsEntry : wmsEntries){
            wmsEntry.getDetailList().forEach(wmsDetail->{
                if(CollectionUtils.isNotEmpty(wmsDetail.getGasBottleQrCodeList())){
                    wmsDetail.getGasBottleQrCodeList().forEach(gasBottleQrCode-> gasBottleQrcodeWmsEntryMap.put(gasBottleQrCode, wmsEntry));
                }
            });
        }
        // 气瓶校验
        OrderDetailReq orderDetailReq = new OrderDetailReq();
        orderDetailReq.setReturnBindGasBottle(true);
        orderDetailReq.setOrderMasterIdList(New.list(orderMasterDO.getId()));
        // 排除掉退货中和退货完成的气瓶及商品
        orderDetailReq.setExcludeReturnStatusList(New.list(RETURNED_OR_RETURNING_STATUS));
        List<OrderDetailDTO> orderDetailDTOList = orderDetailService.findDetailByOrderIdListExcludeReturnStatus(orderDetailReq).getData();
        Map<Integer, OrderDetailDTO> detailIdDTOMap = DictionaryUtils.toMap(orderDetailDTOList, OrderDetailDTO::getId, Function.identity());
        for(GoodsReturnApplyDetailRequestDTO detail : request.getReturnApplyDetailList()){
            OrderDetailDTO matchDetail = detailIdDTOMap.get(detail.getDetailId());
            if(matchDetail == null){
                BusinessErrUtil.isTrue(detail.getQuantity().compareTo(BigDecimal.ZERO) == 0, ExecptionMessageEnum.RETURN_QUANTITY_GREATER_THAN_REFUNDABLE, detail.getGoodsName());
                continue;
            }
            BusinessErrUtil.isTrue(matchDetail.getFquantity().compareTo(detail.getQuantity()) >= 0, ExecptionMessageEnum.RETURN_QUANTITY_GREATER_THAN_REFUNDABLE, detail.getGoodsName());
            // 是否全部退货
            boolean allReturn = matchDetail.getFquantity().compareTo(detail.getQuantity()) == 0;
            List<String> canReturnGasBottles = matchDetail.getBindGasBottleBarcodes();
            // 是否不需要退气瓶：本身没有绑气瓶
            boolean noNeedReturnGasBottle = CollectionUtils.isEmpty(canReturnGasBottles);
            if(!noNeedReturnGasBottle){
                BusinessErrUtil.isTrue(CollectionUtils.isNotEmpty(detail.getReturnGasBottleBarcodes()), ExecptionMessageEnum.ADD_CYLINDER_INFO_FOR_RETURN);
                List<String> returnGasBottleQrCodes = detail.getReturnGasBottleBarcodes() == null ? New.emptyList() : detail.getReturnGasBottleBarcodes();
                BusinessErrUtil.isTrue(new HashSet<>(canReturnGasBottles).containsAll(returnGasBottleQrCodes), ExecptionMessageEnum.SELECTED_NON_RETURNABLE_BOTTLES, detail.getGoodsName());
                // 是否气瓶全部退货
                boolean gasBottleAllReturn = returnGasBottleQrCodes.size() == canReturnGasBottles.size();
                if(allReturn && !gasBottleAllReturn){
                    throw new BusinessInterceptException(ExecptionMessageEnum.UNRETURNED_BOTTLES_EXIST, detail.getGoodsName());
                } else if(!allReturn && gasBottleAllReturn){
                    throw new BusinessInterceptException(ExecptionMessageEnum.RETURN_ALL_BOTTLE_OF_GOODS_ALREADY_RETURN, detail.getGoodsName());
                }
                for(String gasBottleQrCode : returnGasBottleQrCodes){
                    BizWmsEntryDTO entryDTO = gasBottleQrcodeWmsEntryMap.get(gasBottleQrCode);
                    if(entryDTO != null){
                        BusinessErrUtil.isTrue(!InboundStatus.WAREHOUSING.getValue().equals(entryDTO.getStatus()), ExecptionMessageEnum.CANCEL_STORAGE_FIRST);
                        BusinessErrUtil.isTrue(!ApprovalTaskStatusEnum.APPROVALING.getValue().equals(entryDTO.getApprovalStatus()), ExecptionMessageEnum.PENDING_APPROVAL_CANCEL_FIRST);
                    }
                }
            } else if(CollectionUtils.isNotEmpty(detail.getReturnGasBottleBarcodes())){
                throw new BusinessInterceptException(ExecptionMessageEnum.ALL_BOTTLES_ALREADY_RETURNED_NO_NEED_TO_RETURN, detail.getGoodsName());
            }
        }
    }

    /**
     * 构造退货单的气瓶数据
     */
    private void constructReturnDetailGasBottle(GoodsReturnInfoVO goodsReturnInfoVO) {
        Integer orderId = goodsReturnInfoVO.getOrderId();
        List<BaseOrderExtraDTO> baseOrderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
        boolean isEachProductEachCode = CollectionUtils.isNotEmpty(baseOrderExtraDTOList) && CommonValueUtils.parseNumberStrToBoolean(baseOrderExtraDTOList.get(0).getExtraValue());
        if (!isEachProductEachCode) {
            // 非一物一码，直接获取退货单下绑定的气瓶数据
            List<String> returnGasBottleBarcodes = goodsReturnInfoVO.getReturnInfoDetailList().stream().map(GoodsReturnInfoDetailVO::getReturnGasBottleBarcodes).filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(returnGasBottleBarcodes)) {
                return;
            }
            List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(returnGasBottleBarcodes);
            Map<String, GasBottleVO> gasBottleCodeIdentityMap = com.ruijing.store.order.util.DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
            for (GoodsReturnInfoDetailVO detailVO : goodsReturnInfoVO.getReturnInfoDetailList()) {
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(detailVO.getReturnGasBottleBarcodes())) {
                    continue;
                }
                detailVO.setReturnGasBottles(detailVO.getReturnGasBottleBarcodes().stream().map(gasBottleCodeIdentityMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 处理退货单额外信息
     *
     * @param returnPageVOS 退货单分页VOS
     */
    private void fillOrderExtraInfo(List<GoodsReturnPageVO> returnPageVOS) {
        if (CollectionUtils.isEmpty(returnPageVOS)) {
            return;
        }
        List<Integer> orderIdList = returnPageVOS.stream().map(GoodsReturnPageVO::getOrderId).collect(Collectors.toList());
        List<OrderExtraDTO> orderExtraDTOS = orderExtraClient.selectByOrderIdAndExtraKey(orderIdList, New.list(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue()));
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = DictionaryUtils.groupBy(orderExtraDTOS, OrderExtraDTO::getOrderId);
        returnPageVOS.forEach(it -> {
            List<OrderExtraDTO> orderExtraDTOList = orderIdExtraListMap.get(it.getOrderId());
            it.setStockWarehouseType(StockTypeEnum.DEFAULT.getValue());
            if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
                Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
                String stockWarehouseType = extraKeyValueMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), String.valueOf(StockTypeEnum.DEFAULT.getValue()));
                it.setStockWarehouseType(Integer.valueOf(stockWarehouseType));
            }
        });
    }

    /**
     * 获取订单额外信息
     *
     * @param orderId 退货单ID
     */
    private Map<Integer, String> getOrderExtraMap(Integer orderId) {
        List<OrderExtraDTO> orderExtraDTOS = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue()));
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = DictionaryUtils.groupBy(orderExtraDTOS, OrderExtraDTO::getOrderId);
        List<OrderExtraDTO> orderExtraDTOList = orderIdExtraListMap.get(orderId);
        Map<Integer, String> extraKeyValueMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
            extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        }
        return MapUtils.isNotEmpty(extraKeyValueMap) ? extraKeyValueMap : Collections.emptyMap();
    }

    /**
     * 填充订单详情拓展字段
     */
    private void fillOrderDetailInfo(List<GoodsReturnPageVO> returnPageVOS) {
        if (CollectionUtils.isEmpty(returnPageVOS)) {
            return;
        }

        List<GoodsReturnDetailPageVO> returnInfoDetailList = returnPageVOS.stream()
                .map(GoodsReturnPageVO::getGoodsReturnDetailList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<Integer> orderDetailIds = returnInfoDetailList.stream()
                .map(GoodsReturnDetailPageVO::getDetailId)
                .filter(StringUtils::isNotBlank)
                .map(Integer::valueOf)
                .distinct()
                .collect(Collectors.toList());

        // 获取订单详情扩展信息
        List<OrderDetailExtraDTO> orderDetailExtraDTOList = orderDetailExtraClient.listOrderDetailExtra(null, orderDetailIds);

        // 按detailId分组
        Map<Integer, List<OrderDetailExtraDTO>> detailId2ExtraMap = New.emptyMap();
        if (CollectionUtils.isNotEmpty(orderDetailExtraDTOList)) {
            detailId2ExtraMap = orderDetailExtraDTOList.stream()
                    .collect(Collectors.groupingBy(OrderDetailExtraDTO::getOrderDetailId));
        }

        // 查询订单详情主表
        List<OrderDetailDO> orderDetailDOS = orderDetailMapper.findByIdIn(orderDetailIds);
        if(CollectionUtils.isEmpty(orderDetailDOS)){
            return;
        }

        // 根据详情ID分组
        Map<Integer, OrderDetailDO> detailId2OrderDetailMap = orderDetailDOS.stream()
                .collect(Collectors.toMap(OrderDetailDO::getId, Function.identity(), (v1, v2) -> v1));

        // 填充商品信息
        for (GoodsReturnDetailPageVO returnDetailPageVO : returnInfoDetailList) {
            if (StringUtils.isBlank(returnDetailPageVO.getDetailId())) {
                continue;
            }
            Integer detailId = Integer.valueOf(returnDetailPageVO.getDetailId());

            List<OrderDetailExtraDTO> extraDTOList = detailId2ExtraMap.get(detailId);
            OrderDetailDO orderDetailDO = detailId2OrderDetailMap.get(detailId);
            if (Objects.isNull(orderDetailDO)) {
                continue;
            }
            // 填充详情信息
            returnDetailPageVO.setFirstCategoryId(orderDetailDO.getFirstCategoryId());
            returnDetailPageVO.setUnit(orderDetailDO.getFunit());
            returnDetailPageVO.setCasNo(orderDetailDO.getCasno());
            // 填充危化品标签
            Integer dangerousTypeId = orderDetailDO.getDangerousTypeId();
            if (Objects.isNull(dangerousTypeId)) {
                dangerousTypeId = DangerousTypeEnum.UN_DANGEROUS.getValue();
            }
            returnDetailPageVO.setDangerousType(dangerousTypeId);
            returnDetailPageVO.setDangerousTag(DangerousTypeEnum.get(dangerousTypeId).getName());
            // 填充订单详情拓展字段
            fillOrderDetailInfo(returnDetailPageVO, extraDTOList, orderDetailDO);
        }

    }

    /**
     * 填充订单详情拓展字段
     */
    private void fillOrderDetailInfo(GoodsReturnDetailPageVO detailVO, List<OrderDetailExtraDTO> orderDetailExtraDTOS, OrderDetailDO orderDetailDO) {
        if (CollectionUtils.isEmpty(orderDetailExtraDTOS) || Objects.isNull(orderDetailDO)) {
            return;
        }

        Map<Integer, OrderDetailExtraDTO> type2DetailExtraMap = orderDetailExtraDTOS.stream()
                .collect(Collectors.toMap(OrderDetailExtraDTO::getExtraKeyType, Function.identity(), (v1, v2) -> v1));

        // 包装规格 , 化学试剂和危险化学品 取值详情的specification，其他分类取PACKING_UNIT
        if (New.set(CategoryConstant.DANGEROUS_ID, CategoryConstant.CHEMICAL_REAGENTS_ID).contains(orderDetailDO.getFirstCategoryId())) {
            detailVO.setPackingSpec(detailVO.getSpecification());
        } else {
            OrderDetailExtraDTO packUnitExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PACKING_UNIT.getType());
            if (Objects.nonNull(packUnitExtraDTO)) {
                detailVO.setPackingSpec(packUnitExtraDTO.getExtraValue());
            }
        }

        // 浓度
        OrderDetailExtraDTO concentrationExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.CONCENTRATION.getType());
        if (Objects.nonNull(concentrationExtraDTO)) {
            detailVO.setPurity(concentrationExtraDTO.getExtraValue());
        }

        // 注册编码
        OrderDetailExtraDTO registerCertExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.MEDICAL_DEVICE_REGIS_CERT_NUMBER.getType());
        if (Objects.nonNull(registerCertExtraDTO)) {
            detailVO.setMedicalDeviceRegisCertNumber(registerCertExtraDTO.getExtraValue());
        }

        // 完成周期
        OrderDetailExtraDTO completionCycleExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.COMPLETION_CYCLE.getType());
        if (Objects.nonNull(completionCycleExtraDTO)) {
            detailVO.setCompletionCycle(completionCycleExtraDTO.getExtraValue());
        }

        // 出版社
        OrderDetailExtraDTO pressExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PRESS.getType());
        if (Objects.nonNull(pressExtraDTO)) {
            detailVO.setPress(pressExtraDTO.getExtraValue());
        }

        // 产品规格
        OrderDetailExtraDTO productSpecExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.PRODUCT_SPECIFICATION.getType());
        if (Objects.nonNull(productSpecExtraDTO)) {
            detailVO.setProductSpec(productSpecExtraDTO.getExtraValue());
        }

        // 型号
        OrderDetailExtraDTO modelExtraDTO = type2DetailExtraMap.get(OrderDetailExtraEnum.MODEL_NUMBER.getType());
        if (Objects.nonNull(modelExtraDTO)) {
            detailVO.setModelNumber(modelExtraDTO.getExtraValue());
        }
    }


}
