package com.ruijing.store.goodsreturn.service.impl;

import com.reagent.commonbase.enums.OrgEnum;
import com.reagent.order.base.order.dto.BaseOrderExtraDTO;
import com.reagent.order.base.order.dto.OrderAddressDTO;
import com.reagent.order.base.order.dto.OrderUniqueBarCodeDTO;
import com.reagent.order.base.order.enums.DeliveryTypeEnum;
import com.reagent.order.base.order.enums.product.OrderProductTransactionStatusEnum;
import com.reagent.research.custom.enums.ZhongShanDaXueOrgEnum;
import com.reagent.research.custom.utils.ZhongShanDaXueBelongUtils;
import com.reagent.supp.api.wallet.dto.OrderForWalletDTO;
import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.base.gateway.api.dto.RjUserTypeEnum;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.cat.annotation.CatAnnotation;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.common.util.JsonUtils;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.fundamental.remoting.msharp.annotation.MSharpReference;
import com.ruijing.fundamental.remoting.msharp.context.RpcContext;
import com.ruijing.message.api.dto.MessageDTO;
import com.ruijing.message.api.enums.MessageTypeEnum;
import com.ruijing.message.api.service.MessageService;
import com.ruijing.order.utils.BusinessErrUtil;
import com.ruijing.pearl.annotation.PearlValue;
import com.ruijing.shop.crm.api.pojo.dto.SuppUserOrganizationDTO;
import com.ruijing.shop.crm.api.pojo.dto.UserDTO;
import com.ruijing.shop.crm.api.pojo.param.UserParam;
import com.ruijing.shop.crm.api.service.CrmUserService;
import com.ruijing.shop.crm.api.support.ResultBean;
import com.ruijing.shop.crm.api.support.enums.UserTagEnum;
import com.ruijing.shop.goods.api.enums.StockTypeEnum;
import com.ruijing.shop.srm.api.support.enums.ShopGasBottleUnbindEnum;
import com.ruijing.shop.wallet.api.dto.WaitingChargingOrderDTO;
import com.ruijing.shop.wallet.api.dto.WalletOrderReturnDTO;
import com.ruijing.shop.wallet.api.service.WalletOrderService;
import com.ruijing.store.apply.enums.application.ApplyManageOperationEnum;
import com.ruijing.store.cms.api.dto.SendingPersonalAndDefaultDTO;
import com.ruijing.store.cms.api.enums.GroupTypeEnum;
import com.ruijing.store.cms.api.enums.SendingBusinessEnum;
import com.ruijing.store.cms.api.enums.SendingWayEnum;
import com.ruijing.store.cms.api.request.SendingPersonalAndDefaultParam;
import com.ruijing.store.cms.api.request.SendingPersonalSettingParam;
import com.ruijing.store.cms.api.service.SendingPersonalSettingService;
import com.ruijing.store.exception.enums.ExecptionMessageEnum;
import com.ruijing.store.goodsreturn.constant.GoodsReturnConstant;
import com.ruijing.store.goodsreturn.enums.GoodsReturnOperatorTypeEnum;
import com.ruijing.store.goodsreturn.request.*;
import com.ruijing.store.goodsreturn.service.BarCodeGoodsReturnService;
import com.ruijing.store.goodsreturn.service.CommonGoodsReturnService;
import com.ruijing.store.goodsreturn.service.SupplierGoodsReturnService;
import com.ruijing.store.goodsreturn.vo.*;
import com.ruijing.store.order.api.base.common.BasePageResponseDTO;
import com.ruijing.store.order.api.base.enums.*;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;
import com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum;
import com.ruijing.store.order.api.base.ordermaster.dto.UpdateOrderParamDTO;
import com.ruijing.store.order.api.base.other.dto.OrderUnFreezeRequestDTO;
import com.ruijing.store.order.base.core.bo.goodsreturn.GoodsReturnPageRequestBO;
import com.ruijing.store.order.base.core.mapper.*;
import com.ruijing.store.order.base.core.model.*;
import com.ruijing.store.order.base.core.translator.GoodsReturnLogTranslator;
import com.ruijing.store.order.base.core.translator.GoodsReturnTranslator;
import com.ruijing.store.order.business.handler.OrderMessageHandler;
import com.ruijing.store.order.business.service.OrderManageService;
import com.ruijing.store.order.business.service.orgondemand.ClinicalOrderService;
import com.ruijing.store.order.cache.CacheClient;
import com.ruijing.store.order.constant.OrderOperateLogConstant;
import com.ruijing.store.order.gateway.buyercenter.vo.other.GasBottleVO;
import com.ruijing.store.order.other.service.ProductStockService;
import com.ruijing.store.order.other.service.ReimbursementService;
import com.ruijing.store.order.rpc.client.*;
import com.ruijing.store.order.service.ApplicationBaseService;
import com.ruijing.store.order.util.CommonValueUtils;
import com.ruijing.store.order.util.DictionaryUtils;
import com.ruijing.store.order.util.FileUtils;
import com.ruijing.store.order.util.PageResponseUtils;
import com.ruijing.store.supp.include.api.dto.NewSuppOrgDTO;
import com.ruijing.store.supp.include.api.service.OrgIncludeSuppRpcService;
import com.ruijing.store.user.api.dto.DepartmentDTO;
import com.ruijing.store.user.api.dto.OrganizationDTO;
import com.ruijing.store.user.api.dto.UserBaseInfoDTO;
import com.ruijing.store.user.api.enums.userIncludesupp.UserIncludeSuppStatusEnum;
import com.ruijing.store.user.api.service.DepartmentRpcService;
import com.ruijing.store.user.api.service.OrganizationRpcService;
import com.ruijing.store.user.api.service.UserInfoService;
import com.ruijing.store.warehouse.service.WarehouseStockOccupyService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhongyulei
 * @create: 2021/1/4 12:48
 **/
@Service
@CatAnnotation
public class SupplierGoodsReturnServiceImpl implements SupplierGoodsReturnService {

    private final String GOODS_RETURN_STATISTICS_KEY = "SupplierGoodsReturnServiceImpl_getGoodsReturnStatistics";

    private static final String CAT_TYPE = "SupplierGoodsReturnService";

    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @MSharpReference(remoteAppkey = "shop-crm-service")
    private CrmUserService crmUserService;

    @MSharpReference(remoteAppkey = "store-supp-include-service")
    private OrgIncludeSuppRpcService orgIncludeSuppRpcService;

    @Resource
    private GoodsReturnMapper goodsReturnMapper;

    @Resource
    private GoodsReturnLogDOMapper goodsReturnLogDOMapper;

    @MSharpReference(remoteAppkey = "store-user-service")
    private UserInfoService userInfoService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private DepartmentRpcService departmentRpcService;

    @Resource
    private OrderMasterMapper orderMasterMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @MSharpReference(remoteAppkey = "shop-wallet-service")
    private WalletOrderService walletOrderService;

    @MSharpReference(remoteAppkey = "store-user-service")
    private OrganizationRpcService organizationRpcService;

    @Resource
    private CommonGoodsReturnService commonGoodsReturnService;

    @Resource
    private ReimbursementService reimbursementService;

    @Resource
    private ProductStockService productStockService;

    /**
     * 商品-品牌-货号-单位-单价-价格
     */
    private static final String TR_HTML = "<tr>\n" +
            "    <td>#name#</td>\n" +
            "    <td>#brand#</td>\n" +
            "    <td>#code#</td>\n" +
            "    <td>#unit#</td>\n" +
            "</tr>";

    private final String SEND_EMAIL_TARGET = "ourself";

    private final String GOODS_RETURN_EMAIL_TITLE = "退货申请#title#通知";

    /**
     * 供应商是否同意退货  采购退货详情
     */
    @PearlValue(key = "EMAIL_RETURN_WWW")
    private String GOODS_RETURN_HTTP;

    @PearlValue(key = "SEND_EMAIL.TARGET.EMAIL")
    private String SEND_EMAIL_TARGET_EMAIL;

    @PearlValue(key = "SEND_EMAIL.TARGET")
    private String SEND_EMAIL_TARGET_CONFIG;

    @PearlValue(key = "SEND_EMAIL.TARGET.PHONE")
    private String SEND_EMAIL_TARGET_PHONE;

    @MSharpReference(remoteAppkey = "store-cms-service")
    private SendingPersonalSettingService sendingPersonalSettingService;

    @MSharpReference(remoteAppkey = "msharp-message-service")
    private MessageService messageService;

    @Resource
    private RefFundcardOrderMapper refFundcardOrderMapper;

    @Resource
    private OrderManageService orderManageService;

    @Resource
    ApplicationBaseService applicationBaseService;

    @Resource
    private ClinicalOrderService clinicalOrderService;

    @Resource
    private ResearchCustomClient researchCustomClient;

    @Resource
    private WalletOrderRpcClient walletOrderRpcClient;

    @Resource
    private CacheClient cacheClient;

    @Resource
    private StatementPlatformClient statementPlatformClient;

    @Resource
    private OrderAddressRPCClient orderAddressRPCClient;

    @Resource
    private BarCodeGoodsReturnService barCodeGoodsReturnService;

    @Resource
    private OrderMessageHandler orderMessageHandler;

    @Resource
    private SysuClient sysuClient;

    @Resource
    private WarehouseStockOccupyService warehouseStockOccupyService;

    @Resource
    private FilingControlClient filingControlClient;

    @Resource
    private GasBottleClient gasBottleClient;

    @Resource
    private OrderExtraClient orderExtraClient;

    @Resource
    private OrderUniqueBarCodeRPCClient orderUniqueBarCodeRPCClient;

    @Resource
    private InvoiceClient invoiceClient;

    @Override
    public BasePageResponseDTO<GoodsReturnPageVO> getPageGoodsReturn(GoodsReturnPageRequestDTO goodsReturnPageRequestDTO) {
        SupplierAuthorityDTO supplierAuthorityDTO = getSupplierAuthority();
        if (CollectionUtils.isEmpty(supplierAuthorityDTO.getOrgIds()) && !supplierAuthorityDTO.isSuperAdmin()) {
            return new BasePageResponseDTO(goodsReturnPageRequestDTO.getPageNo(),goodsReturnPageRequestDTO.getPageSize(),0, Collections.EMPTY_LIST);
        }
        GoodsReturnPageRequestBO goodsReturnPageRequestBO = new GoodsReturnPageRequestBO();
        if (Objects.nonNull(goodsReturnPageRequestDTO.getOrgId())) {
            if (!supplierAuthorityDTO.getOrgIds().contains(goodsReturnPageRequestDTO.getOrgId())) {
                return new BasePageResponseDTO(goodsReturnPageRequestDTO.getPageNo(),goodsReturnPageRequestDTO.getPageSize(),0, Collections.EMPTY_LIST);
            }
            goodsReturnPageRequestBO.setOrgId(goodsReturnPageRequestDTO.getOrgId());
        } else {
            goodsReturnPageRequestBO.setOrgIds(supplierAuthorityDTO.getOrgIds());
        }
        if(StringUtils.isNotBlank(goodsReturnPageRequestDTO.getOrderNo())){
            // 输入订单号少于八位，则返回空列表
            if(goodsReturnPageRequestDTO.getOrderNo().length() < 8){
                return new BasePageResponseDTO(goodsReturnPageRequestDTO.getPageNo(),goodsReturnPageRequestDTO.getPageSize(),0, Collections.EMPTY_LIST);
            }
            goodsReturnPageRequestBO.setOrderNoLike(goodsReturnPageRequestDTO.getOrderNo());
        }

        if (Objects.nonNull(goodsReturnPageRequestDTO.getDepartmentId())) {
            goodsReturnPageRequestBO.setDepartmentIdList(Arrays.asList(goodsReturnPageRequestDTO.getDepartmentId()));
        }
        List<Integer> returnStatusList = new ArrayList<>();
        if (Objects.nonNull(goodsReturnPageRequestDTO.getReturnStatus())) {
            returnStatusList.add(goodsReturnPageRequestDTO.getReturnStatus());
        } else {
            returnStatusList.add(GoodsReturnStatusEnum.CANCEL_REQUEST.getCode());
            returnStatusList.add(GoodsReturnStatusEnum.SUCCESS.getCode());
            returnStatusList.add(GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode());
            returnStatusList.add(GoodsReturnStatusEnum.RETURNED_GOODS.getCode());
            returnStatusList.add(GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode());
            returnStatusList.add(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode());
        }
        goodsReturnPageRequestBO.setReturnStatusList(returnStatusList);
        goodsReturnPageRequestBO.setSupplierId(supplierAuthorityDTO.getSupplierId());
        goodsReturnPageRequestBO.setBuyerName(goodsReturnPageRequestDTO.getBuyerName());
        goodsReturnPageRequestBO.setStartDate(goodsReturnPageRequestDTO.getStartDate());
        goodsReturnPageRequestBO.setEndDate(goodsReturnPageRequestDTO.getEndDate());

        BasePageResponseDTO<GoodsReturnPageVO> basePageResponseDTO = PageResponseUtils.pageInvoke(
                () -> goodsReturnMapper.findByPageParams(goodsReturnPageRequestBO),
                GoodsReturnTranslator::doToVO,
                goodsReturnPageRequestDTO.getPageNo(), goodsReturnPageRequestDTO.getPageSize()
        );
        // 查询实时订单状态
        List<GoodsReturnPageVO> goodsReturnPageVOS = basePageResponseDTO.getData();
        if(CollectionUtils.isNotEmpty(goodsReturnPageVOS)) {
            Map<Integer, List<OrderMasterDO>> orderStatusMap = orderMasterMapper.findByIdIn(goodsReturnPageVOS.stream().map(GoodsReturnPageVO::getOrderId).collect(Collectors.toSet())).stream().collect(Collectors.groupingBy(OrderMasterDO::getId));
            for (GoodsReturnPageVO pageVO : goodsReturnPageVOS) {
                List<OrderMasterDO> orderMasterDOS = orderStatusMap.get(pageVO.getOrderId());
                if(CollectionUtils.isNotEmpty(orderMasterDOS)) {
                    pageVO.setOrderStatus(orderMasterDOS.get(0).getStatus());
                    pageVO.setOrderType(orderMasterDOS.get(0).getOrderType());
                }
            }
        }
        fillOrderExtraInfo(goodsReturnPageVOS);
        return basePageResponseDTO;
    }

    @Override
    public RemoteResponse<GoodsReturnStatisticsVO> getGoodsReturnStatistics() {
        SupplierAuthorityDTO supplierAuthorityDTO = getSupplierAuthority();
        GoodsReturnStatisticsVO result = new GoodsReturnStatisticsVO().setAgreeReturnCount(0).setReturnedCount(0).setSuccessfulCount(0).setWaitingConfirmCount(0)
                .setCancelReturnCount(0).setRefusedReturnCount(0).setReturnAcceptanceCount(0).setTotal(0).setWaitingConfirmCount(0);
        if (CollectionUtils.isEmpty(supplierAuthorityDTO.getOrgIds()) && !supplierAuthorityDTO.isSuperAdmin()) {
            return RemoteResponse.<GoodsReturnStatisticsVO>custom().setSuccess().setData(result);
        }
        GoodsReturnPageRequestBO goodsReturnPageRequestBO = new GoodsReturnPageRequestBO();
        Integer supplierId = supplierAuthorityDTO.getSupplierId();
        Preconditions.notNull(supplierId, "供应商id不能为空！");
        goodsReturnPageRequestBO.setSupplierId(supplierId);
        goodsReturnPageRequestBO.setOrgIds(supplierAuthorityDTO.getOrgIds());

        // 从缓存中获取，如果不存在则从数据库中查
        String cacheUniqKeyString = GOODS_RETURN_STATISTICS_KEY + ":" + supplierId + ":" + supplierAuthorityDTO.getGuid();
        Object cache = cacheClient.getFromCache(cacheUniqKeyString);
        if (cache != null) {
            result = (GoodsReturnStatisticsVO) cache;
            return RemoteResponse.<GoodsReturnStatisticsVO>custom().setSuccess().setData(result);
        }

        List<GoodsReturnCountDO> goodsReturnCountList = goodsReturnMapper.countGroupByGoodsReturnStatus(goodsReturnPageRequestBO);

        int total = 0;
        for (GoodsReturnCountDO countDO : goodsReturnCountList) {
            Integer goodsReturnStatus = countDO.getGoodsReturnStatus();
            if (GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode().equals(goodsReturnStatus)) {
                result.setWaitingConfirmCount(countDO.getCount());
                total = total + countDO.getCount();
            } else if (GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(goodsReturnStatus)) {
                result.setAgreeReturnCount(countDO.getCount());
                total = total + countDO.getCount();
            } else if (GoodsReturnStatusEnum.RETURNED_GOODS.getCode().equals(goodsReturnStatus)) {
                result.setReturnedCount(countDO.getCount());
                total = total + countDO.getCount();
            } else if (GoodsReturnStatusEnum.SUCCESS.getCode().equals(goodsReturnStatus)) {
                result.setSuccessfulCount(countDO.getCount());
                total = total + countDO.getCount();
            } else {
                total = total + countDO.getCount();
            }
        }
        result.setTotal(total);

        // 将统计结果放入缓存
        cacheClient.putToCache(cacheUniqKeyString, result, 60);
        return RemoteResponse.<GoodsReturnStatisticsVO>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<GoodsReturnInfoVO> getGoodsReturnInfo(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        // 获取退货单详情信息
        Integer returnId = goodsReturnBaseRequestDTO.getReturnId();
        Preconditions.notNull(returnId, "查询详情失败！id不可为空！");
        GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(returnId);
        BusinessErrUtil.notNull(returnId, ExecptionMessageEnum.QUERY_DETAILS_FAILED_RETURN_ORDER_NOT_EXIST);
        SupplierAuthorityDTO supplierAuthority = getSupplierAuthority();
        if (!supplierAuthority.isSuperAdmin()) {
            BusinessErrUtil.isTrue(supplierAuthority.getOrgIds().contains(goodsReturn.getOrgId()), ExecptionMessageEnum.NO_PERMISSION_VIEW);
        }
        //1. 设置退货单详情
        GoodsReturnInfoVO result = GoodsReturnTranslator.doToGoodsReturnInfoVO(goodsReturn);
        Integer orderId = goodsReturn.getOrderId();
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(orderId);
        // 设置现货仓标识
        Map<Integer, String> orderExtraMap = getOrderExtraMap(result.getOrderId());
        String stockWarehouseType = orderExtraMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), String.valueOf(StockTypeEnum.DEFAULT.getValue()));
        result.setStockWarehouseType(Integer.valueOf(stockWarehouseType));
        // 设置采购收货地址和联系方式
        result.setDeliveryAddress(orderMasterDO.getFbiderdeliveryplace());
        result.setOrgCode(orderMasterDO.getFusercode());
        RemoteResponse<List<DepartmentDTO>> remoteResponse = departmentRpcService.getDepartmentsByIds(Arrays.asList(orderMasterDO.getFbuydepartmentid().longValue()));
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        DepartmentDTO departmentDTO = remoteResponse.getData().get(0);
        RemoteResponse<List<UserBaseInfoDTO>> userInfoResponse = userInfoService.getUserByUserIds(Arrays.asList(orderMasterDO.getFbuyerid(), departmentDTO.getManagerId()));
        Preconditions.isTrue(userInfoResponse.isSuccess(), userInfoResponse.getMsg());
        Map<Integer, List<UserBaseInfoDTO>> map = userInfoResponse.getData().stream().collect(Collectors.groupingBy(UserBaseInfoDTO::getId));
        result.setPhone(map.get(orderMasterDO.getFbuyerid()).get(0).getMobile());
        List<UserBaseInfoDTO> userBaseInfoDTOS = map.get(departmentDTO.getManagerId());
        if (CollectionUtils.isNotEmpty(userBaseInfoDTOS)) {
            result.setDepartmentManageName(userBaseInfoDTOS.get(0).getName());
        }
        OrderAddressDTO orderAddressDTO = orderAddressRPCClient.findByOrderId(orderId);
        result.setBuyerContactMan(orderMasterDO.getFbuyercontactman());
        result.setBuyerTelephone(orderMasterDO.getFbuyertelephone());
        if (orderAddressDTO != null && DeliveryTypeEnum.PROXY.getCode().equals(orderAddressDTO.getDeliveryType())) {
            result.setBuyerContactMan(orderAddressDTO.getReceiverNameProxy());
            result.setBuyerTelephone(orderAddressDTO.getReceiverPhoneProxy());
        }
        result.setSuppCode(orderMasterDO.getFsuppcode());

        result.setAutoAcceptDays(-1);
        if(goodsReturn.getReplyTime() != null) {
            long autoAcceptDays = goodsReturn.getReplyTime().getTime() / (1000 * 60 * 60 * 24) - System.currentTimeMillis() / (1000 * 60 * 60 * 24);
            autoAcceptDays = autoAcceptDays > 0 ? autoAcceptDays : 0;
            result.setAutoAcceptDays((int) autoAcceptDays);
        }
        List<OrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue(), OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue()));
        Map<Integer, String> extraKeyValueMap = com.ruijing.order.utils.DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        result.setEachProductEachCode(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue())));
        result.setSuppNeedFillBatchesData(CommonValueUtils.parseNumberStrToBoolean(extraKeyValueMap.get(OrderExtraEnum.SUPP_NEED_FILL_BATCHES_DATA.getValue())));
        result.setOrderType(orderMasterDO.getOrderType());
        // 设置退货的气瓶
        this.constructReturnDetailGasBottle(result);
        return RemoteResponse.<GoodsReturnInfoVO>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<List<GoodsReturnLogVO>> getGoodsReturnLog(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        Integer returnId = goodsReturnBaseRequestDTO.getReturnId();
        Preconditions.notNull(returnId, "更新退货订单失败！退货单id不可空！");
        List<GoodsReturnLogDO> list = goodsReturnLogDOMapper.findByReturnId(returnId);
        List<GoodsReturnLogVO> result = list.stream().map(GoodsReturnLogTranslator::doToVO).collect(Collectors.toList());
        return RemoteResponse.<List<GoodsReturnLogVO>>custom().setSuccess().setData(result);
    }

    @Override
    public RemoteResponse<Boolean> updateGoodsReturnStatus(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        Preconditions.notNull(goodsReturnBaseRequestDTO, "参数为空");
        Integer returnId = goodsReturnBaseRequestDTO.getReturnId();
        Preconditions.notNull(goodsReturnBaseRequestDTO.getReturnId(), "更新退货订单失败！退货单id不可空！");
        Integer returnStatus = goodsReturnBaseRequestDTO.getReturnStatus();
        Preconditions.notNull(returnStatus, "更新退货订单失败！退货单状态不可空！");
        GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(returnId);
        Preconditions.notNull(returnId, "查询详情失败！该退货单不存在！");
        SupplierAuthorityDTO supplierAuthority = getSupplierAuthority();
        if (!supplierAuthority.isSuperAdmin()) {
            BusinessErrUtil.isTrue(supplierAuthority.getOrgIds().contains(goodsReturn.getOrgId()), ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
            BusinessErrUtil.isTrue(supplierAuthority.getSupplierId().equals(goodsReturn.getSupplierId()), ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        }

        // 单个更新操作
        RemoteResponse<Boolean> response = this.updateGoodsReturnStatus(goodsReturnBaseRequestDTO, goodsReturn, supplierAuthority);
        
        if (response.isSuccess()) {
            // 供应商修改任意退货单的退货状态后， 移除数量缓存让前端重新获取
            String cacheUniqKeyString = GOODS_RETURN_STATISTICS_KEY + ":" + supplierAuthority.getSupplierId() + ":" + supplierAuthority.getGuid();
            cacheClient.removeCache(cacheUniqKeyString);
        }
        return response;
    }

    /**
     * 同意退货并确认验收
     */
    @Override
    public RemoteResponse<Boolean> agreeAndAcceptReturn(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO) {
        Preconditions.notNull(goodsReturnBaseRequestDTO, "参数为空");
        Integer returnId = goodsReturnBaseRequestDTO.getReturnId();
        Preconditions.notNull(returnId, "更新退货订单失败！退货单id不可空！");
        GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(returnId);
        BusinessErrUtil.notNull(goodsReturn, ExecptionMessageEnum.QUERY_DETAILS_FAILED_RETURN_ORDER_NOT_EXIST);
        
        SupplierAuthorityDTO supplierAuthority = getSupplierAuthority();
        if (!supplierAuthority.isSuperAdmin()) {
            BusinessErrUtil.isTrue(supplierAuthority.getOrgIds().contains(goodsReturn.getOrgId()), ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
            BusinessErrUtil.isTrue(supplierAuthority.getSupplierId().equals(goodsReturn.getSupplierId()), ExecptionMessageEnum.OPERATION_FAILED_NO_PERMISSION);
        }
        
        // 校验退货单状态是否为待确认
        BusinessErrUtil.isTrue(Objects.equals(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode(), goodsReturn.getGoodsReturnStatus()), 
                ExecptionMessageEnum.RETURN_ORDER_STATUS_CHANGED_REFRESH);
                
        // 执行同意退货并确认验收
        RemoteResponse<Boolean> response = this.executeAgreeAndAcceptReturn(goodsReturnBaseRequestDTO, goodsReturn, supplierAuthority);
        
        if (response.isSuccess()) {
            // 清除缓存
            String cacheUniqKeyString = GOODS_RETURN_STATISTICS_KEY + ":" + supplierAuthority.getSupplierId() + ":" + supplierAuthority.getGuid();
            cacheClient.removeCache(cacheUniqKeyString);
        }
        
        return response;
    }

    /**
     * 执行同意退货并确认验收操作
     */
    private RemoteResponse<Boolean> executeAgreeAndAcceptReturn(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO, GoodsReturn goodsReturn, SupplierAuthorityDTO supplierAuthority) {
        // 构建同意退货的请求
        GoodsReturnBaseRequestDTO agreeDTO = new GoodsReturnBaseRequestDTO();
        agreeDTO.setReturnId(goodsReturn.getId());
        agreeDTO.setReturnStatus(GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode());
        agreeDTO.setReason(goodsReturnBaseRequestDTO.getReason());
        agreeDTO.setPic(goodsReturnBaseRequestDTO.getPic());
        agreeDTO.setGoodsReturnInfoDetailVOList(goodsReturnBaseRequestDTO.getGoodsReturnInfoDetailVOList());
        // 执行 同意退货操作
        RemoteResponse<Boolean> agreeResponse = this.updateGoodsReturnStatus(agreeDTO, goodsReturn, supplierAuthority);

        // 如果同意退货失败，直接返回错误结果
        if (Objects.nonNull(agreeResponse) && !agreeResponse.isSuccess()) {
            return agreeResponse;
        }

        // 查询最新的退货单状态
        GoodsReturn updatedGoodsReturn = goodsReturnMapper.selectByPrimaryKey(goodsReturn.getId());
        Preconditions.notNull(updatedGoodsReturn, "查询详情失败！该退货单不存在！");

        // 构造确认验收的请求
        GoodsReturnBaseRequestDTO acceptDTO = new GoodsReturnBaseRequestDTO();
        acceptDTO.setReturnId(goodsReturn.getId());
        acceptDTO.setReturnStatus(GoodsReturnStatusEnum.SUCCESS.getCode());
        acceptDTO.setPic(goodsReturnBaseRequestDTO.getPic());
        acceptDTO.setReason(goodsReturnBaseRequestDTO.getReason());
        acceptDTO.setGoodsReturnInfoDetailVOList(goodsReturnBaseRequestDTO.getGoodsReturnInfoDetailVOList());

        // 执行确认验收操作
        return this.updateGoodsReturnStatus(acceptDTO, updatedGoodsReturn, supplierAuthority);
    }

    @Override
    public RemoteResponse<Boolean>  updateGoodsReturnStatus(GoodsReturnBaseRequestDTO goodsReturnBaseRequestDTO, GoodsReturn goodsReturn, SupplierAuthorityDTO supplierAuthority) {
        Preconditions.notNull(goodsReturnBaseRequestDTO, "参数为空");
        Preconditions.notNull(goodsReturnBaseRequestDTO.getReturnId(), "更新退货订单失败！退货单id不可空！");
        Integer returnStatus = goodsReturnBaseRequestDTO.getReturnStatus();
        Preconditions.notNull(returnStatus, "更新退货订单失败！退货单状态不可空！");

        RemoteResponse<List<OrganizationDTO>> organizationResponse = organizationRpcService.getByIds(Arrays.asList(goodsReturn.getOrgId()));
        Preconditions.isTrue(organizationResponse.isSuccess(), organizationResponse.getMsg());
        OrganizationDTO organizationDTO = organizationResponse.getData().get(0);
        OrderMasterDO orderMasterDO = orderMasterMapper.selectByPrimaryKey(goodsReturn.getOrderId());
        RemoteResponse<List<UserBaseInfoDTO>> remoteResponse = userInfoService.getUserByUserIds(Arrays.asList(orderMasterDO.getFbuyerid()));
        UserBaseInfoDTO userBaseInfoDTO = remoteResponse.getData().get(0);
        GoodsReturnOperationTypeEnum operationType;
        String returnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
        List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS = GoodsReturnTranslator.parseJSONToInfoDetailVO(returnDetailJSON);
        // 如果入参有detail, 则回写退货商品详情数据
        if(CollectionUtils.isNotEmpty(goodsReturnBaseRequestDTO.getGoodsReturnInfoDetailVOList())){
            Map<String, GoodsReturnInfoDetailVO> goodsCodeDetailVOMap = DictionaryUtils.toMap(goodsReturnBaseRequestDTO.getGoodsReturnInfoDetailVOList(), GoodsReturnInfoDetailVO::getGoodsCode, Function.identity());
            goodsReturnInfoDetailVOS.forEach(item->{
                GoodsReturnInfoDetailVO requestDetail = goodsCodeDetailVOMap.get(item.getGoodsCode());
                if(requestDetail != null){
                    item.setReturnAcceptMan(requestDetail.getReturnAcceptMan());
                    item.setReturnAcceptAddr(requestDetail.getReturnAcceptAddr());
                    item.setReturnAcceptPhone(requestDetail.getReturnAcceptPhone());
                }
            });
            goodsReturn.setGoodsReturnDetailJSON(JsonUtils.toJson(goodsReturnInfoDetailVOS));
        }

        if (GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(returnStatus)) {
//            sysuClient.checkIsApplyStealOff(orderMasterDO.getFuserid());
            BusinessErrUtil.isTrue(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode().equals(goodsReturn.getGoodsReturnStatus()), ExecptionMessageEnum.RETURN_ORDER_STATUS_CHANGED_REFRESH);
            operationType = GoodsReturnOperationTypeEnum.SUPPLIER_AGREE_GOODS_RETURN;
            goodsReturn.setAgreeReason(goodsReturnBaseRequestDTO.getReason());
            //设置自动验收时间
            Date date = new Date();
            date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * 14);
            goodsReturn.setReplyTime(date);
        } else if (GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(returnStatus)) {
            operationType = GoodsReturnOperationTypeEnum.SUPPLIER_REJECT_GOODS_RETURN;
            BusinessErrUtil.isTrue(GoodsReturnStatusEnum.WAITING_FOR_CONFIRM.getCode().equals(goodsReturn.getGoodsReturnStatus()), ExecptionMessageEnum.RETURN_ORDER_STATUS_CHANGED_REFRESH);
            BusinessErrUtil.isTrue(!OrderStatusEnum.PLATFORM_OPERATOR_APPROVAL_REJECT.getValue().equals(orderMasterDO.getStatus()), "本订单不支持拒绝退货。此订单经平台审核后不符合单位管理规定，依据单位要求，此订单需做退货处理");
            goodsReturn.setRefuseReason(goodsReturnBaseRequestDTO.getReason());
        } else if (GoodsReturnStatusEnum.SUCCESS.getCode().equals(returnStatus)) {
            Preconditions.isTrue(GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(goodsReturn.getGoodsReturnStatus())
                    || GoodsReturnStatusEnum.RETURNED_GOODS.getCode().equals(goodsReturn.getGoodsReturnStatus()), "退货单状态已改变，请刷新再看！");
            operationType = GoodsReturnOperationTypeEnum.SUPPLIER_RECEIVE_GOODS_RETURN;
            // 中大调用异步解冻接口, 回调再执行以下操作
            if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(orderMasterDO.getFusercode())) {
                researchCustomClient.agree2ReturnOrderForSYSU(orderMasterDO.getId(), orderMasterDO.getFusercode());
            } else {
                this.returnSuccess(goodsReturn, goodsReturnInfoDetailVOS, organizationDTO, orderMasterDO);
            }
            this.updateBarcodeReturnStatus(goodsReturn, OrderProductTransactionStatusEnum.RETURNED);
            // 解绑气瓶
            this.unbindGasBottleForReturn(orderMasterDO.getId(), orderMasterDO.getFuserid(), goodsReturn.getReturnNo(), goodsReturnInfoDetailVOS);
        } else {
            return RemoteResponse.<Boolean>custom() .setFailure("操作失误，请联系客服").setData(Boolean.FALSE);
        }

        goodsReturn.setGoodsReturnStatus(returnStatus);
        goodsReturn.setPicth(goodsReturnBaseRequestDTO.getPic());
        goodsReturnMapper.updateByPrimaryKey(goodsReturn);
        // 回写detail退货状态
        this.updateOrderDetailReturnStatus(goodsReturn);

        // 4.插入操作日志
        commonGoodsReturnService.saveReturnOperationLog(
                GoodsReturnLogTranslator.buildDO(supplierAuthority.getSupplierId(), supplierAuthority.getUserName(), goodsReturn.getId(), GoodsReturnOperatorTypeEnum.SUPPLIER, operationType, goodsReturnBaseRequestDTO.getReason(), goodsReturnBaseRequestDTO.getPic())
        );
        // 推送到管理平台
        commonGoodsReturnService.pushReturnToThirdPlatform(goodsReturn, orderMasterDO);

        if (GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode().equals(returnStatus)
                || GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(returnStatus)) {
            List<GoodsReturnInfoDetailVO> finalGoodsReturnInfoDetailVOS = goodsReturnInfoDetailVOS;
            AsyncExecutor.runAsync(() -> {
                noticeBuyer(goodsReturn, finalGoodsReturnInfoDetailVOS, userBaseInfoDTO, orderMasterDO);
                orderMessageHandler.sendReturnGoodsMsgToBuyer(orderMasterDO, goodsReturn, userBaseInfoDTO);
            });
        } else if (GoodsReturnStatusEnum.SUCCESS.getCode().equals(returnStatus)){
            filingControlClient.unfreezeFilingControlWhenReturnSuccess(orderMasterDO);
        }
        // 供应商修改任意退货单的退货状态后， 移除数量缓存让前端重新获取
        String cacheUniqKeyString = GOODS_RETURN_STATISTICS_KEY + ":" + supplierAuthority.getSupplierId() + ":" + supplierAuthority.getGuid();
        cacheClient.removeCache(cacheUniqKeyString);
        return RemoteResponse.<Boolean>custom() .setSuccess().setData(Boolean.TRUE);
    }

    /**
     * 更新order_detail退货状态
     * @param goodsReturn
     */
    private void updateOrderDetailReturnStatus(GoodsReturn goodsReturn) {
        // 查询已存在的退货单信息
        List<GoodsReturn> existGoodsReturnList = goodsReturnMapper.findByOrderId(goodsReturn.getOrderId());
        List<OrderDetailDO> detailList = orderDetailMapper.findByFmasterid(goodsReturn.getOrderId());
        // 获取当前最优先展示的退货状态的map
        Map<Integer, Integer> detailIdReturnStatusMap = commonGoodsReturnService.getFirstReturnStatusMapByReturn(existGoodsReturnList);
        if (CollectionUtils.isNotEmpty(detailList)) {
            String goodsReturnDetailJSON = goodsReturn.getGoodsReturnDetailJSON();
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = GoodsReturnTranslator.parseJSONToInfoDetailVO(goodsReturnDetailJSON);

            Map<String, List<GoodsReturnInfoDetailVO>> detailIdGoodsReturnVOMap = goodsReturnInfoDetailVOList.stream().collect(Collectors.groupingBy(GoodsReturnInfoDetailVO::getDetailId));
            for (OrderDetailDO detailDO : detailList) {
                List<GoodsReturnInfoDetailVO> returnListItem = detailIdGoodsReturnVOMap.get(detailDO.getId().toString());
                if (CollectionUtils.isEmpty(returnListItem)) {
                    continue;
                }
                // 更新减去订单明细里的退货数量
                // 状态显示优先级 待确认0->同意退货1->拒绝退货2->采购人已退货4->已完成退货5
                detailDO.setReturnStatus(detailIdReturnStatusMap.get(detailDO.getId()) != null ? detailIdReturnStatusMap.get(detailDO.getId()) : GoodsReturnStatusEnum.REFUSED_TO_RETURN.getCode());
            }
            orderDetailMapper.loopUpdateByIdIn(detailList);
        }
    }

    /**
     * <AUTHOR>
     * @Param [goodsReturn, goodsReturnInfoDetailVOS]
     * @return void
     * @Description //退货成功
     **/
    @Override
    public void returnSuccess(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS,
                               OrganizationDTO organizationDTO, OrderMasterDO orderMasterDO) {
        BigDecimal alreadyReturnAmount = Objects.nonNull(orderMasterDO.getReturnAmount()) ? BigDecimal.valueOf(orderMasterDO.getReturnAmount()) : BigDecimal.ZERO;
        BigDecimal orderAmountTotal = orderMasterDO.getForderamounttotal();

        // 针对旧退货单，这个旧退货单是指改版前2021-03-04之前未收货的退货单，要反查一下detail表的退货状态，退货总金额不累加已完成退货的商品金额
        List<OrderDetailDO> detailByMasterId = orderDetailMapper.findByFmasterid(orderMasterDO.getId());
        Set<String> unFinishReturnDetailId = detailByMasterId.stream().filter(d -> !GoodsReturnStatusEnum.SUCCESS.getCode().equals(d.getReturnStatus())).map(d -> String.valueOf(d.getId())).collect(Collectors.toSet());
        // 非中大的单位，不用过滤未退货的商品明细总额，因为中大是退完货，并且解冻回调成功才更新记录
        if (!ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(goodsReturn.getOrgId())) {
            goodsReturnInfoDetailVOS = goodsReturnInfoDetailVOS.stream().filter(g -> unFinishReturnDetailId.contains(g.getDetailId())).collect(Collectors.toList());
        }
        // 本次退货总额
        BigDecimal thisReturnTotalAmount = goodsReturnInfoDetailVOS.stream().map(GoodsReturnInfoDetailVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BusinessErrUtil.isTrue(alreadyReturnAmount.add(thisReturnTotalAmount).compareTo(orderAmountTotal) <= 0, ExecptionMessageEnum.TOTAL_RETURN_AMOUNT_GREATER_CONTACT_SERVICE);

        // 计算完成本次退货后的退货总额
        UpdateOrderParamDTO updated = new UpdateOrderParamDTO();
        updated.setOrderId(orderMasterDO.getId());
        BigDecimal orderReturnAmount = Objects.nonNull(orderMasterDO.getReturnAmount()) ? BigDecimal.valueOf(orderMasterDO.getReturnAmount()) : BigDecimal.ZERO;
        orderReturnAmount = orderReturnAmount.add(thisReturnTotalAmount);
        orderMasterDO.setReturnAmount(orderReturnAmount.doubleValue());
        updated.setReturnAmount(orderReturnAmount.doubleValue());

        // 1.本次退货需要更新的信息
        // 1.1.更新经费卡使用金额
        if (OrgEnum.ZHONG_SHAN_DA_XUE_ZHONG_SHAN_YAN_KE_ZHONG_XIN.getCode().equals(organizationDTO.getCode())) {
            BigDecimal useMoney = orderAmountTotal.subtract(thisReturnTotalAmount);
            RefFundcardOrderDO refFundcardOrderDO = new RefFundcardOrderDO();
            refFundcardOrderDO.setOrderId(orderMasterDO.getId().toString());
            refFundcardOrderDO.setUsemoney(useMoney);
            refFundcardOrderMapper.updateByOrderId(refFundcardOrderDO);
        }
        // 1.2.返回商品库存
        boolean updateStockResult = productStockService.addSku(orderMasterDO.getFsuppid(), goodsReturnInfoDetailVOS, goodsReturn.getReturnNo(), orderMasterDO);
        BusinessErrUtil.isTrue(updateStockResult, ExecptionMessageEnum.RETURN_PRODUCT_INVENTORY_FAILED);
        // 1.3.释放合同金额
        clinicalOrderService.releaseReagentProductAnnualUsage(OrderApprovalEnum.GOODS_RETURN, orderMasterDO, goodsReturn);
        // 1.4.如果已经产生扣费记录，则同步保存到wallet, 生成退款单
        this.walletReturn(orderMasterDO, goodsReturn, thisReturnTotalAmount);
        // 1.5采购金额统计
        applicationBaseService.updateApplyManageProductUsage(orderMasterDO, ApplyManageOperationEnum.PRODUCT_RETURN.getValue(), null, goodsReturnInfoDetailVOS);

        // 2.判断是否完成退货，完成则处理逻辑
        List<GoodsReturn> goodsReturns = goodsReturnMapper.findByOrderId(orderMasterDO.getId());
        // 获取已完成退货的数据
        Map<String, List<GoodsReturnInfoDetailVO>> detailIdReturnDetailsMap = goodsReturns.stream()
                .filter((o -> !goodsReturn.getId().equals(o.getId())))
                .filter(o -> GoodsReturnStatusEnum.SUCCESS.getCode().equals(o.getGoodsReturnStatus()))
                .map(o -> GoodsReturnTranslator.parseJSONToInfoDetailVO(o.getGoodsReturnDetailJSON()))
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(GoodsReturnInfoDetailVO::getDetailId));
        // 当前退货单即将完成，状态未更新，这里也算进来
        goodsReturnInfoDetailVOS.forEach(item-> detailIdReturnDetailsMap.computeIfAbsent(item.getDetailId(), (k)->New.list()).add(item));
        // 若所有退货商品退货数量=购买数量，则为全部退货
        boolean allReturn = detailByMasterId.stream().allMatch(detail->{
            List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOList = detailIdReturnDetailsMap.getOrDefault(detail.getId().toString(), New.emptyList());
            BigDecimal returnQuantity = goodsReturnInfoDetailVOList.stream().map(GoodsReturnInfoDetailVO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            return detail.getFquantity().compareTo(returnQuantity) == 0;
        });
        if (allReturn) {
            // 2.1.删除发票，移除待结算记录
            invoiceClient.deleteInvoiceByOrderIds(New.list(orderMasterDO.getId()));
            statementPlatformClient.deleteWaitingStatementByOrderId(Collections.singletonList(orderMasterDO.getId()));
            // 订单标记为已关闭
            Date now = new Date();
            updated.setStatus(OrderStatusEnum.Close.getValue());
            updated.setCancelReason(OrderOperateLogConstant.RETURN_FINISH_REASON);
            updated.setCancelDate(now);
            updated.setShutDownDate(now);
            // 更新查出来的数据，方便后面使用
            orderMasterDO.setStatus(OrderStatusEnum.Close.getValue());
        }
        orderMasterMapper.updateOrderById(updated);
        // 2.2.解冻经费
        orderFundCardUnFreeze(goodsReturn, orderMasterDO, organizationDTO, allReturn, thisReturnTotalAmount);
        // 2.3.释放预占库存
        warehouseStockOccupyService.releaseAfterReturnGoodsComplete(goodsReturn, goodsReturnInfoDetailVOS);
        // 2.4.订单已关闭，则删除汇卡记录
        reimbursementService.deleteRecord(orderMasterDO);
    }

    /**
     * 返还钱包费用
     * @param orderMasterDO 订单
     * @param goodsReturn 退货
     * @param thisReturnTotalAmount 本次退货金额
     */
    private void walletReturn(OrderMasterDO orderMasterDO, GoodsReturn goodsReturn, BigDecimal thisReturnTotalAmount){
        List<WaitingChargingOrderDTO> walletRecords = walletOrderRpcClient.queryWaitingChargingOrderPage(orderMasterDO.getForderno());
        // 是否存在，新表订单收货的扣费记录
        boolean newHasCharging = CollectionUtils.isNotEmpty(walletRecords);
        // 是否存在，旧表订单收货的扣费记录
        boolean oldHasCharging = false;
        if (!newHasCharging) {
            List<OrderForWalletDTO> oldChargingList = walletOrderRpcClient.findByOrderNo(orderMasterDO.getForderno());
            oldHasCharging = CollectionUtils.isNotEmpty(oldChargingList);
        }
        // 包含扣费记录，则生成退款记录
        if (newHasCharging || oldHasCharging) {
            // 反查一下退款单记录
            List<WalletOrderReturnDTO> refundByReturnNo = walletOrderRpcClient.findRefundByReturnNo(goodsReturn.getReturnNo());
            //  控制
            if (CollectionUtils.isNotEmpty(refundByReturnNo)) {
                BigDecimal alreadyReturnRecord = refundByReturnNo.stream().map(WalletOrderReturnDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BusinessErrUtil.isTrue(alreadyReturnRecord.add(thisReturnTotalAmount).subtract(orderMasterDO.getForderamounttotal()).compareTo(BigDecimal.ZERO) <= 0, ExecptionMessageEnum.TOTAL_RETURN_AMOUNT_GREATER_CANNOT_OPERATE);
            }
            List<WalletOrderReturnDTO> walletOrderReturnDTOS = New.list();
            WalletOrderReturnDTO walletOrderReturnDTO = new WalletOrderReturnDTO();
            walletOrderReturnDTO.setOrderNo(orderMasterDO.getForderno());
            walletOrderReturnDTO.setOrgId(Long.valueOf(orderMasterDO.getFuserid()));
            walletOrderReturnDTO.setSuppId(orderMasterDO.getFsuppid());
            walletOrderReturnDTO.setAmount(thisReturnTotalAmount);
            walletOrderReturnDTO.setReturnNo(goodsReturn.getReturnNo());
            walletOrderReturnDTOS.add(walletOrderReturnDTO);
            walletOrderRpcClient.saveOrderReturn(walletOrderReturnDTOS);
        }
    }

    /**
     * <AUTHOR>
     * @Param []
     * @return void
     * @Description //解冻经费卡
     **/
    private void orderFundCardUnFreeze(GoodsReturn goodsReturn, OrderMasterDO orderMasterDO, OrganizationDTO organizationDTO,
                                       boolean allReturn, BigDecimal returnAmount) {
        if (ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgCode(organizationDTO.getCode())) {
            return;
        } else if (GoodsReturnConstant.ALL_RETURN_ORG.containsKey(organizationDTO.getId())) {  // 只能整单退货的采购单位解冻经费卡
            OrderUnFreezeRequestDTO orderUnFreezeRequestDTO = new OrderUnFreezeRequestDTO();
            orderUnFreezeRequestDTO.setOrderId(orderMasterDO.getId());
            orderUnFreezeRequestDTO.setReturnId(goodsReturn.getId().toString());
            orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.RETURN);
            orderUnFreezeRequestDTO.setFreezeAmount(orderMasterDO.getForderamounttotal());
            orderManageService.orderFundCardUnFreeze(orderUnFreezeRequestDTO);
            //华农未上线
/*            if (OrgEnum.HUA_NAN_NONG_YE_DA_XUE.equals(organizationDTO.getCode())) {
                UpdateOrderParamDTO updateOrderParamDTO = new UpdateOrderParamDTO();
                updateOrderParamDTO.setOrderId(orderMasterDO.getId());
                updateOrderParamDTO.setOrderNo(orderMasterDO.getForderno());
                updateOrderParamDTO.setStatus(OrderStatusEnum.Close.getValue());
                RemoteResponse<Boolean> response = orderRelatedRPCService.updateThirdPlatformOrderForSupplier(updateOrderParamDTO);
                Preconditions.isTrue(response.isSuccess(), response.getMsg());
            }*/

        } else {// 非只能整单退货的采购单位解冻经费卡
            OrderUnFreezeRequestDTO orderUnFreezeRequestDTO = new OrderUnFreezeRequestDTO();
            orderUnFreezeRequestDTO.setOrderId(orderMasterDO.getId());
            orderUnFreezeRequestDTO.setReturnId(goodsReturn.getId().toString());
            if (allReturn) { // 整单退货
                orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.RETURN);
            } else {
                orderUnFreezeRequestDTO.setOrderUnFreezeTypeEnum(OrderUnFreezeTypeEnum.RETURN_PARTIALLY);
            }
            orderUnFreezeRequestDTO.setFreezeAmount(returnAmount);
            orderManageService.orderFundCardUnFreeze(orderUnFreezeRequestDTO);
        }
    }

    /**
     * <AUTHOR>
     * @Param []
     * @return void
     * @Description //通知采购人
     **/
    private void noticeBuyer(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS,
                             UserBaseInfoDTO userBaseInfoDTO, OrderMasterDO orderMasterDO) {
        boolean isAgree = GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode().equals(goodsReturn.getGoodsReturnStatus()) ? Boolean.TRUE : Boolean.FALSE;
        //标题
        String result = isAgree ? "通过" : "驳回";
        SendingPersonalSettingParam param = new SendingPersonalSettingParam();
        param.setGuid(userBaseInfoDTO.getGuid());
        param.setWay(SendingWayEnum.MESSAGE.getValue().byteValue());
        if (isAgree) {
            param.setBusiness(SendingBusinessEnum.RETURN_GOODS_AGREE_TO_ORG.getValue());
        } else {
            param.setBusiness(SendingBusinessEnum.RETURN_GOODS_DISAGREE_TO_ORG.getValue());
        }
        boolean smsSettingIsEnable = getSettingResult(
                userBaseInfoDTO.getGuid(),
                orderMasterDO.getFuserid().toString(),
                GroupTypeEnum.ORGANIZATION,
                param);
        param.setWay(SendingWayEnum.EMAIL.getValue().byteValue());

        BigDecimal returnAmount = goodsReturnInfoDetailVOS.stream().map(GoodsReturnInfoDetailVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (smsSettingIsEnable) {
            sendMessageToBuyer(goodsReturn, userBaseInfoDTO, result, returnAmount);
        }

        SendingBusinessEnum businessEnum = isAgree ? SendingBusinessEnum.RETURN_GOODS_AGREE_TO_ORG : SendingBusinessEnum.RETURN_GOODS_DISAGREE_TO_ORG;
        if (orderMessageHandler.isBuyerEmailEnabled(userBaseInfoDTO.getGuid(), orderMasterDO.getFuserid(), businessEnum)) {
            orderMessageHandler.sendGoodsReturnEmailToBuyer(goodsReturn, goodsReturnInfoDetailVOS, userBaseInfoDTO, result, returnAmount);
        }
    }

    /**
     * @return void
     * <AUTHOR>
     * @Param [goodsReturn, goodsReturnInfoDetailVOS]
     * @Description //退货发送短信
     **/
    private void sendMessageToBuyer(GoodsReturn goodsReturn, UserBaseInfoDTO userBaseInfoDTO, String result, BigDecimal returnAmount) {
        //发送SMS 用户
        String targetPhone =
                SEND_EMAIL_TARGET.equals(SEND_EMAIL_TARGET_CONFIG) ?
                        SEND_EMAIL_TARGET_PHONE : userBaseInfoDTO.getMobile();
        //读取模板
        String tmpString = FileUtils.translateTemplate("msg-templet/goods-return.txt");
        //短信内容
        String phoneContext = tmpString.replace("#suppName#", goodsReturn.getSupplierName())
                .replace("#result#", result)
                .replace("#price#", returnAmount.setScale(2, RoundingMode.HALF_UP).toString());
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setOrgId(goodsReturn.getOrgId());
        messageDTO.setReceiver(new String[]{targetPhone});
        messageDTO.setContent(phoneContext);
        messageDTO.setMessageType(MessageTypeEnum.PHONE_MESSAGE);
        messageService.asyncSend(messageDTO);
    }


  /**
   * <AUTHOR>
   * @Param [goodsReturn, goodsReturnInfoDetailVOS]
   * @return void
   * @Description //退货发送邮件
   **/
    private void sendEmailToBuyer(GoodsReturn goodsReturn, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS,
                                  UserBaseInfoDTO userBaseInfoDTO, String result, BigDecimal returnAmount) {
        //发送邮件 用户
        String targetEmail =
                SEND_EMAIL_TARGET.equals(SEND_EMAIL_TARGET_CONFIG.trim()) ?
                        SEND_EMAIL_TARGET_EMAIL : userBaseInfoDTO.getEmail();
        //标题
        String emailTitle = GOODS_RETURN_EMAIL_TITLE.replace("#title#", result);
        //内容 获取行内容
        StringBuilder stringBuilder = new StringBuilder();
        goodsReturnInfoDetailVOS.forEach(goodsReturnInfoDetailVO -> {
            stringBuilder.append(TR_HTML.replace("#name#", goodsReturnInfoDetailVO.getGoodsName())
                    .replace("#brand#", goodsReturnInfoDetailVO.getBrand())
                    .replace("#code#", goodsReturnInfoDetailVO.getGoodsCode())
                    .replace("#unit#", goodsReturnInfoDetailVO.getUnit()));
        });
        String url = ZhongShanDaXueBelongUtils.isContainZhongShanDaXueByOrgId(goodsReturn.getOrgId()) ?
                sysuClient.getAppHostByOrgCode(ZhongShanDaXueOrgEnum.getByOrgId(goodsReturn.getOrgId()).getOrgCode()) + "/index.html?comeFromCASLogin=1#refunddetails/%s/false"
                : GOODS_RETURN_HTTP;
        //读取模板
        String tmpString = FileUtils.translateTemplate("email-templet/goods-return.txt");
        //邮件内容
        String emailContext = tmpString.replace("#suppName#", goodsReturn.getSupplierName())
                .replace("#result#", result)
                .replaceAll("#returnNo#", goodsReturn.getReturnNo())
                .replaceAll("#goodsReturnUrl#",String.format(url, goodsReturn.getId()))
                .replace("#price#", returnAmount.setScale(2, RoundingMode.HALF_UP).toString())
                .replace("#trHtml#", stringBuilder.toString());
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setOrgId(goodsReturn.getOrgId());
        messageDTO.setReceiver(new String[]{targetEmail});
        messageDTO.setContent(emailContext);
        messageDTO.setSubject(emailTitle);
        messageDTO.setMessageType(MessageTypeEnum.EMAIL_HTML);
        messageService.asyncSend(messageDTO);
    }


    /**
     * @param guid
     * @param groupId
     * @param groupTypeEnum
     * @param sendingPersonalSettingParams
     * @return  是否配置了发送
     */
    private boolean getSettingResult(String guid,
                                    String groupId,
                                    GroupTypeEnum groupTypeEnum,
                                    SendingPersonalSettingParam sendingPersonalSettingParams) {
        SendingPersonalAndDefaultParam personalAndDefaultParam = new SendingPersonalAndDefaultParam();
        personalAndDefaultParam.setGuid(guid);
        if (groupTypeEnum.getValue().equals(GroupTypeEnum.SUPPLIER.getValue())) {
            personalAndDefaultParam.setGroupType(GroupTypeEnum.SUPPLIER.getValue());
        } else {
            personalAndDefaultParam.setGroupType(GroupTypeEnum.ORGANIZATION.getValue());
        }
        personalAndDefaultParam.setGroupId(groupId);

        List<SendingPersonalSettingParam> param = New.list(sendingPersonalSettingParams);
        personalAndDefaultParam.setPersonalSettingList(param);

        RemoteResponse<List<SendingPersonalAndDefaultDTO>> remoteResponse =
                sendingPersonalSettingService.getSettingListByPersonalAndDefault(personalAndDefaultParam);
        List<SendingPersonalAndDefaultDTO> list = remoteResponse.getData();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            for (SendingPersonalAndDefaultDTO sendingPersonalAndDefaultDTO : list) {
                if (sendingPersonalAndDefaultDTO.getWay().equals(SendingWayEnum.EMAIL.getValue().byteValue())) {
                    if (sendingPersonalAndDefaultDTO.getEnable() == 1) {
                        return true;
                    }
                }
                if (sendingPersonalAndDefaultDTO.getWay().equals(SendingWayEnum.MESSAGE.getValue().byteValue())) {
                    if (sendingPersonalAndDefaultDTO.getEnable() == 1) {
                        return true;
                    }
                }
                if (sendingPersonalAndDefaultDTO.getWay().equals(SendingWayEnum.WECHAT.getValue().byteValue())) {
                    if (sendingPersonalAndDefaultDTO.getEnable() == 1) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


    /**
     * <AUTHOR>
     * @Param [suppId, guid]
     * @return java.util.List<java.lang.Integer>
     * @Description //该登陆用户的访问权限
     **/
    @Override
    public SupplierAuthorityDTO getSupplierAuthority() {
        RjSessionInfo sessionInfo = (RjSessionInfo) RpcContext.getProviderContext().getCallAttachment("RJ_SESSION_INFO");
        BusinessErrUtil.notNull(sessionInfo, ExecptionMessageEnum.LOGIN_EXPIRED_RELOGIN);
        BusinessErrUtil.isTrue(RjUserTypeEnum.SUPPLIER_USER == sessionInfo.getUserType(), ExecptionMessageEnum.NO_PERMISSION_ONLY_MERCHANT_USER, sessionInfo.getUserType().getName());
        String guid = sessionInfo.getGuid();
        Integer suppId = sessionInfo.getSuppId();
        SupplierAuthorityDTO supplierCache = (SupplierAuthorityDTO) cacheClient.getFromCache(suppId + ":" + guid);
        if (supplierCache != null) {
            return supplierCache;
        }

        UserParam userParam = new UserParam();
        userParam.setGuid(guid);
        ResultBean<UserDTO> resultBean = crmUserService.getUser(userParam);
        Preconditions.isTrue(ResultBean.SUCCESS == resultBean.getCode(), resultBean.getMsg());
        BusinessErrUtil.notNull(resultBean.getData(), ExecptionMessageEnum.ACCOUNT_EXCEPTION_NO_USER_FOUND, resultBean.getMsg());
        // 这里查询供应商当前登录用户所关联的医院id集合
        UserDTO supplier = resultBean.getData();

        String tag = supplier.getTag();
        List<Integer> allOrgIds = Optional.ofNullable(supplier.getSuppUserOrgs()).orElseGet(ArrayList::new)
                .stream().map(SuppUserOrganizationDTO::getOrgId).collect(Collectors.toList());
        allOrgIds = filterOrgIdByTag(allOrgIds, tag);
        // 这里，status传null是查询该供应商所入驻的所有医院信息（包含被供应商拉黑，供应商欠费冻结等信息）
        RemoteResponse<List<NewSuppOrgDTO>> remoteResponse = orgIncludeSuppRpcService.findIncludeBySuppId(suppId, null);
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        // 筛选供应商入驻状态
        List<Integer> settledInOrgIds = Optional.ofNullable(remoteResponse.getData()).orElseGet(ArrayList::new)
                .stream().filter(f -> UserIncludeSuppStatusEnum.SETTLED_IN.getValue().equals(f.getStatus())
                        || UserIncludeSuppStatusEnum.IN_BLACK_LIST.getValue().equals(f.getStatus())
                        || UserIncludeSuppStatusEnum.FREEZE.getValue().equals(f.getStatus())
                        || UserIncludeSuppStatusEnum.WITHDRAW_SETTLED_IN.getValue().equals(f.getStatus()))
                .map(NewSuppOrgDTO::getOrgId).collect(Collectors.toList());
        List<Integer> canUseOrgIds = settledInOrgIds;
        if(!UserTagEnum.SUPER_ADMIN.getValue().equals(tag)){
            canUseOrgIds = allOrgIds.stream().filter(settledInOrgIds::contains).collect(Collectors.toList());
        }
        SupplierAuthorityDTO supplierAuthorityDTO = new SupplierAuthorityDTO();
        supplierAuthorityDTO.setGuid(guid);
        supplierAuthorityDTO.setOrgIds(canUseOrgIds);
        supplierAuthorityDTO.setSupplierId(suppId);
        supplierAuthorityDTO.setUserId(sessionInfo.getUserId(RjUserTypeEnum.STORE_USER));
        supplierAuthorityDTO.setUserName(supplier.getRealName());
        supplierAuthorityDTO.setSuperAdmin(UserTagEnum.SUPER_ADMIN.getValue().equals(tag));

        cacheClient.putToCache(suppId + ":" + guid, supplierAuthorityDTO, 1800);
        return supplierAuthorityDTO;
    }

    @Override
    public void delayAccept(DelayAcceptDTO delayAcceptDTO) {
        SupplierAuthorityDTO supplierAuthority = getSupplierAuthority();
        GoodsReturn goodsReturn = goodsReturnMapper.selectByPrimaryKey(delayAcceptDTO.getReturnId());
        BusinessErrUtil.isTrue(goodsReturn.getSupplierId().equals(supplierAuthority.getSupplierId()), ExecptionMessageEnum.NO_DELAY_ACCEPTANCE_OTHER_SUPPLIERS);
        Integer status = goodsReturn.getGoodsReturnStatus();
        boolean canDelay = status.equals(GoodsReturnStatusEnum.AGREE_TO_RETURN.getCode())|| status.equals(GoodsReturnStatusEnum.RETURNED_GOODS.getCode());
        BusinessErrUtil.isTrue(canDelay, ExecptionMessageEnum.AGREE_RETURN_GOODS_DELAY_ACCEPTANCE);
        BusinessErrUtil.isTrue(goodsReturn.getDelayAcceptCount() < 1, ExecptionMessageEnum.NO_REPEAT_DELAYED_ACCEPTANCE);

        //延迟7天
        Date date = goodsReturn.getReplyTime() == null ? new Date() : goodsReturn.getReplyTime();
        date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * 7);
        GoodsReturn updateDo = new GoodsReturn();
        updateDo.setId(goodsReturn.getId());
        updateDo.setReplyTime(date);
        updateDo.setDelayAcceptCount(goodsReturn.getDelayAcceptCount() + 1);
        goodsReturnMapper.updateByPrimaryKeySelective(updateDo);

        //插入操作日志
        commonGoodsReturnService.saveReturnOperationLog(GoodsReturnLogTranslator.buildDO(supplierAuthority.getSupplierId(), supplierAuthority.getUserName(), goodsReturn.getId(), GoodsReturnOperatorTypeEnum.SUPPLIER, GoodsReturnOperationTypeEnum.SUPPLIER_DELAY_ACCEPT, null, null));
    }

    private List<Integer> filterOrgIdByTag(List<Integer> allOrgIds, String tag) {
        if (UserTagEnum.ORG_ADMIN.getValue().equals(tag) || UserTagEnum.SUPER_ADMIN.getValue().equals(tag)) {
            return allOrgIds;
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 更新一物退货状态
     * @param goodsReturn 退货信息
     * @param orderProductTransactionStatusEnum 退货状态
     */
    private void updateBarcodeReturnStatus(GoodsReturn goodsReturn, OrderProductTransactionStatusEnum orderProductTransactionStatusEnum) {
        AsyncExecutor.listenableRunAsync(() -> {
            barCodeGoodsReturnService.updateOrderReturnForBarCode(goodsReturn, orderProductTransactionStatusEnum.getCode());
        }).addFailureCallback(throwable -> {
            LOGGER.error("商家端更新条形码状态失败, 退货单号：" + goodsReturn.getReturnNo() + throwable);
            Cat.logError(CAT_TYPE, "updateOrderReturnForBarCode", "商家端更新条形码状态失败", throwable);
        });
    }

    /**
     * 解绑气瓶
     *
     * @param orderId                  订单id
     * @param orgId                    订单号
     * @param goodsReturnNo            退货单号
     * @param goodsReturnInfoDetailVOS 退货商品
     */
    private void unbindGasBottleForReturn(Integer orderId, Integer orgId, String goodsReturnNo, List<GoodsReturnInfoDetailVO> goodsReturnInfoDetailVOS){
        AsyncExecutor.listenableRunAsync(() -> {
            List<BaseOrderExtraDTO> orderExtraDTOList = orderExtraClient.selectByOrderIdInAndExtraKey(New.list(orderId), OrderExtraEnum.EACH_PRODUCT_EACH_CODE.getValue());
            boolean eachProductEachCode = org.apache.commons.collections.CollectionUtils.isNotEmpty(orderExtraDTOList) && CommonValueUtils.parseNumberStrToBoolean(orderExtraDTOList.get(0).getExtraValue());
            if(!eachProductEachCode){
                List<String> returnGasBottles = goodsReturnInfoDetailVOS.stream()
                        .map(GoodsReturnInfoDetailVO::getReturnGasBottleBarcodes)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream).collect(Collectors.toList());
                gasBottleClient.unbindShopGasBottleByQrCode(goodsReturnNo, orgId, returnGasBottles, ShopGasBottleUnbindEnum.RETURN_GOODS);
            } else {
                List<String> involveBarcodes = goodsReturnInfoDetailVOS.stream()
                        .map(GoodsReturnInfoDetailVO::getGoodsReturnBarcodeDataDTOList)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream)
                        .map(GoodsReturnBarcodeDataDTO::getBarcode).collect(Collectors.toList());
                List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByBarCode(involveBarcodes);
                List<String> returnGasBottles = orderUniqueBarCodeDTOList.stream().map(OrderUniqueBarCodeDTO::getGasBottleBarcode).filter(Objects::nonNull).collect(Collectors.toList());
                gasBottleClient.unbindShopGasBottleByQrCode(goodsReturnNo, orgId, returnGasBottles, ShopGasBottleUnbindEnum.RETURN_GOODS);
            }
        }).addFailureCallback(throwable -> {
            LOGGER.error("解绑气瓶失败：" + goodsReturnNo + throwable);
            Cat.logError(CAT_TYPE, "unbindGasBottleForReturn", "解绑气瓶失败", throwable);
        });
    }

    /**
     * 构造退货单的气瓶数据
     */
    private void constructReturnDetailGasBottle(GoodsReturnInfoVO goodsReturnInfoVO) {
        Integer orderId = goodsReturnInfoVO.getOrderId();
        if (!Boolean.TRUE.equals(goodsReturnInfoVO.getEachProductEachCode())) {
            // 非一物一码，直接获取退货单下绑定的气瓶数据
            List<String> returnGasBottleBarcodes = goodsReturnInfoVO.getReturnInfoDetailList().stream().map(GoodsReturnInfoDetailVO::getReturnGasBottleBarcodes).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(returnGasBottleBarcodes)) {
                return;
            }
            List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(returnGasBottleBarcodes);
            Map<String, GasBottleVO> gasBottleCodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
            for (GoodsReturnInfoDetailVO detailVO : goodsReturnInfoVO.getReturnInfoDetailList()) {
                if (CollectionUtils.isEmpty(detailVO.getReturnGasBottleBarcodes())) {
                    continue;
                }
                detailVO.setReturnGasBottles(detailVO.getReturnGasBottleBarcodes().stream().map(gasBottleCodeIdentityMap::get).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        } else {
            // 一物一码，先获取退货单相关的一物一码信息
            Map<String, List<String>> detailIdBarcodesMap = new HashMap<>(goodsReturnInfoVO.getReturnInfoDetailList().size());
            for (GoodsReturnInfoDetailVO detailVO : goodsReturnInfoVO.getReturnInfoDetailList()) {
                if (CollectionUtils.isNotEmpty(detailVO.getGoodsReturnBarcodeDataDTOList())) {
                    detailIdBarcodesMap.put(detailVO.getDetailId(), detailVO.getGoodsReturnBarcodeDataDTOList().stream().map(GoodsReturnBarcodeDataDTO::getBarcode).collect(Collectors.toList()));
                }
            }
            if (detailIdBarcodesMap.isEmpty()) {
                return;
            }
            List<String> productBarcodes = detailIdBarcodesMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
            List<OrderUniqueBarCodeDTO> orderUniqueBarCodeDTOList = orderUniqueBarCodeRPCClient.findByBarCode(productBarcodes);
            Map<String, String> productBarcodeGasBottleCodeMap = orderUniqueBarCodeDTOList.stream().filter(orderUniqueBarCodeDTO -> StringUtils.isNotEmpty(orderUniqueBarCodeDTO.getGasBottleBarcode())).collect(Collectors.toMap(OrderUniqueBarCodeDTO::getUniBarCode, OrderUniqueBarCodeDTO::getGasBottleBarcode, (o, n) -> n));
            if (productBarcodeGasBottleCodeMap.isEmpty()) {
                return;
            }
            // 获取气瓶数据
            List<GasBottleVO> gasBottleVOList = gasBottleClient.getGasBottleVOByQrCodes(New.list(productBarcodeGasBottleCodeMap.values()));
            Map<String, GasBottleVO> gasBottleCodeIdentityMap = DictionaryUtils.toMap(gasBottleVOList, GasBottleVO::getQrCode, Function.identity());
            // 绑定赋值
            goodsReturnInfoVO.getReturnInfoDetailList().forEach(detail -> {
                List<String> matchProductBarcodes = detailIdBarcodesMap.get(detail.getDetailId());
                if (CollectionUtils.isNotEmpty(matchProductBarcodes)) {
                    detail.setReturnGasBottles(matchProductBarcodes.stream().map(pBarcode -> {
                        String gCode = productBarcodeGasBottleCodeMap.get(pBarcode);
                        if (gCode == null) {
                            return null;
                        }
                        GasBottleVO gasBottleVO = gasBottleCodeIdentityMap.get(gCode);
                        if (gasBottleVO == null) {
                            return null;
                        }
                        return gasBottleVO.setOrderProductBarcode(pBarcode);
                    }).filter(Objects::nonNull).collect(Collectors.toList()));
                }
            });
        }
    }

    private void fillOrderExtraInfo(List<GoodsReturnPageVO> returnPageVOS) {
        if (CollectionUtils.isEmpty(returnPageVOS)) {
            return;
        }
        List<Integer> orderIdList = returnPageVOS.stream().map(GoodsReturnPageVO::getOrderId).collect(Collectors.toList());
        List<OrderExtraDTO> orderExtraDTOS = orderExtraClient.selectByOrderIdAndExtraKey(orderIdList, New.list(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue()));
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = DictionaryUtils.groupBy(orderExtraDTOS, OrderExtraDTO::getOrderId);
        returnPageVOS.forEach(it -> {
            List<OrderExtraDTO> orderExtraDTOList = orderIdExtraListMap.get(it.getOrderId());
            it.setStockWarehouseType(StockTypeEnum.DEFAULT.getValue());
            if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
                Map<Integer, String> extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
                String stockWarehouseType = extraKeyValueMap.getOrDefault(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue(), String.valueOf(StockTypeEnum.DEFAULT.getValue()));
                it.setStockWarehouseType(Integer.valueOf(stockWarehouseType));
            }
        });
    }

    /**
     * 获取订单额外信息
     *
     * @param orderId 退货单ID
     */
    private Map<Integer, String> getOrderExtraMap(Integer orderId) {
        List<OrderExtraDTO> orderExtraDTOS = orderExtraClient.selectByOrderIdAndExtraKey(New.list(orderId), New.list(OrderExtraEnum.STOCK_WAREHOUSE_TYPE.getValue()));
        Map<Integer, List<OrderExtraDTO>> orderIdExtraListMap = com.ruijing.order.utils.DictionaryUtils.groupBy(orderExtraDTOS, OrderExtraDTO::getOrderId);
        List<OrderExtraDTO> orderExtraDTOList = orderIdExtraListMap.get(orderId);
        Map<Integer, String> extraKeyValueMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(orderExtraDTOList)) {
            extraKeyValueMap = DictionaryUtils.toMap(orderExtraDTOList, OrderExtraDTO::getExtraKey, OrderExtraDTO::getExtraValue);
        }
        return MapUtils.isNotEmpty(extraKeyValueMap) ? extraKeyValueMap : Collections.emptyMap();
    }
}
