package com.ruijing.store.goodsreturn.request;

import java.io.Serializable;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-04-18 09:36
 * @description: 一物一码退货数据
 */
public class GoodsReturnBarcodeDataDTO implements Serializable {

    private static final long serialVersionUID = 6829836702379227106L;
    /**
     * 码
     */
    private String barcode;

    /**
     * 退货原因
     */
    private String returnReason;

    /**
     * 退货详情
     */
    private String returnDescription;

    public String getBarcode() {
        return barcode;
    }

    public GoodsReturnBarcodeDataDTO setBarcode(String barcode) {
        this.barcode = barcode;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public GoodsReturnBarcodeDataDTO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getReturnDescription() {
        return returnDescription;
    }

    public GoodsReturnBarcodeDataDTO setReturnDescription(String returnDescription) {
        this.returnDescription = returnDescription;
        return this;
    }

    @Override
    public String toString() {
        return "GoodsReturnBarcodeDTO{" +
                "barcode='" + barcode + '\'' +
                ", returnReason='" + returnReason + '\'' +
                ", returnDescription='" + returnDescription + '\'' +
                '}';
    }
}
