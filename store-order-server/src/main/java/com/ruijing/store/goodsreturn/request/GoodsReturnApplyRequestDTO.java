package com.ruijing.store.goodsreturn.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 退货申请入参DTO
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 15:13
 **/
public class GoodsReturnApplyRequestDTO implements Serializable {

    private static final long serialVersionUID = -6025786442280678353L;

    @RpcModelProperty("退货单id, 首次申请退货可以不传，编辑退货单需要传")
    private Integer id;

    @RpcModelProperty("退货单单号, 前端不需要传")
    private String returnNo;

    /**
     * 外部管理平台退货单号，不用前端传
     */
    private String outerBuyerReturnNo;

    @RpcModelProperty("退货状态, 首次申请退货可以不传，编辑退货单需要传")
    private Integer returnStatus;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("部门/课题组id")
    private Integer departmentId;

    @RpcModelProperty("部门/课题组名称")
    private String departmentName;

    @RpcModelProperty("申请人姓名")
    private String buyerName;

    @RpcModelProperty("供应商id")
    private Integer supplierId;

    @RpcModelProperty("供应商名称")
    private String supplierName;

    @RpcModelProperty("退货商品详情")
    private List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList;

    @RpcModelProperty("退货凭证")
    private List<String> goodsImageList;

    @RpcModelProperty(value = "退货原因", description = "退货单粒度")
    private String returnReason;

    @RpcModelProperty(value = "退货说明", description = "退货单粒度")
    private String remark;

    public Integer getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(Integer returnStatus) {
        this.returnStatus = returnStatus;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<GoodsReturnApplyDetailRequestDTO> getReturnApplyDetailList() {
        return returnApplyDetailList;
    }

    public void setReturnApplyDetailList(List<GoodsReturnApplyDetailRequestDTO> returnApplyDetailList) {
        this.returnApplyDetailList = returnApplyDetailList;
    }

    public List<String> getGoodsImageList() {
        return goodsImageList;
    }

    public void setGoodsImageList(List<String> goodsImageList) {
        this.goodsImageList = goodsImageList;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public String getOuterBuyerReturnNo() {
        return outerBuyerReturnNo;
    }

    public GoodsReturnApplyRequestDTO setOuterBuyerReturnNo(String outerBuyerReturnNo) {
        this.outerBuyerReturnNo = outerBuyerReturnNo;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "GoodsReturnApplyRequestDTO{" +
                "id=" + id +
                ", returnNo='" + returnNo + '\'' +
                ", outerBuyerReturnNo='" + outerBuyerReturnNo + '\'' +
                ", returnStatus=" + returnStatus +
                ", orderNo='" + orderNo + '\'' +
                ", departmentId=" + departmentId +
                ", departmentName='" + departmentName + '\'' +
                ", buyerName='" + buyerName + '\'' +
                ", supplierId=" + supplierId +
                ", supplierName='" + supplierName + '\'' +
                ", returnApplyDetailList=" + returnApplyDetailList +
                ", goodsImageList=" + goodsImageList +
                ", returnReason='" + returnReason + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
