package com.ruijing.store.goodsreturn.service;

import com.ruijing.base.gateway.api.dto.RjSessionInfo;
import com.ruijing.store.goodsreturn.request.GoodsReturnApplyRequestDTO;
import com.ruijing.store.goodsreturn.vo.GoodsReturnApplyResponseVO;
import com.ruijing.store.order.base.core.model.GoodsReturn;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.BarCodeGoodsReturnRequest;
import com.ruijing.store.order.gateway.buyercenter.request.goodsreturn.GoodsReturnBarCodeRequest;
import com.ruijing.store.order.gateway.buyercenter.vo.barcode.BarCodeReturnResultVO;

/**
 * <AUTHOR>
 * @description: 二维码退货Service
 * @date 2021-10-26$ $
 */
public interface BarCodeGoodsReturnService {

    /**
     * 提交退货申请。商品纬度退货，具体退哪个二维码的商品，随机（伪随机）--临时方案
     * @param request       商品&条形码信息
     * @param rjSessionInfo 商品&条形码信息
     * @return              是否成功
     */
    GoodsReturnApplyResponseVO applyGoodsReturnWithRandomBarCode(RjSessionInfo rjSessionInfo, GoodsReturnApplyRequestDTO request);

    /**
     * 提交退货申请
     * @param request       商品&条形码信息
     * @param rjSessionInfo 商品&条形码信息
     * @return              是否成功
     */
    boolean applyOrderReturnForBarCode(RjSessionInfo rjSessionInfo, GoodsReturnBarCodeRequest request);

    /**
     * 扫码提交退货申请, 聚合操作
     * @param request       商品&条形码信息
     * @param rjSessionInfo 商品&条形码信息
     * @return              退货结果，包含成功和失败的信息
     */
    BarCodeReturnResultVO applyOrderReturnByBarCodeAssemble(RjSessionInfo rjSessionInfo, BarCodeGoodsReturnRequest request);

    /**
     * 采购人更新退货单
     * @param request
     * @return
     */
    boolean updateOrderReturnForBarCode(GoodsReturn request, Integer status);

    /**
     * 取消退货后，恢复一物一码状态
     * @param request 退货单
     */
    void resetBarcodeStatusAfterCancelReturn(GoodsReturn request);
}
