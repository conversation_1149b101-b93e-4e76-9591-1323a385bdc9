package com.ruijing.store.statistic.api.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 广告投放订单聚合信息类
 * @date 2023/9/4 14:48
 */
public class AdvertisementOrderAggDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @RpcModelProperty("广告id")
    private Integer advertId;

    @RpcModelProperty("供应商id")
    private Integer suppId;

    @RpcModelProperty("订单日期")
    private String orderDateStr;

    @RpcModelProperty("订单数量")
    private Integer orderCount;

    @RpcModelProperty("买家数量")
    private Integer buyerCount;

    @RpcModelProperty("订单总金额")
    private BigDecimal amountCount;

    public Integer getAdvertId() {
        return advertId;
    }

    public void setAdvertId(Integer advertId) {
        this.advertId = advertId;
    }

    public Integer getSuppId() {
        return suppId;
    }

    public void setSuppId(Integer suppId) {
        this.suppId = suppId;
    }

    public String getOrderDateStr() {
        return orderDateStr;
    }

    public void setOrderDateStr(String orderDateStr) {
        this.orderDateStr = orderDateStr;
    }

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getBuyerCount() {
        return buyerCount;
    }

    public void setBuyerCount(Integer buyerCount) {
        this.buyerCount = buyerCount;
    }

    public BigDecimal getAmountCount() {
        return amountCount;
    }

    public void setAmountCount(BigDecimal amountCount) {
        this.amountCount = amountCount;
    }
}
