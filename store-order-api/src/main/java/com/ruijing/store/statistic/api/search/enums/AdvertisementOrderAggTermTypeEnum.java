package com.ruijing.store.statistic.api.search.enums;

/**
 * <AUTHOR>
 * @description 广告订单聚合分组类型
 * @date 2023/9/11 52
 */
public enum AdvertisementOrderAggTermTypeEnum {

    NONE(0, "不分组"),
    SUPP_ID(1, "供应商"),
    ADVERT_ID(2 , "广告"),
    ORDER_DATE(3, "订单日期，按日分组");

    AdvertisementOrderAggTermTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private final Integer code;
    private final String  description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
