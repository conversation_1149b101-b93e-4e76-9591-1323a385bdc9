package com.ruijing.store.order.api.sysu.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.common.OrderBasicParamDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnDTO;
import com.ruijing.store.order.api.base.other.dto.GoodsReturnLogDTO;

import java.util.List;

/**
 * 中大退货相关的RPC接口
 */
public interface SYSUGoodsReturnRPCService {

    /**
     * 根据订单号生成退货单号RPC接口，入参orderNo必填
     * @param request   订单入参信息
     * @return          退货单信息, 退货单号returnNo
     */
    RemoteResponse<GoodsReturnDTO> generateReturnNo(OrderBasicParamDTO request);

    /**
     * 根据订单id查询退货日志
     * @param request   订单入参信息, orderId必填
     * @return          退货单日志信息
     */
    RemoteResponse<List<GoodsReturnLogDTO>> findByOrderId(OrderBasicParamDTO request);
}
