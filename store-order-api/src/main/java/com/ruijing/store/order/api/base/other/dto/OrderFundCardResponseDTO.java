package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 经费卡列表出参
 * <AUTHOR>
 */
public class OrderFundCardResponseDTO implements Serializable {
    private static final long serialVersionUID = -6877374002874682085L;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 项目负责人
     */
    private String projectManager;

    /**
     * 经费卡id
     */
    private String fundCardId;

    /**
     * 经费卡号
     */
    private String fundCardNo;

    /**
     * 经费卡种类
     */
    private String fundCategory;

    /**
     * 金额
     */
    private BigDecimal referenceAmount;

    /**
     * 是否选中
     */
    private Boolean selected;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(String projectCode) {
        this.projectCode = projectCode;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getFundCardId() {
        return fundCardId;
    }

    public void setFundCardId(String fundCardId) {
        this.fundCardId = fundCardId;
    }

    public String getFundCardNo() {
        return fundCardNo;
    }

    public void setFundCardNo(String fundCardNo) {
        this.fundCardNo = fundCardNo;
    }

    public String getFundCategory() {
        return fundCategory;
    }

    public void setFundCategory(String fundCategory) {
        this.fundCategory = fundCategory;
    }

    public BigDecimal getReferenceAmount() {
        return referenceAmount;
    }

    public void setReferenceAmount(BigDecimal referenceAmount) {
        this.referenceAmount = referenceAmount;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public String getProjectManager() {
        return projectManager;
    }

    public void setProjectManager(String projectManager) {
        this.projectManager = projectManager;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderFundCardResponseDTO{");
        sb.append("projectId='").append(projectId).append('\'');
        sb.append(", projectCode='").append(projectCode).append('\'');
        sb.append(", projectName='").append(projectName).append('\'');
        sb.append(", projectManager='").append(projectManager).append('\'');
        sb.append(", fundCardId='").append(fundCardId).append('\'');
        sb.append(", fundCardNo='").append(fundCardNo).append('\'');
        sb.append(", fundCategory='").append(fundCategory).append('\'');
        sb.append(", referenceAmount=").append(referenceAmount);
        sb.append(", selected=").append(selected);
        sb.append('}');
        return sb.toString();
    }
}
