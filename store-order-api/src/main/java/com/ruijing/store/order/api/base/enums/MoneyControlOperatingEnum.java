package com.ruijing.store.order.api.base.enums;

/**
 * 订单金额管控操作枚举
 */
public enum MoneyControlOperatingEnum {
    CHANGE_CARD_SUCCESS(0, "换卡成功"),
    CHANGE_CARD_FAIL(1, "换卡失败"),
    ;
    MoneyControlOperatingEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String  description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * @description: 通过枚举值获取枚举
     * @date: 2021/3/3 14:46
     * @author: zengyanru
     * @param code
     * @return com.ruijing.store.order.api.base.enums.OrderTypeEnum
     */
    public final static MoneyControlOperatingEnum getByCode(Integer code) {
        for (MoneyControlOperatingEnum orderTypeEnum : MoneyControlOperatingEnum.values()) {
            if (orderTypeEnum.getCode().equals(code)) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}
