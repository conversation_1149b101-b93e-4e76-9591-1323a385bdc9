package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;

/**
 * @description: 订单统计管理结果
 * @author: zhuk
 * @create: 2019-09-10 19:43
 **/
public class StatisticsManagerResultDTO implements Serializable {

    private static final long serialVersionUID = 3725866322460678191L;

    /**
     * 订单金额
     */
    private Double originalAmount;

    /**
     * 课题组数量
     */
    private Double departmentQuantity;

    /**
     * 供应商数量
     */
    private Double supplierQuantity;

    /**
     * 订单数量
     */
    private Long orderQuantity;

    public Double getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(Double originalAmount) {
        this.originalAmount = originalAmount;
    }

    public Double getDepartmentQuantity() {
        return departmentQuantity;
    }

    public void setDepartmentQuantity(Double departmentQuantity) {
        this.departmentQuantity = departmentQuantity;
    }

    public Double getSupplierQuantity() {
        return supplierQuantity;
    }

    public void setSupplierQuantity(Double supplierQuantity) {
        this.supplierQuantity = supplierQuantity;
    }

    public Long getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(Long orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    @Override
    public String toString() {
        return "StatisticsManagerResultDTO{" +
                "originalAmount=" + originalAmount +
                ", departmentQuantity=" + departmentQuantity +
                ", supplierQuantity=" + supplierQuantity +
                ", orderQuantity=" + orderQuantity +
                '}';
    }
}
