package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;

/**
 * @author: zhukai
 * @date : 2020/2/19 11:10 上午
 * @description: 搜索 字段范围查询DTO
 */
public class FieldRangeDTO implements Serializable {

    /**
     * 范围查询 字段名称
     */
    private String field;

    /**
     * 下限
     */
    private String lower;

    /**
     * 上限
     */
    private String upper;

    /**
     * 是否包含下限 默认包含
     */
    private boolean includeLower = true;

    /**
     * 是否包含上限 默认不包含
     */
    private boolean includeUpper = false;

    public FieldRangeDTO() {
        super();
    }

    public FieldRangeDTO(String field, String lower, String upper) {
        this.field = field;
        this.lower = lower;
        this.upper = upper;
    }

    public FieldRangeDTO(String field, String lower, String upper, boolean includeLower, boolean includeUpper) {
        this.field = field;
        this.lower = lower;
        this.upper = upper;
        this.includeLower = includeLower;
        this.includeUpper = includeUpper;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getLower() {
        return lower;
    }

    public void setLower(String lower) {
        this.lower = lower;
    }

    public String getUpper() {
        return upper;
    }

    public void setUpper(String upper) {
        this.upper = upper;
    }

    public boolean getIncludeLower() {
        return includeLower;
    }

    public void setIncludeLower(boolean includeLower) {
        this.includeLower = includeLower;
    }

    public boolean getIncludeUpper() {
        return includeUpper;
    }

    public void setIncludeUpper(boolean includeUpper) {
        this.includeUpper = includeUpper;
    }

    @Override
    public String toString() {
        return "FieldRangeDTO{" +
                "field='" + field + '\'' +
                ", lower='" + lower + '\'' +
                ", upper='" + upper + '\'' +
                ", includeLower=" + includeLower +
                ", includeUpper=" + includeUpper +
                '}';
    }
}
