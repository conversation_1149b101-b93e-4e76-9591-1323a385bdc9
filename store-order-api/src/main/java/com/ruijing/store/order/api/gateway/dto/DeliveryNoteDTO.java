package com.ruijing.store.order.api.gateway.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.store.order.api.base.other.dto.OrderPurchaseApprovalLogPrintDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: zhu<PERSON>
 * @date : 2020/9/2 4:27 下午
 * @description: 送货单dto
 */
public class DeliveryNoteDTO implements Serializable {

    private static final long serialVersionUID = -8427558897261049058L;

    @RpcModelProperty("订单号")
    private String orderNo;

    @RpcModelProperty("订单时间")
    private Date orderDate;

    @RpcModelProperty("收货人名称")
    private String receiverName;

    @RpcModelProperty("收货人手机号")
    private String receiverPhone;

    @RpcModelProperty("收货地址")
    private String receiverAddress;

    @RpcModelProperty("采购单位名称")
    private String orgName;

    @RpcModelProperty("课题组名称")
    private String departmentName;

    @RpcModelProperty("采购人名称")
    private String buyerName;

    @RpcModelProperty("供应商名称")
    private String suppName;

    @RpcModelProperty("供应商用户信息列表")
    private List<SupplierUserDTO> suppUsers;

    @RpcModelProperty("商品信息列表")
    private List<DeliveryNoteProductDTO> productsInfoList;

    @RpcModelProperty("二维码")
    private String orderNoQrCode;

    @RpcModelProperty("合计数量")
    private String sumQuantity;

    @RpcModelProperty("合计金额")
    private String sumAmount;

    @RpcModelProperty("经费对接方式")
    private Integer fundStatus;

    @RpcModelProperty("线下采购渠道")
    private String procurementChannel;

    @RpcModelProperty("发票集合")
    private List<InvoicePrintDTO> invoicePrintList;

    /**
     * 订单审批日志打印模型数组
     */
    @RpcModelProperty("订单审批日志打印模型数组")
    private List<OrderPurchaseApprovalLogPrintDTO> orderPurchaseApprovalLogPrintList;

    @RpcModelProperty("买家留言")
    private String orderRemark;

    @RpcModelProperty("旧单标识")
    private Boolean oldFlag;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public List<SupplierUserDTO> getSuppUsers() {
        return suppUsers;
    }

    public void setSuppUsers(List<SupplierUserDTO> suppUsers) {
        this.suppUsers = suppUsers;
    }

    public List<DeliveryNoteProductDTO> getProductsInfoList() {
        return productsInfoList;
    }

    public void setProductsInfoList(List<DeliveryNoteProductDTO> productsInfoList) {
        this.productsInfoList = productsInfoList;
    }

    public String getOrderNoQrCode() {
        return orderNoQrCode;
    }

    public void setOrderNoQrCode(String orderNoQrCode) {
        this.orderNoQrCode = orderNoQrCode;
    }

    public String getSumQuantity() {
        return sumQuantity;
    }

    public void setSumQuantity(String sumQuantity) {
        this.sumQuantity = sumQuantity;
    }

    public String getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(String sumAmount) {
        this.sumAmount = sumAmount;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public String getProcurementChannel() {
        return procurementChannel;
    }

    public void setProcurementChannel(String procurementChannel) {
        this.procurementChannel = procurementChannel;
    }

    public List<InvoicePrintDTO> getInvoicePrintList() {
        return invoicePrintList;
    }

    public void setInvoicePrintList(List<InvoicePrintDTO> invoicePrintList) {
        this.invoicePrintList = invoicePrintList;
    }

    public List<OrderPurchaseApprovalLogPrintDTO> getOrderPurchaseApprovalLogPrintList() {
        return orderPurchaseApprovalLogPrintList;
    }

    public void setOrderPurchaseApprovalLogPrintList(List<OrderPurchaseApprovalLogPrintDTO> orderPurchaseApprovalLogPrintList) {
        this.orderPurchaseApprovalLogPrintList = orderPurchaseApprovalLogPrintList;
    }

    public String getOrderRemark() {
        return orderRemark;
    }

    public void setOrderRemark(String orderRemark) {
        this.orderRemark = orderRemark;
    }

    public Boolean getOldFlag() {
        return oldFlag;
    }

    public DeliveryNoteDTO setOldFlag(Boolean oldFlag) {
        this.oldFlag = oldFlag;
        return this;
    }
}
