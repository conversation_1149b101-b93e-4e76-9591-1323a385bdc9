package com.ruijing.store.order.api.base.refinvoiceorder.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/14 14:35
 * @description 发票上传数据，用于上传到第三方
 */
public class InvoiceUploadDTO implements Serializable {

    private static final long serialVersionUID = 7607691058158355749L;
    
    @RpcModelProperty("发票号")
    private String invoiceNumber;
    
    @RpcModelProperty("发票金额")
    private BigDecimal invoiceAmount;
    
    @RpcModelProperty("开票单位")
    private String drawer;
    
    @RpcModelProperty("银行名称")
    private String bankBranchName;
    
    @RpcModelProperty("银行账号")
    private String bankNumber;
    
    @RpcModelProperty("发票url")
    private List<String> urls;
    
    @RpcModelProperty("供应商-省份")
    private String provinceName;
    
    @RpcModelProperty("供应商-城市")
    private String cityName;

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getDrawer() {
        return drawer;
    }

    public void setDrawer(String drawer) {
        this.drawer = drawer;
    }

    public String getBankBranchName() {
        return bankBranchName;
    }

    public void setBankBranchName(String bankBranchName) {
        this.bankBranchName = bankBranchName;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("InvoiceUploadDTO{");
        sb.append("invoiceNumber='").append(invoiceNumber).append('\'');
        sb.append(", invoiceAmount=").append(invoiceAmount);
        sb.append(", drawer='").append(drawer).append('\'');
        sb.append(", bankBranchName='").append(bankBranchName).append('\'');
        sb.append(", bankNumber='").append(bankNumber).append('\'');
        sb.append(", urls=").append(urls);
        sb.append(", provinceName='").append(provinceName).append('\'');
        sb.append(", cityName='").append(cityName).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
