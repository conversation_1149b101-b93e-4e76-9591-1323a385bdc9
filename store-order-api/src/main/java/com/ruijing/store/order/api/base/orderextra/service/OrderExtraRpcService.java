package com.ruijing.store.order.api.base.orderextra.service;

import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.orderextra.dto.OrderExtraDTO;

import java.util.Collection;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>
 * @Description: 提供对外查询订单额外数据的方法
 * @DateTime: 2021/6/29 9:33
 */
public interface OrderExtraRpcService {

    /**
     * 通过订单id列表和拓展表操作类型id查找订单额外数据的数组
     * @param orderIdCollection 订单id的集合
     * @param extraKey {@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum} 这个是订单额外字段的枚举，传入值取 OrderExtraEnum.getValue()
     * @return List<OrderExtraDTO> extraKey为额外字段的类型，extraValue是额外字段的对应值，当查询不出来时，会返回空数组
     */
    RemoteResponse<List<OrderExtraDTO>> selectByOrderIdInAndExtraValue(Collection<Integer> orderIdCollection, Integer extraKey);

    /**
     * 通过订单id列表查找订单额外数据的数组
     * @param orderIdCollection 订单id的集合
     * @return List<OrderExtraDTO> extraKey为额外字段的类型，extraValue是额外字段的对应值，当查询不出来时，会返回空数组
     * extraKey:{@link com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum} 为 OrderExtraEnum.getValue()
     */
    RemoteResponse<List<OrderExtraDTO>> selectByOrderIdIn(Collection<Integer> orderIdCollection);

    RemoteResponse<Boolean> addOrderExtra(Collection<OrderExtraDTO> extraDTOS);
}
