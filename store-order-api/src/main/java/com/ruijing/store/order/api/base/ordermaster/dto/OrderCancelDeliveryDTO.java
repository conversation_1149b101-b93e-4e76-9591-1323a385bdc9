package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.fundamental.api.annotation.Model;
import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;

@Model("供应商取消发货入参")
public class OrderCancelDeliveryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单Id")
    private Integer orderId;

    @ModelProperty("取消发货原因")
    private String cancelReason;

    @ModelProperty("取消发货操作人Id")
    private Integer cancelManId;

    public Integer getOrderId() {
        return orderId;
    }

    public OrderCancelDeliveryDTO setOrderId(Integer orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public OrderCancelDeliveryDTO setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
        return this;
    }

    public Integer getCancelManId() {
        return cancelManId;
    }

    public OrderCancelDeliveryDTO setCancelManId(Integer cancelManId) {
        this.cancelManId = cancelManId;
        return this;
    }

    @Override
    public String toString() {
        return "OrderCancelDeliveryDTO{" +
                "orderId=" + orderId +
                ", cancelReason='" + cancelReason + '\'' +
                ", cancelManId=" + cancelManId +
                '}';
    }
}