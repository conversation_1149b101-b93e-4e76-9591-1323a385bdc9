package com.ruijing.store.order.api.base.docking.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class ThirdPartQueryOrderDetailResponseDTO implements Serializable {

    private static final long serialVersionUID = -7385201631606193615L;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("货号")
    private String goodsCode;

    @RpcModelProperty("单位")
    private String unit;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("单价")
    private BigDecimal price;

    @RpcModelProperty("数量")
    private BigDecimal quantity;

    @RpcModelProperty("一级分类id")
    private String firstGoodsCategoryId;

    @RpcModelProperty("一级分类名称")
    private String firstGoodsCategoryName;

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getFirstGoodsCategoryId() {
        return firstGoodsCategoryId;
    }

    public void setFirstGoodsCategoryId(String firstGoodsCategoryId) {
        this.firstGoodsCategoryId = firstGoodsCategoryId;
    }

    public String getFirstGoodsCategoryName() {
        return firstGoodsCategoryName;
    }

    public void setFirstGoodsCategoryName(String firstGoodsCategoryName) {
        this.firstGoodsCategoryName = firstGoodsCategoryName;
    }
}
