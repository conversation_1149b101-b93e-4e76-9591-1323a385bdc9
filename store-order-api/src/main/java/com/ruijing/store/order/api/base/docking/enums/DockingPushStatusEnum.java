package com.ruijing.store.order.api.base.docking.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 对接订单的推送状态
 */
public enum DockingPushStatusEnum {
    SUCCESS(0, "推送成功"),
    PROCESSING(-1, "推送中"),
    FAILED(-2, "推送失败"),
    ARGUMENT_EXCEPTION(-3, "推送失败（调用TPI失败）"),
    ;

    private static Map<Integer, DockingPushStatusEnum> enumMap;

    static {
        DockingPushStatusEnum[] values = DockingPushStatusEnum.values();
        if (values.length < 1) {
            throw new IllegalArgumentException("the DockingPushStatusEnum instance is 0");
        }
        enumMap = new HashMap<>(values.length);
        for (DockingPushStatusEnum it : values) {
            enumMap.put(it.getCode(), it);
        }
    }

    DockingPushStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private Integer code;
    private String description;

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 通过code获取映射枚举值
     * @param code
     * @return  enum instance
     */
    public static DockingPushStatusEnum getByCode(Integer code) {
        DockingPushStatusEnum instance = enumMap.get(code);
        if (instance == null) {
            throw new IllegalStateException("unknown DockingPushStatus, current code:" + code);
        }
        return instance;
    }
}
