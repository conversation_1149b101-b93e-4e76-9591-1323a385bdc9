package com.ruijing.store.order.api.base.common;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 订单打印单据通用入参
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/9/16 15:26
 **/
public class OrderCommonPrintParamDTO implements Serializable {

    private static final long serialVersionUID = 6345543058515935763L;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id数组")
    private List<Integer> orderIdList;

    /**
     * 额外订单打印所需的数据类型
     */
    @RpcModelProperty("额外打印所需的单据类型，2-入库单，3-出库单，4-退货单，5-发票相关信息,6-送货单相关信息")
    private List<Integer> printDataTypeList;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<Integer> getPrintDataTypeList() {
        return printDataTypeList;
    }

    public void setPrintDataTypeList(List<Integer> printDataTypeList) {
        this.printDataTypeList = printDataTypeList;
    }
}

