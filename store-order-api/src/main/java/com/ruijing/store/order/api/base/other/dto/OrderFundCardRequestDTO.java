package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.store.order.api.base.common.BasePageParamDTO;

import java.io.Serializable;

/**
 * 通过pi工号查询经费卡入参
 * <AUTHOR>
 */
public class OrderFundCardRequestDTO extends BasePageParamDTO implements Serializable {
    private static final long serialVersionUID = -7864505427501319639L;

    private String orgCode;

    private Integer orgId;
    /**
     * 订单id
     */
    private Integer orderId;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderFundCardRequestDTO{");
        sb.append("orgCode='").append(orgCode).append('\'');
        sb.append(", orgId=").append(orgId);
        sb.append(", orderId=").append(orderId);
        sb.append('}');
        return sb.toString();
    }
}
