package com.ruijing.store.order.api.base.docking.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 第三方单位管理平台更新商城订单出参
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/3/23 16:16
 **/
public class ThirdPartUpdateResponseDTO<T> implements Serializable {

    private static final long serialVersionUID = 4964301173865950026L;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 订单更新出参
     */
    private List<T> thirdPartUpdateOrderResponseDTOList;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<T> getThirdPartUpdateOrderResponseDTOList() {
        return thirdPartUpdateOrderResponseDTOList;
    }

    public void setThirdPartUpdateOrderResponseDTOList(List<T> thirdPartUpdateOrderResponseDTOList) {
        this.thirdPartUpdateOrderResponseDTOList = thirdPartUpdateOrderResponseDTOList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("ThirdPartUpdateResponseDTO{");
        sb.append("orgCode='").append(orgCode).append('\'');
        sb.append(", thirdPartUpdateOrderResponseDTOList=").append(thirdPartUpdateOrderResponseDTOList);
        sb.append('}');
        return sb.toString();
    }
}
