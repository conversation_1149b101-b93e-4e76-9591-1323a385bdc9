package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 订单通用入参DTO
 * @author: zhuk
 * @create: 2019-07-04 14:23
 **/
public class OrderMasterCommonReqDTO implements Serializable {
    private static final long serialVersionUID = 8922844642520374472L;

    /**
     * orderMasterid list
     */
    @RpcModelProperty("订单id数组")
    private List<Integer> orderMasterIds;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号")
    private String orderMasterNo;

    /**
     * 订单号
     */
    @RpcModelProperty("订单号数组")
    private List<String> orderMasterNoList;

    /**
     * orderMasterId
     */
    @RpcModelProperty("订单id")
    private Integer orderMasterId;

    /**
     * 订单状态
     * @return
     */
    @RpcModelProperty("订单状态")
    private Integer orderStatus;

    /**
     *截止  取消订单时间
     * @return
     */
    @RpcModelProperty("取消订单时间")
    private LocalDate deadFcanceldate;

    /**
     * 截止 订单生成时间
     */
    @RpcModelProperty("订单生成时间")
    private LocalDate deadForderdate;

    /**
     * 精确订单时间 精确到天
     */
    @RpcModelProperty("精确订单时间 精确到天")
    private LocalDate equalForderdate;

    /**
     * 精确订单取消时间  精确到天
     */
    @RpcModelProperty("精确订单取消时间  精确到天")
    private LocalDate equalFcanceldate;

    /**
     * 需要被排除的org
     */
    @RpcModelProperty("需要被排除的org")
    private List<Integer> excludeOrgIds;

    /**
     * 组织id 医院id
     */
    @RpcModelProperty("组织id 医院id")
    private Integer orgId;

    /**
     * 返回条数
     */
    @RpcModelProperty("返回条数")
    private Integer limit;

    /**
     * 采购单id
     */
    @RpcModelProperty("采购单id")
    private List<Integer> applicationIdList;

    /**
     * 根据条件查询的左区间
     */
    @RpcModelProperty("根据条件查询的左区间")
    private Integer leftInterval;

    /**
     * 根据条件查询的右区间
     */
    @RpcModelProperty("根据条件查询的右区间")
    private Integer rightInterval;

    /**
     * 订单更新时间
     */
    @RpcModelProperty("订单更新时间")
    private Date updateTime;

    public List<String> getOrderMasterNoList() {
        return orderMasterNoList;
    }

    public void setOrderMasterNoList(List<String> orderMasterNoList) {
        this.orderMasterNoList = orderMasterNoList;
    }

    public List<Integer> getExcludeOrgIds() {
        return excludeOrgIds;
    }

    public void setExcludeOrgIds(List<Integer> excludeOrgIds) {
        this.excludeOrgIds = excludeOrgIds;
    }

    public List<Integer> getOrderMasterIds() {
        return orderMasterIds;
    }

    public void setOrderMasterIds(List<Integer> orderMasterIds) {
        this.orderMasterIds = orderMasterIds;
    }

    public Integer getOrderMasterId() {
        return orderMasterId;
    }

    public void setOrderMasterId(Integer orderMasterId) {
        this.orderMasterId = orderMasterId;
    }

    public String getOrderMasterNo() {
        return orderMasterNo;
    }

    public void setOrderMasterNo(String orderMasterNo) {
        this.orderMasterNo = orderMasterNo;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public LocalDate getEqualForderdate() {
        return equalForderdate;
    }

    public void setEqualForderdate(LocalDate equalForderdate) {
        this.equalForderdate = equalForderdate;
    }

    public LocalDate getEqualFcanceldate() {
        return equalFcanceldate;
    }

    public void setEqualFcanceldate(LocalDate equalFcanceldate) {
        this.equalFcanceldate = equalFcanceldate;
    }

    public LocalDate getDeadFcanceldate() {
        return deadFcanceldate;
    }

    public void setDeadFcanceldate(LocalDate deadFcanceldate) {
        this.deadFcanceldate = deadFcanceldate;
    }

    public LocalDate getDeadForderdate() {
        return deadForderdate;
    }

    public void setDeadForderdate(LocalDate deadForderdate) {
        this.deadForderdate = deadForderdate;
    }

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<Integer> getApplicationIdList() {
        return applicationIdList;
    }

    public void setApplicationIdList(List<Integer> applicationIdList) {
        this.applicationIdList = applicationIdList;
    }

    public Integer getLeftInterval() {
        return leftInterval;
    }

    public void setLeftInterval(Integer leftInterval) {
        this.leftInterval = leftInterval;
    }

    public Integer getRightInterval() {
        return rightInterval;
    }

    public void setRightInterval(Integer rightInterval) {
        this.rightInterval = rightInterval;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    @Override
    public String toString() {
        return new StringJoiner(", ", OrderMasterCommonReqDTO.class.getSimpleName() + "[", "]")
                .add("orderMasterIds=" + orderMasterIds)
                .add("orderMasterNo='" + orderMasterNo + "'")
                .add("orderMasterNoList=" + orderMasterNoList)
                .add("orderMasterId=" + orderMasterId)
                .add("orderStatus=" + orderStatus)
                .add("deadFcanceldate=" + deadFcanceldate)
                .add("deadForderdate=" + deadForderdate)
                .add("equalForderdate=" + equalForderdate)
                .add("equalFcanceldate=" + equalFcanceldate)
                .add("excludeOrgIds=" + excludeOrgIds)
                .add("orgId=" + orgId)
                .add("limit=" + limit)
                .add("applicationIdList=" + applicationIdList)
                .add("leftInterval=" + leftInterval)
                .add("rightInterval=" + rightInterval)
                .add("updateTime=" + updateTime)
                .toString();
    }
}