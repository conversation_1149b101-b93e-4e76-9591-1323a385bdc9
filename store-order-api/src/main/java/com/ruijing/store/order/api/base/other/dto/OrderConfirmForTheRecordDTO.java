package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/5/7 0007 15:44
 * @Version 1.0
 * @Desc:描述
 */
/**
 * 备案信息DTO
 */
public class OrderConfirmForTheRecordDTO implements Serializable {


    private static final long serialVersionUID = 4668500702044872050L;

    /**
     * id
     */
    private String id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 备案图片
     */
    private String pics;

    /**
     * 追加备案图片
     */
    private String addPics;

    /**
     * 是否已备案0未1已
     */
    private Boolean isConfirm;

    /**
     * 备案类型1易制毒2易制爆
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Date creationTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否被删除
     */
    private Boolean isDeleted;

    /**
     * 逻辑删除时间
     */
    private Date deletionTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getPics() {
        return pics;
    }

    public void setPics(String pics) {
        this.pics = pics;
    }

    public String getAddPics() {
        return addPics;
    }

    public void setAddPics(String addPics) {
        this.addPics = addPics;
    }

    public Boolean getConfirm() {
        return isConfirm;
    }

    public void setConfirm(Boolean confirm) {
        isConfirm = confirm;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderConfirmForTheRecordDTO{");
        sb.append("id='").append(id).append('\'');
        sb.append(", orderId=").append(orderId);
        sb.append(", pics='").append(pics).append('\'');
        sb.append(", addPics='").append(addPics).append('\'');
        sb.append(", isConfirm=").append(isConfirm);
        sb.append(", type=").append(type);
        sb.append(", creationTime=").append(creationTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", deletionTime=").append(deletionTime);
        sb.append('}');
        return sb.toString();
    }
}
