package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @title: PrintApproveDTO
 * @projectName research-statement-web
 * @description: 验收单打印基本信息
 * @author：zhongyu<PERSON>i
 * @date 2019-12-06 11:35
 */
public class PrintApproveDTO implements Serializable {


    private static final long serialVersionUID = -5407162653636097995L;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     *  医院名称
     */
    private String hospitalName;

    /**
     * 课题组名称
     */
    private String departmentName;

    /**
     * 课题组负责人名称
     */
    private String deptManagerName;

    /**
     * 条形码
     */
    private String baseImg;

    /**
     * 汇总单号
     */
    private String summaryNo;

    /**
     * 供应商名称
     */
    private String suppName;

    /**
     * 供应商编码
     */
    private String suppCode;

    /**
     *  申购人
     */
    private String buyerName;

    /**
     * 联系方式
     */
    private String telPhone;

    /**
     * 结算日期
     */
    private String balanceDate;

    /**
     * 项目名称
     */
    private Set<String> projectName;

    /**
     * 经费卡信息
     */
    private List<PrintFundCardDTO> fundCardDtoList;

    /**
     * 项目编号
     */
    private Set<String> projectCode;

    /**
     * 发票数组
     */
    private Set<String> invoiceNoSet;

    /**
     * 订单详情
     */
    private List<PrintOrderDTO> orderDtoList;

    /**
     * 总数目
     */
    private Integer totalQuantity;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 是否存在验收图片
     */
    private Boolean existReceivePhoto;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getBaseImg() {
        return baseImg;
    }

    public void setBaseImg(String baseImg) {
        this.baseImg = baseImg;
    }

    public String getSummaryNo() {
        return summaryNo;
    }

    public void setSummaryNo(String summaryNo) {
        this.summaryNo = summaryNo;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getTelPhone() {
        return telPhone;
    }

    public void setTelPhone(String telPhone) {
        this.telPhone = telPhone;
    }

    public String getBalanceDate() {
        return balanceDate;
    }

    public void setBalanceDate(String balanceDate) {
        this.balanceDate = balanceDate;
    }

    public List<PrintFundCardDTO> getFundCardDtoList() {
        return fundCardDtoList;
    }

    public void setFundCardDtoList(List<PrintFundCardDTO> fundCardDtoList) {
        this.fundCardDtoList = fundCardDtoList;
    }

    public Set<String> getProjectName() {
        return projectName;
    }

    public void setProjectName(Set<String> projectName) {
        this.projectName = projectName;
    }

    public Set<String> getProjectCode() {
        return projectCode;
    }

    public void setProjectCode(Set<String> projectCode) {
        this.projectCode = projectCode;
    }

    public Set<String> getInvoiceNoSet() {
        return invoiceNoSet;
    }

    public void setInvoiceNoSet(Set<String> invoiceNoSet) {
        this.invoiceNoSet = invoiceNoSet;
    }

    public List<PrintOrderDTO> getOrderDtoList() {
        return orderDtoList;
    }

    public void setOrderDtoList(List<PrintOrderDTO> orderDtoList) {
        this.orderDtoList = orderDtoList;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getDeptManagerName() {
        return deptManagerName;
    }

    public PrintApproveDTO setDeptManagerName(String deptManagerName) {
        this.deptManagerName = deptManagerName;
        return this;
    }

    public Boolean getExistReceivePhoto() {
        return existReceivePhoto;
    }

    public void setExistReceivePhoto(Boolean existReceivePhoto) {
        this.existReceivePhoto = existReceivePhoto;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PrintApproveDTO{");
        sb.append("orderId=").append(orderId);
        sb.append(", hospitalName='").append(hospitalName).append('\'');
        sb.append(", departmentName='").append(departmentName).append('\'');
        sb.append(", deptManagerName='").append(deptManagerName).append('\'');
        sb.append(", baseImg='").append(baseImg).append('\'');
        sb.append(", summaryNo='").append(summaryNo).append('\'');
        sb.append(", suppName='").append(suppName).append('\'');
        sb.append(", suppCode='").append(suppCode).append('\'');
        sb.append(", buyerName='").append(buyerName).append('\'');
        sb.append(", telPhone='").append(telPhone).append('\'');
        sb.append(", balanceDate='").append(balanceDate).append('\'');
        sb.append(", projectName=").append(projectName);
        sb.append(", fundCardDtoList=").append(fundCardDtoList);
        sb.append(", projectCode=").append(projectCode);
        sb.append(", invoiceNoSet=").append(invoiceNoSet);
        sb.append(", orderDtoList=").append(orderDtoList);
        sb.append(", totalQuantity=").append(totalQuantity);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", existReceivePhoto=").append(existReceivePhoto);
        sb.append('}');
        return sb.toString();
    }
}