package com.ruijing.store.order.api.search.enums;

/**
 * @author: zhu<PERSON>
 * @date : 2020/2/17 2:05 下午
 * @description: 时间单位枚举
 */
public enum DateUnitEnum {

    /**
     * 年
     */
    YEAR("y","年"),

    /**
     * 季度
     */
    QUARTER("q","季度"),

    /**
     * 月
     */
    MONTH("month","月"),

    /**
     * 周
     */
    WEEK("w","周"),

    /**
     * 日
     */
    DAY("d","日"),

    /**
     * 小时
     */
    HOUR("h","小时");


    DateUnitEnum(String symbol, String name) {
        this.symbol = symbol;
        this.name = name;
    }

    private String symbol;

    private String name;

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
