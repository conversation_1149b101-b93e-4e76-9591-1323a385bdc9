package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-11-25 10:40
 */
public enum  DeliveryStatusEnum {

    /**
     * 初始状态
     */
    INIT(-1, "生成代配送订单"),

    //--待收货时用到
    /**
     * 待分拣
     */
    SORT(0, "待分拣"),

    /**
     * 待配送
     */
    DELIVER(1, "待配送"),

    /**
     * 完成配送
     */
    DELIVERED(2, "完成配送"),

    //--待发货时用到
    /**
     * 供应商申请取消代配送
     */
    SUPP_APPLY_CANCEL(3, "供应商申请取消代配送"),

    /**
     * 采购人拒绝取消代配送
     */
    BUYER_REJECT_CANCEL(4, "采购人拒绝取消代配送"),

    /**
     * 已关闭
     */
    SHUT_DOWN(5, "已关闭")
    ;
    /**
     * 编码
     */
    private final Integer value;

    /**
     * 描述
     */
    private final String description;

    DeliveryStatusEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * @description: 通过枚举值获取枚举
     * @param value  枚举编码
     * @return      枚举描述
     */
    public static String getDescriptionByValue(Integer value) {
        for (DeliveryStatusEnum deliveryStatusEnum : DeliveryStatusEnum.values()) {
            if (deliveryStatusEnum.getValue().equals(value)) {
                return deliveryStatusEnum.getDescription();
            }
        }
        return "";
    }

    public static DeliveryStatusEnum getByValue(Integer value) {
        for (DeliveryStatusEnum deliveryStatusEnum : DeliveryStatusEnum.values()) {
            if (deliveryStatusEnum.getValue().equals(value)) {
                return deliveryStatusEnum;
            }
        }
        return null;
    }
}
