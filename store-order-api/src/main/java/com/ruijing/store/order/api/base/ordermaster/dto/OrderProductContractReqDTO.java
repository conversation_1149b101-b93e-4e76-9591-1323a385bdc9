package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021/06/28 11:10
 */
public class OrderProductContractReqDTO  implements Serializable {

    private static final long serialVersionUID = -2982414982505438591L;

    @RpcModelProperty("订单id List")
    private List<Integer> orderIdList;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }
}
