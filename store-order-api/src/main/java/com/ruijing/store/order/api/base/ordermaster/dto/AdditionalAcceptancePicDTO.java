package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenhaibo
 * @Date:2020/3/26 0026 10:35
 * @Version:
 * @Desc:追加验收图片DTO
 */
public class AdditionalAcceptancePicDTO implements Serializable {

    private static final long serialVersionUID = -4579210082612469699L;

    /**
     * 订单Id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 验收图片
     */
    private String receivePicUrls;

    /**
     * 用户Id
     * @return
     */
    private Integer userId;

    /**
     * 新增验收图片集合
     */
    private List<String> listReceivePicUrl;

    /**
     * 跳过日志记录
     */
    private Boolean skipLog;

    public Boolean getSkipLog() {
        return skipLog;
    }

    public AdditionalAcceptancePicDTO setSkipLog(Boolean skipLog) {
        this.skipLog = skipLog;
        return this;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReceivePicUrls() {
        return receivePicUrls;
    }

    public void setReceivePicUrls(String receivePicUrls) {
        this.receivePicUrls = receivePicUrls;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public List<String> getListReceivePicUrl() {
        return listReceivePicUrl;
    }

    public void setListReceivePicUrl(List<String> listReceivePicUrl) {
        this.listReceivePicUrl = listReceivePicUrl;
    }

    @Override
    public String toString() {
        return "AdditionalAcceptancePicDTO{" +
                "orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", receivePicUrls='" + receivePicUrls + '\'' +
                ", userId=" + userId +
                ", listReceivePicUrl=" + listReceivePicUrl +
                ", skipLog=" + skipLog +
                '}';
    }
}
