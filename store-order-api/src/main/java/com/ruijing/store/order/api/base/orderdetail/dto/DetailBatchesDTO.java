package com.ruijing.store.order.api.base.orderdetail.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.StringJoiner;

public class DetailBatchesDTO implements Serializable {

    private static final long serialVersionUID = -8791905280042430415L;

    /**
     * 订单明细id
     */
    @RpcModelProperty("订单明细id")
    private Integer detailId;

    /**
     * 批次号
     */
    @RpcModelProperty("批次号")
    private String batch;

    /**
     * 有效期
     */
    @RpcModelProperty("有效期")
    private String expiration;

    @RpcModelProperty("生厂厂家")
    private String manufacturer;

    @RpcModelProperty("生产日期")
    private String productionDate;

    /**
     * 外观, 耐久度
     */
    @RpcModelProperty("外观, 耐久度 0正常1破损")
    private Integer exterior;

    @RpcModelProperty("绑定的气瓶码")
    private String gasBottleBarcode;

    /**
     * 条形码
     */
    @RpcModelProperty("条形码")
    private String uniqueBarCode;

    @RpcModelProperty("条形码类型")
    private Integer type;

    public Integer getDetailId() {
        return detailId;
    }

    public DetailBatchesDTO setDetailId(Integer detailId) {
        this.detailId = detailId;
        return this;
    }

    public String getBatch() {
        return batch;
    }

    public DetailBatchesDTO setBatch(String batch) {
        this.batch = batch;
        return this;
    }

    public String getExpiration() {
        return expiration;
    }

    public DetailBatchesDTO setExpiration(String expiration) {
        this.expiration = expiration;
        return this;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public DetailBatchesDTO setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public DetailBatchesDTO setProductionDate(String productionDate) {
        this.productionDate = productionDate;
        return this;
    }

    public Integer getExterior() {
        return exterior;
    }

    public DetailBatchesDTO setExterior(Integer exterior) {
        this.exterior = exterior;
        return this;
    }

    public String getUniqueBarCode() {
        return uniqueBarCode;
    }

    public DetailBatchesDTO setUniqueBarCode(String uniqueBarCode) {
        this.uniqueBarCode = uniqueBarCode;
        return this;
    }

    public Integer getType() {
        return type;
    }

    public DetailBatchesDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public String getGasBottleBarcode() {
        return gasBottleBarcode;
    }

    public DetailBatchesDTO setGasBottleBarcode(String gasBottleBarcode) {
        this.gasBottleBarcode = gasBottleBarcode;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", DetailBatchesDTO.class.getSimpleName() + "[", "]")
                .add("detailId=" + detailId)
                .add("batch='" + batch + "'")
                .add("expiration='" + expiration + "'")
                .add("manufacturer='" + manufacturer + "'")
                .add("productionDate='" + productionDate + "'")
                .add("exterior=" + exterior)
                .add("gasBottleBarcode='" + gasBottleBarcode + "'")
                .add("uniqueBarCode='" + uniqueBarCode + "'")
                .add("type=" + type)
                .toString();
    }
}
