package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @Date 2020/11/18 17:50
 * @Description
 **/
public enum OrderConfirmStatusEnum {

    ALL_CONFIRM_STATUS(-1,"全部"),
    NO_CONFIRM_RECORD(0,"未备案"),
    HAS_CONFIRM_RECORD(1,"已备案");

    public final Integer value;

    public final String name;

    OrderConfirmStatusEnum(int value, String name){
        this.value = value;
        this.name = name;
    }

    public final Integer getValue() {
        return value;
    }

    public final String getDesc() {
        return name;
    }

    public static final OrderConfirmStatusEnum getByName(String name) {
        for (OrderConfirmStatusEnum orderConfirmStatusEnum : OrderConfirmStatusEnum.values()) {
            if (orderConfirmStatusEnum.name.equals(name)){
                return orderConfirmStatusEnum;
            }
        }
        return null;
    }

    public static final OrderConfirmStatusEnum getByValue(Integer value) {
        for (OrderConfirmStatusEnum orderConfirmStatusEnum : OrderConfirmStatusEnum.values()) {
            if (orderConfirmStatusEnum.value.equals(value)){
                return orderConfirmStatusEnum;
            }
        }
        return null;
    }
}
