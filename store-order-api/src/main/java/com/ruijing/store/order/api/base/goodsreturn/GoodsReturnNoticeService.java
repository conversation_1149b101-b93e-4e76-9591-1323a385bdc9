package com.ruijing.store.order.api.base.goodsreturn;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcMethod;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcApi;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.store.order.api.base.goodsreturn.dto.GoodsReturnNoticeDTO;

/**
 * <AUTHOR>
 * @date 2022/9/16 16:33
 * @description
 */
@RpcApi(
        value = "退货相关通知服务",
        description = "退货相关通知服务"
)
public interface GoodsReturnNoticeService {

    /**
     * 退货前校验，通知对应的服务需要校验，获取返回值后如果为RemoteResponse的code不为200，则阻止退货
     * @param goodsReturnNoticeDTO 订单数据
     * @return 是否成功
     */
    @RpcMethod("退货前校验，通知对应的服务需要校验--获取返回值后如果为RemoteResponse的code不为200，则阻止退货")
    default RemoteResponse<Boolean> verifyBeforeApplyGoodsReturn(GoodsReturnNoticeDTO goodsReturnNoticeDTO){
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    /**
     * 通知订单发起申请退货成功
     *
     * @param goodsReturnNoticeDTO
     * @return
     */
    @RpcMethod("通知订单发起申请退货成功")
    default RemoteResponse<Boolean> applyGoodsReturnComplete(GoodsReturnNoticeDTO goodsReturnNoticeDTO) {
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }

    /**
     * 通知退货单撤销
     *
     * @param goodsReturnNoticeDTO
     * @return
     */
    @RpcMethod("通知退货单撤销")
    default RemoteResponse<Boolean> goodsReturnCancelComplete(GoodsReturnNoticeDTO goodsReturnNoticeDTO) {
        return RemoteResponse.<Boolean>custom().setSuccess().setData(true);
    }
}
