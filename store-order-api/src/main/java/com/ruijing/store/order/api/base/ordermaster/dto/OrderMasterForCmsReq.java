package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 接口入参
 * @author: zhuk
 * @create: 2019-07-02 10:49
 **/
public class OrderMasterForCmsReq implements Serializable {

    private static final long serialVersionUID = 7738102201815279744L;
    /**
     * 采购人id
     */
    private Integer buyerId;

    /**
     * 状态列表
     */
    private List<Integer> statusList;

    public Integer getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Integer buyerId) {
        this.buyerId = buyerId;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    @Override
    public String toString() {
        return "OrderMasterForCmsReq{" +
                "buyerId=" + buyerId +
                ", statusList=" + statusList +
                '}';
    }
}
