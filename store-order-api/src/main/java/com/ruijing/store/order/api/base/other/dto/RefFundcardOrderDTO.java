package com.ruijing.store.order.api.base.other.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2019/10/9 15:13
 **/
public class RefFundcardOrderDTO implements Serializable {
    private static final long serialVersionUID = 4660456596420368505L;
    /**
     * uuid
     */
    private String id;

    /**
     * 申请单ID
     */
    private String applicationId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 竞价单ID
     */
    private String bidId;

    /**
     * 经费卡ID
     */
    private String cardId;

    /**
     * 科目ID
     */
    private String subjectId;

    /**
     * 序号
     */
    private Integer serialNumber;

    /**
     * 订单使用金额
     */
    private BigDecimal usemoney;

    /**
     * 逻辑删除时间
     */
    private Date deletionTime;

    /**
     * 是否被删除
     */
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private Date creationTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 经费卡编号
     */
    private String cardNo;

    /**
     * 中肿经费卡类型0普卡3零余额卡4民口账户
     */
    private Integer fundType;

    /**
     * 中大审批人id
     */
    private Integer approveUser;

    /**
     * 采购申请或订单对应的经费卡需要冻结的金额
     */
    private BigDecimal freezeAmount;

    /**
     * 实验耗材费，只有中肿用
     */
    private BigDecimal consumablesFee;

    /**
     * 测试分析费，只有中肿用
     */
    private BigDecimal analysisFee;

    /**
     * 经费卡项目id，只有中肿用
     */
    private String projectId;

    /**
     * 院区编码
     */
    private String campusCode;

    /**
     * 院区名称
     */
    private String campusName;

    /**
     * 经费支出申请单号
     */
    private String expenseApplyNo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBidId() {
        return bidId;
    }

    public void setBidId(String bidId) {
        this.bidId = bidId;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(String subjectId) {
        this.subjectId = subjectId;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public BigDecimal getUsemoney() {
        return usemoney;
    }

    public void setUsemoney(BigDecimal usemoney) {
        this.usemoney = usemoney;
    }

    public Date getDeletionTime() {
        return deletionTime;
    }

    public void setDeletionTime(Date deletionTime) {
        this.deletionTime = deletionTime;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getFundType() {
        return fundType;
    }

    public void setFundType(Integer fundType) {
        this.fundType = fundType;
    }

    public Integer getApproveUser() {
        return approveUser;
    }

    public void setApproveUser(Integer approveUser) {
        this.approveUser = approveUser;
    }

    public BigDecimal getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(BigDecimal freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public BigDecimal getAnalysisFee() {
        return analysisFee;
    }

    public void setAnalysisFee(BigDecimal analysisFee) {
        this.analysisFee = analysisFee;
    }

    public BigDecimal getConsumablesFee() {
        return consumablesFee;
    }

    public void setConsumablesFee(BigDecimal consumablesFee) {
        this.consumablesFee = consumablesFee;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getCampusCode() {
        return campusCode;
    }

    public void setCampusCode(String campusCode) {
        this.campusCode = campusCode;
    }

    public String getCampusName() {
        return campusName;
    }

    public void setCampusName(String campusName) {
        this.campusName = campusName;
    }

    public String getExpenseApplyNo() {
        return expenseApplyNo;
    }

    public void setExpenseApplyNo(String expenseApplyNo) {
        this.expenseApplyNo = expenseApplyNo;
    }
}
