package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @author: zhukai
 * @date : 2020/1/9 4:09 下午
 * @description: 订单验收审批入参对象
 */
@RpcModel("订单-验收审批")
public class OrderApprovalParamDTO implements Serializable {

    private static final long serialVersionUID = -3836284470713234076L;

    /**
     * 订单id
     */
    @RpcModelProperty("订单id")
    private Integer orderId;

    /**
     * 组织code
     */
    @RpcModelProperty("组织code")
    private String orgCode;

    /**
     * 用户ID
     */
    @RpcModelProperty("用户ID")
    private Integer userId;

    /**
     * 用户名称
     */
    @RpcModelProperty("用户名称")
    private String userName;

    /**
     * 用户全局唯一id
     */
    @RpcModelProperty("用户全局唯一id")
    private String userGuid;

    /**
     * 用户工号
     */
    @RpcModelProperty("用户工号")
    private String jobNumber;

    /**
     * 部门id
     */
    @RpcModelProperty("部门id")
    private Integer departmentId;

    /**
     * 审批原因
     */
    @RpcModelProperty("审批原因")
    private String reason;

    /**
     * 操作类型 1 审批通过，其他审批驳回。
     */
    @RpcModelProperty("操作类型 1 审批通过，其他审批驳回")
    private Integer operateType;

    /**
     * 经费状态
     */
    @RpcModelProperty("经费状态")
    private Integer fundStatus;

    /**
     * 审批方式,0手动审批,1自动审批
     */
    private Integer approvalMode;

    /**
     * 电子签名密码
     */
    @RpcModelProperty("电子签名密码(是否使用电子签名为true是否免密为false时必填)")
    private String password;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getJobNumber() {
        return jobNumber;
    }

    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public Integer getApprovalMode() {
        return approvalMode;
    }

    public void setApprovalMode(Integer approvalMode) {
        this.approvalMode = approvalMode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderApprovalParamDTO{");
        sb.append("orderId=").append(orderId);
        sb.append(", orgCode='").append(orgCode).append('\'');
        sb.append(", userId=").append(userId);
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", userGuid='").append(userGuid).append('\'');
        sb.append(", jobNumber='").append(jobNumber).append('\'');
        sb.append(", departmentId=").append(departmentId);
        sb.append(", reason='").append(reason).append('\'');
        sb.append(", operateType=").append(operateType);
        sb.append(", fundStatus=").append(fundStatus);
        sb.append(", approvalMode=").append(approvalMode);
        sb.append(", password='").append(password).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
