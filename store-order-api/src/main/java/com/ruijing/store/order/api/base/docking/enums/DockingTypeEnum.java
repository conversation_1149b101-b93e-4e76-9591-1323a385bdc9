package com.ruijing.store.order.api.base.docking.enums;


/**
 * <AUTHOR>
 */

public enum DockingTypeEnum {

    // 对接类型
    Order(0),
    Statement(1),
    Purchase(2);

    private Integer value;

    DockingTypeEnum(Integer value) {
        this.value = value;
    }

    public static DockingTypeEnum valueOf(Integer value) {
        for (DockingTypeEnum processSpecies : DockingTypeEnum.values()) {
            if (processSpecies.getValue().equals(value)) {
                return processSpecies;
            }
        }
        throw new RuntimeException("Unknow enum value: " + value);
    }

    public Integer getValue() {
        return value;
    }
}
