package com.ruijing.store.order.api.base.delivery.enums;

/**
 * <AUTHOR>
 * @date 2023/1/29 9:49
 * @description 代配送来源类型枚举
 */
public enum DeliveryProxySourceTypeEnum {

    /**
     * 未开启代配送，默认值
     */
    NONE(0, "无"),

    /**
     * 商家开启
     */
    SUPP(1, "商家开启"),

    /**
     * 采购人手动开启
     */
    BUYER(2, "采购人开启");

    /**
     * 代码值
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;


    DeliveryProxySourceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DeliveryProxySourceTypeEnum getByValue(Integer value) {
        for (DeliveryProxySourceTypeEnum enumItem : DeliveryProxySourceTypeEnum.values()) {
            if (enumItem.code.equals(value)) {
                return enumItem;
            }
        }
        return null;
    }
}
