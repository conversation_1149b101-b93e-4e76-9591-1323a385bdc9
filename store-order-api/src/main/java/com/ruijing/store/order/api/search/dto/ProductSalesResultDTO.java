package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;

/**
 * @description: 商品销量 结果对象
 * @author: zhuk
 * @create: 2019-09-09 15:43
 **/
public class ProductSalesResultDTO implements Serializable {

    private static final long serialVersionUID = 7058269029946474218L;

    /**
     * 产品Id
     */
    private Long productId;

    /**
     * 数量
     */
    private Double quantity;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    @Override
    public String toString() {
        return "ProductSalesResultDTO{" +
                "productId=" + productId +
                ", quantity=" + quantity +
                '}';
    }
}
