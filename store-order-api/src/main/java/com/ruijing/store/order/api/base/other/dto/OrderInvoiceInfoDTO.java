package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/16 16:21
 * @Description
 **/
public class OrderInvoiceInfoDTO implements Serializable {

    private static final long serialVersionUID = -8989155252494241639L;

    /**
     * 发票id
     */
    @RpcModelProperty("发票id")
    private Integer invoiceId;

    /**
     *发票号
     */
    @RpcModelProperty("发票号")
    private String invoiceNo;

    /**
     * 发票code
     */
    @RpcModelProperty("发票code")
    private String invoiceCode;

    /**
     *发票金额
     */
    @RpcModelProperty("发票金额")
    private BigDecimal amount;

    /**
     *发票日期
     */
    @RpcModelProperty("发票日期")
    private String issueDate;

    /**
     *发票备注
     */
    @RpcModelProperty("发票备注")
    private String remark;

    /**
     *开票人
     */
    @RpcModelProperty("开票人")
    private String drawer;

    /**
     *银行名字
     */
    @RpcModelProperty("银行名字")
    private String bankName;

    /**
     *银行编号
     */
    @RpcModelProperty("银行编号")
    private String bankNo;

    /**
     *订单号
     */
    @RpcModelProperty("订单号")
    private List<String> orderNoList;

    /**
     *发票图片链接
     */
    @RpcModelProperty("发票图片链接")
    private List<String> picturePathList;

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public OrderInvoiceInfoDTO setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
        return this;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public OrderInvoiceInfoDTO setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
        return this;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public OrderInvoiceInfoDTO setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
        return this;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public OrderInvoiceInfoDTO setAmount(BigDecimal amount) {
        this.amount = amount;
        return this;
    }

    public String getIssueDate() {
        return issueDate;
    }

    public OrderInvoiceInfoDTO setIssueDate(String issueDate) {
        this.issueDate = issueDate;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public OrderInvoiceInfoDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getDrawer() {
        return drawer;
    }

    public OrderInvoiceInfoDTO setDrawer(String drawer) {
        this.drawer = drawer;
        return this;
    }

    public String getBankName() {
        return bankName;
    }

    public OrderInvoiceInfoDTO setBankName(String bankName) {
        this.bankName = bankName;
        return this;
    }

    public String getBankNo() {
        return bankNo;
    }

    public OrderInvoiceInfoDTO setBankNo(String bankNo) {
        this.bankNo = bankNo;
        return this;
    }

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public OrderInvoiceInfoDTO setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
        return this;
    }

    public List<String> getPicturePathList() {
        return picturePathList;
    }

    public OrderInvoiceInfoDTO setPicturePathList(List<String> picturePathList) {
        this.picturePathList = picturePathList;
        return this;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderInvoiceInfoDTO{");
        sb.append("invoiceId=").append(invoiceId);
        sb.append(", invoiceNo='").append(invoiceNo).append('\'');
        sb.append(", invoiceCode='").append(invoiceCode).append('\'');
        sb.append(", amount=").append(amount);
        sb.append(", issueDate='").append(issueDate).append('\'');
        sb.append(", remark='").append(remark).append('\'');
        sb.append(", drawer='").append(drawer).append('\'');
        sb.append(", bankName='").append(bankName).append('\'');
        sb.append(", bankNo='").append(bankNo).append('\'');
        sb.append(", orderNoList=").append(orderNoList);
        sb.append(", picturePathList=").append(picturePathList);
        sb.append('}');
        return sb.toString();
    }
}
