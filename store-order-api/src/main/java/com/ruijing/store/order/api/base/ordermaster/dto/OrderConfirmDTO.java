package com.ruijing.store.order.api.base.ordermaster.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;
import com.ruijing.base.swagger.api.rpc.annotation.RpcModel;

import java.io.Serializable;
import java.util.Date;

/**
 * Name: OrderConfirmDTO
 * Description:
 *
 * <AUTHOR>
 * @version 1.0
 * Date: 2023/12/4
 */
@RpcModel("供应商确认订单入参")
public class OrderConfirmDTO implements Serializable {


    /**
     * 订单Id
     */
    @RpcModelProperty("订单Id")
    private Integer orderId;

    /**
     * 订单编号
     */
    @RpcModelProperty("订单编号")
    private String orderNo;


    /**
     * 订单确认时间
     */
    @RpcModelProperty("订单确认时间")
    private Date confirmDate;


    /**
     * 供应商确认人Id
     */
    @RpcModelProperty("供应商确认人Id")
    private String confirmManId;

    /**
     * 供应商确认人名称
     */
    @RpcModelProperty("供应商确认人名称")
    private String confirmMan;


    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Date getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
    }

    public String getConfirmManId() {
        return confirmManId;
    }

    public void setConfirmManId(String confirmManId) {
        this.confirmManId = confirmManId;
    }

    public String getConfirmMan() {
        return confirmMan;
    }

    public void setConfirmMan(String confirmMan) {
        this.confirmMan = confirmMan;
    }
}
