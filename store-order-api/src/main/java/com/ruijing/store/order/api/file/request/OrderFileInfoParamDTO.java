package com.ruijing.store.order.api.file.request;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29 10:31
 * @description
 */
@RpcModel("文件上传-获取上传文件信息")
public class OrderFileInfoParamDTO implements Serializable {

    private static final long serialVersionUID = -2808317604363597414L;
    
    @RpcModelProperty("订单id列表-必传")
    private List<Integer> orderIdList;
    
    @RpcModelProperty("文件业务类型-可空")
    private List<Integer> fileBusinessTypeList;

    public List<Integer> getOrderIdList() {
        return orderIdList;
    }

    public void setOrderIdList(List<Integer> orderIdList) {
        this.orderIdList = orderIdList;
    }

    public List<Integer> getFileBusinessTypeList() {
        return fileBusinessTypeList;
    }

    public void setFileBusinessTypeList(List<Integer> fileBusinessTypeList) {
        this.fileBusinessTypeList = fileBusinessTypeList;
    }

    @Override
    public String toString() {
        return "OrderFileInfoRequestDTO{" +
                "orderIdList=" + orderIdList +
                ", fileBusinessTypeList=" + fileBusinessTypeList +
                '}';
    }
}
