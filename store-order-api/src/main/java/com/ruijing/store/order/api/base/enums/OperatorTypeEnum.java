package com.ruijing.store.order.api.base.enums;

/**
 * <AUTHOR>
 * @description：
 * @date ：Created in 2022-11-29 15:31
 */
public enum  OperatorTypeEnum {
    PURCHASE(1, "采购用户"),
    HMS(2, "HMS用户"),
    SYSTEM(3, "系统"),
    OMS(4, "OMS用户");
    public final Integer value;
    public final String name;

    OperatorTypeEnum(int value, String msg) {
        this.value = value;
        this.name = msg;
    }
    public Integer getValue()
    {
        return this.value;
    }

    public String getName()
    {
        return this.name;
    }

    public static final OperatorTypeEnum get(final int type) {
        for (OperatorTypeEnum typeEnum : OperatorTypeEnum.values()) {
            if (typeEnum.value == type){
                return typeEnum;
            }
        }
        return null;
    }
}
