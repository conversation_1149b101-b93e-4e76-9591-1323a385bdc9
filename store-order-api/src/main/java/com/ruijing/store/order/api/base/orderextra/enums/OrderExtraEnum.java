package com.ruijing.store.order.api.base.orderextra.enums;

import java.util.Objects;

/**
 * @Author: <PERSON><PERSON>
 * @Description: 订单拓展表的描述枚举（针对单位个性化业务，如陆军军医的第二验收人记录）
 * @DateTime: 2021/6/28 17:38
 */
public enum OrderExtraEnum {

    /**
     * 第二验收人
     */
    SECOND_RECEIVER_NAME(1, "第二验收人"),

    /**
     * 修改订单收货地址
     */
    MODIFY_ADDRESS(2, "修改订单收货地址"),

    /**
     * 单据到达时间
     */
    DOCUMENT_ARRIVAL_TIME(3, "单据到达时间"),

    /**
     * 收货地址对应校区编号
     */
    RECEIPT_ADDRESS_CAMPUS_CODE(4, "收货地址对应校区编号"),

    /**
     * 收货地址对应校区名
     */
    RECEIPT_ADDRESS_CAMPUS_NAME(5, "收货地址对应校区名"),

    /**
     * 经费审批流标记
     */
    FLOW_FUND_TYPE(6, "经费审批流标记"),

    /**
     * 赛默飞优惠券码
     */
    COUPON_CODE(7, "赛默飞优惠券码"),

    SELF_DEFINED_CATEGORY(8, "自定义采购分类"),

    /**
     * 经费卡的校区字段
     */
    CAMPUS_NAME_ON_FUNDCARD(9, "经费卡的校区字段"),

    /**
     * 采购类型，集中采购还是分散采购
     */
    PURCHASE_TYPE(10, "采购类型"),

    /**
     * 是否已上传服务技术合同书(常州市一需求)
     */
    UPLOAD_SERVICE_TECHNOLOGY_CONTRACT_STATUS(11, "是否已上传服务技术合同书"),

    /**
     * 有权限验收审批的人，值类型List<Integer>
     */
    ACCEPT_APPROVE_USERS(12, "有权限验收审批的人", java.util.List.class),

    /**
     * 是否属于试用订单，0为否，1为是。
     */
    IS_TRIAL_ORDER(13, "是否属于试用订单"),

    /**
     * 使用的优惠券ID
     */
    USED_COUPON_ID(14, "使用的优惠券ID"),

    ADVERTISEMENT_ID(15, "关联的广告id"),

    /**
     * 购销合同（管制品订单），目前仅中大使用
     */
    BUY_SALE_CONTRACT(16, "购销合同（管制品用）"),

    ACCEPT_APPROVE_LEVEL(17, "订单验收审批等级"),

    /**
     * 订单对接版本，0旧单，1新单
     */
    DOCKING_VERSION(18, "订单对接版本"),


    ORDER_ATTR(19,"资产属性"),


    RISK_VERIFIED_STATUS(20,"订单待核实"),

    STATEMENT_WAY_ID(21, "结算方式id", false),

    /**
     * 对应结算方式id的快照--是否启用经费对接 1--是 0--否
     */
    STATEMENT_WAY_ENABLE_FUND_DOCKING(22, "结算方式-是否启用经费对接", false),

    /**
     * 对应结算方式id的快照--是否使用结算系统 1--是 0--否
     */
    STATEMENT_WAY_USE_STATEMENT(23, "结算方式-是否使用结算系统", false),

    /**
     * 缺货标签，目前只有当当网对接用到。 1--是， 0--否
     */
    LACK_OF_GOODS(24, "缺货", false),

    /**
     * 货物已全部到达，针对当当网类对接，用于判断是否可以推送退货单，1--是，0--否
     */
    GOODS_ALL_ARRIVAL(25, "货物已全部到达"),

    /**
     * 订单备注
     */
    REMARK(26, "备注"),

    /**
     * 是否一物一码单，1--是，0--否
     */
    EACH_PRODUCT_EACH_CODE(27, "是否一物一码单", false),

    /**
     * 商家是否需要填写批次信息，1--是，0--否
     */
    SUPP_NEED_FILL_BATCHES_DATA(28, "商家是否需要填写批次信息", false),

    /**
     * 外部供应商状态--目前只有当当使用
     */
    OUTER_SUPP_STATUS(29, "外部供应商状态"),

    /**
     * 验收方式，{@link com.ruijing.store.order.api.base.enums.OrderAcceptanceWayEnum}
     */
    ACCEPTANCE_WAY(30, "验收方式", false),

    /**
     * 货仓ID，目前用的是库房ID
     */
    STOCK_WAREHOUSE_ID(31, "货仓ID"),

    /**
     * 货仓类型
     * {@link com.ruijing.shop.goods.api.enums.StockTypeEnum}
     */
    STOCK_WAREHOUSE_TYPE(32, "货仓类型"),

    /**
     * 是否用户订阅了供应商，1是,否则不写数据
     */
    CUSTOMER_SUBSCRIBE_SUPP(33, "是否已关注用户下单"),

    /**
     * 是否老用户/常客下单（对供应商来说），1是，否则不写数据
     */
    REGULAR_CUSTOMER_PURCHASE(34, "是否老用户下单"),

    /**
     * 订单变为待结算时间
     */
    WAITING_STATEMENT_TIME(35, "订单变为待结算时间"),

    /**
     * 绑定卡的fund_type字段。解绑后也不会删除，杭州三院用于判断是否需要调用科管的报销接口和解冻接口（管理平台接口）。
     */
    BIND_CARD_FUND_TYPE(36, "绑定卡的经费类型", false),

    /**
     * 出库科室，中科大附一专属字段
     */
    EXIT_DEPT(37, "出库科室", false),

    /**
     * 实验数据网盘链接,江西中医附院定制
     */
    EXPERIMENT_DATA_URL(38, "实验数据网盘链接", false),

    /**
     * 订单列表对接错误信息提示，对应小红点。目前暨大使用，后续单位对接考虑都收归到这里
     */
    ORDER_LIST_DOCKING_ERROR_HINT(39, "订单列表对接错误信息提示", false),

    /**
     * 商家是否需要填写商品信息，1--是，0--否
     */
    SUPP_NEED_FILL_PRODUCT_DATA(40, "商家是否需要填写商品信息", false),

    /**
     * 报账状态，1--已报账
     */
    REPORT_EXPENSE_STATUS(41, "报账状态"),
    ;

    /**
     * 代码值
     */
    private final Integer value;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 数据类型
     */
    private final Class<?> dataTypeClass;

    /**
     * 是否需要同步到搜索
     */
    private final Boolean needSyncToSearch;

    OrderExtraEnum(Integer value, String desc) {
        this(value, desc, String.class);
    }

    OrderExtraEnum(Integer value, String desc, Boolean needSyncToSearch) {
        this(value, desc, String.class, needSyncToSearch);
    }

    OrderExtraEnum(Integer value, String desc, Class<?> dataTypeClass) {
        this(value, desc, dataTypeClass, true);
    }

    OrderExtraEnum(Integer value, String desc, Class<?> dataTypeClass, Boolean needSyncToSearch) {
        this.value = value;
        this.desc = desc;
        this.dataTypeClass = dataTypeClass;
        this.needSyncToSearch = needSyncToSearch;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public Class<?> getDataTypeClass() {
        return dataTypeClass;
    }

    public Boolean getNeedSyncToSearch() {
        return needSyncToSearch;
    }

    public static OrderExtraEnum getByValue(Integer value) {
        for (OrderExtraEnum orderExtraEnum : OrderExtraEnum.values()) {
            if (Objects.equals(orderExtraEnum.value, value)) {
                return orderExtraEnum;
            }
        }
        return null;
    }
}
