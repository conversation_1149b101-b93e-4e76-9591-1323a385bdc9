package com.ruijing.store.order.api.base.other.dto;


import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * @description: 退货单详情
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020/12/30 11:15
 **/
public class GoodsReturnDetailDTO implements Serializable {

    private static final long serialVersionUID = -7028750467053359069L;

    @RpcModelProperty("商品名称")
    private String goodsName;

    @RpcModelProperty("商品货号")
    private String goodsCode;

    @RpcModelProperty("规格")
    private String specification;

    @RpcModelProperty("品牌")
    private String brand;

    @RpcModelProperty("商品单价")
    private String price;

    @RpcModelProperty("商品数量")
    private String quantity;

    @RpcModelProperty("商品总价")
    private String amount;

    @RpcModelProperty("商品图片")
    private String goodsPicturePath;

    @RpcModelProperty("订单详情id")
    private Integer detailId;
    
    @RpcModelProperty("商品单位")
    private String unit;

    @RpcModelProperty("商品id")
    private String productId;

    @RpcModelProperty("一并退货的气瓶二维码，非一物一码模式才需要")
    private List<String> returnGasBottleBarcodes;

    @RpcModelProperty("退货原因")
    private String returnReason;

    @RpcModelProperty("说明")
    private String remark;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsCode() {
        return goodsCode;
    }

    public void setGoodsCode(String goodsCode) {
        this.goodsCode = goodsCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public List<String> getReturnGasBottleBarcodes() {
        return returnGasBottleBarcodes;
    }

    public GoodsReturnDetailDTO setReturnGasBottleBarcodes(List<String> returnGasBottleBarcodes) {
        this.returnGasBottleBarcodes = returnGasBottleBarcodes;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public GoodsReturnDetailDTO setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", GoodsReturnDetailDTO.class.getSimpleName() + "[", "]")
                .add("goodsName='" + goodsName + "'")
                .add("goodsCode='" + goodsCode + "'")
                .add("specification='" + specification + "'")
                .add("brand='" + brand + "'")
                .add("price='" + price + "'")
                .add("quantity='" + quantity + "'")
                .add("amount='" + amount + "'")
                .add("goodsPicturePath='" + goodsPicturePath + "'")
                .add("detailId=" + detailId)
                .add("unit='" + unit + "'")
                .add("productId='" + productId + "'")
                .add("returnGasBottleBarcodes=" + returnGasBottleBarcodes)
                .add("returnReason='" + returnReason + "'")
                .add("remark='" + remark + "'")
                .toString();
    }
}
