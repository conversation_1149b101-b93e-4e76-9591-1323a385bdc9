package com.ruijing.store.order.api.base.orderapprovallog.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: OrderApproLogDTO
 * @author: zhuk
 * @create: 2019-07-05 16:54
 **/
public class OrderApprovalLogDTO implements Serializable {

    private static final long serialVersionUID = 6605635159026587951L;

    private Integer id;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 收货照片,多张用分号分隔
     */
    private String photo;

    /**
     * 审批原因
     */
    private String reason;

    /**
     * 审批结果状态0驳回1通过
     */
    private Integer approveStatus;

    /**
     * 操作人ID
     */
    private Integer operatorId;

    /**
     * 操作时间
     */
    private Date creationTime;

    public Integer getId() {
        return id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public String getPhoto() {
        return photo;
    }

    public String getReason() {
        return reason;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public Integer getOperatorId() {
        return operatorId;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public void setOperatorId(Integer operatorId) {
        this.operatorId = operatorId;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }
}
