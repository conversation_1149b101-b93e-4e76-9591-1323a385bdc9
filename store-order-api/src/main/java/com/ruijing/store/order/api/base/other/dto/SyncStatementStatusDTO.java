package com.ruijing.store.order.api.base.other.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;

/**
 * @description: 结算状态同步
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2021/12/22 15:36
 **/
public class SyncStatementStatusDTO implements Serializable {
    private static final long serialVersionUID = 5736836709196539810L;
    /**
     * 订单id
     */
    @RpcModelProperty(description = "订单id", required = true)
    private Integer statementId;

    /**
     * 结算状态
     */
    @RpcModelProperty(description = "结算状态", required = true)
    private Integer statementStatus;

    public Integer getStatementId() {
        return statementId;
    }

    public void setStatementId(Integer statementId) {
        this.statementId = statementId;
    }

    public Integer getStatementStatus() {
        return statementStatus;
    }

    public void setStatementStatus(Integer statementStatus) {
        this.statementStatus = statementStatus;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("StatementStatusDTO{");
        sb.append("statementId=").append(statementId);
        sb.append(", statementStatus=").append(statementStatus);
        sb.append('}');
        return sb.toString();
    }
}
