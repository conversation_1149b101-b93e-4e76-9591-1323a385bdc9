package com.ruijing.store.order.api.search.dto;

import java.io.Serializable;
import java.util.List;
/**
 * @description: dtp
 * @author: 曾文聪
 * @create: 2019-09-17
 **/
public class OrderOrgStatParamDTO implements Serializable {

    private static final long serialVersionUID = 7345131316683995212L;
    /**
     * 医院id
     */
    private Integer orgId;
    /**
     * top 前几
     */
    private Integer top;
    /**
     * 合作商id
     */
    private List<Integer> suppIds;
    /**
     * 状态id
     */
    private List<Integer> statuses;

    /**
     * 机构编码
     */
    private List<String> orgCodeList;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     *结束时间
     */
    private String endTime;

    public Integer getOrgId() {
        return orgId;
    }

    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    public Integer getTop() {
        return top;
    }

    public void setTop(Integer top) {
        this.top = top;
    }

    public List<Integer> getStatuses() {
        return statuses;
    }

    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }

    public List<Integer> getSuppIds() {
        return suppIds;
    }

    public void setSuppIds(List<Integer> suppIds) {
        this.suppIds = suppIds;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<String> getOrgCodeList() {
        return orgCodeList;
    }

    public void setOrgCodeList(List<String> orgCodeList) {
        this.orgCodeList = orgCodeList;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OrderOrgStatParamDTO{");
        sb.append("orgId=").append(orgId);
        sb.append(", top=").append(top);
        sb.append(", suppIds=").append(suppIds);
        sb.append(", statuses=").append(statuses);
        sb.append(", orgCodeList=").append(orgCodeList);
        sb.append(", startTime='").append(startTime).append('\'');
        sb.append(", endTime='").append(endTime).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
