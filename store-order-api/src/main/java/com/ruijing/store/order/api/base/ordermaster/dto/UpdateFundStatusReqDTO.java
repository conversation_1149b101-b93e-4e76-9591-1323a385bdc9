package com.ruijing.store.order.api.base.ordermaster.dto;

import java.io.Serializable;

/**
 * @description: 根据订单号 修改订单经费状态 参数对象
 * @author: zhuk
 * @create: 2019-07-09 16:02
 **/
public class UpdateFundStatusReqDTO implements Serializable {

    private static final long serialVersionUID = -1842857131362287865L;
    /**
     * 订单号
     */
    private String orderMasterNo;

    /**
     * 经费状态
     */
    private Integer fundStatus;

    /**
     * 失败原因
     */
    private String failReason;

    public String getOrderMasterNo() {
        return orderMasterNo;
    }

    public void setOrderMasterNo(String orderMasterNo) {
        this.orderMasterNo = orderMasterNo;
    }

    public Integer getFundStatus() {
        return fundStatus;
    }

    public void setFundStatus(Integer fundStatus) {
        this.fundStatus = fundStatus;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    @Override
    public String toString() {
        return "UpdateFundStatusReqDTO{" +
                "orderMasterNo=" + orderMasterNo +
                ", fundStatus=" + fundStatus +
                '}';
    }
}
