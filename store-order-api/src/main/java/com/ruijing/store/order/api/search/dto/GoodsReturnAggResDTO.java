package com.ruijing.store.order.api.search.dto;

import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModel;
import com.ruijing.base.biz.api.server.api.rpc.annotation.RpcModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Zeng <PERSON>
 * @Description:
 * @DateTime: 2022/5/16 15:18
 */
@RpcModel
public class GoodsReturnAggResDTO implements Serializable {

    private static final long serialVersionUID = -9014829244796237709L;

    @RpcModelProperty("聚合字段的Id")
    private Long aggFieldId;

    @RpcModelProperty("退货金额")
    private BigDecimal returnAmount;

    public Long getAggFieldId() {
        return aggFieldId;
    }

    public void setAggFieldId(Long aggFieldId) {
        this.aggFieldId = aggFieldId;
    }

    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("GoodsReturnAggResDTO{");
        sb.append("aggFieldId=").append(aggFieldId);
        sb.append(", returnAmount=").append(returnAmount);
        sb.append('}');
        return sb.toString();
    }
}
